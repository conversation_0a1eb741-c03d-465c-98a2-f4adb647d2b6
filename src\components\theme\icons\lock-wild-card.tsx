export default function LockWildCardIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#665E66"
        d="M9.437 11.962a1.143 1.143 0 0 1-1.063 1.1 22.586 22.586 0 0 1-3.276 0 1.143 1.143 0 0 1-1.064-1.1 112.908 112.908 0 0 1 0-7.684A1.143 1.143 0 0 1 5.097 3.18a22.596 22.596 0 0 1 3.277 0 1.143 1.143 0 0 1 1.064 1.1c.013.376.023.753.032 1.13h2.198c-.01-.403-.021-.805-.035-1.206A3.335 3.335 0 0 0 8.535.988a24.6 24.6 0 0 0-3.599 0 3.336 3.336 0 0 0-3.098 3.215 114.84 114.84 0 0 0 0 7.834 3.336 3.336 0 0 0 3.098 3.215 24.578 24.578 0 0 0 3.6 0 3.337 3.337 0 0 0 3.097-3.215l.02-.645H9.455l-.018.57Z"
      />
      <path
        fill="#544F57"
        d="M8.374 3.178c-.361-.027-.724-.044-1.085-.053a21.469 21.469 0 0 1 2.193.053 1.143 1.143 0 0 1 1.063 1.099c.013.376.024.753.033 1.131H9.47c-.01-.378-.02-.755-.033-1.131a1.143 1.143 0 0 0-1.063-1.1Z"
      />
      <path
        fill="#544F57"
        d="M6.044 15.252a3.336 3.336 0 0 1-3.098-3.215c-.089-2.602-.089-5.238 0-7.834A3.336 3.336 0 0 1 6.044.988c.414-.03.831-.05 1.246-.059a24.154 24.154 0 0 0-2.354.06 3.336 3.336 0 0 0-3.098 3.214 115.009 115.009 0 0 0 0 7.834 3.336 3.336 0 0 0 3.098 3.215 24.581 24.581 0 0 0 2.354.06c-.415-.01-.831-.03-1.246-.06Z"
      />
      <path
        fill="#F4DD45"
        d="M6.736 17.963c-1.454 0-2.92-.042-4.385-.126a2.084 2.084 0 0 1-1.96-1.949 49.164 49.164 0 0 1 0-6.208 2.084 2.084 0 0 1 1.96-1.949 77.038 77.038 0 0 1 8.77 0 2.083 2.083 0 0 1 1.96 1.949c.13 2.065.13 4.14-.001 6.208a2.084 2.084 0 0 1-1.96 1.95c-1.464.083-2.93.125-4.384.125Z"
      />
      <path
        fill="#ECC32E"
        d="M2.85 17.862a51.111 51.111 0 0 1-.499-.026 2.083 2.083 0 0 1-1.96-1.95 49.197 49.197 0 0 1 0-6.208A2.084 2.084 0 0 1 2.35 7.73l.498-.026a2.217 2.217 0 0 0-1.092 1.774 52.261 52.261 0 0 0 0 6.61 2.217 2.217 0 0 0 1.093 1.775Z"
      />
      <path
        fill="#F9F6F9"
        d="M18.289 18.973c-3.872.144-7.744.144-11.617 0a1.355 1.355 0 0 1-1.283-1.138 10.08 10.08 0 0 1 0-3.303 1.355 1.355 0 0 1 1.283-1.138 155.48 155.48 0 0 1 11.617 0 1.355 1.355 0 0 1 1.283 1.138 10.077 10.077 0 0 1 0 3.303 1.355 1.355 0 0 1-1.283 1.138Z"
      />
      <path
        className="fill-black"
        d="M19.86 14.482a1.653 1.653 0 0 0-1.56-1.383c-1.604-.06-3.216-.094-4.83-.105.005-1.11-.027-2.23-.098-3.335a2.378 2.378 0 0 0-2.235-2.223 77.853 77.853 0 0 0-6.872-.085c.004-.628.013-1.264.028-1.895a.293.293 0 1 0-.586-.014 113.09 113.09 0 0 0-.028 1.93c-.449.018-.898.039-1.345.064-.09.006-.18.016-.267.032.006-1.088.027-2.18.064-3.256A3.043 3.043 0 0 1 4.957 1.28a24.34 24.34 0 0 1 3.556 0 3.043 3.043 0 0 1 2.827 2.933c.01.293.02.596.027.904H9.756c-.008-.284-.016-.568-.026-.85a1.436 1.436 0 0 0-1.335-1.38 22.935 22.935 0 0 0-3.32 0 1.436 1.436 0 0 0-1.334 1.38.293.293 0 0 0 .585.021.85.85 0 0 1 .792-.817 22.335 22.335 0 0 1 3.234 0 .85.85 0 0 1 .792.817c.013.374.024.753.033 1.129a.293.293 0 0 0 .293.286h2.197a.293.293 0 0 0 .293-.3c-.01-.412-.02-.82-.035-1.21A3.628 3.628 0 0 0 8.556.696a24.928 24.928 0 0 0-3.642 0 3.628 3.628 0 0 0-3.369 3.496c-.039 1.142-.06 2.303-.065 3.457a2.38 2.38 0 0 0-1.382 2.01c-.13 2.07-.13 4.17 0 6.246a2.378 2.378 0 0 0 2.236 2.223c.954.054 1.917.09 2.867.11a1.652 1.652 0 0 0 1.46 1.026 156.057 156.057 0 0 0 9.962.053.293.293 0 0 0-.016-.586c-3.294.088-6.634.07-9.924-.053a1.066 1.066 0 0 1-1.005-.893 9.836 9.836 0 0 1 0-3.208 1.065 1.065 0 0 1 1.005-.892c3.847-.144 7.747-.144 11.594 0 .5.018.923.394 1.005.892a9.834 9.834 0 0 1 0 3.208 1.066 1.066 0 0 1-1.005.893l-.504.018a.293.293 0 0 0 .02.586l.506-.018a1.654 1.654 0 0 0 1.561-1.383 10.417 10.417 0 0 0 0-3.399ZM6.661 13.099A1.653 1.653 0 0 0 5.1 14.482a10.416 10.416 0 0 0-.036 3.167c-.895-.02-1.8-.055-2.696-.107a1.792 1.792 0 0 1-1.684-1.674 49.07 49.07 0 0 1-.001-6.172 1.792 1.792 0 0 1 1.684-1.675 77.248 77.248 0 0 1 4.368-.124c1.442 0 2.911.042 4.368.124a1.792 1.792 0 0 1 1.685 1.675c.069 1.092.1 2.199.096 3.296-2.079-.006-4.159.03-6.223.107Z"
      />
      <path
        fill="black"
        d="M8.977 15.368a.293.293 0 0 0-.4-.107l-.717.414v-.827a.293.293 0 0 0-.586 0v.827l-.716-.414a.293.293 0 1 0-.293.508l.716.413-.716.414a.293.293 0 1 0 .293.507l.716-.413v.827a.293.293 0 0 0 .586 0v-.827l.717.414a.292.292 0 0 0 .4-.108.293.293 0 0 0-.107-.4l-.717-.414.717-.413c.14-.081.188-.26.107-.4Zm3.223 0a.293.293 0 0 0-.4-.107l-.716.414v-.827a.293.293 0 1 0-.586 0v.827l-.717-.414a.293.293 0 1 0-.293.508l.717.413-.717.414a.293.293 0 1 0 .293.508l.717-.414v.827a.293.293 0 1 0 .586 0v-.827l.716.414a.293.293 0 1 0 .293-.507l-.716-.415.716-.413c.14-.081.188-.26.107-.4Zm3.225 0a.293.293 0 0 0-.4-.107l-.717.414v-.827a.293.293 0 1 0-.586 0v.827l-.716-.414a.293.293 0 1 0-.293.508l.716.413-.716.414a.293.293 0 0 0 .293.508l.716-.414v.827a.293.293 0 1 0 .586 0v-.827l.717.414a.293.293 0 1 0 .293-.507l-.717-.415.717-.413c.14-.081.188-.26.107-.4Zm2.107 1.322.717.414a.293.293 0 1 0 .293-.507l-.717-.415.716-.413a.293.293 0 0 0-.293-.508l-.716.414v-.827a.293.293 0 0 0-.586 0v.827l-.716-.414a.293.293 0 1 0-.293.508l.716.413-.716.414a.293.293 0 1 0 .293.508l.716-.414v.827a.293.293 0 0 0 .586 0v-.827ZM12.582 5.99a.292.292 0 0 0 .39-.14l.565-1.194a.293.293 0 1 0-.53-.25L12.442 5.6a.293.293 0 0 0 .14.39Zm.82 1.06a.293.293 0 1 0-.25.53l1.195.565a.292.292 0 0 0 .39-.14.293.293 0 0 0-.14-.39l-1.194-.564Zm-.355-.55a.293.293 0 0 0 .374.177l1.244-.445a.293.293 0 1 0-.197-.552l-1.244.446a.293.293 0 0 0-.178.374Z"
      />
    </svg>
  );
}
