import ButtonDelete from "@/components/container/button/button-delete";
import ButtonPaid from "@/components/container/button/button-paid";
import StatusColor from "@/components/container/color/statusColor";
import { putApiAppPaymentsById } from "@/client";
import { Skeleton } from "@/components/theme/ui/skeleton";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { swalError } from "@/lib/helper/swal-error";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { PiEye, PiInvoiceFill } from "react-icons/pi";
import { Button, Popover, Text, Tooltip } from "rizzui";
import type { SelectOption } from "rizzui";
import type {
  PaymentsDto,
  CreateUpdatePaymentsDto,
  ReservationDetailsDto,
} from "@/client";

export default function PopInvoice({
  item,
  getPaymentsLoading,
  getPayments,
  paymentStatusOptions,
  setModalPreview,
  setPaymentId,
  setAttachmentId,
  setResvDetailsId,
  label,
}: {
  item: ReservationDetailsDto;
  getPaymentsLoading: boolean;
  getPayments: (SelectOption & PaymentsDto)[];
  paymentStatusOptions: SelectOption[];
  setModalPreview: React.Dispatch<React.SetStateAction<boolean>>;
  setPaymentId: React.Dispatch<React.SetStateAction<string>>;
  setAttachmentId: React.Dispatch<React.SetStateAction<string>>;
  setResvDetailsId?: (id: string) => void;
  label?: string;
}) {
  const queryClient = useQueryClient();
  const [postLoading, setPostLoading] = useState<boolean>(false);
  const total = getPayments?.reduce((total, item) => {
    return total + Number(item.grantTotal);
  }, 0);
  const totalPaid = getPayments?.reduce((total, item) => {
    return item.status?.code == "paid"
      ? total + Number(item.grantTotal)
      : total;
  }, 0);

  // DEFINE MUTATIONS
  const updatePaymentsMutation = useMutation({
    mutationFn: async (dataMutation: PaymentsDto) => {
      const { id } = dataMutation;

      return putApiAppPaymentsById({
        path: { id: id! },
        body: dataMutation as CreateUpdatePaymentsDto,
      });
    },
    onSuccess: async () => {
      setPostLoading(false);
      await swal({
        title: "Success",
        text: "Check In Successfully",
        icon: "success",
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppPaymentDetailsById],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  // DEFINCE ACTIONS
  const updateStatus = async (id: string, code: "cancelled" | "paid") => {
    const data = getPayments?.find((e) => e.value === id);
    if (!data) return;
    setPostLoading(true);
    const statusId = paymentStatusOptions?.find((opt) => opt.code == code)
      ?.value as string; // GET STATUSID BY PAYMENT STATUS
    console.log("paymentStatusOptions", paymentStatusOptions);
    data.statusId = statusId;
    console.log("POST data", data);
    await updatePaymentsMutation.mutateAsync({
      id: data.id,
      statusId: statusId,
      paymentCode: data.paymentCode,
      reservationsId: data.reservationsId,
      paymentMethodId: data.paymentMethodId,
      totalAmount: data.totalAmount,
      paidAmount: data.paidAmount,
      vatRate: data.vatRate,
      vatAmount: data.vatAmount,
      grantTotal: data.grantTotal,
      transactionDate: data.transactionDate,
      taxId: data.taxId,
      paymentDetails: data.paymentDetails,
    });
    setPostLoading(false);
  };
  return (
    <Popover placement="left-start">
      <Popover.Trigger>
        <Tooltip size="sm" content="Invoices" placement="top">
          <Button
            size="sm"
            className="bg-yellow-500 text-white hover:bg-yellow-600"
            onClick={() => {
              if (setResvDetailsId) setResvDetailsId(item.id ?? "");
            }}
          >
            {label ? (
              <Text className="me-2">{label}</Text>
            ) : (
              <span className="flex items-center">
                <PiInvoiceFill className="h-4 w-4" />
              </span>
            )}
          </Button>
        </Tooltip>
      </Popover.Trigger>
      <Popover.Content>
        <div className="flex justify-between">
          <Text className="mb-2 text-sm font-bold">Invoice List</Text>
          <Text className="mb-2 text-sm font-bold">
            {item.reservation?.reservationCode}
          </Text>
        </div>
        <div>
          <table className="text-sm">
            <thead>
              <tr className="border-y">
                <th className="px-3 py-2 text-xs">Payment Code</th>
                <th className="px-3 py-2 text-xs">Amount</th>
                <th className="px-3 py-2 text-xs">Payments</th>
                <th className="px-3 py-2 text-xs">Status</th>
                <th className="px-3 py-2 text-xs">Action</th>
              </tr>
            </thead>
            <tbody>
              {!getPaymentsLoading &&
                getPayments?.map((e, i) => (
                  <tr key={i} className="border-b hover:bg-gray-100">
                    <td className="px-3 text-xs">{e.paymentCode}</td>
                    <td className="px-3 text-xs">
                      {formatCurrencyIDR(Number(e.grantTotal))}
                    </td>
                    <td className="px-3 text-xs">{e.paymentMethod?.name}</td>
                    <td className="px-3 text-xs">
                      <StatusColor
                        name={e?.status?.name}
                        color={e?.status?.color}
                      />
                    </td>
                    <td className="text-center">
                      <Button
                        size="sm"
                        variant="text"
                        className="px-2 hover:scale-125 hover:text-blue-500"
                        onClick={() => {
                          setModalPreview(true);
                          setPaymentId(String(e.value));
                          setAttachmentId(
                            String(e.paymentAttachments?.[0]?.id),
                          );
                        }}
                      >
                        <PiEye className="h-4 w-4" />
                      </Button>
                      {/* <Button
                      size="sm"
                      variant="text"
                      className="px-2 hover:text-red-500"
                      onClick={() => {
                        // 
                      }}
                    >
                      <PiTrash className="w-4 h-4" />
                    </Button> */}
                      <ButtonPaid
                        onClick={async () => {
                          await updateStatus(String(e.value), "cancelled");
                        }}
                        title={"Cancel Invoice?"}
                        text={
                          "This action will Cancel the selected invoice. Continue?"
                        }
                      />
                      <ButtonDelete
                        onClick={async () => {
                          await updateStatus(String(e.value), "cancelled");
                        }}
                        title={"Cancel Invoice?"}
                        text={
                          "This action will Cancel the selected invoice. Continue?"
                        }
                      />
                    </td>
                  </tr>
                ))}
              <tr className="border-y">
                {/* <td className="px-3 py-2 font-medium">
                  TOTAL ALL<br />
                </td>
                <td className="px-3 font-medium">
                  {formatCurrencyIDR(total)}<br />
                </td> */}
                <td className="px-3 py-2 font-medium">
                  <span className="text-green-500">TOTAL PAID</span>
                </td>
                <td colSpan={2} className="px-3 font-medium">
                  <span className="text-green-500">
                    {formatCurrencyIDR(totalPaid)}
                  </span>
                </td>
              </tr>
              {getPayments?.length === 0 && !getPaymentsLoading && (
                <tr>
                  <td colSpan={5} className="text-center">
                    <Text>No invoice found</Text>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {getPaymentsLoading && <Skeleton className="h-2 w-full" />}
      </Popover.Content>
    </Popover>
  );
}
