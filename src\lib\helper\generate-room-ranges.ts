interface Room {
  roomNumber: string;
}

interface RoomRange {
  title: string;
  range: number[];
}

export function generateRoomRanges(data: Room[]): RoomRange[] {
  const ranges = data.reduce(
    (acc: Record<string, { title: string; range: number[] }>, room: Room) => {
      const digitFloor = room.roomNumber.charAt(1);
      const floorKey = `Lantai : ${digitFloor}`;

      const pureRoomNumber = room.roomNumber.split("(")[0];
      const roomNum = Number(pureRoomNumber);

      if (!isNaN(roomNum)) {
        if (!acc[floorKey]) {
          acc[floorKey] = { title: floorKey, range: [] };
        }

        acc[floorKey].range.push(roomNum);
      }

      return acc;
    },
    {},
  );

  // console.log(ranges);

  const range = Object.values(ranges).map((group) => ({
    ...group,
    range: [Math.min(...group.range), Math.max(...group.range)],
  }));

  // console.log(range);

  return range;
}
