INSERT INTO [fowisma_dev].[dbo].[MasterTaxes]
       ([Id]
       ,[Code]
       ,[Name]
       ,[Rate]
       ,[StartDate]
       ,[EndDate]
       ,[IsActive]
       ,[ExtraProperties]
       ,[ConcurrencyStamp]
       ,[CreationTime]
       ,[CreatorId])
VALUES
       (NEWID(),            -- Id: gunakan GUID baru
       'VAT11',             -- Code
       'PPN 11%',           -- Name
       11.00,               -- Rate
       '2025-01-01',        -- StartDate
       '2025-12-31',        -- EndDate
       1,                   -- IsActive (1 = true, 0 = false)
       NULL,                -- ExtraProperties (gunakan NULL jika tidak digunakan)
       LOWER(REPLACE(CONVERT(VARCHAR(36), NEWID()), '-', '')),             -- ConcurrencyStamp (gunakan GUID untuk concurrency control)
       GETDATE(),           -- CreationTime
       'B570D4F5-7010-44A0-8CD9-73FD6F468EBA') -- CreatorId (gunakan sesuai User Id atau NULL) // ELKO