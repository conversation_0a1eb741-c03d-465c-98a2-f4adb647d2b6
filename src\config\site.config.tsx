import { type Metadata } from "next";
import { LAYOUT_OPTIONS } from "@/config/enums";
import logoImg from "@public/IMIP_Logo.png";
import logoIconImg from "@public/logo-imip.png";
import logoWhite from "@public/logo-imip-putih.png"
import { type OpenGraph } from "next/dist/lib/metadata/types/opengraph-types";
import { type ISiteConfig } from "@/types/user";

enum MODE {
  DARK = "dark",
  LIGHT = "light",
}

export const siteConfig: ISiteConfig = {
  title: "WISMA",
  description: `Lets make a great day `,
  logo: logoImg,
  logoWhite: logoWhite,
  icon: logoIconImg,
  mode: MODE.LIGHT,
  // layout: LAYOUT_OPTIONS.HYDROGEN,
  layout: LAYOUT_OPTIONS.LITHIUM,
  // TODO: favicon
};

export const metaObject = (
  title?: string,
  openGraph?: OpenGraph,
  description: string = siteConfig.description,
): Metadata => {
  return {
    title: title ? `${title} - WISMA` : siteConfig.title,
    description,
    openGraph: openGraph ?? {
      title: title ? `${title} - WISMA` : title,
      description,
      url: "https://isomorphic-furyroad.vercel.app",
      siteName: "WISMA", // https://developers.google.com/search/docs/appearance/site-names
      images: {
        url: "https://s3.amazonaws.com/redqteam.com/isomorphic-furyroad/itemdep/isobanner.png",
        width: 1200,
        height: 630,
      },
      locale: "en_US",
      type: "website",
    },
  };
};
