##### DEPENDENCIES

FROM --platform=linux/amd64 node:20-alpine AS deps
RUN apk add --no-cache libc6-compat openssl
WORKDIR /app

# Add support for cache directories
ARG YARN_CACHE_FOLDER
ARG NPM_CACHE_FOLDER

# Create cache directories if they are provided
RUN if [ -n "$YARN_CACHE_FOLDER" ]; then mkdir -p $YARN_CACHE_FOLDER; fi
RUN if [ -n "$NPM_CACHE_FOLDER" ]; then mkdir -p $NPM_CACHE_FOLDER; fi

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./

RUN \
  if [ -f yarn.lock ]; then \
    if [ -n "$YARN_CACHE_FOLDER" ]; then \
      echo "Using yarn cache folder: $YARN_CACHE_FOLDER"; \
      YARN_CACHE_FOLDER=$YARN_CACHE_FOLDER yarn --frozen-lockfile; \
    else \
      yarn --frozen-lockfile; \
    fi; \
  elif [ -f package-lock.json ]; then \
    if [ -n "$NPM_CACHE_FOLDER" ]; then \
      echo "Using npm cache folder: $NPM_CACHE_FOLDER"; \
      NPM_CONFIG_CACHE=$NPM_CACHE_FOLDER npm ci; \
    else \
      npm ci; \
    fi; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i; \
  else echo "Lockfile not found." && exit 1; \
  fi

##### BUILDER

FROM --platform=linux/amd64 node:20-alpine AS builder
# Environment variables needed at build time
ARG NODE_ENV
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_CLIENT_ID
ARG NEXT_PUBLIC_APP_URL
ARG NEXT_PUBLIC_SCOPE
ARG NEXT_PUBLIC_CLIENT_SECRET
ARG NEXT_PUBLIC_GOOGLE_ANALYTICS_TRACKING_ID
ARG NEXT_PUBLIC_UMAMI_SCRIPT_URL
ARG NEXT_PUBLIC_UMAMI_WEBSITE_ID
# Add SESSION_PASSWORD for build time (will be overridden at runtime)
ARG SESSION_PASSWORD=build-time-session-password-placeholder
# Add NODE_OPTIONS for increased memory during build
ARG NODE_OPTIONS=--max-old-space-size=4096
# Add support for cache directories
ARG YARN_CACHE_FOLDER
ARG NPM_CACHE_FOLDER

# Create cache directories if they are provided
RUN if [ -n "$YARN_CACHE_FOLDER" ]; then mkdir -p $YARN_CACHE_FOLDER; fi
RUN if [ -n "$NPM_CACHE_FOLDER" ]; then mkdir -p $NPM_CACHE_FOLDER; fi

# Set environment variables for build time
# Force NODE_ENV to be production for the build
ENV NODE_ENV=production
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_CLIENT_ID=${NEXT_PUBLIC_CLIENT_ID}
ENV NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
ENV NEXT_PUBLIC_SCOPE=${NEXT_PUBLIC_SCOPE}
ENV NEXT_PUBLIC_CLIENT_SECRET=${NEXT_PUBLIC_CLIENT_SECRET}
ENV NEXT_PUBLIC_GOOGLE_ANALYTICS_TRACKING_ID=${NEXT_PUBLIC_GOOGLE_ANALYTICS_TRACKING_ID}
ENV SESSION_PASSWORD=${SESSION_PASSWORD}
ENV NEXT_PUBLIC_UMAMI_SCRIPT_URL=${NEXT_PUBLIC_UMAMI_SCRIPT_URL}
ENV NEXT_PUBLIC_UMAMI_WEBSITE_ID=${NEXT_PUBLIC_UMAMI_WEBSITE_ID}

WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Check which config file exists and create appropriate symlinks if needed
RUN if [ -f next.config.mjs ]; then \
      echo "next.config.mjs exists, creating symlink to next.config.js"; \
      ln -sf next.config.mjs next.config.js; \
    elif [ -f next.config.js ]; then \
      echo "next.config.js exists"; \
    else \
      echo "No Next.js config file found!" && exit 1; \
    fi

ENV NEXT_TELEMETRY_DISABLED 1

RUN \
  if [ -f yarn.lock ]; then \
    if [ -n "$YARN_CACHE_FOLDER" ]; then \
      echo "Using yarn cache folder for build: $YARN_CACHE_FOLDER"; \
      YARN_CACHE_FOLDER=$YARN_CACHE_FOLDER SKIP_ENV_VALIDATION=1 yarn build; \
    else \
      SKIP_ENV_VALIDATION=1 yarn build; \
    fi; \
  elif [ -f package-lock.json ]; then \
    if [ -n "$NPM_CACHE_FOLDER" ]; then \
      echo "Using npm cache folder for build: $NPM_CACHE_FOLDER"; \
      NPM_CONFIG_CACHE=$NPM_CACHE_FOLDER SKIP_ENV_VALIDATION=1 npm run build; \
    else \
      SKIP_ENV_VALIDATION=1 npm run build; \
    fi; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && SKIP_ENV_VALIDATION=1 pnpm run build; \
  else echo "Lockfile not found." && exit 1; \
  fi

##### RUNNER

FROM --platform=linux/amd64 gcr.io/distroless/nodejs20-debian12 AS runner
WORKDIR /app

# Set production environment
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Runtime environment variables will be provided by Kubernetes
# These are just defaults and will be overridden
ENV PORT 3000

# Copy next.config.js (which should always exist now due to our earlier check)
COPY --from=builder /app/next.config.js ./
# We don't need to copy next.config.mjs since we're ensuring next.config.js exists
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000

CMD ["server.js"]
