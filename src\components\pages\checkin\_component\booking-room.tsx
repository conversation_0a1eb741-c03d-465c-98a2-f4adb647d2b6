import React, { useEffect, useState } from "react";
import { Input, Title } from "rizzui";
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input";
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";
import { DateInput, DatetimeInput } from "@/components/theme/ui/input-type/dates-input";
import { type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { useMasterRoomOptions } from "@/lib/hooks/useMasterRoom";
import { useMasterRoomTypesOptions } from "@/lib/hooks/useMasterRoomTypes";
import { FilterGroup, ReservationsDto } from "@/client";
import { countDays } from "@/lib/helper/count-days";

export default function BookingRoom({
  register,
  errors,
  setValue,
  getValues,
  watch,
}: {
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<ReservationsDto>;
}) {
  const filterRooms: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "roomStatus.name",
        operator: "Equals",
        value: "READY"
      }
    ]
  }

  // OPTIONS
  const { data: roomOptions } = useMasterRoomOptions(0, 1000, filterRooms);
  const { data: roomTypeOptions } = useMasterRoomTypesOptions(0, 1000);
  const isExtraBed = Boolean(getValues('reservationDetails.0.isExtraBed'));

  // FUNCTIONS
  // UPDATE DAYS IF CHECK IN/OUT CHANGES
  const countPrice = () => {
    // const selectedRoom = roomOptions?.find((opt) => opt.value == getValues('reservationDetails.0.roomId'));
    // const days = countDays(new Date(getValues('reservationDetails.0.checkInDate') as string), new Date(getValues('reservationDetails.0.checkOutDate') as string));
    // setValue("reservationDetails.0.room.days", days);
    // setValue('reservationDetails.0.price', ((selectedRoom?.price as number) * days))
    // console.log('in', getValues('reservationDetails.0.checkInDate'));
    // console.log('out', getValues('reservationDetails.0.checkOutDate'));
    // console.log('_____________');
  }

  useEffect(() => {
    const selectedRoom = roomOptions?.find((opt) => opt.value == getValues('reservationDetails.0.roomId'));
    const price = isExtraBed ? 500000 : selectedRoom?.price as number;
    const days = getValues('reservationDetails.0.days') ? Number(getValues('reservationDetails.0.days')) : 1;
    setValue('reservationDetails.0.price', (price * days))
  }, [watch("reservationDetails.0.roomId"), watch("reservationDetails.0.days")]);

  useEffect(() => {
    if (getValues("reservationDetails.0.id")) {
    //   console.log('in', typeof (getValues('reservationDetails.0.checkInDate')));
    //   console.log('out', typeof (getValues('reservationDetails.0.checkOutDate')));
    //   console.log('in', new Date(getValues('reservationDetails.0.checkInDate')));
    //   console.log('out', new Date(getValues('reservationDetails.0.checkOutDate')));
    //   console.log('___________________________');
      
      const days = countDays(new Date(getValues('reservationDetails.0.checkInDate') as string), new Date(getValues('reservationDetails.0.checkOutDate') as string));
      setValue("reservationDetails.0.days", days);
    }
  }, [watch("reservationDetails.0.id"), watch("reservationDetails.0.checkInDate"), watch("reservationDetails.0.checkOutDate")]);

  return (
    <div className="p-4 border-2 rounded bg-gray-50 mt-5">
      <div className="grid grid-cols-1 gap-2">
        {/* ROOM INFORMATION */}
        <Title as="h6" className="mb-0">
          Booking Room {isExtraBed && "(Extra Bed)"}
        </Title>
        <div className="grid grid-cols-12 gap-2">
          <DropdownInput
            className="col-span-2"
            label={"Type"}
            name={"reservationDetails.0.room.roomTypeId"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomTypeOptions}
            required={true}
            size="sm"
          />
          <AutocompleteSelect
            className="col-span-1"
            label={"Room"}
            name={"reservationDetails.0.roomId"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomOptions?.filter((val) => val.roomTypeId == watch("reservationDetails.0.room.roomTypeId"))}
            required={true}
            size="sm"
          />
          <DatetimeInput
            className="col-span-3"
            label={"Check In Date"}
            name={"reservationDetails.0.checkInDate"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            // inline={true}
            size="sm"
            readOnly={true}
            onChange={()=> void countPrice()}
          // maxDate={watch("reservationDetails.0.checkOutDate") as Date | undefined}
          />
          <DatetimeInput
            className="col-span-3"
            label={"Check Out Date"}
            name={"reservationDetails.0.checkOutDate"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            // inline={true}
            size="sm"
            onChange={()=> void countPrice()}
          // minDate={watch("reservationDetails.0.checkInDate") as Date | undefined}
          />
          <Input
            label="Days"
            size="sm"
            // disabled={isLoading}
            placeholder="Please fill in Days"
            {...register("reservationDetails.0.days", { required: true })}
            // error={errors.reservationDetails?.[0].days? "Days is required" : undefined}
            className="col-span-1"
            readOnly={true}
          />
          <CurrencyIDR
            label={"Rate"}
            name={"reservationDetails.0.price"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomOptions}
            required={true}
            size="sm"
            className="col-span-2"
          />
          {/* -{getValues('reservationDetails.0.room.price')}- */}
        </div>
      </div>
    </div>
  );
}
