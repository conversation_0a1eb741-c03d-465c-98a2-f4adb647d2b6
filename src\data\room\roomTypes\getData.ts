"use server";
import { type RoomTypeResponse, type RoomType } from "@/interfaces/rooms/roomType";
import axios from "axios";


export const getDataroomType = () => {
  return axios.get<RoomTypeResponse>(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room-type`)
    .then((response) => {
      // console.log('GET getDataroomType', response.data);
      return response.data;
    })
    .catch((error) => {
      console.error("Error fetching reservations:", error);
      throw error;
    });
}

// export const getDataroomType: RoomType[] = [
//   {
//     "id": "1",
//     "name": "OCEAN VIEW SUITE",
//     "status": "Active"
//   },
//   {
//     "id": "2",
//     "name": "EXECUTIVE SUITE",
//     "status": "Active"
//   },
//   {
//     "id": "3",
//     "name": "DELUXE SUITE",
//     "status": "Active"
//   },
//   {
//     "id": "4",
//     "name": "OCEAN VIEW KING SUITE",
//     "status": "Active"
//   },
//   {
//     "id": "5",
//     "name": "DELUXE KING SUITE",
//     "status": "Active"
//   },
//   {
//     "id": "6",
//     "name": "BUSINESS KING ROOM",
//     "status": "Active"
//   },
//   {
//     "id": "7",
//     "name": "EXTRA BED",
//     "status": "Active"
//   }
// ]

