export default function SettingsWarningIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#2F5274"
        fillRule="evenodd"
        d="M1.965 11.852v.493c0 .18.112.334.282.39l1.03.335a.397.397 0 0 1 .27.284c.154.589.387 1.146.687 1.658a.397.397 0 0 1 .01.391l-.491.967a.404.404 0 0 0 .075.474l.98.98a.404.404 0 0 0 .474.076l.967-.493a.395.395 0 0 1 .391.012c.513.3 1.07.532 1.658.688.**************.285.268l.335 1.03c.055.171.209.282.389.282h1.386c.18 0 .332-.111.388-.282l.335-1.03a.394.394 0 0 1 .285-.268 6.687 6.687 0 0 0 1.658-.688.395.395 0 0 1 .392-.012l.965.493a.405.405 0 0 0 .475-.075l.98-.98a.4.4 0 0 0 .074-.475l-.491-.966a.398.398 0 0 1 .01-.392c.3-.513.534-1.07.688-1.658a.398.398 0 0 1 .27-.284l1.029-.335a.403.403 0 0 0 .282-.39v-.493a.2.2 0 0 0-.199-.2H2.164a.2.2 0 0 0-.2.2Z"
        clipRule="evenodd"
      />
      <path
        fill="#EBF5FC"
        fillRule="evenodd"
        d="M13.778 11.652a3.778 3.778 0 0 1-7.555 0h7.555Z"
        clipRule="evenodd"
      />
      <path
        fill="#FFD400"
        fillRule="evenodd"
        d="m11.025.904 4.26 7.38c.218.376.218.807 0 1.182-.216.377-.59.593-1.024.593H5.74c-.435 0-.808-.216-1.026-.593a1.158 1.158 0 0 1 0-1.183l4.26-7.38A1.156 1.156 0 0 1 10 .314c.435 0 .808.214 1.025.59Z"
        clipRule="evenodd"
      />
      <path
        fill="#FF4646"
        fillRule="evenodd"
        d="m9.424 2.866.135 2.725c.012.23.2.411.431.411h.02c.229 0 .419-.18.43-.41l.136-2.726a.549.549 0 0 0-.155-.418.547.547 0 0 0-.408-.175h-.026a.544.544 0 0 0-.409.175.544.544 0 0 0-.154.418ZM10 8.096a.512.512 0 1 0-.003-1.023A.512.512 0 0 0 10 8.096Z"
        clipRule="evenodd"
      />
      <path
        fill="#E6BF00"
        fillRule="evenodd"
        d="M6.537 10.059H5.74c-.435 0-.808-.216-1.026-.593a1.158 1.158 0 0 1 0-1.183l4.26-7.38A1.156 1.156 0 0 1 10 .314c.14 0 .273.022.398.065-.26.09-.48.272-.627.526l-4.26 7.38a1.154 1.154 0 0 0 0 1.182c.218.377.59.593 1.026.593Z"
        clipRule="evenodd"
      />
      <path
        fill="#D4DDE3"
        fillRule="evenodd"
        d="M10.41 15.407a3.778 3.778 0 0 1-4.188-3.755h.821a3.777 3.777 0 0 0 3.367 3.755Z"
        clipRule="evenodd"
      />
      <path
        fill="#2A4A68"
        fillRule="evenodd"
        d="M1.965 11.852v.493c0 .18.112.334.282.39l1.03.335a.397.397 0 0 1 .27.284c.154.589.387 1.146.687 1.658a.397.397 0 0 1 .01.391l-.491.967a.404.404 0 0 0 .075.474l.98.98a.404.404 0 0 0 .474.076l.373-.19-.866-.866a.402.402 0 0 1-.075-.474l.492-.967a.397.397 0 0 0-.011-.39 6.668 6.668 0 0 1-.688-1.66.397.397 0 0 0-.268-.284l-1.03-.334a.404.404 0 0 1-.283-.39v-.493c0-.11.09-.2.2-.2h-.961a.2.2 0 0 0-.2.2Zm4.937 5.712c.439.23.907.414 1.397.542.**************.284.268l.335 1.031c.056.17.209.282.39.282h.96a.405.405 0 0 1-.39-.282l-.334-1.03a.394.394 0 0 0-.284-.269 6.65 6.65 0 0 1-1.658-.687.397.397 0 0 0-.393-.012l-.307.157Z"
        clipRule="evenodd"
      />
      <path
        fill="currentColor"
        d="M17.835 11.34H2.165a.512.512 0 0 0-.513.512v.493c0 .314.2.59.498.686l1.03.334a.087.087 0 0 1 .064.069c.16.606.402 1.19.72 1.734.*************.002.094l-.491.965a.72.72 0 0 0 .132.838l.98.98a.717.717 0 0 0 .837.133l.963-.491c.036-.018.063-.017.095.001a6.92 6.92 0 0 0 1.737.72c.**************.067.064l.335 1.03a.719.719 0 0 0 .686.498h1.385c.313 0 .59-.2.686-.498l.336-1.033c.01-.033.03-.052.066-.061a6.923 6.923 0 0 0 1.738-.72.087.087 0 0 1 .09-.003l.966.493a.72.72 0 0 0 .838-.133l.98-.98a.72.72 0 0 0 .133-.838l-.492-.966a.086.086 0 0 1 .003-.092c.317-.545.56-1.129.72-1.738a.084.084 0 0 1 .062-.066l1.03-.334a.718.718 0 0 0 .5-.686v-.493a.513.513 0 0 0-.513-.512Zm-4.384.625a3.47 3.47 0 0 1-3.452 3.153 3.47 3.47 0 0 1-3.45-3.153h6.902Zm4.271.38a.09.09 0 0 1-.066.092l-1.031.335a.704.704 0 0 0-.474.503 6.339 6.339 0 0 1-.655 1.578.705.705 0 0 0-.02.692l.492.966c.************-.018.112l-.98.98a.092.092 0 0 1-.112.018l-.967-.493a.71.71 0 0 0-.689.02 6.304 6.304 0 0 1-1.58.655.705.705 0 0 0-.503.475l-.335 1.031a.092.092 0 0 1-.092.066H9.307a.092.092 0 0 1-.092-.066l-.336-1.031a.71.71 0 0 0-.502-.475 6.312 6.312 0 0 1-1.581-.655.717.717 0 0 0-.69-.02l-.967.494a.09.09 0 0 1-.11-.019l-.98-.98a.092.092 0 0 1-.019-.113l.491-.964a.71.71 0 0 0-.019-.692 6.356 6.356 0 0 1-.656-1.58.709.709 0 0 0-.474-.503l-1.03-.335a.09.09 0 0 1-.067-.091v-.38H5.92A4.097 4.097 0 0 0 10 15.741a4.096 4.096 0 0 0 4.078-3.778h3.645v.381ZM5.74 10.372h8.521c.55 0 1.022-.273 1.296-.749a1.464 1.464 0 0 0 0-1.496L11.295.747A1.461 1.461 0 0 0 9.999 0a1.46 1.46 0 0 0-1.294.747l-4.262 7.38a1.464 1.464 0 0 0 0 1.496c.275.476.747.749 1.296.749ZM4.984 8.44l4.261-7.38A.84.84 0 0 1 10 .625a.84.84 0 0 1 .755.435l4.26 7.38a.842.842 0 0 1 0 .871.84.84 0 0 1-.754.436H5.74a.84.84 0 0 1-.755-.436.843.843 0 0 1 0-.871Zm4.127-5.556.135 2.727a.745.745 0 0 0 .744.705h.019c.397 0 .723-.31.743-.706l.136-2.726a.86.86 0 0 0-.24-.648.857.857 0 0 0-.636-.272h-.025a.858.858 0 0 0-.635.272.857.857 0 0 0-.24.648Zm.693-.218a.235.235 0 0 1 .183-.078h.025c.075 0 .131.024.182.078a.233.233 0 0 1 .07.186l-.137 2.727a.118.118 0 0 1-.118.112h-.02a.12.12 0 0 1-.12-.112l-.134-2.726a.237.237 0 0 1 .07-.187ZM10 6.762a.824.824 0 1 0 .002 1.648A.824.824 0 0 0 10 6.762Zm0 1.022a.199.199 0 1 1 .002-.397.199.199 0 0 1-.002.397Z"
      />
    </svg>
  );
}
