"use server";

import { type ReservationResponse } from "@/interfaces/reservations/reservation";
import axios from "axios";

export const getDataReservations = () => {
  return axios.get<ReservationResponse>(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/reservations`)
    .then((response) => {
      // console.log('GET getDataReservations', response.data);
      return response.data;
    })
    .catch((error) => {
      console.error("Error fetching reservations:", error);
      throw error;
    });
}

// export const getDataReservations_: Reservation[] = [
//   {
//     "id": "0",
//     "reservationCode": "p",
//     "bookerIdentityNumber": "p",
//     "bookerName": "p",
//     "companyName": "p",
//     "bookerEmail": "p",
//     "bookerPhoneNumber": "p",
//     "days": 10,
//     "groupCode": "p",
//     "ReservationDetails": [
//       {
//         "Guests": {
//           "fullname": "p",
//           "identityNumber": "p",
//           "nationality": "p",
//           "phoneNumber": "p",
//           "companyName": "p"
//         },
//         "checkInDate": new Date("2025-03-01T00:00:00.000Z"),
//         "checkOutDate": new Date("2025-03-12T00:00:00.000Z"),
//         "Rooms": {
//           "RoomType": {
//             "id": "1"
//           },
//           "id": "1",
//           "price": 6600000
//         }
//       },
//       {
//         "Guests": {
//           "companyName": "p",
//           "fullname": "p",
//           "identityNumber": "p",
//           "nationality": "p",
//           "phoneNumber": "p"
//         },
//         "Rooms": {
//           "id": "1",
//           "RoomType": {
//             "id": "7"
//           },
//           "price": 500000
//         },
//         "checkInDate": new Date("2025-03-01T00:00:00.000Z"),
//         "checkOutDate": new Date("2025-03-12T00:00:00.000Z")
//       }
//     ],
//     "arrivalDate": "2025-03-13T00:00:00.000Z"
//   },
//   {
//     "id": "1",
//     "reservationCode": "RSV1001",
//     "groupCode": "GC-001",
//     "bookerName": "John Doe",
//     "bookerEmail": "<EMAIL>",
//     "companyName": "PT SMI",
//     "bookerPhoneNumber": "+6281234567890",
//     "arrivalDate": "2025-03-18",
//     "days": 3,
//     "status": "Confirmed",
//     "bookerIdentityNumber": "747121241290001",
//     "paymentMethod": "Credit Card",
//     "action": "View",
//     "ReservationDetails": [
//       {
//         "checkInDate": new Date("2025-03-18"),
//         "checkOutDate": new Date("2025-03-21"),
//         "Guests": {
//           "fullname": "Michael",
//           "phoneNumber": "+6289876543210"
//         },
//         "Rooms": {
//           "id": "1",
//           "roomNumber": "101",
//           "RoomType": {
//             "id": "1"
//           }
//         }
//       },
//       {
//         "checkInDate": new Date("2025-03-18"),
//         "checkOutDate": new Date("2025-03-21"),
//         "Guests": {
//           "fullname": "Michael Jr.",
//           "phoneNumber": "+6289876543210"
//         },
//         "Rooms": {
//           "id": "7",
//           "roomNumber": "101",
//           "RoomType": {
//             "id": "7"
//           }
//         }
//       }
//     ]
//   },
//   {
//     "id": "2",
//     "reservationCode": "RSV1002",
//     "groupCode": "GC-002",
//     "bookerName": "Jane Smith",
//     "bookerEmail": "<EMAIL>",
//     "companyName": "PT XYZ",
//     "bookerPhoneNumber": "+*************",
//     "arrivalDate": "2025-03-19",
//     "days": 2,
//     "status": "Pending",
//     "bookerIdentityNumber": "***************",
//     "paymentMethod": "Bank Transfer",
//     "action": "View",
//     "ReservationDetails": [
//       {
//         "checkInDate": new Date("2025-03-19"),
//         "checkOutDate": new Date("2025-03-21"),
//         "Guests": {
//           "fullname": "Emily",
//           "phoneNumber": "+*************"
//         },
//         "Rooms": {
//           "id": "2",
//           "roomNumber": "102",
//           "RoomType": {
//             "id": "2"
//           }
//         }
//       }
//     ]
//   },
//   {
//     "id": "3",
//     "reservationCode": "RSV1003",
//     "groupCode": "GC-003",
//     "bookerName": "Alice Johnson",
//     "bookerEmail": "<EMAIL>",
//     "companyName": "PT Alpha",
//     "bookerPhoneNumber": "+*************",
//     "arrivalDate": "2025-03-20",
//     "days": 4,
//     "status": "Cancelled",
//     "bookerIdentityNumber": "***************",
//     "paymentMethod": "Cash",
//     "action": "View",
//     "ReservationDetails": [
//       {
//         "checkInDate": new Date("2025-03-20"),
//         "checkOutDate": new Date("2025-03-24"),
//         "Guests": {
//           "fullname": "Daniel",
//           "phoneNumber": "+6287766554433"
//         },
//         "Rooms": {
//           "id": "3",
//           "roomNumber": "103",
//           "RoomType": {
//             "id": "3"
//           }
//         }
//       }
//     ]
//   },
//   {
//     "id": "4",
//     "reservationCode": "RSV1004",
//     "groupCode": "GC-004",
//     "bookerName": "Robert Brown",
//     "bookerEmail": "<EMAIL>",
//     "companyName": "PT Beta",
//     "bookerPhoneNumber": "+6281223344556",
//     "arrivalDate": "2025-03-21",
//     "days": 1,
//     "status": "Confirmed",
//     "bookerIdentityNumber": "320121241290004",
//     "paymentMethod": "E-Wallet",
//     "action": "View",
//     "ReservationDetails": [
//       {
//         "checkInDate": new Date("2025-03-21"),
//         "checkOutDate": new Date("2025-03-22"),
//         "Guests": {
//           "fullname": "Sophia",
//           "phoneNumber": "+6285556667778"
//         },
//         "Rooms": {
//           "id": "4",
//           "roomNumber": "104",
//           "RoomType": {
//             "id": "4"
//           }
//         }
//       }
//     ]
//   },
//   {
//     "id": "5",
//     "reservationCode": "RSV1005",
//     "groupCode": "GC-005",
//     "bookerName": "James Wilson",
//     "bookerEmail": "<EMAIL>",
//     "companyName": "PT Gamma",
//     "bookerPhoneNumber": "+6281334455667",
//     "arrivalDate": "2025-03-22",
//     "days": 2,
//     "status": "Pending",
//     "bookerIdentityNumber": "320121241290005",
//     "paymentMethod": "Credit Card",
//     "action": "View",
//     "ReservationDetails": [
//       {
//         "checkInDate": new Date("2025-03-22"),
//         "checkOutDate": new Date("2025-03-24"),
//         "Guests": {
//           "fullname": "Ethan",
//           "phoneNumber": "+6281122993344"
//         },
//         "Rooms": {
//           "id": "5",
//           "roomNumber": "105",
//           "RoomType": {
//             "id": "5"
//           }
//         }
//       }
//     ]
//   },
//   {
//     "id": "6",
//     "reservationCode": "RSV1006",
//     "groupCode": "GC-006",
//     "bookerName": "William Davis",
//     "bookerEmail": "<EMAIL>",
//     "companyName": "PT Delta",
//     "bookerPhoneNumber": "+6281445566778",
//     "arrivalDate": "2025-03-23",
//     "days": 5,
//     "status": "Confirmed",
//     "bookerIdentityNumber": "320121241290006",
//     "paymentMethod": "PayPal",
//     "action": "View",
//     "ReservationDetails": [
//       {
//         "checkInDate": new Date("2025-03-23"),
//         "checkOutDate": new Date("2025-03-28"),
//         "Guests": {
//           "fullname": "Olivia",
//           "phoneNumber": "+6281199887766"
//         },
//         "Rooms": {
//           "id": "6",
//           "roomNumber": "106",
//           "RoomType": {
//             "id": "6"
//           }
//         }
//       }
//     ]
//   },
//   {
//     "id": "7",
//     "reservationCode": "RSV1007",
//     "groupCode": "GC-007",
//     "bookerName": "Lucas Martinez",
//     "bookerEmail": "<EMAIL>",
//     "companyName": "PT Epsilon",
//     "bookerPhoneNumber": "+6281556677889",
//     "arrivalDate": "2025-03-24",
//     "days": 3,
//     "status": "Pending",
//     "bookerIdentityNumber": "320121241290007",
//     "paymentMethod": "Credit Card",
//     "action": "View",
//     "ReservationDetails": [
//       {
//         "checkInDate": new Date("2025-03-24"),
//         "checkOutDate": new Date("2025-03-27"),
//         "Guests": {
//           "fullname": "Mason",
//           "phoneNumber": "+6281188776655"
//         },
//         "Rooms": {
//           "id": "7",
//           "roomNumber": "107",
//           "RoomType": {
//             "id": "7"
//           }
//         }
//       }
//     ]
//   }
// ]
