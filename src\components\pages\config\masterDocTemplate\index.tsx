"use client";
import React, { useEffect, useState } from "react";
import <PERSON>HeaderCustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/tableV2";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import {
  type DocumentTemplateDto,
  type CreateDocumentTemplateDto,
  postApiDocumentTemplatesUpload,
  putApiDocumentTemplatesById,
  deleteApiDocumentTemplatesById,
} from "@/client";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { swalError } from "@/lib/helper/swal-error";
import { useMasterDocTemplate } from "@/lib/hooks/config/masterDocTemplate";
import ButtonDownloadDocx from "@/components/container/button/button-downloadDocx";
import type {
  DocumentType,
  UpdateDocumentTemplateDto,
} from "@/client/types.gen";
import type { SelectOption } from "rizzui";

export default function MasterDocTemplate({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isData, setData] = useState<DocumentTemplateDto[]>([]);
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});
  const [isDoctype, setDocType] = useState<SelectOption | undefined>();
  const [isDefault, setIsDefault] = useState<SelectOption | undefined>();

  // GET DATA
  const { isLoading, data } = useMasterDocTemplate();
  const docTypeOptions = [
    { label: "1", value: 1 as DocumentType },
    { label: "2", value: 2 as DocumentType },
    { label: "3", value: 3 as DocumentType },
    { label: "4", value: 4 as DocumentType },
    { label: "5", value: 5 as DocumentType },
    { label: "99", value: 99 as DocumentType },
  ];
  const isDefaultOpt: SelectOption[] = [
    { label: "true", value: "true" },
    { label: "false", value: "false" },
  ];

  useEffect(() => {
    if (data) {
      const mappedData = data.map((item) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
        documentTypeName: item.documentTypeName ?? "",
        description: item.description ?? "",
      }));
      setData(mappedData as DocumentTemplateDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "name", title: "Name", filter: "text" as const },
    {
      dataIndex: "documentTypeName",
      title: "Document Type",
      filter: "text" as const,
    },
    {
      dataIndex: "description",
      title: "Description",
      filter: "text" as const,
    },
    {
      dataIndex: "isDefault",
      title: "Is Default",
      filter: "text" as const,
    },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
    setDocType(undefined);
    setIsDefault(undefined);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.getDocumentTemplate],
    });
  };

  const deleteMasterDocTemplateMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiDocumentTemplatesById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Doc Template deleted successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Doc Template will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        console.log("Delete Doc Template with id:", id);
        await deleteMasterDocTemplateMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the Doc Template. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const data = isData.find((e) => e.id === id);
    console.log("Selected Doc Template Data:", data?.id);

    if (data) {
      setIsEditMode(true);
      Object.entries(data).forEach(([key, value]) => {
        if (key === "documentType") {
          setDocType({
            label: String(value),
            value: String(value),
          });
        } else if (key === "isDefault") {
          setIsDefault({
            label: String(value),
            value: String(value),
          });
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createMasterDocTemplateMutation = useMutation({
    mutationFn: async (
      data: CreateDocumentTemplateDto & { docTemplate: File },
    ) => {
      return postApiDocumentTemplatesUpload({
        body: {
          Name: data.name,
          DocumentType: data.documentType,
          Description: data.description ?? "",
          File: data.docTemplate,
          IsDefault: data.isDefault ?? false,
        },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Doc Template created successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateMasterDocTemplateMutation = useMutation({
    mutationFn: async (
      data: UpdateDocumentTemplateDto & { docTemplate: File; id: string },
    ) => {
      const { id, ...updateData } = data;
      return putApiDocumentTemplatesById({
        path: { id: id },
        body: updateData as UpdateDocumentTemplateDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Doc Template updated successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(
    formData: CreateDocumentTemplateDto & { docTemplate: File; id: string },
  ) {
    try {
      const data = { ...formData };
      data.isDefault = isDefault?.value === "true" ? true : false;

      if (isEditMode) {
        data.id = formData.id;
        console.log("Updated Doc Template :", data);
        await updateMasterDocTemplateMutation.mutateAsync(data);
      } else {
        console.log("Creating Doc Template :", data);
        await createMasterDocTemplateMutation.mutateAsync(data);
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  // if (!can("WismaApp.")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Config" },
            { name: "Master Doc Template" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) =>
                onSubmit(
                  data as unknown as CreateDocumentTemplateDto & {
                    docTemplate: File;
                    id: string;
                  },
                )
              }
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              isDocType={isDoctype}
              setDocType={setDocType}
              docTypeOptions={docTypeOptions}
              isDefault={isDefault}
              setIsDefault={setIsDefault}
              isDefaultOpt={isDefaultOpt}
            />
          </div>
        )}

        <CustomTable
          columns={columns}
          dataSource={
            data?.map((e) => ({
              ...e,
              action: (
                <>
                  <ButtonDetail
                    itemId={String(e.id)}
                    handleDetail={handleDetail}
                  />
                  {e.attachmentId && (
                    <ButtonDownloadDocx
                      itemId={String(e.attachmentId)}
                      name={String(e.name)}
                    />
                  )}
                </>
              ),
            })) ?? []
          }
          rowKey="id"
          pageSize={10}
          isLoading={isLoading}
          totalCount={data?.length ?? 0}
          searchTerms={searchTerms}
          setSearchTerms={setSearchTerms}
          height={isFormVisible ? "45vh" : "70vh"}
        />
      </div>
    </div>
  );
}
