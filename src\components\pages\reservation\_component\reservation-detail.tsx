import { DateInput } from "@/components/theme/ui/input-type/dates-input"
import { UploadFile } from "@/components/theme/ui/file-upload/upload-file"
import React, { useCallback, useEffect, useState } from "react"
import { UseFormUnregister, type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form"
import { Button, Input, type SelectOption, Title } from "rizzui"
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete"
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input"
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType"
import { useApiAppGuestOptions } from "@/lib/hooks/useApiAppGuest"
import { useApiMasterDiningOptionsOptions } from "@/lib/hooks/useApiMasterDiningOptions"
import { useApiMasterPaymentMethodOptions } from "@/lib/hooks/useApiMasterPaymentMethod"
import { useApiAppReservationTypesOptions } from "@/lib/hooks/useMasterReservationTypes"
import { useApiMasterCompanyOptions } from "@/lib/hooks/useApiMasterCompany"
import Link from "next/link"
import { ReservationsDto } from "@/client"
import { useApiAppReservationsById } from "@/lib/hooks/useApiAppReservationsById"

export default function ReservationDetail({
  isLoading = false,
  register,
  unregister,
  errors,
  setValue,
  getValues,
  watch,
  reservationId,
  isAddReservationDetail
}: {
  isLoading: boolean
  register: UseFormRegister<FieldValues>
  unregister: UseFormUnregister<FieldValues>
  setValue: UseFormSetValue<FieldValues>
  getValues: UseFormGetValues<FieldValues>
  watch: UseFormWatch<FieldValues>
  errors: FieldErrors<ReservationsDto>
  reservationId?: string
  isAddReservationDetail?: boolean
}) {

  // OPTIONS
  const { data: paymentMethodOptions } = useApiMasterPaymentMethodOptions(0, 1000, "",);
  const { data: diningOptions } = useApiMasterDiningOptionsOptions(0, 1000, "",);
  const { data: statusResvOptions } = useMasterStatusByDocTypeOptions("reservations")
  const { data: statusResvDetOptions } = useMasterStatusByDocTypeOptions("reservationDetails")
  const { data: guestCriteriaOptions } = useMasterStatusByDocTypeOptions("reservationDetailsGuestCriteria")
  const { data: reservationTypesOptions } = useApiAppReservationTypesOptions(0, 1000, "")
  const { data: companyOptions } = useApiMasterCompanyOptions(0, 1000, "")
  const { data: dataGuestOptions } = useApiAppGuestOptions(0, 1000, "",)
  const [guestOptions, setGuestOptions] = useState<SelectOption[]>([])
  useEffect(() => { setGuestOptions(dataGuestOptions ?? []) }, [dataGuestOptions])

  // INIT
  const init = useCallback(() => {
    register("reservationDetails.0.rfid", { value: "" })
    // register("reservationTypeId", { value: "9396072e-f88f-b34d-09a8-3a196d18238a" })
  }, [])
  useEffect(() => { init() }, [init])

  // INIT STATUS ID
  useEffect(() => {
    const statusId = statusResvOptions?.find(opt => opt.code == "open")?.value // code fore Open
    setValue("statusId", statusId ?? 0)
  }, [statusResvOptions])

  // INIT RESERVATION TYPE ID
  useEffect(() => {
    const reservationTypesId = reservationTypesOptions?.find(opt => opt.name == "INDIVIDU (BSG)") // code fore Individu
    setValue("reservationTypeId", reservationTypesId?.value ?? 0)
    setValue("groupCode", reservationTypesId?.label ?? 0)
  }, [reservationTypesOptions])

  // INIT STATUS RESERVATION ID
  useEffect(() => {
    const statusId = statusResvDetOptions?.find(opt => opt.code == "reserved")?.value // code fore Reserved
    setValue("reservationDetails.0.statusId", statusId ?? 0)
  }, [statusResvDetOptions])


  // UPDATE GUEST WHEN IDENTITY NUMBER CHANGED
  const identityNumber = watch("reservationDetails.0.guest.identityNumber") as string
  useEffect(() => {
    const selectedOpt = guestOptions?.find(opt => opt.value == identityNumber)
    if (selectedOpt?.fullname) {
      setValue("reservationDetails.0.guestId", selectedOpt.id)
      setValue("reservationDetails.0.guest", selectedOpt)
    } else {
      unregister("reservationDetails.0.guest");
      setValue("reservationDetails.0.guestId", null)
      setValue("reservationDetails.0.guest.fullname", null)
      setValue("reservationDetails.0.guest.nationality", null)
      setValue("reservationDetails.0.guest.phoneNumber", null)
      setValue("reservationDetails.0.guest.companyName", null)
      setValue("reservationDetails.0.guest.city", null)
    }
  }, [identityNumber])

  const onChange = () => {
    const identityNumber = getValues("reservationDetails.0.guest.identityNumber") as string
    const tempOpt: SelectOption = { label: identityNumber, value: 0, id: 0 }
    setGuestOptions(prev => [tempOpt, ...prev.slice(1)])
  }
  return (
    <div className="">
      <div className="grid grid-cols-2 gap-2">
        {/* RESERVATION DETAIL */}
        <Title as="h6" className="mb-0 col-span-2">
          Reservation Detail
        </Title>
        <Input
          label="Booker Identity Number"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Booker Identity Number"
          {...register("bookerIdentityNumber", { required: true })}
          error={errors.bookerIdentityNumber ? "Booker Identity Number is required" : undefined}
          className="w-full"
          readOnly={isAddReservationDetail}
        />
        <Input
          label="Booker Name"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Booker Name"
          {...register("bookerName", { required: true })}
          error={errors.bookerName ? "Booker Name is required" : undefined}
          className="w-full"
          readOnly={isAddReservationDetail}
        />
        <AutocompleteSelect
          label={"Company Name"}
          size="sm"
          name={"companyId"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          onChange={onChange}
          options={companyOptions}
          required={true && !isAddReservationDetail}
          readOnly={isAddReservationDetail}
        />
        <Input
          label="Email"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Email"
          {...register("bookerEmail", { required: true })}
          error={errors.bookerEmail ? "Email is required" : undefined}
          className="w-full"
          readOnly={isAddReservationDetail}
        />
        <Input
          label="Phone Number"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Phone Number"
          {...register("bookerPhoneNumber", { required: true })}
          error={errors.bookerPhoneNumber ? "Phone Number is required" : undefined}
          className="w-full"
          readOnly={isAddReservationDetail}
        />
        <DateInput
          label={"Arrival Date"}
          size="sm"
          name={"arrivalDate"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          required={true && !isAddReservationDetail}
          readOnly={isAddReservationDetail}
        />
        <DropdownInput
          label={"Payment Method"}
          size="sm"
          name={"paymentMethodId"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={paymentMethodOptions}
          required={true && !isAddReservationDetail}
          readOnly={isAddReservationDetail}
        />
        <Input
          label="Days"
          size="sm"
          type="number"
          disabled={isLoading}
          placeholder="Please fill in days"
          {...register("days", { required: true })}
          error={errors.days ? "days is required" : undefined}
          className="w-full"
          readOnly={isAddReservationDetail}
        />
        <DropdownInput
          label={"Criteria Options"}
          name={"reservationDetails.0.criteriaId"}
          size="sm"
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={guestCriteriaOptions}
          required={true && !isAddReservationDetail}
          readOnly={isAddReservationDetail}
        />
        <DropdownInput
          label={"Dining Options"}
          name={"diningOptionsId"}
          size="sm"
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={diningOptions}
          required={true && !isAddReservationDetail}
          readOnly={isAddReservationDetail}
        />
        {/* GUEST INFORMATION */}
        <div className="grid grid-cols-3 col-span-2 gap-2">
          <Title as="h6" className="mb-0 col-span-3 pt-5">
            Guest Information
          </Title>
          <AutocompleteSelect
            label={"Guest Identity Number"}
            size="sm"
            name={"reservationDetails.0.guest.identityNumber"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            onChange={onChange}
            options={guestOptions}
            // required={true && !isAddReservationDetail}
          />
          <Input
            label="Guest Name"
            size="sm"
            disabled={isLoading}
            placeholder="Please fill in Guest Name"
            {...register("reservationDetails.0.guest.fullname", { required: true })}
            error={errors.reservationDetails?.[0]?.guest?.fullname ? "Guest Name is required" : undefined}
            className="w-full"
          />
          <Input
            label="Guest Company"
            size="sm"
            disabled={isLoading}
            placeholder="Please fill in Guest Company"
            {...register("reservationDetails.0.guest.companyName", { required: true })}
            error={errors.reservationDetails?.[0]?.guest?.companyName ? "Guest Company is required" : undefined}
            className="w-full"
          />
          <Input
            label="Guest Nationality"
            size="sm"
            disabled={isLoading}
            placeholder="Please fill in Guest Nationality"
            {...register("reservationDetails.0.guest.nationality", { required: true })}
            error={errors.reservationDetails?.[0]?.guest?.nationality ? "Guest Nationality is required" : undefined}
            className="w-full"
          />
          <Input
            label="Guest City"
            size="sm"
            disabled={isLoading}
            placeholder="Please fill in Guest City"
            {...register("reservationDetails.0.guest.city", { required: true })}
            error={errors.reservationDetails?.[0]?.guest?.city ? "Guest City is required" : undefined}
            className="w-full"
          />
          <Input
            label="Guest Phone Number"
            size="sm"
            disabled={isLoading}
            placeholder="Please fill in Guest Phone Number"
            {...register("reservationDetails.0.guest.phoneNumber", { required: true })}
            error={errors.reservationDetails?.[0]?.guest?.phoneNumber ? "Guest Phone Number is required" : undefined}
            className="w-full"
          />
          {/* <div></div> */}
          <div className="col-span-2">
            <UploadFile
              label={"Document Reservation"}
              name={"attachment"}
              setValue={setValue}
              getValues={getValues}
              register={register}
              errors={errors}
              accept="application/pdf"
              // required={true && !isAddReservationDetail}
              size="sm"
            />
          </div>
          {/* BUTTON */}
          <div className="flex justify-end gap-2 pt-5">
            <Button
              type="submit"
              size="sm"
              disabled={isLoading}
              className="rounded-lg bg-green-500 px-4 py-2 text-white disabled:bg-gray-400"
            >
              Accept
            </Button>
            <Link href={"/reservation"}>
              <Button
                type="button"
                size="sm"
                disabled={isLoading}
                variant="outline"
                className="rounded-lg px-4 py-2 disabled:bg-gray-400"
              >
                Quit
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}