export interface RoomStatus {
  id: string;
  name: string;
  code: string;
  color: string;
  creationTime: string;
  creatorId: string | null;
  lastModificationTime: string | null;
  lastModifierId: string | null;
}

export interface RoomStatusResponse {
  totalCount: number;
  items: RoomStatus[];
}

export interface RoomStatusPostSuccessResponse {
  success: true;
  message: string;
  data: RoomStatus;
}

export interface RoomStatusPostErrorResponse {
  success: false;
  message: string;
  data: null;
  error: {
    code: string;
    message: string;
    details: string;
    data?: Record<string, string>;
    validationErrors?: {
      message: string;
      members: string[];
    }[];
  };
}

export type RoomStatusPostResponse =
  | RoomStatusPostSuccessResponse
  | RoomStatusPostErrorResponse;
