export default function TentIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="1 -25 512 511" {...props}>
      <g fillRule="evenodd">
        <path fill="#ff6359" d="M256 50.5h78l-20-20 20-20h-78zm0 0" />
        <path
          fill="#ffb86e"
          d="M436 170.5c-66.9-17.93-132.46-52.46-180-100-47.54 47.54-113.1 82.07-180 100zm0 0"
        />
        <path
          fill="#ff6359"
          d="M436 170.5H72c-8.8 0-16 7.2-16 16s7.2 16 16 16h368c8.8 0 16-7.2 16-16s-7.2-16-16-16zm0 0"
        />
        <path
          fill="#ffb86e"
          d="M126.62 450.5c20.01-71.25 40-182.5 40-248H72c0 65.5-19.99 176.75-40 248zm0 0"
        />
        <path
          fill="#ffeeda"
          d="M166.62 202.5c0 65.5-19.99 176.75-40 248h80.02L171.38 390 256 338V202.5zm0 0"
        />
        <path
          fill="#ffb86e"
          d="m256 338-84.62 52 35.26 60.5 24.68-56.25zm0 0"
        />
        <path
          fill="#ff6359"
          d="m256 338-24.68 56.25-24.68 56.25h98.72l-24.68-56.25zm0 0"
        />
        <path
          fill="#ffeeda"
          d="m256 338 24.68 56.25 24.68 56.25 35.26-60.5zm0 0"
        />
        <path
          fill="#ffb86e"
          d="m256 338 84.62 52-35.26 60.5h80.02c-20-71.25-40-182.5-40-248H256zm0 0"
        />
        <path
          fill="#ffeeda"
          d="M345.38 202.5c0 65.5 20 176.75 40 248H480c-20.01-71.25-40-182.5-40-248zm0 0"
        />
        <path
          fill="#ffd447"
          d="M289.33 155.83v80L256 202.5l-33.33 33.33v-80zm0 0"
        />
      </g>
      <path d="M204.46 122.65c1.93 0 3.9-.56 5.61-1.73l.22-.15a10 10 0 0 0-11.24-16.54l-.23.15a10 10 0 0 0 5.64 18.27zm0 0" />
      <path d="M502 440.5h-14.38c-18.06-66.76-36.08-166.42-37.52-230.04A26.04 26.04 0 0 0 466 186.5c0-14.34-11.66-26-26-26h-2.65C370.54 142.39 309.9 109.04 266 66.3v-5.8h68a10 10 0 0 0 7.07-17.07L328.14 30.5l12.93-12.93A10 10 0 0 0 334 .5h-78a10 10 0 0 0-10 10v55.8c-5.02 4.88-10.3 9.7-15.76 14.34a10 10 0 0 0 12.96 15.24c4.39-3.73 8.66-7.58 12.8-11.47 32.89 30.96 74.04 57.01 119.74 76.09h-76.4v-4.67a10 10 0 0 0-10-10h-66.67a10 10 0 0 0-10 10v4.67h-76.45a439.45 439.45 0 0 0 48.94-24.1 10 10 0 0 0-9.95-17.35A426.87 426.87 0 0 1 74.65 160.5H72c-14.34 0-26 11.66-26 26a26.05 26.05 0 0 0 15.9 23.96c-1.44 63.62-19.46 163.28-37.52 230.04H10a10 10 0 1 0 0 20h492a10 10 0 0 0 0-20zm-236-420h43.86l-2.93 2.93a10 10 0 0 0 0 14.14l2.93 2.93H266zm127 420c-17.87-66.05-35.7-164.3-37.47-228h74.61c1.73 63.64 19.04 160.72 36.78 228zm-180.33-228v23.33a10 10 0 0 0 17.07 7.07L246 226.64v105.77l-79.85 49.07a10 10 0 0 0-3.4 13.56l26.48 45.46H139.7c17.75-67.28 35.05-164.36 36.78-228zm-27.73 180.9L234 363.26l-28.62 65.22zm164.32 1.64a10 10 0 0 0-3.4-13.56L266 332.4V226.64l16.26 16.27a10.02 10.02 0 0 0 17.07-7.07V212.5h36.2c1.72 63.64 19.02 160.72 36.77 228h-49.53zm-42.64 33.44L278 363.26l49.06 30.14zM440 180.5c3.25 0 6 2.75 6 6s-2.75 6-6 6H299.33v-12zm-207.33-14.67h46.66v45.86l-16.26-16.26-.14-.13c-.2-.18-.38-.36-.59-.53l-.42-.32-.36-.26-.49-.3-.35-.2a8.34 8.34 0 0 0-.5-.24c-.13-.07-.25-.13-.38-.18-.16-.07-.33-.13-.5-.18l-.43-.16-.48-.12-.47-.12-.55-.08-.42-.06a8.89 8.89 0 0 0-.97-.05H255.93a8.95 8.95 0 0 0-1.4.12l-.48.07-.53.13-.41.11c-.18.05-.35.12-.52.18l-.4.15c-.17.06-.32.14-.48.22l-.41.2-.44.25-.4.25c-.16.1-.3.21-.44.32l-.36.27c-.2.17-.41.36-.6.55l-.13.11-16.26 16.26zM72 180.5h140.67v12H72c-3.25 0-6-2.75-6-6s2.75-6 6-6zm9.86 32h74.61c-1.77 63.7-19.6 161.95-37.47 228H45.08c17.75-67.28 35.05-164.36 36.78-228zm140.09 228L256 362.89l34.05 77.61zm0 0" />
      <path d="M435.08 402.14a10 10 0 0 0 7.07-12.24l-8.03-29.98a10 10 0 0 0-19.32 5.17l8.04 29.98a10 10 0 0 0 12.24 7.07zM449.66 417.93l-.07-.26a10 10 0 1 0-19.32 5.13l.07.27a10 10 0 1 0 19.32-5.14zm0 0" />
    </svg>
  );
}
