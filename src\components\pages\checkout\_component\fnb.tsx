import {
  type CreateUpdateReservationFoodAndBeveragesDto,
  deleteApiAppReservationFoodAndBeveragesById,
  putApiAppReservationFoodAndBeveragesById,
  type ReservationFoodAndBeveragesDto
} from "@/client";
import ButtonDelete from "@/components/container/button/button-delete";
import ButtonMin from "@/components/container/button/buttonMin";
import ButtonPlus from "@/components/container/button/buttonPlus";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { swalError } from "@/lib/helper/swal-error";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { type UseFormUnregister, type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import { PiTrash } from "react-icons/pi";
import { Checkbox, Text } from "rizzui";

export default function CheckoutFnB({
  register,
  unregister,
  setValue,
  fnbs,
  readonly = false,
}: {
  register: UseFormRegister<FieldValues>;
  unregister: UseFormUnregister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  fnbs: (ReservationFoodAndBeveragesDto & { selected: boolean })[];
  readonly?: boolean;
}) {
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  // const fnbs = watch("reservationDetails.0.reservationFoodAndBeverages") as ReservationFoodAndBeveragesDto[];
  // const fnbs = fnbs?.filter((fnb) => fnb.paymentStatus?.code !== "paid");
  const totalPrice = fnbs?.reduce((total, item) => {
    const price = item.selected ? Number(item.totalPrice) : 0;
    return total + price;
  }, 0);
  const columns = [
    { dataIndex: "foodAndBeverageName", title: "Name" },
    { dataIndex: "foodAndBeverageTypeName", title: "Type" },
    { dataIndex: "quantity", title: "QTY" },
    { dataIndex: "totalPrice", title: "Price" },
  ];

  // DEFINE MUTATION
  const updateApiAppReservationFoodAndBeveragesById = useMutation({
    mutationFn: async (data: CreateUpdateReservationFoodAndBeveragesDto) => {
      const { id, ...updateData } = data;
      return putApiAppReservationFoodAndBeveragesById({
        path: { id: id! },
        body: updateData,
      })
    },
    onSuccess: async (res) => {
      console.log('success', res)
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationFoodAndBeveragesByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const removeApiAppReservationFoodAndBeveragesById = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppReservationFoodAndBeveragesById({
        path: { id: id },
      })
    },
    onSuccess: async (res) => {
      console.log('success remove', res)
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationFoodAndBeveragesByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  // FUNCTIONS
  const updateFnbQty = async (key: number, add: number) => {
    const data = fnbs[key] as CreateUpdateReservationFoodAndBeveragesDto & { selected?: boolean };
    const qty = Number(data?.quantity) + add;
    const pricePerItem = Number(data?.totalPrice) / Number(data?.quantity);
    const totalPrice = pricePerItem * qty;
    if (data) {
      data.quantity = qty;
      data.totalPrice = totalPrice;
      setValue(`reservationDetails.0.reservationFoodAndBeverages.${key}`, data)
      data.selected = true;
      await updateApiAppReservationFoodAndBeveragesById.mutateAsync(data);
    }
  }
  const removeFnb = async (key: number) => {
    // const confirmation = (await swal({
    //   title: "Remove",
    //   text: "Remove Transaction List?",
    //   icon: "info",
    //   buttons: ["Cancel", "Yes"],
    // })) as unknown as boolean;
    // if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

    const data = fnbs[key];
    if (data?.id) {
      unregister(`reservationDetails.0.reservationFoodAndBeverages.${key}`)
      await removeApiAppReservationFoodAndBeveragesById.mutateAsync(data.id)
    }
  }

  // EFFECTS
  useEffect(() => {
    setValue("payments.fnbTotalPrice", totalPrice);
  }, [totalPrice]);

  const hasSetDefaultSelected = useRef(false);
  useEffect(() => {
    if (fnbs?.length && !hasSetDefaultSelected.current) {
      fnbs.forEach((_, key) => {
        setValue(`reservationDetails.0.reservationFoodAndBeverages.${key}.selected`, true);
      });
      hasSetDefaultSelected.current = true; // Jangan jalankan lagi
    }
  }, [fnbs, setValue]);

  return (
    <div className="p-4 py-3 border-2 rounded bg-gray-50 mt-3">
      <div className="grid grid-cols-1 gap-0">
        <Text as="p" className="mb-0 font-bold text-sm text-gray-800">
          F&B Transactions
        </Text>
        <>
          {/* Header Table */}
          <table className="min-w-full border-collapse table-auto">
            <thead className="bg-slate-50">
              <tr>
                {!readonly &&
                  <th className="sticky top-0 bg-slate-50 px-2 py-1 text-center text-xs font-semibold tracking-wider text-gray-500 w-[4%]">
                    <Checkbox
                      size="sm"
                      inputClassName="h-4 w-4"
                      iconClassName="h-4 w-4"
                      onChange={(e) => {
                        fnbs.forEach((_, key) => {
                          setValue(`reservationDetails.0.reservationFoodAndBeverages.${key}.selected`, e.target.checked);
                        });
                      }}
                    />
                  </th>
                }
                <th className="sticky top-0 bg-slate-50 px-2 py-1 text-center text-xs font-semibold tracking-wider text-gray-500 w-[6%]">
                  No
                </th>
                {columns.map((column, key) => (
                  <th
                    key={key}
                    className="sticky top-0 bg-slate-50 px-1 py-1 text-left text-xs font-semibold tracking-wider text-gray-500 w-[22%]"
                  >
                    {column.title}
                  </th>
                ))}
                <th className="sticky top-0 w-5 bg-slate-50 px-2 py-1 text-left text-xs tracking-wider text-gray-500 w-[4%]">
                  {/* # */}
                </th>
              </tr>
            </thead>
          </table>

          {/* Scrollable Body Table */}
          <div className="h-[120px] max-h-[120px] overflow-y-auto">
            <table className="min-w-full border-collapse table-auto">
              <tbody className="divide-y divide-gray-200">
                {fnbs?.length === 0 && (
                  <tr>
                    <td className="p-6 text-center text-xs tracking-wider text-gray-500">
                      {fnbs?.length > 0 ? "All F&B transactions have been paid." : "No F&B transaction found."}
                    </td>
                  </tr>
                )}
                {fnbs?.map((row, key) => (
                  <tr className="hover:bg-gray-50" key={key}>
                    {!readonly &&
                      <td className="px-2 text-center text-xs tracking-wider text-gray-500 w-[4%]">
                        <Checkbox
                          size="sm"
                          inputClassName="h-4 w-4"
                          iconClassName="h-4 w-4"
                          {...register(`reservationDetails.0.reservationFoodAndBeverages.${key}.selected`)}
                        />
                      </td>
                    }
                    <td className="px-2 text-center px-2 py-1 text-xs tracking-wider text-gray-500 w-[6%]">
                      {key + 1}.
                    </td>
                    <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                      {row.foodAndBeverageName}
                    </td>
                    <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                      {row.foodAndBeverageTypeName}
                    </td>
                    <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                      <div className="flex">
                        {(!readonly && can("WismaApp.ReservationFoodAndBeverages.Edit")) &&
                          <ButtonMin
                            type="button"
                            onClick={async () => await updateFnbQty(key, -1)}
                            disabled={(Number(row?.quantity) ?? 1) <= 1}
                          />
                        }
                        <div className="p-2 py-1">{row.quantity}</div>
                        {(!readonly && can("WismaApp.ReservationFoodAndBeverages.Edit")) &&
                          <ButtonPlus
                            type="button"
                            onClick={async () => await updateFnbQty(key, 1)}
                          />
                        }
                      </div>
                    </td>
                    <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                      {formatCurrencyIDR(row?.totalPrice ?? 0)}
                    </td>
                    <td className="w-[4%]">
                      {(!readonly && can("WismaApp.ReservationFoodAndBeverages.Delete")) &&
                        // <button
                        //   type="button"
                        //   onClick={async () => await removeFnb(key)}
                        //   className="rounded-md px-2 py-1 text-xs text-white"
                        // >
                        //   <PiTrash className="h-4 w-4 text-red-500 transition-transform duration-200 hover:scale-125 hover:text-red-700" />
                        // </button>
                        <ButtonDelete
                          onClick={async () => await removeFnb(key)}
                          title={"Are you sure?"}
                          text={
                            "This action will delete the selected tansaction. Continue?"
                          }
                        />
                      }
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Footer Table */}
          <table className="min-w-full border-collapse table-auto">
            <tfoot className="">
              <tr>
                <th className="text-medium px-1 py-1 text-start tracking-wider text-gray-500" >
                  TOTAL
                </th>
                <th className="text-medium px-1 py-1 text-start tracking-wider text-gray-500 w-[29%]">
                  {formatCurrencyIDR(totalPrice)}
                </th>
              </tr>
            </tfoot>
          </table>
        </>

      </div>
    </div>
  );
}
