/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { type CustomSession } from "./custom-types";
import os from "os";
import { getSession } from "./actions";
import type { CurrentUserDto } from "@/client";
import { formatDate } from "./helper/format-date";

export const ActivityLog = async (
  status = "INFO",
  activity: string,
  message: string,
  data: any = {},
  headers: any = {},
  currentUser: CurrentUserDto | undefined
) => {
  const session = await getSession();
  if (currentUser?.roles?.includes("WISMA Superuser") && status == "INFO") {
    status = "MONITOR";
  }
  // GET SERVER IP
  const networkInterfaces = os.networkInterfaces();
  let serverIpAddress = "Not available";

  for (const interfaceName in networkInterfaces) {
    const addresses = networkInterfaces[interfaceName];
    if (!addresses) continue;
    for (const address of addresses) {
      if (address.family === "IPv4" && !address.internal) {
        serverIpAddress = address.address;
        break;
      }
    }
    if (serverIpAddress !== "Not available") break;
  }
  headers.serverIpAddress = serverIpAddress;
  // END GET SERVER IP

  await toTelegram(status, activity, message, data, headers, currentUser);
};

async function toTelegram(
  status = "INFO",
  activity: string,
  message: string,
  data: any = {},
  headers: any = {},
  currentUser: CurrentUserDto | undefined
) {
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  const chatId = process.env.TELEGRAM_CHAT_ID;

  if (!botToken || !chatId) {
    // throw "Telegram bot token or chat ID is not configured";
    // console.log("Telegram bot token or chat ID is not configured");
  }

  const messages = `
WISMA (${status})
Env: ${process.env.STACK_ENV} ${headers.serverIpAddress}
[${formatDate(new Date(), "datetimesecond")}]
${currentUser?.name ?? "unknown"} ${message}
[${JSON.stringify(data)}]
  `;

  try {
    const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: messages
      })
    });

    if (!response.ok) {
      // console.log("Failed to send telegram message");
    }
  } catch (error) {
    throw (
      "An error occurred while sending the telegram message:" +
      JSON.stringify(error) // error
    );
  }
}
