export default function InvoiceIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#FFAB56"
        d="M5.585 2.78c4.611 0 7.778-2.39 10-2.39 2.222 0 6.888 1.278 6.888 11.5s-6.277 11.722-8.721 11.722c-11.445 0-16.334-20.833-8.167-20.833Z"
      />
      <path
        fill="#FFAB56"
        d="m18.602 2.078.471-.471.943.943-.471.47-.943-.942Zm2.71 2.711.472-.471.943.942-.472.472-.943-.943Zm-.118-2.242.943-.943.471.471-.942.943-.472-.471Z"
      />
      <path
        fill="#00D38E"
        d="M5.833 8.835A1.333 1.333 0 0 1 4.5 7.5v-1c0-.736.597-1.333 1.333-1.333h12.334c.736 0 1.333.597 1.333 1.333v1c0 .737-.597 1.334-1.333 1.334H5.833Z"
      />
      <path
        fill="#F3F3F1"
        d="m15.5 18.835-2.167-2-2 2-2-2-2.166 2V9.168a2 2 0 0 1 2-2H17.5a2 2 0 0 0-2 2v9.667Z"
      />
      <path
        fill="#FFAB56"
        d="M6 7.501v-1c0-.736.597-1.333 1.333-1.333h-1.5c-.736 0-1.333.597-1.333 1.333v1c0 .737.597 1.334 1.333 1.334h1.5A1.333 1.333 0 0 1 6 7.5Z"
      />
      <path
        fill="#FF94BE"
        d="M8.666 9.168a2 2 0 0 1 2-2h-1.5a2 2 0 0 0-2 2v9.667l1.5-1.385V9.168Z"
      />
      <path
        fill="#000"
        d="M18.167 9.335h-2.5a.5.5 0 0 1 0-1h2.5c.459 0 .833-.374.833-.834v-1a.834.834 0 0 0-.833-.833H5.833A.834.834 0 0 0 5 6.501v1c0 .46.374.834.833.834H7a.5.5 0 0 1 0 1H5.833A1.835 1.835 0 0 1 4 7.5v-1c0-1.011.822-1.833 1.833-1.833h12.334c1.011 0 1.833.822 1.833 1.833v1a1.835 1.835 0 0 1-1.833 1.834Z"
      />
      <path fill="#000" d="M17.5 7.668h-11a.5.5 0 0 1 0-1h11a.5.5 0 0 1 0 1Z" />
      <path
        fill="#000"
        d="M15.5 19.335a.5.5 0 0 1-.34-.133l-1.813-1.674-1.66 1.66a.5.5 0 0 1-.708 0l-1.66-1.66-1.814 1.674a.501.501 0 0 1-.839-.367V9.168c0-1.379 1.122-2.5 2.5-2.5H17.5a.5.5 0 0 1 0 1c-.827 0-1.5.673-1.5 1.5v9.667a.5.5 0 0 1-.5.5Zm-6.167-3c.128 0 .256.049.354.146l1.646 1.647 1.647-1.647a.5.5 0 0 1 .693-.014L15 17.693V9.168c0-.562.187-1.082.501-1.5H9.166c-.827 0-1.5.673-1.5 1.5v8.525l1.328-1.226a.498.498 0 0 1 .34-.132Z"
      />
    </svg>
  );
}
