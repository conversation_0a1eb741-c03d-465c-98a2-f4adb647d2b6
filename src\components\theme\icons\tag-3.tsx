export default function TagIcon3({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="45"
      height="44"
      viewBox="0 0 45 44"
      fill="none"
      {...props}
    >
      <path
        d="M10.5334 5.09016C18.9875 5.09016 24.793 0.710938 28.8667 0.710938C32.9404 0.710938 41.4959 3.05272 41.4959 21.793C41.4959 40.5334 29.9863 43.2834 25.5056 43.2834C4.5237 43.2846 -4.43886 5.09016 10.5334 5.09016Z"
        fill="#FFE2FC"
      />
      <path
        d="M38.7943 7.33073C37.7835 7.33073 36.9609 6.50817 36.9609 5.4974C36.9609 4.48662 37.7835 3.66406 38.7943 3.66406C39.805 3.66406 40.6276 4.48662 40.6276 5.4974C40.6276 6.50817 39.805 7.33073 38.7943 7.33073ZM38.7943 4.88628C38.4569 4.88628 38.1832 5.16006 38.1832 5.4974C38.1832 5.83473 38.4569 6.10851 38.7943 6.10851C39.1316 6.10851 39.4054 5.83473 39.4054 5.4974C39.4054 5.16006 39.1316 4.88628 38.7943 4.88628Z"
        fill="#A4AFC1"
      />
      <path
        d="M28.7144 11.9542C29.3011 11.8442 29.9244 12.0154 30.3766 12.4554L35.4855 17.6498C35.8522 18.0042 36.0477 18.4809 36.0477 18.9454V33.9176C36.0477 34.382 35.8522 34.8587 35.4855 35.2131C35.1189 35.5676 34.6177 35.7509 34.1289 35.7509H23.9111C23.4222 35.7509 22.9211 35.5676 22.5544 35.2131C22.1877 34.8587 21.9922 34.382 21.9922 33.9176V30.092L28.7144 11.9542Z"
        fill="#FFB7F8"
      />
      <path
        d="M29.9358 13.7474V21.0807C29.9358 21.5818 29.728 22.0463 29.398 22.3763L19.0091 32.7652C18.6791 33.0952 18.2147 33.303 17.7135 33.303C17.2124 33.303 16.748 33.0952 16.418 32.7652L9.08465 25.4318C8.75465 25.1018 8.54688 24.6374 8.54688 24.1363C8.54688 23.6352 8.75465 23.1707 9.08465 22.8407L19.4735 12.4518C19.8035 12.1218 20.268 11.9141 20.7691 11.9141H28.1024C28.2124 11.9141 28.3102 11.9263 28.408 11.9507C29.2758 12.0852 29.9358 12.843 29.9358 13.7474Z"
        fill="white"
      />
      <path
        d="M11.8347 25.4318C11.5047 25.1018 11.2969 24.6374 11.2969 24.1363C11.2969 23.6352 11.5047 23.1707 11.8347 22.8407L22.2235 12.4518C22.5535 12.1218 23.018 11.9141 23.5191 11.9141H20.7691C20.268 11.9141 19.8035 12.1218 19.4735 12.4518L9.08465 22.8407C8.75465 23.1707 8.54688 23.6352 8.54688 24.1363C8.54688 24.6374 8.75465 25.1018 9.08465 25.4318L16.418 32.7652C16.748 33.0952 17.2124 33.303 17.7135 33.303C18.2147 33.303 18.6791 33.0952 19.0091 32.7652L19.0885 32.6857L11.8347 25.4318Z"
        fill="#D5DBE1"
      />
      <path
        d="M24.7422 33.9146V30.089L26.5413 25.2344L22.1719 29.6038L21.9922 30.089V33.9146C21.9922 34.379 22.1877 34.8557 22.5544 35.2102C22.9211 35.5646 23.4222 35.7479 23.9111 35.7479H26.6611C26.1722 35.7479 25.6711 35.5646 25.3044 35.2102C24.9377 34.8557 24.7422 34.379 24.7422 33.9146Z"
        fill="#D900C3"
      />
      <path
        d="M17.7161 34.2222C16.9938 34.2222 16.2849 33.9289 15.7728 33.4156L8.43948 26.0822C7.92615 25.5701 7.63281 24.8612 7.63281 24.1389C7.63281 23.4166 7.92615 22.7077 8.43948 22.1956L18.8284 11.8067C19.3405 11.2933 20.0494 11 20.7717 11H28.105C28.2884 11 28.4619 11.0208 28.633 11.0636C29.865 11.2518 30.855 12.4129 30.855 13.75V21.0833C30.855 21.8057 30.5617 22.5146 30.0484 23.0267L19.6595 33.4156C19.1474 33.9289 18.4385 34.2222 17.7161 34.2222ZM20.7717 12.8333C20.5309 12.8333 20.295 12.9311 20.1239 13.1022L9.73503 23.4911C9.56392 23.6622 9.46615 23.8981 9.46615 24.1389C9.46615 24.3797 9.56392 24.6156 9.73503 24.7867L17.0684 32.12C17.4106 32.4622 18.0205 32.4622 18.3639 32.12L28.7528 21.7311C28.9239 21.56 29.0217 21.3241 29.0217 21.0833V13.75C29.0217 13.3088 28.6978 12.925 28.27 12.859L28.105 12.8333H20.7717Z"
        fill="black"
      />
      <path
        d="M34.1315 36.6704H23.9137C23.1693 36.6704 22.4421 36.3808 21.9202 35.8748C21.3849 35.3578 21.0781 34.6452 21.0781 33.9204V30.0949H22.9115V33.9204C22.9115 34.1478 23.0166 34.3861 23.1938 34.5572C23.3759 34.7332 23.6448 34.8371 23.9137 34.8371H34.1315C34.4003 34.8371 34.6692 34.7332 34.8501 34.5572C35.0273 34.3861 35.1325 34.1478 35.1325 33.9204V18.9482C35.1325 18.7209 35.0273 18.4826 34.8501 18.3114L29.7253 13.1011C29.5139 12.8958 29.1925 12.7992 28.8857 12.8579L28.5483 11.0551C29.443 10.8889 30.3682 11.1663 31.0185 11.8007L36.142 17.0098C36.6602 17.5109 36.967 18.2234 36.967 18.9482V33.9204C36.967 34.6452 36.6602 35.3578 36.1261 35.8748C35.603 36.3808 34.877 36.6704 34.1315 36.6704Z"
        fill="black"
      />
      <path
        d="M26.5755 17.112C27.588 17.112 28.4089 16.2912 28.4089 15.2786C28.4089 14.2661 27.588 13.4453 26.5755 13.4453C25.563 13.4453 24.7422 14.2661 24.7422 15.2786C24.7422 16.2912 25.563 17.112 26.5755 17.112Z"
        fill="black"
      />
      <path
        d="M34.8229 16.197H32.9896V11.9193C32.9896 10.4025 31.7564 9.16927 30.2396 9.16927C28.7228 9.16927 27.4896 10.4025 27.4896 11.9193V14.9748H25.6562V11.9193C25.6562 9.39172 27.712 7.33594 30.2396 7.33594C32.7671 7.33594 34.8229 9.39172 34.8229 11.9193V16.197Z"
        fill="black"
      />
    </svg>
  );
}
