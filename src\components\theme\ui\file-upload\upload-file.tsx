import { get } from "lodash";
import { type ChangeEvent } from "react";
import type {
  UseFormGetValues,
  FieldValues,
  UseFormSetValue,
  UseFormRegister,
  FieldErrors,
} from "react-hook-form";
import { FileInput, Text } from "rizzui";

export function UploadFile({
  type = "base64",
  ...props
}: {
  label: string;
  name: string;
  getValues: UseFormGetValues<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  register: UseFormRegister<FieldValues>;
  errors: FieldErrors<FieldValues>;
  accept?: string;
  imagePreview?: string;
  filename?: string;
  size?: "md" | "sm" | "lg" | "xl" | undefined;
  required?: boolean;
  readOnly?: boolean;
  type?: "file" | "base64";
}) {
  props.register(props.name, {
    required: props.required,
  });
  // const [base64File, setBase64File] = useState<string>("");
  // const [imagePreview, setImagePreview] = useState<string>();
  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64: string = reader?.result as string;
        // setBase64File(base64);
        // console.log("base64", base64.slice(0, 35));
        if (type === "file") {
          props.setValue(`${props.name}`, file as unknown as never); // Set the base64 value to the form
        } else {
          props.setValue(`${props.name}`, base64 as unknown as never); // Set the base64 value to the form
        }
      };
      reader.readAsDataURL(file as unknown as Blob);
    }
  };
  // // console.log("imagePreview", imagePreview);
  // useEffect(() => {
  //   if (base64File) {
  //     const blob = fetch(base64File).then((res) => res.blob());
  //     blob
  //       .then((data) => {
  //         const link = URL.createObjectURL(data);
  //         // setImagePreview(link);
  //       })
  //       .catch((err) => // console.log(err));
  //   }
  // }, [base64File]);

  return (
    <>
      <FileInput
        label={props.label}
        onChange={(e) => handleFileChange(e)}
        accept={props.accept ?? "image/*"}
        size={props.size ?? "md"}
      />
      {get(props.errors, props.name) && (
        <Text as="small" className="text-red">
          {props.label + " is required"}
        </Text>
      )}
      {/* {(downloadLink ?? props.downloadLink) && (
        <Link
          href={downloadLink ?? props.downloadLink ?? "/"}
          target="_blank"
          className="mt-1 flex justify-end"
          download={props.filename}
        >
          saved (.docx)
          <PiDownload className="ml-2 mt-1" />
        </Link>
      )} */}
      {/* {(imagePreview ?? props.imagePreview) && (
        <figure className="group relative mt-4 h-40 rounded-md bg-gray-50">
          <div
            className="overflow-hidden rounded-md"
            style={{ width: "100%", height: "100%" }}
          >
            <img
              src={imagePreview ? imagePreview : props.imagePreview}
              alt="background"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
                objectPosition: "center",
              }}
            />
          </div>
        </figure>
      )} */}
    </>
  );
}
