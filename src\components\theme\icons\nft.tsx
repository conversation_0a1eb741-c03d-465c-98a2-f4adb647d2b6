export default function NftIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      viewBox="0 0 682.667 682.667"
      {...props}
    >
      <defs>
        <clipPath id="clipPath5186" clipPathUnits="userSpaceOnUse">
          <path d="M0 512h512V0H0z"></path>
        </clipPath>
      </defs>
      <g transform="matrix(1.33333 0 0 -1.33333 0 682.667)">
        <g>
          <g clipPath="url(#clipPath5186)">
            <g transform="translate(371 82)">
              <path
                fill="#faf7f5"
                fillOpacity="1"
                fillRule="nonzero"
                stroke="none"
                d="M0 0h-230c-11.046 0-20 8.954-20 20v344c0 11.046 8.954 20 20 20H0c11.046 0 20-8.954 20-20V20C20 8.954 11.046 0 0 0"
              ></path>
            </g>
            <g transform="translate(371 82)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0h-230c-11.046 0-20 8.954-20 20v344c0 11.046 8.954 20 20 20H0c11.046 0 20-8.954 20-20V20C20 8.954 11.046 0 0 0z"
              ></path>
            </g>
            <g transform="translate(487.19 382.084)">
              <path
                fill="#ff7d97"
                fillOpacity="1"
                fillRule="nonzero"
                stroke="none"
                d="M0 0l-96.19 23.916v-304c0-11.046-8.956-20-20-20h-115l146.583-35.321c1.76-.424 3.688-.677 5.417-.679 8.417-.008 16.532 6.445 18.705 15.41l23.936 95.053 22.074 87.659L14.219-24.02s.591 2.874.591 6.43C14.81-8.001 8.32-2.249 0 0"
              ></path>
            </g>
            <g transform="translate(472.716 244.122)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0l28.693 113.942s.591 2.873.591 6.43c0 9.589-6.489 15.341-14.809 17.59l-96.191 23.916v-304c0-11.046-8.955-20-20-20h-115l146.583-35.321c1.761-.424 3.689-.677 5.417-.679 8.417-.008 16.532 6.445 18.705 15.41l23.937 95.053"
              ></path>
            </g>
            <g transform="translate(461.672 200.156)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0v0"
              ></path>
            </g>
            <g transform="translate(161 132)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0v79.851L38.521 1.675C39.235.229 40 .937 40 2.696V80"
              ></path>
            </g>
            <g transform="translate(271 212)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0h-30v-80"
              ></path>
            </g>
            <g transform="translate(241 172.758)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0h27.617"
              ></path>
            </g>
            <g transform="translate(311 212)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0h40"
              ></path>
            </g>
            <g transform="translate(330.919 209.445)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0v-77.445"
              ></path>
            </g>
            <g transform="translate(187 376)">
              <path
                fill="#ffda8f"
                fillOpacity="1"
                fillRule="nonzero"
                stroke="none"
                d="M0 0v-80l69-40 69 40V0L69 40z"
              ></path>
            </g>
            <g transform="translate(187 376)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0v-80l69-40 69 40V0L69 40z"
              ></path>
            </g>
            <g transform="translate(391 253.5)">
              <path
                fill="#ffda8f"
                fillOpacity="1"
                fillRule="nonzero"
                stroke="none"
                d="M0 0l19 4 23 66-42 47z"
              ></path>
            </g>
            <g transform="translate(391 253.5)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0l19 4 23 66-42 47z"
              ></path>
            </g>
            <g transform="translate(24.81 382.084)">
              <path
                fill="#bfdbff"
                fillOpacity="1"
                fillRule="nonzero"
                stroke="none"
                d="M0 0l96.19 23.916v-304c0-11.046 8.956-20 20-20h115L84.607-335.405c-1.76-.424-3.688-.677-5.417-.679-8.417-.008-16.532 6.445-18.705 15.41L-14.219-24.02s-.591 3.603-.591 6.43C-14.81-7.834-8.32-2.249 0 0"
              ></path>
            </g>
            <g transform="translate(24.81 382.084)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0l96.19 23.916v-304c0-11.046 8.956-20 20-20h115L84.607-335.405c-1.76-.424-3.688-.677-5.417-.679-8.417-.008-16.532 6.445-18.705 15.41L-14.219-24.02s-.591 3.603-.591 6.43C-14.81-7.834-8.32-2.249 0 0z"
              ></path>
            </g>
            <g transform="translate(121 253.5)">
              <path
                fill="#ffda8f"
                fillOpacity="1"
                fillRule="nonzero"
                stroke="none"
                d="M0 0l-19 4-23 66 42 47z"
              ></path>
            </g>
            <g transform="translate(121 253.5)">
              <path
                fill="none"
                stroke="#000"
                strokeDasharray="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit="10"
                strokeOpacity="1"
                strokeWidth="20"
                d="M0 0l-19 4-23 66 42 47z"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}
