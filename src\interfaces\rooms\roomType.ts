export interface RoomType {
  id: string;
  name: string;
  alias: string;
  status: number;
  creationTime: string;
  creatorId: string | null;
  lastModificationTime: string | null;
  lastModifierId: string | null;
}

export interface RoomTypeResponse {
  totalCount: number;
  items: RoomType[];
}

export interface RoomTypePostSuccessResponse {
  success: true;
  message: string;
  data: RoomType;
}

export interface RoomTypePostErrorResponse {
  success: false;
  message: string;
  data: null;
  error: {
    code: string;
    message: string;
    details: string;
    data?: Record<string, string>;
    validationErrors?: {
      message: string;
      members: string[];
    }[];
  };
}

export type RoomTypePostResponse =
  | RoomTypePostSuccessResponse
  | RoomTypePostErrorResponse;
