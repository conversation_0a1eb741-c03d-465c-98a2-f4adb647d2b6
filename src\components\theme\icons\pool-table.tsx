export default function PoolTableIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_5588_10644)">
        <path
          d="M18.897 0H5.10207C4.41286 0 3.85205 0.560766 3.85205 1.25002V22.75C3.85205 23.4392 4.41282 24 5.10207 24H18.897C19.5862 24 20.147 23.4392 20.147 22.75V1.25002C20.147 0.560812 19.5862 0 18.897 0ZM18.0158 20.6743C17.3569 20.6743 16.8211 21.2102 16.8211 21.8691C16.8211 21.9139 16.8294 21.9564 16.8343 21.9999H7.16447C7.1693 21.9564 7.17769 21.9139 7.17769 21.8691C7.17769 21.2102 6.64182 20.6743 5.98289 20.6743C5.93808 20.6743 5.89557 20.6827 5.85207 20.6875V13.1816C5.89561 13.1864 5.93808 13.1948 5.98289 13.1948C6.64182 13.1948 7.17769 12.6589 7.17769 12C7.17769 11.3411 6.64182 10.8052 5.98289 10.8052C5.93808 10.8052 5.89557 10.8136 5.85207 10.8184V3.31247C5.89561 3.31725 5.93808 3.32564 5.98289 3.32564C6.64182 3.32564 7.17769 2.78977 7.17769 2.13084C7.17769 2.08603 7.1693 2.04352 7.16447 2.00002H16.8342C16.8294 2.04356 16.821 2.08603 16.821 2.13084C16.821 2.78977 17.3569 3.32564 18.0158 3.32564C18.0608 3.32564 18.1035 3.31725 18.1472 3.31242V10.8184C18.1035 10.8135 18.0608 10.8052 18.0158 10.8052C17.3569 10.8052 16.821 11.341 16.821 12C16.821 12.6589 17.3569 13.1947 18.0158 13.1947C18.0608 13.1947 18.1035 13.1864 18.1472 13.1815V20.6875C18.1035 20.6827 18.0608 20.6743 18.0158 20.6743ZM5.28586 12C5.28586 11.6169 5.59861 11.3052 5.98289 11.3052C6.36596 11.3052 6.67772 11.6169 6.67772 12C6.67772 12.3831 6.36596 12.6948 5.98289 12.6948C5.59861 12.6948 5.28586 12.3831 5.28586 12ZM5.01328 12.6951H4.35207V11.3049H5.01328C4.87163 11.5012 4.78589 11.7401 4.78589 12C4.78585 12.2599 4.87163 12.4988 5.01328 12.6951ZM18.0158 12.6948C17.6328 12.6948 17.321 12.3831 17.321 12C17.321 11.6169 17.6328 11.3052 18.0158 11.3052C18.4001 11.3052 18.7129 11.6169 18.7129 12C18.7129 12.3831 18.4001 12.6948 18.0158 12.6948ZM18.9857 11.3052H19.647V12.6951H18.9855C19.1272 12.4987 19.2129 12.2599 19.2129 12C19.2128 11.7403 19.1272 11.5014 18.9857 11.3052ZM18.6472 10.8052V3.24H19.647V10.8052H18.6472ZM18.0158 2.82572C17.6328 2.82572 17.321 2.51395 17.321 2.13089C17.321 2.02739 17.3453 1.93003 17.386 1.84172C17.3865 1.84078 17.3871 1.84003 17.3874 1.83905C17.4981 1.60186 17.7373 1.43602 18.0158 1.43602C18.4001 1.43602 18.7128 1.74778 18.7128 2.13084C18.7128 2.51391 18.4001 2.82572 18.0158 2.82572ZM16.907 1.50005H7.09205V0.500016H16.907V1.50005ZM6.67777 2.13084C6.67777 2.51391 6.366 2.82567 5.98294 2.82567C5.59866 2.82567 5.28591 2.51391 5.28591 2.13084C5.28591 1.74778 5.59866 1.43602 5.98294 1.43602C6.36596 1.43606 6.67777 1.74783 6.67777 2.13084ZM5.35205 3.24V10.8049H4.35207V3.24H5.35205ZM4.35207 13.195H5.35205V20.76H4.35207V13.195ZM5.98289 21.1743C6.36596 21.1743 6.67772 21.486 6.67772 21.8691C6.67772 22.2522 6.36596 22.5639 5.98289 22.5639C5.59861 22.5639 5.28586 22.2522 5.28586 21.8691C5.28586 21.486 5.59861 21.1743 5.98289 21.1743ZM7.09205 22.5H16.907V23.5H7.09205V22.5ZM17.3875 22.1609C17.3871 22.1599 17.3865 22.1592 17.386 22.1582C17.3453 22.0699 17.3211 21.9725 17.3211 21.8691C17.3211 21.486 17.6328 21.1742 18.0159 21.1742C18.4002 21.1742 18.7129 21.486 18.7129 21.8691C18.7129 22.2521 18.4002 22.5639 18.0159 22.5639C17.7373 22.5639 17.4982 22.3981 17.3875 22.1609ZM18.6472 20.76V13.195H19.647V20.76H18.6472ZM19.647 1.25002V2.73998H19.0401C19.1474 2.56102 19.2129 2.3542 19.2129 2.13084C19.2129 1.47192 18.676 0.936047 18.0158 0.936047C17.7926 0.936047 17.5859 1.00144 17.4069 1.10845V0.500016H18.8969C19.3105 0.500016 19.647 0.836437 19.647 1.25002ZM5.10207 0.500016H6.59204V1.10859C6.41307 1.00148 6.20625 0.936047 5.98289 0.936047C5.32271 0.936047 4.78585 1.47192 4.78585 2.13084C4.78585 2.3542 4.85138 2.56097 4.95868 2.73998H4.35207V1.25002C4.35207 0.836437 4.68844 0.500016 5.10207 0.500016ZM4.35207 22.75V21.26H4.95872C4.85143 21.439 4.78589 21.6458 4.78589 21.8692C4.78589 22.5281 5.32275 23.064 5.98294 23.064C6.2063 23.064 6.41307 22.9985 6.59208 22.8914V23.5H5.10211C4.68844 23.5 4.35207 23.1636 4.35207 22.75ZM18.897 23.5H17.407V22.8915C17.5859 22.9986 17.7926 23.064 18.0159 23.064C18.6761 23.064 19.2129 22.5281 19.2129 21.8692C19.2129 21.6458 19.1474 21.439 19.0401 21.26H19.647V22.75C19.647 23.1636 19.3105 23.5 18.897 23.5Z"
          fill="currentColor"
        />
        <path
          d="M14.3938 4.60645C14.0735 4.60645 13.7897 4.75345 13.597 4.97995C13.4052 4.75199 13.1189 4.60645 12.7983 4.60645C12.4765 4.60645 12.1919 4.75429 12.0002 4.98299C11.808 4.75476 11.5238 4.60645 11.2028 4.60645C10.8811 4.60645 10.5964 4.75476 10.4038 4.9829C10.2117 4.75495 9.92831 4.60645 9.60764 4.60645C9.02906 4.60645 8.55811 5.07613 8.55811 5.65359C8.55811 6.16743 8.93011 6.59446 9.41854 6.68399C9.37861 6.79363 9.35568 6.91138 9.35568 7.0349C9.35568 7.54945 9.72867 7.97648 10.218 8.06526C10.1779 8.17504 10.1535 8.29237 10.1535 8.41621C10.1535 8.93104 10.5269 9.35849 11.0166 9.44695C10.9767 9.55738 10.9512 9.67476 10.9512 9.79879C10.9512 10.3774 11.4221 10.8481 12.0007 10.8481C12.5781 10.8481 13.0478 10.3774 13.0478 9.79879C13.0478 9.67471 13.023 9.5572 12.9827 9.44709C13.4721 9.3592 13.8455 8.93155 13.8455 8.41621C13.8455 8.2926 13.8221 8.17504 13.7825 8.06526C14.2709 7.97657 14.643 7.54945 14.643 7.03485C14.643 6.91129 14.6191 6.79401 14.5794 6.68423C15.0682 6.59582 15.4409 6.16837 15.4409 5.65345C15.4409 5.07623 14.9712 4.60645 14.3938 4.60645ZM12.2494 5.64754C12.2527 5.34862 12.4974 5.10646 12.7983 5.10646C13.098 5.10646 13.3415 5.34862 13.3448 5.64754C13.3448 5.6496 13.3442 5.65152 13.3442 5.65354C13.3442 5.65565 13.3448 5.65752 13.3448 5.65954C13.3415 5.95963 13.098 6.20305 12.7983 6.20305C12.4974 6.20305 12.2527 5.95968 12.2494 5.65954C12.2494 5.65748 12.25 5.65555 12.25 5.65354C12.25 5.65152 12.2494 5.64965 12.2494 5.64754ZM12.0007 6.48782C12.3025 6.48782 12.5478 6.73321 12.5478 7.03495C12.5478 7.3379 12.3024 7.58427 12.0007 7.58427C11.6977 7.58427 11.4511 7.33795 11.4511 7.03495C11.4511 6.73316 11.6977 6.48782 12.0007 6.48782ZM11.2028 5.10646C11.5046 5.10646 11.75 5.35185 11.75 5.65359C11.75 5.95654 11.5046 6.20315 11.2028 6.20315C10.8999 6.20315 10.6535 5.95659 10.6535 5.65359C10.6536 5.35185 10.8999 5.10646 11.2028 5.10646ZM9.05812 5.65359C9.05812 5.3518 9.30468 5.10646 9.60768 5.10646C9.90736 5.10646 10.1509 5.34862 10.1542 5.64754C10.1542 5.6496 10.1535 5.65152 10.1535 5.65354C10.1535 5.65565 10.1542 5.65752 10.1542 5.65954C10.1508 5.95963 9.90736 6.20305 9.60768 6.20305C9.30468 6.20315 9.05812 5.95659 9.05812 5.65359ZM9.8557 7.03495C9.8557 6.73316 10.1023 6.48782 10.4053 6.48782C10.707 6.48782 10.9524 6.73321 10.9524 7.03495C10.9524 7.3379 10.707 7.58427 10.4053 7.58427C10.1023 7.58423 9.8557 7.3379 9.8557 7.03495ZM11.2028 8.96582C10.8999 8.96582 10.6535 8.71926 10.6535 8.41626C10.6535 8.11448 10.8998 7.86913 11.2028 7.86913C11.5046 7.86913 11.75 8.11452 11.75 8.41626C11.75 8.71926 11.5046 8.96582 11.2028 8.96582ZM12.0007 10.3482C11.6977 10.3482 11.4511 10.1018 11.4511 9.79884C11.4511 9.49706 11.6977 9.25171 12.0007 9.25171C12.3025 9.25171 12.5478 9.4971 12.5478 9.79884C12.5478 10.1018 12.3025 10.3482 12.0007 10.3482ZM12.7983 8.96582C12.4954 8.96582 12.2488 8.71926 12.2488 8.41626C12.2488 8.11448 12.4953 7.86913 12.7983 7.86913C13.1001 7.86913 13.3454 8.11452 13.3454 8.41626C13.3454 8.71926 13.1001 8.96582 12.7983 8.96582ZM13.5959 7.58423C13.293 7.58423 13.0466 7.3379 13.0466 7.0349C13.0466 6.73312 13.2929 6.48777 13.5959 6.48777C13.8977 6.48777 14.1431 6.73316 14.1431 7.0349C14.143 7.3379 13.8977 7.58423 13.5959 7.58423ZM14.3938 6.20315C14.0908 6.20315 13.8442 5.95659 13.8442 5.65359C13.8442 5.3518 14.0908 5.10646 14.3938 5.10646C14.6955 5.10646 14.9409 5.35185 14.9409 5.65359C14.9409 5.95659 14.6955 6.20315 14.3938 6.20315Z"
          fill="currentColor"
        />
        <path
          d="M12.0007 16.2012C11.4221 16.2012 10.9512 16.6709 10.9512 17.2483C10.9512 17.8269 11.4221 18.2976 12.0007 18.2976C12.5781 18.2976 13.0478 17.8269 13.0478 17.2483C13.0478 16.671 12.5781 16.2012 12.0007 16.2012ZM12.0007 17.7976C11.6978 17.7976 11.4511 17.5513 11.4511 17.2483C11.4511 16.9465 11.6977 16.7012 12.0007 16.7012C12.3025 16.7012 12.5478 16.9466 12.5478 17.2483C12.5478 17.5513 12.3025 17.7976 12.0007 17.7976Z"
          fill="currentColor"
        />
        <path
          d="M1.77863 5.1155C1.76813 4.79445 1.50811 4.54297 1.18683 4.54297C0.865784 4.54297 0.605768 4.79441 0.595268 5.1155L0.00103384 22.9942C-0.0172005 23.5459 0.423987 23.9998 0.973925 23.9998H1.39997C1.95071 23.9998 2.39133 23.5445 2.37263 22.9942C2.21152 18.1449 1.89014 8.47114 1.77863 5.1155ZM1.09477 5.13209C1.09646 5.08208 1.137 5.04298 1.18683 5.04298C1.23685 5.04298 1.27739 5.08208 1.27913 5.13209L1.69992 17.7983H0.673784L1.09477 5.13209ZM1.39997 23.4998H0.973925C0.707159 23.4998 0.491956 23.2796 0.500534 23.0108L0.657518 18.2984H1.71638L1.87313 23.0108C1.88175 23.2802 1.66627 23.4998 1.39997 23.4998Z"
          fill="currentColor"
        />
        <path
          d="M23.4046 5.1155C23.3941 4.79445 23.1341 4.54297 22.8128 4.54297C22.492 4.54297 22.232 4.79441 22.221 5.1155C22.1106 8.44006 21.7889 18.1235 21.627 22.9941C21.6083 23.5445 22.049 23.9997 22.5997 23.9997H23.026C23.5767 23.9997 24.0173 23.5445 23.9986 22.9941C23.8381 18.1615 23.516 8.46575 23.4046 5.1155ZM22.7206 5.13233C22.7223 5.08231 22.7628 5.04298 22.8129 5.04298C22.8629 5.04298 22.9034 5.08208 22.9052 5.13209L23.326 17.7983H22.2997L22.7206 5.13233ZM23.026 23.4998H22.5997C22.3334 23.4998 22.118 23.2802 22.1266 23.0108L22.2832 18.2984H23.3426L23.4992 23.0108C23.5078 23.2802 23.2923 23.4998 23.026 23.4998Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_5588_10644">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
