"use client";
import React, { useEffect, useState } from "react";
import PageHeader<PERSON>ustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import ButtonForm from "@/components/container/button/button-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useMasterServices } from "@/lib/hooks/services/masterServices";
import {
  deleteApiAppServicesById,
  postApiAppServices,
  putApiAppServicesById,
  type CreateUpdateServicesDto,
  type ServicesDto,
} from "@/client";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import type { SelectOption } from "rizzui";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function MasterServices({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isDataServices, setDataServices] = useState<ServicesDto[]>([]);
  const [serviceType, setServiceType] = useState<SelectOption | undefined>();

  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 1000,
  });

  const { isLoading, data } = useMasterServices(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item: ServicesDto) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
        price: item.price ?? 0,
        usageTime: item.usageTime ?? 0,
        information: item.information ?? "",
        serviceTypeId: item.serviceTypeId ?? "",
        serviceTypeName: item.serviceTypeName ?? "",
      }));
      setDataServices(mappedData);
    }
  }, [data]);

  const columns = [
    { dataIndex: "name", title: "Name", filter: "text" as const },
    {
      dataIndex: "serviceTypeName",
      title: "Service Type",
      filter: "select" as const,
    },
    { dataIndex: "usageTime", title: "Usage Time", filter: "text" as const },
    { dataIndex: "price", title: "Price", filter: "text" as const },
    { dataIndex: "information", title: "Information", filter: "text" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setServiceType(undefined);
    setIsEditMode(false);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetRoomType],
    });
  };

  const deleteServiceMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppServicesById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Services deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetServices],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Service will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete service with id:", id);
        await deleteServiceMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the service. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const service = isDataServices.find((item) => item.id === id);
    if (service) {
      setIsEditMode(true);
      Object.entries(service).forEach(([key, value]) => {
        if (key === "serviceTypeName") {
          setServiceType({
            label: service.serviceTypeName ?? "",
            value: String(value),
          });
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createServicesMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateServicesDto) =>
      postApiAppServices({
        body: dataMutation,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Services Created Successfully",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetServices],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateServicesMutation = useMutation({
    mutationFn: async (dataMutation: ServicesDto) => {
      const { id, ...updateData } = dataMutation;

      return putApiAppServicesById({
        path: { id: id! },
        body: updateData as CreateUpdateServicesDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Services Updated Successfully",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetServices],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      if (isEditMode) {
        // console.log("Updating service:", formData);
        await updateServicesMutation.mutateAsync(formData);
      } else {
        formData.usageTime = Number(formData.usageTime);
        const { ...dataMutation } = formData;
        // console.log("Creating service:", dataMutation);
        await createServicesMutation.mutateAsync(
          dataMutation as unknown as CreateUpdateServicesDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  if (!can("WismaApp.ReservationRoom.View")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Services" },
            { name: "Master Service" },
          ]}
        >
          {can("WismaApp.ReservationRoom.Create") &&
            can("WismaApp.ReservationRoom.Edit") && (
              <ButtonForm
                isLoading={isLoading}
                isFormVisible={isFormVisible}
                setIsFormVisible={(visible) => {
                  if (visible) {
                    handleReset();
                  }
                  setIsFormVisible(visible);
                }}
              />
            )}
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              serviceType={serviceType}
              setServiceType={setServiceType}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  price: formatCurrencyIDR(e.price ?? 0),
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
