/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import axios from "axios";
import swal from "sweetalert";
import { type CustomFile, type TForm } from "../custom-types";
import React from "react";

export function dateNow() {
  return new Date(new Date().getTime() + 8 * 60 * 60 * 1000); // UTC+8
}

export function getRadioNote(value = "") {
  const drpNote = value?.match(/\(\((.*?)\)\)/)?.[1];
  const drpValue = value?.replace(/\(\((.*?)\)\)/, "");
  return { drpNote, drpValue };
}

export function getDate(date: Date | null | undefined, format?: string) {
  const months: string[] = [
    "Januari",
    "Februari",
    "Mare<PERSON>",
    "April",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>gus<PERSON>",
    "September",
    "Ok<PERSON><PERSON>",
    "November",
    "Desember",
  ];
  if (!date) return "";
  const currentDate = new Date(date);
  const dayOfMonth = currentDate.getDate();
  const monthIndex = currentDate.getMonth();
  const year = currentDate.getFullYear();
  // const time = currentDate.getFullYear();

  const formattedDate = `${dayOfMonth} ${months[monthIndex]} ${year}`;
  if (format == "datetime") {
    const hours = currentDate.getHours().toString().padStart(2, "0");
    const minutes = currentDate.getMinutes().toString().padStart(2, "0");
    const time = `${hours}:${minutes}`;
    return `${formattedDate}, ${time}`;
  }
  return formattedDate;
}

export function toUTC8(date: string, resetTime = false): string | null {
  if (!date) return null;

  // Parse the input date string
  const utcDate = new Date(date);

  // Calculate the UTC+8 offset in milliseconds
  const utcOffset = 8 * 60 * 60 * 1000;

  // Apply the offset to the date
  const localDate = new Date(utcDate.getTime() + utcOffset);

  // If resetTime is true, set the time to 00:00:00.000
  if (resetTime) {
    localDate.setUTCHours(0, 0, 0, 0);
  }

  // Format to ISO string with UTC+8 offset
  return localDate.toISOString().replace("Z", "+08:00");
}

export function getRomanMonth(): string | undefined {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1; // Months are zero-based
  // const currentYear = currentDate.getFullYear();
  const romanNumerals: Record<number, string> = {
    1: "I",
    2: "II",
    3: "III",
    4: "IV",
    5: "V",
    6: "VI",
    7: "VII",
    8: "VIII",
    9: "IX",
    10: "X",
    11: "XI",
    12: "XII",
  };
  return romanNumerals[currentMonth];
}

export function removeIds(obj: TForm): void {
  for (const key in obj) {
    if (key === "id") {
      delete obj[key];
    } else if (typeof obj[key] === "object" && obj[key] !== null) {
      removeIds(obj[key] as TForm);
    }
  }
}

export async function getPublicAttachment(
  // access_token: string,
  name: string,
  path: string,
): Promise<CustomFile | null> {
  try {
    const res = await axios({
      url: `${process.env.NEXT_PUBLIC_IMIP_AUTH_URL}/api/get_attachment`,
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      params: { path: path },
    });

    const base64Data = (res.data as TForm).base64 as string; // Assuming the response is a base64 string
    const matches: RegExpMatchArray | null = base64Data.match(
      /^data:([A-Za-z-+/]+);base64,(.+)$/,
    );
    const fileBuffer = matches
      ? Buffer.from(matches[2] ?? "", "base64")
      : Buffer.alloc(0);
    const blob = new Blob([fileBuffer], {
      type: matches ? matches[1] : "",
    });
    const fileName: string = name?.substring(name.lastIndexOf("/") + 1);
    const file: CustomFile = new File([blob], fileName, {
      type: blob.type,
    });
    file.preview = URL.createObjectURL(file);
    file.base64 = base64Data;
    // // console.log("----------file---------", file);
    return file;
  } catch (error) {
    await swal("Opps..", error as string, "error");
    console.error("Error fetching attachment:", error);
    return null;
  }
}

export async function activityLogClient(
  status = "INFO",
  activity: string,
  msg: string,
  data: any = {},
) {
  await axios({
    url: `/api/activity_log/0`,
    method: "POST",
    data: { msg, activity, data, status },
  });
}

// Helper function to handle circular references in JSON.stringify
export const renderCellValue = (value: unknown): React.ReactNode => {
  if (value === null || value === undefined) return "N/A";
  if (React.isValidElement(value)) return value; // Render React elements as-is
  if (typeof value === "object") {
    try {
      return JSON.stringify(value, getCircularReplacer()); // Safely stringify objects
    } catch {
      return "[Circular]";
    }
  }
  return String(value); // Convert everything else to string
};
export const getCircularReplacer = () => {
  const seen = new WeakSet();
  return (_key: string, value: unknown) => {
    if (typeof value === "object" && value !== null) {
      if (seen.has(value)) return "[Circular]";
      seen.add(value);
    }
    return value;
  };
};
