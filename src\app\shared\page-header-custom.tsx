import Breadcrumb from "@/components/theme/ui/breadcrumb";
import cn from "@/utils/class-names";
import { Title } from "rizzui";

export type PageHeaderTypes = {
  title?: string;
  breadcrumb: { name: string; href?: string }[];
  className?: string;
};

export default function PageHeaderCustom({
  title,
  breadcrumb,
  children,
  className,
}: React.PropsWithChildren<PageHeaderTypes>) {
  return (
    <header className={cn("mb-2 @container lg:mb-2", className)}>
      <div className="flex flex-col @lg:flex-row @lg:items-center @lg:justify-between">
        <div>
          <Breadcrumb
            separator=""
            separatorVariant="circle"
            className="flex-wrap"
          >
            {breadcrumb.map((item, index) => {
              const isLastItem = index === breadcrumb.length - 1;
              return (
                <Breadcrumb.Item
                  key={index}
                  {...(item?.href && { href: item?.href })}
                  className={isLastItem ? "text-green-500" : "text-slate-800"}
                >
                  <span
                    className={`text-sm font-medium ${
                      isLastItem ? "text-green-500" : "text-slate-800"
                    }`}
                  >
                    {item.name}
                  </span>
                </Breadcrumb.Item>
              );
            })}
          </Breadcrumb>
          {title && (
            <Title
              as="h1"
              className="mb-3 text-xl font-bold capitalize text-slate-800"
            >
              {title}
            </Title>
          )}
        </div>
        {children}
      </div>
    </header>
  );
}
