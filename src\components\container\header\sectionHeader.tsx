import type { ReactNode } from "react";

interface SectionHeaderProps {
  title: string;
  icon?: ReactNode;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({ title, icon }) => {
  return (
    <div className="col-span-full flex">
      <h5 className="grow text-slate-700">{title}</h5>
      {icon && <span className="h-5 w-5 text-[#49a785]">{icon}</span>}
    </div>
  );
};

export default SectionHeader;
