import { siteConfig } from '@/config/site.config';
import Image from 'next/image';
import React, { useEffect, useRef } from 'react';

const LoadingScreenAnimated = () => {
  const loaderRef = useRef(null);

  // Generate a random number between low and high (inclusive)
  // const randomNumber = (low, high) =>
  //   Math.floor(Math.random() * (high - low + 1)) + low;

  // // Generate a new RGB color string
  // const newRGB = () => {
  //   const r = randomNumber(0, 255);
  //   const g = randomNumber(0, 255);
  //   const b = randomNumber(0, 255);
  //   return `rgb(${r},${g},${b})`;
  // };

  // // Change loader background color every 1.3 seconds
  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     if (loaderRef.current) {
  //       loaderRef.current.style.backgroundColor = newRGB();
  //     }
  //   }, 1300);

  //   return () => clearInterval(interval);
  // }, []);

  return (
    <div className="loading-screen-fullscreen">
      <div className="loading-screen-loader" ref={loaderRef}>
        <div className="loading-screen-one"></div>
        <div className="loading-screen-two"></div>
        <div className="loading-screen-logo">
          <Image
            src={siteConfig.logo}
            alt={"IMIP"}
            // className="w-[140px]"
          />
        </div>
      </div>
    </div>
  );
};

export default LoadingScreenAnimated;
