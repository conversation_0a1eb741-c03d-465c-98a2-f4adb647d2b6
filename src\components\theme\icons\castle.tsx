export default function CastleIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 496" {...props}>
      <path fill="#d3a06c" d="m248 64 64 72H184z" />
      <path
        fill="#cf9e76"
        d="M328 144v16a8 8 0 0 1-8 8H176a8 8 0 0 1-8-8v-16a8 8 0 0 1 8-8h144a8 8 0 0 1 8 8z"
      />
      <path
        fill="#dfb28b"
        d="M312 168v96h-32v-32c0-8.84-3.58-16.84-9.37-22.63S256.84 200 248 200a32 32 0 0 0-32 32v32h-32v-96z"
      />
      <path fill="#ef7385" d="m424 104 48 80h-96zM72 104l48 80H24z" />
      <path
        fill="#ccd1d9"
        d="M488 192v16a8 8 0 0 1-8 8H368a8 8 0 0 1-8-8v-16a8 8 0 0 1 8-8h112a8 8 0 0 1 8 8zM136 192v16a8 8 0 0 1-8 8H16a8 8 0 0 1-8-8v-16a8 8 0 0 1 8-8h112a8 8 0 0 1 8 8z"
      />
      <path
        fill="#e6e9ed"
        d="M472 216v168l-96-48V216zM120 264v72l-96 48V216h96z"
      />
      <path
        fill="#ef7385"
        d="M248 16v24h-40a8 8 0 0 1-8-8V16a8 8 0 0 1 8-8h32a8 8 0 0 1 8 8zM424 48v24h-40a8 8 0 0 1-8-8V48a8 8 0 0 1 8-8h32a8 8 0 0 1 8 8zM72 48v24H32a8 8 0 0 1-8-8V48a8 8 0 0 1 8-8h32a8 8 0 0 1 8 8z"
      />
      <path
        fill="#69d6f4"
        d="M440 256v32a8 8 0 0 1-8 8h-16a8 8 0 0 1-8-8v-32a8 8 0 0 1 8-8h16a8 8 0 0 1 8 8zM280 232v32h-8a8 8 0 0 0-8 8v16a8 8 0 0 1-8 8h-16a8 8 0 0 1-8-8v-16a8 8 0 0 0-8-8h-8v-32a32 32 0 0 1 64 0zM88 256v32a8 8 0 0 1-8 8H64a8 8 0 0 1-8-8v-32a8 8 0 0 1 8-8h16a8 8 0 0 1 8 8z"
      />
      <path
        fill="#ecc19c"
        d="M339.65 392A92.23 92.23 0 0 0 296 339.19a91.49 91.49 0 0 0-48-11.1 91.51 91.51 0 0 0-48 16.01v.01A92.07 92.07 0 0 0 164.35 392a91.6 91.6 0 0 0-4.35 28v68h-40V264h24a8 8 0 0 1 8 8v16a8 8 0 0 0 8 8h16a8 8 0 0 0 8-8v-24h40a8 8 0 0 1 8 8v16a8 8 0 0 0 8 8h16a8 8 0 0 0 8-8v-16a8 8 0 0 1 8-8h40v24a8 8 0 0 0 8 8h16a8 8 0 0 0 8-8v-16a8 8 0 0 1 8-8h24v224h-32v-68a91.6 91.6 0 0 0-4.35-28z"
      />
      <path
        fill="#cf9e76"
        d="M296 392h43.65a91.6 91.6 0 0 1 4.35 28v68H160v-68a91.6 91.6 0 0 1 4.35-28H248z"
      />
      <path
        fill="#d3a06c"
        d="M317.05 354.95A92.23 92.23 0 0 0 296 339.19a91.49 91.49 0 0 0-48-11.1 91.51 91.51 0 0 0-48 16.01v.01A92.07 92.07 0 0 0 164.35 392h175.3a91.94 91.94 0 0 0-22.6-37.05z"
      />
      <path
        fill="#e6e9ed"
        d="M472 423.8V488h-96v-64h20.69a27.29 27.29 0 0 0 19.31-8c5.12 5.12 12.07 8 19.31 8h1.38a27.29 27.29 0 0 0 19.31-8 27.3 27.3 0 0 0 16 7.79zM120 424v64H24V423.79A27.3 27.3 0 0 0 40 416a27.34 27.34 0 0 0 19.31 8h1.38c7.24 0 14.19-2.88 19.31-8a27.34 27.34 0 0 0 19.31 8z"
      />
      <path
        fill="#ef7385"
        d="M488 396.94V416a8 8 0 0 1-8 8h-4.69a27.3 27.3 0 0 1-19.31-8 27.34 27.34 0 0 1-19.31 8h-1.38a27.32 27.32 0 0 1-19.31-8 27.34 27.34 0 0 1-19.31 8H376v-88l96 48 11.58 5.79a7.98 7.98 0 0 1 4.42 7.15zM120 336v88H99.31A27.29 27.29 0 0 1 80 416a27.32 27.32 0 0 1-19.31 8h-1.38A27.29 27.29 0 0 1 40 416a27.3 27.3 0 0 1-19.31 8H16a8 8 0 0 1-8-8v-19.06c0-3.03 1.71-5.8 4.42-7.15L24 384z"
      />
      <path d="M80 240H64c-8.82 0-16 7.18-16 16v32c0 8.82 7.18 16 16 16h16c8.82 0 16-7.18 16-16v-32c0-8.82-7.18-16-16-16zm0 48H64v-32h16l.01 32H80z" />
      <path d="M496 208v-16c0-8.82-7.18-16-16-16h-3.47L432 101.78V48c0-8.82-7.18-16-16-16h-32c-8.82 0-16 7.18-16 16v16c0 8.82 7.18 16 16 16h32v21.78L371.47 176H368c-8.82 0-16 7.18-16 16v16c0 8.82 7.18 16 16 16v32h-16c-8.82 0-16 7.18-16 16v16h-16V176c8.82 0 16-7.18 16-16v-16c0-8.82-7.18-16-16-16h-4.4L256 60.96V16c0-8.82-7.18-16-16-16h-32c-8.82 0-16 7.18-16 16v16c0 8.82 7.18 16 16 16h32v12.96L180.4 128H176c-8.82 0-16 7.18-16 16v16c0 8.82 7.18 16 16 16v112h-16v-16c0-8.82-7.18-16-16-16h-16v-32c8.82 0 16-7.18 16-16v-16c0-8.82-7.18-16-16-16h-3.47L80 101.78V48c0-8.82-7.18-16-16-16H32c-8.82 0-16 7.18-16 16v16c0 8.82 7.18 16 16 16h32v21.78L19.47 176H16c-8.82 0-16 7.18-16 16v16c0 8.82 7.18 16 16 16v155.06l-7.15 3.57A15.91 15.91 0 0 0 0 396.94V416c0 8.82 7.18 16 16 16v64h464v-64c8.82 0 16-7.18 16-16v-19.06c0-6.1-3.39-11.58-8.84-14.3l-7.16-3.58V224c8.82 0 16-7.18 16-16zM384 64V48h32v16zm40 55.55L457.87 176h-67.74zM368 192h112v16H368zM208 32V16h32v16zm40 44.04L294.19 128H201.8zM176 144h144l.01 16H176zm16 32h112v80h-16v-24c0-22.06-17.94-40-40-40s-40 17.94-40 40v24h-16zm32 80v-24c0-13.23 10.77-24 24-24s24 10.77 24 24v24c-8.82 0-16 7.18-16 16v16h-16v-16c0-8.82-7.18-16-16-16zm-64 48h16c8.82 0 16-7.18 16-16v-16h32v16c0 8.82 7.18 16 16 16h16c8.82 0 16-7.18 16-16v-16h32v16c0 8.82 7.18 16 16 16h16c8.82 0 16-7.18 16-16v-16h16v208h-16v-60c0-55.14-44.86-100-100-100s-100 44.86-100 100v60h-24V272h16v16c0 8.82 7.18 16 16 16zm167.88 80H304v-29.9a84.5 84.5 0 0 1 23.88 29.9zM288 384h-32v-47.9a83.39 83.39 0 0 1 32 8.02zm-48 0h-32v-35.52a83.42 83.42 0 0 1 32-11.6zm-48 0h-15.88A84.36 84.36 0 0 1 192 361.3zm-24 36c0-6.9.85-13.59 2.42-20H192v16h16v-16h32v16h16v-16h32v16h16v-16h29.57a83.86 83.86 0 0 1 2.43 20v60H168zM32 64V48h32v16zm40 55.55L105.87 176H38.13zM16 192h112l.01 16H16zm16 32h80v107.06l-80 40zM16 396.94l96-48V416H99.31c-5.16 0-10-2-13.65-5.66L80 404.7l-5.66 5.65A19.18 19.18 0 0 1 60.7 416H59.3c-5.15 0-10-2-13.65-5.66L40 404.7l-5.66 5.65A19.18 19.18 0 0 1 20.7 416H16zm16 33.2c2.81-.96 5.5-2.24 8-3.88A35.07 35.07 0 0 0 59.31 432h1.38c6.96 0 13.62-2 19.3-5.74A35.08 35.08 0 0 0 99.32 432H112v48H32zM384 480v-48h12.69c6.96 0 13.62-2 19.31-5.74a35.08 35.08 0 0 0 19.31 5.74h1.38c6.96 0 13.62-2 19.3-5.74a34.92 34.92 0 0 0 8 3.87V480zm96-64h-4.69c-5.16 0-10-2-13.65-5.66L456 404.7l-5.66 5.65A19.18 19.18 0 0 1 436.7 416h-1.38c-5.15 0-10-2-13.65-5.66L416 404.7l-5.66 5.65A19.18 19.18 0 0 1 396.7 416H384v-67.06l96 48zm-16-44.94-80-40V224h80z" />
      <path d="M416 304h16c8.82 0 16-7.18 16-16v-32c0-8.82-7.18-16-16-16h-16c-8.82 0-16 7.18-16 16v32c0 8.82 7.18 16 16 16zm0-48h16v32h-16z" />
    </svg>
  );
}
