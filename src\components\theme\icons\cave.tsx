export default function CaveIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" {...props}>
      <path fill="#785353" d="M111.71 348.19 87.66 179.85l64.13 112.22z" />
      <path
        fill="#d19a6e"
        d="M466.3 332.94a24.05 24.05 0 0 0-9.08-14.2l-56.93-42.7-21.47-85.88a32.06 32.06 0 0 0-10.8-17.04l-67.24-55.01a16.03 16.03 0 0 0-22.8 2.56l-46.03 59.18-40.7 6.78c-4.69.78-8.78 3.6-11.19 7.69L103.7 324.14 67 336.37a24.05 24.05 0 0 0-15.2 15.2l-28.27 84.8h464.94z"
      />
      <path
        fill="#c38c66"
        d="m402 378.11.61-20.91a24.05 24.05 0 0 0-8.62-19.18l-36.04-30.1a40.09 40.09 0 0 0-27.1-9.3l-42.12 1.47 23.7 136.27h148.1l-49.32-38.6a24.05 24.05 0 0 1-9.22-19.65zM226.27 206.18a32.07 32.07 0 0 0 21.4-13.84l50.6-75.9a16.02 16.02 0 0 0-20.3 4.23l-46.02 59.18-40.7 6.78c-4.69.78-8.78 3.6-11.19 7.69L103.7 324.14 67 336.37a24.05 24.05 0 0 0-15.2 15.2l-28.26 84.8h112.22l27.36-82.1a24.05 24.05 0 0 0-5.8-24.6l-1.69-1.7a24.05 24.05 0 0 1-4.83-27.07l33.96-73.58a32.06 32.06 0 0 1 23.85-18.2z"
      />
      <path
        fill="#785353"
        d="M356.54 339.35A40.08 40.08 0 0 0 338.63 316l-55.28-33.17a24.05 24.05 0 0 0-23.12-.89l-26.5 13.26a40.1 40.1 0 0 0-15.74 14.07l-82.23 127.1h248.5z"
      />
      <path
        fill="#966a5b"
        d="m358.02 436.36-24.08-84.27c-1.53-5.36-5-9.89-9.79-12.76l-50.3-30.18a7.22 7.22 0 0 0-7-.27l-24.13 12.07a21.88 21.88 0 0 0-8.59 7.68l-69.71 107.73h193.6z"
      />
      <path
        fill="#b57f5f"
        d="M203.53 399.2a32.06 32.06 0 0 0-13.42-17.04l-46.22-28.9a8.02 8.02 0 0 0-11.7 3.83l-12.46 31.18-96.2 48.1h192.4z"
      />
      <path d="M504.5 428.86h-9.97l-20.89-97.5a31.55 31.55 0 0 0-11.92-18.62l-54.8-41.1-20.82-83.3a39.82 39.82 0 0 0-13.33-21.03l-67.24-55a23.34 23.34 0 0 0-17.53-5.18 23.35 23.35 0 0 0-15.94 8.94l-44.23 56.86-37.82 6.3a23.47 23.47 0 0 0-16.41 11.29l-36.87 62.67-39.74-79.47 48.44-41.53 20.02 6.68a7.5 7.5 0 0 0 4.75-14.23l-16.94-5.65 5.65-16.93a7.5 7.5 0 1 0-14.23-4.74l-7.36 22.07-42.16 36.14V117.8l22.98-38.3a7.5 7.5 0 0 0-12.86-7.72l-20 33.34-17.5-11.67a7.5 7.5 0 1 0-8.32 12.48l20.7 13.8v72.03l-18.66-9.33-13.72-34.3 6.51-13.02a7.5 7.5 0 1 0-13.42-6.71l-5.32 10.65-26.23-6.56a7.5 7.5 0 0 0-3.64 14.55l28.35 7.09 14.6 36.52a7.5 7.5 0 0 0 3.61 3.92l28.51 14.26 14.7 110.15-30.82 10.27a31.7 31.7 0 0 0-19.95 19.96l-26.55 79.65H7.5a7.5 7.5 0 0 0 0 15h497a7.5 7.5 0 0 0 0-15zM127.66 268.6l-19.4 33-13.06-97.93zm-68.75 85.35a16.63 16.63 0 0 1 10.47-10.47l36.69-12.23a7.5 7.5 0 0 0 4.09-3.3l76.36-129.83a8.51 8.51 0 0 1 5.96-4.09l40.7-6.78a7.5 7.5 0 0 0 4.7-2.8l46.02-59.17a8.46 8.46 0 0 1 5.78-3.24c2.3-.26 4.56.4 6.35 1.87l67.24 55.01c4.06 3.33 7 7.96 8.27 13.06l21.48 85.88a7.5 7.5 0 0 0 2.77 4.18l56.93 42.7c3.2 2.4 5.42 5.87 6.25 9.77l20.22 94.35h-89.27l-26.17-91.57a47.42 47.42 0 0 0-21.27-27.72l-55.27-33.17a31.4 31.4 0 0 0-30.34-1.16l-26.77 13.38a47.66 47.66 0 0 0-18.3 16.17l-26.17 39.24a7.5 7.5 0 1 0 12.48 8.32l26.16-39.24a32.64 32.64 0 0 1 12.54-11.07l26.77-13.39a16.47 16.47 0 0 1 15.91.62l55.28 33.16a32.47 32.47 0 0 1 14.56 18.99l24.99 87.44h-153l-10.67-32.02a39.65 39.65 0 0 0-16.56-21.04l-46.23-28.9a15.47 15.47 0 0 0-13.04-1.58 15.47 15.47 0 0 0-9.58 8.98l-11.42 28.54-77.45 38.72zm64.17 41.03a7.5 7.5 0 0 0 3.61-3.93l12.47-31.18a.48.48 0 0 1 .32-.3c.23-.07.38.02.44.06l46.22 28.89a24.62 24.62 0 0 1 10.28 13.06l9.1 27.29H55.3z" />
    </svg>
  );
}
