import ButtonReset from "@/components/container/button/button-reset";
import type { FormProps } from "@/interfaces/form/formType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import React from "react";
import { Button, Input } from "rizzui";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  //   setValue,
  getValues,
  //   watch,
  onDelete,
  handleReset,
}: FormProps & {
  handleReset: () => void;
}) {
  const { can } = useGrantedPolicies();
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-2 gap-2">
        <div>
          <Input
            size="sm"
            label="Name"
            disabled={isLoading}
            placeholder="Please fill in Name"
            {...register("name", { required: true })}
            error={errors.name ? "Name is required" : undefined}
            className="w-full"
          />
        </div>
        <div className="flex items-end justify-start gap-2">
          {(can("WismaApp.DiningOptions.Create") && can("WismaApp.DiningOptions.Edit")) &&
            <Button
              size="sm"
              type="submit"
              disabled={isLoading}
              className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${getValues("id") ? "bg-blue-500" : "bg-green-500"
                }`}
            >
              {getValues("id") ? "Update" : "Create"}
            </Button>}
          {(getValues("id") && can("WismaApp.DiningOptions.Delete")) && (
            <Button
              size="sm"
              disabled={isLoading}
              className={`rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400`}
              onClick={() => {
                onDelete(String(getValues("id")));
              }}
            >
              Delete
            </Button>
          )}
          <ButtonReset isLoading={isLoading} handleReset={handleReset} />
        </div>
      </div>
    </form>
  );
}
