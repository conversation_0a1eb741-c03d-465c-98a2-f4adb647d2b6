stages:
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  # Cache directories
  YARN_CACHE_FOLDER: .yarn-cache
  NPM_CACHE_FOLDER: .npm-cache

.build_template: &build_definition
  stage: build
  image: docker:20.10
  services:
    - docker:20.10-dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    # Disable BuildKit to avoid compatibility issues
    DOCKER_BUILDKIT: "0"
    # Use gzip compression for better compatibility
    DOCKER_DEFAULT_PLATFORM: "linux/amd64"
  # Cache Docker layers and node_modules between builds
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - ${YARN_CACHE_FOLDER}
      - ${NPM_CACHE_FOLDER}
      - node_modules/
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # Create cache directories
    - mkdir -p ${YARN_CACHE_FOLDER}
    - mkdir -p ${NPM_CACHE_FOLDER}
    # Pull the latest image for layer caching
    - docker pull $CI_REGISTRY_IMAGE/wismafrontend:latest-${ENVIRONMENT} || true
    # Force NODE_ENV to be production for the build
    - echo "Building with NODE_ENV=production"
    # Use standard build flags with layer caching
    - >
      docker build
      --cache-from $CI_REGISTRY_IMAGE/wismafrontend:latest-${ENVIRONMENT}
      --build-arg NODE_ENV="production"
      --build-arg NEXT_PUBLIC_API_URL="${NEXT_PUBLIC_API_URL}"
      --build-arg NEXT_PUBLIC_CLIENT_ID="${NEXT_PUBLIC_CLIENT_ID}"
      --build-arg NEXT_PUBLIC_APP_URL="${NEXT_PUBLIC_APP_URL}"
      --build-arg NEXT_PUBLIC_SCOPE="${NEXT_PUBLIC_SCOPE}"
      --build-arg NEXT_PUBLIC_CLIENT_SECRET="${NEXT_PUBLIC_CLIENT_SECRET}"
      --build-arg NEXT_PUBLIC_GOOGLE_ANALYTICS_TRACKING_ID="${NEXT_PUBLIC_GOOGLE_ANALYTICS_TRACKING_ID}"
      --build-arg NEXT_PUBLIC_UMAMI_SCRIPT_URL="${NEXT_PUBLIC_UMAMI_SCRIPT_URL}"
      --build-arg NEXT_PUBLIC_IMIP_AUTH_URL="${NEXT_PUBLIC_IMIP_AUTH_URL}"
      --build-arg NEXT_PUBLIC_UMAMI_WEBSITE_ID="${NEXT_PUBLIC_UMAMI_WEBSITE_ID}"
      --build-arg YARN_CACHE_FOLDER="/cache/${YARN_CACHE_FOLDER}"
      --build-arg NPM_CACHE_FOLDER="/cache/${NPM_CACHE_FOLDER}"
      --build-arg SESSION_PASSWORD="build-time-session-password"
      -t "$CI_REGISTRY_IMAGE/wismafrontend:${CI_COMMIT_SHA}-${ENVIRONMENT}"
      -t "$CI_REGISTRY_IMAGE/wismafrontend:latest-${ENVIRONMENT}"
      .
    # Push both the commit-specific and latest tags
    - docker push $CI_REGISTRY_IMAGE/wismafrontend:${CI_COMMIT_SHA}-${ENVIRONMENT}
    - docker push $CI_REGISTRY_IMAGE/wismafrontend:latest-${ENVIRONMENT}

build-dev:
  <<: *build_definition
  tags:
    - docker-builder
  variables:
    ENVIRONMENT: development
    NODE_ENV: development
    NEXT_PUBLIC_API_URL: https://api-wisma-dev.imip.co.id
    NEXT_PUBLIC_CLIENT_ID: WismaClientDev
    NEXT_PUBLIC_CLIENT_SECRET: RKlDFEab5ikBisnNAWhMp5JI7kLY2apQ
    NEXT_PUBLIC_APP_URL: https://wisma-dev.imip.co.id
    IMIP_AUTH_URL: https://api-identity-dev.imip.co.id
    NEXT_PUBLIC_IMIP_AUTH_URL: https://api-identity-dev.imip.co.id
    NEXT_PUBLIC_SCOPE: "openid profile email"
  only:
    - main-dev/25021254-aplikasi-wisma-imip

build-prod:
  <<: *build_definition
  tags:
    - docker-builder-prod
  variables:
    ENVIRONMENT: production
    NODE_ENV: production
    NEXT_PUBLIC_API_URL: https://api-wisma.imip.co.id
    NEXT_PUBLIC_CLIENT_ID: WismaClientProd
    NEXT_PUBLIC_CLIENT_SECRET: wfH61tZLqyr7sJilBbKVXxcLQuuouP3n
    NEXT_PUBLIC_APP_URL: https://wisma.imip.co.id
    IMIP_AUTH_URL: https://identity.imip.co.id
    NEXT_PUBLIC_IMIP_AUTH_URL: https://identity.imip.co.id
    NEXT_PUBLIC_SCOPE: "openid profile email"
  only:
    - master

deploy-dev:
  stage: deploy
  tags:
    - frontofficewismabackend
  variables:
    # These should be set in GitLab CI/CD variables
    REDIS_PASSWORD: ${REDIS_PASSWORD}
    SESSION_SECRET: ${SESSION_SECRET}
    # Ensure SESSION_PASSWORD is at least 32 characters long
    # If not set in CI/CD variables, generate a secure one
    SESSION_PASSWORD: ${SESSION_PASSWORD:-$(openssl rand -base64 32)}
  script:
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" kubernetes/dev/deployment.yaml
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHA-development|g" kubernetes/dev/deployment.yaml
    # Create namespace if it doesn't exist
    - kubectl create namespace wismafrontend-dev --dry-run=client -o yaml | kubectl apply -f -
    # Generate a secure SESSION_PASSWORD if not provided
    - |
      if [ -z "$SESSION_PASSWORD" ] || [ "$SESSION_PASSWORD" = "${SESSION_PASSWORD}" ]; then
        echo "Generating a secure SESSION_PASSWORD..."
        export SESSION_PASSWORD=$(openssl rand -base64 32)
        echo "Generated SESSION_PASSWORD of length: ${#SESSION_PASSWORD}"
      else
        echo "Using provided SESSION_PASSWORD of length: ${#SESSION_PASSWORD}"
      fi
    # Create the required application secrets
    - |
      kubectl create secret generic wismafrontend-dev-secrets \
        --namespace=wismafrontend-dev \
        --from-literal=redis-password="$REDIS_PASSWORD" \
        --from-literal=session-secret="$SESSION_SECRET" \
        --from-literal=session-password="$SESSION_PASSWORD" \
        --dry-run=client -o yaml | kubectl apply -f -
    # Create GitLab registry credentials
    - |
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=wismafrontend-dev \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$CI_REGISTRY_PASSWORD \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -
    # Apply Kubernetes configurations
    # Redis deployment removed - using external Redis at 10.87.1.32
    - kubectl apply -f kubernetes/dev/deployment.yaml
    # Add imagePullSecrets to the deployment
    - kubectl patch deployment wismafrontend -n wismafrontend-dev --patch '{"spec":{"template":{"spec":{"imagePullSecrets":[{"name":"gitlab-registry-credentials"}]}}}}' --type=merge || true
    # Wait for deployment to stabilize
    - kubectl rollout status deployment/wismafrontend -n wismafrontend-dev --timeout=300s || true
    # Verify pod placement on k8s-worker1 node
    - echo "Checking pod placement on k8s-worker1 node:"
    - kubectl get pods -n wismafrontend-dev -l app=wismafrontend -o wide
  environment:
    name: development
    url: https://wisma-dev.imip.co.id
  only:
    - main-dev/25021254-aplikasi-wisma-imip

deploy-prod:
  stage: deploy
  tags:
    - frontofficewismabackendprod
  variables:
    # These should be set in GitLab CI/CD variables
    REDIS_PASSWORD: ${REDIS_PASSWORD}
    SESSION_SECRET: ${SESSION_SECRET}
    # Ensure SESSION_PASSWORD is at least 32 characters long
    # If not set in CI/CD variables, generate a secure one
    SESSION_PASSWORD: ${SESSION_PASSWORD:-$(openssl rand -base64 32)}
  script:
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" kubernetes/prod/deployment.yaml
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHA-production|g" kubernetes/prod/deployment.yaml
    # Create namespace if it doesn't exist
    - kubectl create namespace wismafrontend-prod --dry-run=client -o yaml | kubectl apply -f -
    # Generate a secure SESSION_PASSWORD if not provided
    - |
      if [ -z "$SESSION_PASSWORD" ] || [ "$SESSION_PASSWORD" = "${SESSION_PASSWORD}" ]; then
        echo "Generating a secure SESSION_PASSWORD..."
        export SESSION_PASSWORD=$(openssl rand -base64 32)
        echo "Generated SESSION_PASSWORD of length: ${#SESSION_PASSWORD}"
      else
        echo "Using provided SESSION_PASSWORD of length: ${#SESSION_PASSWORD}"
      fi
    # Create the required application secrets
    - |
      kubectl create secret generic wismafrontend-prod-secrets \
        --namespace=wismafrontend-prod \
        --from-literal=redis-password="$REDIS_PASSWORD" \
        --from-literal=session-secret="$SESSION_SECRET" \
        --from-literal=session-password="$SESSION_PASSWORD" \
        --dry-run=client -o yaml | kubectl apply -f -
    # Create GitLab registry credentials
    - |
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=wismafrontend-prod \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$CI_REGISTRY_PASSWORD \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -
    # Apply Kubernetes configurations
    # Redis deployment removed - using external Redis at 10.87.1.32
    - kubectl apply -f kubernetes/prod/deployment.yaml
    # Add imagePullSecrets to the deployment
    - kubectl patch deployment wismafrontend -n wismafrontend-prod --patch '{"spec":{"template":{"spec":{"imagePullSecrets":[{"name":"gitlab-registry-credentials"}]}}}}' --type=merge || true
    # Wait for deployment to stabilize
    - kubectl rollout status deployment/wismafrontend -n wismafrontend-prod --timeout=300s || true
    # Verify pod placement on k8s-worker1 node
    - echo "Checking pod placement on k8s-worker1 node:"
    - kubectl get pods -n wismafrontend-prod -l app=wismafrontend -o wide
  environment:
    name: production
    url: https://wisma.imip.co.id
  only:
    - master
