export default function LunchIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#5B3374"
        d="M2.108 15.652a.308.308 0 0 1-.33-.132 9.735 9.735 0 0 1-1.559-5.3C.22 4.825 4.587.45 9.98.44a9.762 9.762 0 0 1 9.801 9.72 9.737 9.737 0 0 1-1.563 5.367.296.296 0 0 1-.316.127c-2.406-.567-5.082-.883-7.901-.883-2.816 0-5.489.315-7.892.88Z"
      />
      <path
        fill="#F4AA39"
        d="M18.224 11.125v4.391c-.245.379-.515.74-.808 1.08h-.026c-.649 0-1.175.527-1.175 1.176a9.747 9.747 0 0 1-3.95 1.964h-.771a.176.176 0 0 0-.176.176 9.862 9.862 0 0 1-4.352-.392 9.806 9.806 0 0 1-5.19-4.004v-4.39c0-1.288.208-2.567.615-3.788l1.228-3.684A1.996 1.996 0 0 1 4.73 0h10.538a1.996 1.996 0 0 1 1.112 3.654l1.228 3.684c.407 1.221.615 2.5.615 3.787Z"
      />
      <path
        fill="#D98800"
        d="M14.334 2.794h-7.49a.199.199 0 0 1-.193-.25A2.005 2.005 0 0 0 4.738 0a1.996 1.996 0 0 0-1.12 3.653L2.392 7.338a11.975 11.975 0 0 0-.615 3.787v4.391a9.806 9.806 0 0 0 5.19 4.004v-8.395c0-1.287-.207-2.566-.615-3.787L5.5 4.78a.599.599 0 0 1 .568-.788h8.284c.34 0 .615-.284.598-.628a.61.61 0 0 0-.615-.57Z"
      />
      <path
        fill="#C98001"
        d="M5.37 1.996a.798.798 0 1 1-1.598 0 .798.798 0 0 1 1.597 0Z"
      />
      <path
        fill="#F7E6B0"
        d="M13.854 12.389c0 .225-.017.439-.05.64h-.316c-.082 0-.123.1-.065.157l.28.28a3.055 3.055 0 0 1-.09.254h-.339c-.081 0-.123.099-.065.157l.217.216c-.186.31-.425.562-.702.748l-.459 4.895c-.31.074-.626.132-.947.176a2953.29 2953.29 0 0 0-.033-4.694v-6.28c0-.086.068-.156.153-.156h.001c1.334 0 2.415 1.829 2.415 3.607Zm4.112-3.524a.046.046 0 0 0-.08.03l-.113 3.276a.269.269 0 0 1-.537.001l-.097-2.458a1.668 1.668 0 0 0-.316-.913.046.046 0 0 0-.074 0 1.668 1.668 0 0 0-.316.913l-.097 2.458a.268.268 0 0 1-.537-.001l-.113-3.277a.046.046 0 0 0-.08-.029c-.588.65-.976 2.098-.976 3.105 0 1.241.59 2.088 1.415 2.398l.17 3.404a9.84 9.84 0 0 0 1.201-1.176l.11-2.228c.827-.31 1.416-1.157 1.416-2.398 0-1.007-.388-2.455-.976-3.105Z"
      />
      <path
        fill="#FFF5F5"
        d="M13.854 12.389c0 .225-.017.439-.05.64h-.094a.184.184 0 0 0-.13.315l.122.122a2.994 2.994 0 0 1-.09.253h-.116a.184.184 0 0 0-.13.315l.06.06c-.186.31-.425.56-.702.747l-.28 2.985c-.02.208-.328.194-.329-.015l-.015-2.593h-.007v-6.28l.005-.004c1.014.473 1.756 1.981 1.756 3.455Zm4.112-3.524a.046.046 0 0 0-.08.03l-.113 3.276a.269.269 0 0 1-.537.001l-.097-2.458a1.668 1.668 0 0 0-.316-.913.046.046 0 0 0-.074 0 1.669 1.669 0 0 0-.316.913l-.097 2.458a.268.268 0 0 1-.537-.001L15.687 8.9c-.37.6-.597 1.38-.597 2.15 0 1.588.965 2.53 2.156 2.53.628 0 1.194-.262 1.588-.743.07-.262.108-.552.108-.867 0-1.007-.388-2.455-.976-3.105Z"
      />
      <path
        fill="#F52C2C"
        d="M11.357 3.988v2.31c0 .05-.04.09-.09.09h-.01a.09.09 0 0 1-.063-.027l-.172-.172a.09.09 0 0 0-.127 0l-.173.172a.09.09 0 0 1-.127 0l-.172-.172a.09.09 0 0 0-.127 0l-.173.172a.09.09 0 0 1-.127 0l-.172-.172a.09.09 0 0 0-.127 0l-.172.172a.09.09 0 0 1-.064.026h-.01a.09.09 0 0 1-.09-.09V3.992a2.01 2.01 0 0 0 1.757-1.996A1.996 1.996 0 0 0 9.122 0h2.083c1.105 0 2.025.884 2.028 1.99a1.996 1.996 0 0 1-1.876 1.998Z"
      />
      <path
        fill="#AB2538"
        d="M13.068 2.794a2 2 0 0 1-1.71 1.198H9.36s1.211-.237 1.59-1.198h2.117Z"
      />
    </svg>
  );
}
