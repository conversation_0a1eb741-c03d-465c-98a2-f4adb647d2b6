import { type TForm } from "./custom-types";
import { type NextRequest } from "next/server";
import { getSession } from "@/lib/actions";

export async function authentication(request: Request | NextRequest) {
  const Authorization = request.headers.get("Authorization");
  const session = await getSession()
  if (Authorization !== "Bearer " + (session?.access_token)) {
    // return new Response(
    //   JSON.stringify({
    //     error: "error",
    //     response: { data: { message: "Unauthorized" } },
    //   }),
    //   {
    //     status: 401,
    //   },
    // );
    throw "Unauthorized";
  }
  return null; // Return null if authentication is successful
}
