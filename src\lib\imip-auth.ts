import {
  type OAuthConfig,
  // type OAuthUserConfig,
} from "@auth/core/providers/oauth";
import axios from "axios";
import { type OAuthProviderOptions } from "@/types";
import { type User } from "@/types/user";
import { env } from "@/env";

// const callbackUrl = `${process.env.NEXTAUTH_URL}/api/auth/callback`;

const ImipAuthProvider = (
  options: OAuthProviderOptions,
): OAuthConfig<User> => ({
  ...{
    id: "imip-auth",
    name: "IMIP Auth",
    type: "oauth",
    allowDangerousEmailAccountLinking: true,
    // callbackUrl,
    authorization: {
      url: env.IMIP_AUTH_URL + "/oauth/authorize",
      params: {
        // scope must be explicitly omitted in development
        // scope: undefined,
        scope: "",
        // next-auth passes by default, double params
        // client_id: options.clientId,
        // redirect_uri: callbackUrl,
        // response_type: 'code',
      },
    },
    token: env.IMIP_AUTH_URL + "/oauth/token",

    // userinfo: 'https://api.uber.com/v1.2/me',
    userinfo: {
      url: env.IMIP_AUTH_URL + "/api/user",
      async request(context: {
        tokens: {
          access_token: string;
          expires_at: string;
          refresh_token: string;
        };
      }): Promise<User> {
        try {
          const { access_token } = context.tokens;
          const options = {
            method: "GET",
            url: env.IMIP_AUTH_URL + "/api/user",
            headers: {
              Authorization: "Bearer " + access_token,
              "Accept-Language": "en_US",
              "Content-Type": "application/json",
            },
          };

          const response = await axios(options);
          return response.data as User; // Type assertion
        } catch (error) {
          console.error(error);
          throw error; // Rethrow or handle error appropriately
        }
      },
    },

    profile: (profile) => {
      // console.log("profile", profile);
      // console.log("profile.department", profile.department);

      return {
        id: profile.id,
        name: profile.name,
        email: profile.email,
        username: profile.username,
        image: profile.picture,
        department: profile.department,
      };
    },
  },
  ...options,
});

export default ImipAuthProvider;
