"use client"
import React, { useState } from "react";
import { AdvancedRadio, Button, Input, RadioGroup, Text } from "rizzui";
import { FieldValues, useForm } from "react-hook-form";
import { DateInput } from "@/components/theme/ui/input-type/dates-input";
import { PiCheckCircleFill } from "react-icons/pi";
import { useReportFnb, useReportHouseKeeping, useReportRoomDaily, useReportRoomMonthly, useReportRoomRental } from "@/lib/hooks/useReportRoom";
import { ReportRoomHotTable } from "../_component/reservation-group-hot-table";
import { formatDate } from "@/lib/helper/format-date";
import { Settings } from "handsontable/plugins/nestedHeaders";

export default function ReportFnb() {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const [reportType, setReportType] = useState("fnb");
  const reportTypeOptions = [
    {
      value: 'fnb',
      title: 'House Keeping',
      description: '',
    },
  ]

  const report = useReportFnb("", "");

  const columns = [
    // { title: "Room Number", data: "RoomNumber", type: "text", width: 100, },
    { title: "Signature Bill Number", data: "signature", type: "text", width: 160, },
    { title: "05.01", data: "05.01", type: "text", width: 100, },
    { title: "05.02", data: "05.02", type: "text", width: 100, },
    { title: "05.03", data: "05.03", type: "text", width: 100, },
    { title: "05.04", data: "05.04", type: "text", width: 100, },
    { title: "05.05", data: "05.05", type: "text", width: 100, },
    { title: "05.06", data: "05.06", type: "text", width: 100, },
    { title: "05.07", data: "05.07", type: "text", width: 100, },
    { title: "05.08", data: "05.08", type: "text", width: 100, },
    { title: "05.09", data: "05.09", type: "text", width: 100, },
    { title: "05.10", data: "05.10", type: "text", width: 100, },
    { title: "05.11", data: "05.11", type: "text", width: 100, },
    { title: "05.12", data: "05.12", type: "text", width: 100, },
    { title: "05.13", data: "05.13", type: "text", width: 100, },
    { title: "05.14", data: "05.14", type: "text", width: 100, },
    { title: "05.15", data: "05.15", type: "text", width: 100, },
    { title: "05.16", data: "05.16", type: "text", width: 100, },
    { title: "05.17", data: "05.17", type: "text", width: 100, },
    { title: "05.18", data: "05.18", type: "text", width: 100, },
    { title: "05.19", data: "05.19", type: "text", width: 100, },
    { title: "05.20", data: "05.20", type: "text", width: 100, },
    { title: "05.21", data: "05.21", type: "text", width: 100, },
    { title: "05.22", data: "05.22", type: "text", width: 100, },
    { title: "05.23", data: "05.23", type: "text", width: 100, },
    { title: "05.24", data: "05.24", type: "text", width: 100, },
    { title: "05.25", data: "05.25", type: "text", width: 100, },
    { title: "05.26", data: "05.26", type: "text", width: 100, },
    { title: "05.27", data: "05.27", type: "text", width: 100, },
    { title: "05.28", data: "05.28", type: "text", width: 100, },
    { title: "05.29", data: "05.29", type: "text", width: 100, },
    { title: "05.30", data: "05.30", type: "text", width: 100, },
    { title: "05.31", data: "05.31", type: "text", width: 100, },
  ]

  const reportTitle = `House Keeping Report at ${formatDate(String(watch('dateStart')), 'date')} to ${formatDate(String(watch('dateEnd')), 'date')}`;


  async function onSubmit(submitData: FieldValues) {
    try {
      console.log('submitData', submitData)
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  }
  return (<div className={"mb-2 @container"}>
    {/* <PageHeaderCustom
      // title={"Reservations"}
      breadcrumb={[
        { name: "Report" },
        { name: "Room" },
      ]}
    >
    </PageHeaderCustom> */}
    <div className="flex justify-center">
      {/* FORM FILTER */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="flex gap-4">
          <div className="">
            <Text as="p" className={"mb-1.5 font-medium"}>
              Select Report type
            </Text>
            <RadioGroup
              value={reportType}
              setValue={setReportType}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // console.log('e', e.target.value)
                setValue('reportType', e.target.value)
              }}
              className="grid grid-cols-1 sm:grid-cols-2 max-w-md mx-auto gap-4 mb-3"
            >
              {reportTypeOptions.map((item) => (
                <AdvancedRadio
                  key={item.value}
                  name="payment"
                  value={item.value}
                  inputClassName="[&:checked~span_.icon]:block"
                  readOnly={true}
                >
                  <span className="flex justify-between">
                    <Text as="b">{item.title}</Text>
                    <PiCheckCircleFill className="icon hidden h-5 w-5 text-secondary" />
                  </span>
                  <Text>{item.description}</Text>
                </AdvancedRadio>
              ))}
            </RadioGroup>
          </div>
          <div className="flex gap-4">
            <DateInput
              label={"Select Report Date"}
              name={"dateStart"}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
            // required={true}
            // size="sm"
            // inline={true}
            />
            <DateInput
              label={"Select Report Date"}
              name={"dateEnd"}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
            // className="w-full"
            // required={true}
            // size="sm"
            // inline={true}
            />
            <div className="pt-6">
              <Button
                // size="sm"
                type="submit"
                // disabled={isLoading}
                className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-green-500`}
              >
                Generate Report
              </Button>
            </div>
            <div className="pt-6">
              <Button
                // size="sm"
                type="button"
                // disabled={isLoading}
                className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-blue-500`}
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = '/reportFnbSample.xlsx';
                  link.download = 'reportFnbSample.xlsx'; // Optional: customize file name
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                Download Report
              </Button>
            </div>
          </div>
        </div>
        {/* {JSON.stringify(errors)} */}
      </form>
    </div>

    <div className="w-full">
      <div>
        {/* HOT TABLE */}
        <ReportRoomHotTable
          isLoading={false}
          title={reportTitle}
          data={report}
          columns={columns}
          withNumber={true}
        />
      </div>
    </div>
  </div>
  );
}
