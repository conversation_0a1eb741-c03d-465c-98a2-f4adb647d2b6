export interface SelectFilterProps {
  column: { dataIndex: string };
  dataSource: Record<string, unknown>[];
  tempSelectFilters: Record<string, string[]>;
  setTempSelectFilters: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >;
  handleSelectFilterChange: (dataIndex: string, values: string[]) => void;
  searchTerms: Record<string, string>;
  setSearchTerms: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  showDropdown: string | null;
  setShowDropdown: React.Dispatch<React.SetStateAction<string | null>>;
}
