import React, { useEffect } from "react";
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input";
import { type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { type ReservationFoodAndBeveragesDto, type ReservationRoomsDto, type ReservationsDto } from "@/client";
import { useApiMasterPaymentMethodOptions } from "@/lib/hooks/useApiMasterPaymentMethod";
import { useMasterTaxes } from "@/lib/hooks/useMasterTaxes";
import { Input } from "rizzui";
import { useApiMasterCompanyOptions } from "@/lib/hooks/useApiMasterCompany";
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";

export default function Payments({
  register,
  errors,
  setValue,
  getValues,
  watch,
  selectedFnbs,
  selectedServices,
  isDetail = false,
}: {
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<ReservationsDto>;
  selectedFnbs: ReservationFoodAndBeveragesDto[];
  selectedServices: ReservationRoomsDto[];
  isDetail?: boolean;
}) {
  const { data: paymentMethodOptions } = useApiMasterPaymentMethodOptions(0, 1000, "",);
  const { data: taxOptions } = useMasterTaxes(1, 1000)
  const { data: companyOptions } = useApiMasterCompanyOptions(0, 1000, "")
  // ROOM PRICE
  const paymentStatus = watch("reservationDetails.0.paymentStatus.code") as string;
  const roomPrice = paymentStatus == "pending" ? 0 : watch("reservationDetails.0.price") as number;
  // F&B PRICE
  // const fnbs = watch("reservationDetails.0.reservationFoodAndBeverages") as Record<string, string>[];
  const fnbsPrice = selectedFnbs?.reduce((total, item) => {
    const price = Number(item.totalPrice);
    return total + price;
  }, 0);
  // RESV ROOM PRICE
  // const services = watch("reservationDetails.0.reservationRooms") as Record<string, string>[];
  const servicesPrice = selectedServices?.reduce((total, item) => {
    const price = Number(item.totalPrice);
    return total + price;
  }, 0);
  // TAX
  const taxName = paymentMethodOptions?.find(opt => opt.value == watch('payments.paymentMethodId'))?.name as string // get payments payment method name;
  const tax = (taxName == 'Company Invoice') ? 0 : ((fnbsPrice + servicesPrice + roomPrice) * ((taxOptions?.items?.[0]?.rate ?? 0) / 100))
  // GRAND TOTAL PRICE
  // const totalPrice = fnbsPrice + servicesPrice + roomPrice + tax;
  const grantTotal = watch('payments.grantTotal') as number ?? 0;
  useEffect(() => {
    setValue('payments.paidAmount', grantTotal)
    setValue('payments.paidTax', tax)
    setValue('payments.taxId', tax ? taxOptions?.items?.[0]?.id : null)
    setValue('payments.taxRate', tax ? taxOptions?.items?.[0]?.rate : 0)
  }, [grantTotal, tax])

  // SET DEFAULT PAYMENT METHOD
  useEffect(() => {
    setValue('payments.paymentMethodId', getValues('paymentMethodId'))
  }, [watch('paymentMethodId')])

  return (
    <div className="px-4 py-2 border-2 rounded bg-gray-50">
      <div className="grid grid-cols-4 gap-2">
        {
          isDetail ?
            <Input
              label={"Payment Method"}
              {...register("payments.paymentMethodName")}
              readOnly={true}
              size="sm" /> :
            <DropdownInput
              label={"Payment Method"}
              name={"payments.paymentMethodId"}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
              options={paymentMethodOptions}
              required={true}
              size="sm"
            />
        }
        <AutocompleteSelect
          label={"Paid By Company"}
          name={"payments.paidCompanyId"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={companyOptions}
          // required={true}
          size="sm"
          optionsHeight="70px"
        // readOnly={true}
        />
        <CurrencyIDR
          // label={`Tax 11% ${tax ? '✔️' : ''}`}
          label={isDetail ? 'Paid Tax' : `${(tax && taxOptions?.totalCount) ? taxOptions.items?.[0]?.name : 'PPN 0%'}`}
          name={"payments.paidTax"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          // required={true}
          size="sm"
          disabled={true}
        // inputClassName="input-text-right"
        />
        <CurrencyIDR
          label={"Paid Amount"}
          name={"payments.paidAmount"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          required={true}
          size="sm"
          disabled={isDetail ?? taxName != 'Cash'}
        // inputClassName="input-text-right"
        />
      </div>
    </div>
  );
}
