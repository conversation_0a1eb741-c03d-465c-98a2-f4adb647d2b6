export default function XMLIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 56 56"
      fill="none"
      {...props}
    >
      <path
        fill="#00733B"
        fillRule="evenodd"
        d="M14.348 0h18.73l15.864 16.551v32.156A7.296 7.296 0 0 1 41.65 56H14.348a7.296 7.296 0 0 1-7.293-7.293V7.293A7.296 7.296 0 0 1 14.348 0Z"
        clipRule="evenodd"
      />
      <path
        fill="#fff"
        fillRule="evenodd"
        d="M33.055 0v16.41h15.888L33.055 0Z"
        clipRule="evenodd"
        opacity={0.302}
      />
      <path
        fill="#fff"
        fillRule="evenodd"
        d="M19.39 41.083h7.436V43.9H19.39v-2.817Zm9.756-14.397h7.459v2.794h-7.459v-2.794Zm-9.755 0h7.435v2.794H19.39v-2.794Zm9.755 4.736h7.459v2.817h-7.459v-2.817Zm-9.755 0h7.435v2.817H19.39v-2.817Zm9.755 4.925h7.459v2.818h-7.459v-2.818Zm-9.755 0h7.435v2.818H19.39v-2.818Zm9.755 4.736h7.459V43.9h-7.459v-2.817Z"
        clipRule="evenodd"
      />
    </svg>
  );
}
