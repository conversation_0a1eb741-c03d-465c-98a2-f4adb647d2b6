import { Text, Title } from "rizzui";
import cn from "@/utils/class-names";
import Breadcrumb from "@/components/theme/ui/breadcrumb";
import { PiCalendarBlank } from "react-icons/pi";

import { type CustomBreadcrumb } from "@/lib/custom-types";
import { formatDate } from "@/lib/helper/format-date";

export type PageHeaderTypes = {
  title: string;
  breadcrumb: CustomBreadcrumb[];
  className?: string;
  codeDocument?: string;
  createdAt?: Date;
};

export default function PageHeader({
  title,
  breadcrumb,
  className,
  codeDocument,
  createdAt,
}: React.PropsWithChildren<PageHeaderTypes>) {
  return (
    <header className={cn("mb-6 @container xs:-mt-2 lg:mb-7", className)}>
      <div className="flex flex-col @lg:flex-row @lg:items-center @lg:justify-between">
        <div>
          <Title
            as="h2"
            className="mb-2 mt-3 text-[22px] lg:text-2xl 4xl:text-[26px]"
          >
            {title}
          </Title>

          <Breadcrumb
            separator=""
            separatorVariant="circle"
            className="flex-wrap"
          >
            {breadcrumb?.map((item) => (
              <Breadcrumb.Item
                key={item.name}
                {...(item?.href && { href: item?.href })}
              >
                {item.name}
              </Breadcrumb.Item>
            ))}
          </Breadcrumb>
        </div>
        {/* DATE */}
        {codeDocument && (
          <div className="col-12 mt-3 grid justify-items-end">
            <Text as="b" className="flex font-semibold text-black">
              {codeDocument}
            </Text>
            <Text as="p" className="flex font-medium text-gray-900">
              {/* Dibuat pada 27 April 2024 */}
              Dibuat pada {formatDate(createdAt ?? new Date(), "date")}
              <PiCalendarBlank className="ml-2 h-6 w-5 text-gray-600" />
            </Text>
          </div>
        )}
      </div>
    </header>
  );
}
