"use client";

import Link from "next/link";
import HamburgerButton from "@/layouts/hamburger-button";
import Sidebar from "@/layouts/hydrogen/sidebar";
import HeaderMenuRight from "@/layouts/header-menu-right";
import StickyHeader from "@/layouts/sticky-header";
import { siteConfig } from "@/config/site.config";
import Image from "next/image";
import { ActionIcon, Text, Title } from "rizzui";
import { useEffect } from "react";
import { useSession } from "next-auth/react";
import swal from "sweetalert";

export default function Header({
  isSidebarOpen,
  toggleSidebar,
}: {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
}) {
  const { data: session, status } = useSession();
  // const router = useRouter();
  async function toLogin() {
    await swal(
      "Opps..",
      "Sesi anda telah berhakhir, silahkan login kembali.",
      "error",
    ).then(() => {
      // redirect("/login");
      location.reload();
    });
  }
  useEffect(() => {
    if (status === "unauthenticated") {
      // console.log("unauthenticated, redirect to login", session);
      void toLogin();
    } else {
    }
  }, [session, status]);

  return (
    <StickyHeader className="z-[990] 2xl:py-5 3xl:px-8  4xl:px-10">
      <div className="flex w-full max-w-2xl items-center">
        <ActionIcon
          aria-label="Open Sidebar Menu"
          variant="text"
          className={"me-3 hidden h-auto w-auto p-0 sm:me-4 xl:block"}
          onClick={() => toggleSidebar()}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={2}
            stroke="currentColor"
            className="h-6 w-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M3.75 6.75h16.5M3.75 12H12m-8.25 5.25h16.5"
            />
          </svg>
        </ActionIcon>
        <HamburgerButton
          view={<Sidebar className="static w-full 2xl:w-full" />}
        />
        <Link
          href={"/"}
          aria-label="Site Logo"
          className={`me-4 shrink-0 text-gray-800 hover:text-gray-900 lg:me-5 ${isSidebarOpen ? "xl:hidden" : "xl:block"}`}
        >
          {/* <Title>LOGO</Title> */}
          <Image
            src={siteConfig.icon}
            alt={"IMIP"}
            className="w-[65px] md:w-[100px]"
          />
        </Link>

        {/* <SearchWidget /> */}
        <div className="hidden xs:block">
          <Title
            as="h5"
            className="order-1 basis-1/2 text-slate-800 md:order-1 md:me-3 md:basis-auto"
          >
            WISMA IMIP
          </Title>
          <Text
            as="b"
            className="order-1 -mt-2 basis-1/2 text-slate-800 md:order-1 md:me-3 md:basis-auto"
          >
            {/* Every Moment Is Art */}
            {/* A Stay To Remember */}
            A Moment To Remember
          </Text>
        </div>

        {/* <p className="hidden xs:inline sm:hidden">XS = 250%-UP</p>
        <p className="hidden sm:inline md:hidden">SM = 200%-UP</p>
        <p className="hidden md:inline xl:hidden">MD = 175%-UP</p>
        <p className="hidden xl:inline 2xl:hidden">XL = 150%-UP</p>
        <p className="hidden 2xl:inline 3xl:hidden">2XL = 100%-UP</p>
        <p className="hidden 3xl:inline">3XL = 0-100%</p> */}
      </div>

      <HeaderMenuRight />
    </StickyHeader>
  );
}
