﻿import { getApiMasterStatus, getApiMasterStatusServiceByDocTypeByDocType } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import { type SelectOption } from "rizzui";

export const useMasterStatusByDocType = (
  docType: string,
) => {
  return useQuery({
    queryKey: [QueryNames.getApiMasterStatusServiceByDocTypeByDocType, docType],
    queryFn: async () => {
      const { data } = await getApiMasterStatusServiceByDocTypeByDocType({
        path: { docType },
      });

      return data;
    },
  });
};

export const useMasterStatusByDocTypeOptions = (
  docType: string,
) => {
  return useQuery({
    queryKey: [QueryNames.getApiMasterStatusServiceByDocTypeByDocType, docType],
    queryFn: async () => {
      const { data } = await getApiMasterStatusServiceByDocTypeByDocType({
        path: { docType },
      });

      // GENERATE OPTIONS
      const options: SelectOption[] = data?.map(val => ({
        label: val.name ?? "",
        value: val.id ?? "",
        id: val.id ?? 0,
        ...val
      })) ?? [];

      // RETURN OPTIONS
      return [
        // {label: "--Select--", value: 0, disabled: true},
        ...options
      ];
    },
  });
};
