import { type TForm } from "@/lib/custom-types";
import { useEffect, useState } from "react";
import {
  type Control,
  FieldValues,
  UseFieldArrayRemove,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  useFieldArray,
} from "react-hook-form";
import { PiTrash } from "react-icons/pi";
import { Button, Checkbox, Input, Select, Text, type SelectOption } from "rizzui";

export function MasterReportParameters({
  register,
  setValue,
  index,
  remove
}: {
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  index: number;
  remove: UseFieldArrayRemove;
}) {
  const name = `parameters.${index}`

  // useEffect(() => {
  //   // SET NOTES
  //   const notes = getValues(`${name}.notes`,);
  //   if (typeof notes != undefined) setNotes({ label: notes ? "True" : "False", value: notes });

  // }, [
  //   getValues(
  //     `parameters.create.${index}`,
  //   ),
  // ]);

  return (
    <div className="col-span-full mb-1 grid grid-cols-12 gap-2 text-xs">
      <Input
        label={index == 0 && "Label"}
        placeholder="Label"
        size="sm"
        className={"col-span-4"}
        {...register(`${name}.label`,)}
      />
      <Input
        label={index == 0 && "Name"}
        placeholder="Name"
        size="sm"
        className={"col-span-3"}
        {...register(`${name}.name`,)}
      />
      <Input
        label={index == 0 && "type"}
        placeholder="type"
        size="sm"
        className={"col-span-3"}
        {...register(`${name}.type`,)}
      />
      <div>
        {index == 0 &&
          <Text as="p" className={"mb-1.5 font-medium text-xs"}>
            Required
          </Text>
        }
        <Checkbox
          size="lg"
          {...register(`${name}.required`)}
        />
      </div>
      <div className="">
        <Button
          variant="text"
          size="md"
          className={`hover:text-red ${index == 0 ? "mt-3" : "-mt-1"}`}
          onClick={() => { remove(index); }}
        >
          <PiTrash className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
