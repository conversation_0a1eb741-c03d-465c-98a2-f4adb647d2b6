import {
  type CreateUpdateReservationRoomsDto,
  deleteApiAppReservationRoomsById,
  putApiAppReservationRoomsById,
  type ReservationDetailsDto,
  type ReservationRoomsDto
} from "@/client";
import ButtonDelete from "@/components/container/button/button-delete";
import ButtonMin from "@/components/container/button/buttonMin";
import ButtonPlus from "@/components/container/button/buttonPlus";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { swalError } from "@/lib/helper/swal-error";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { type UseFormUnregister, type FieldValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import { Checkbox, Text } from "rizzui";

export default function CheckoutServices({
  register,
  unregister,
  setValue,
  watch,
  readonly = false,
}: {
  register: UseFormRegister<FieldValues>;
  unregister: UseFormUnregister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  readonly?: boolean;
}) {
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const { data: paymentStatusOptions } = useMasterStatusByDocTypeOptions("paymentStatus");
  const resvDet = (watch("reservationDetails") ?? []) as ReservationDetailsDto[]
  // console.log("resvDet", resvDet);

  let totalPrice = 0;
  let serviceLength = 0;
  resvDet.map((row) => {
    const services = (row.reservationRooms ?? []) as (ReservationRoomsDto & { selected: boolean })[];
    const temp = services?.reduce((total, item) => {
      const price = item.selected ? Number(item.totalPrice) : 0;
      return total + price;
    }, 0);
    totalPrice += temp;
    serviceLength += services.length;
  })

  const columns = [
    { dataIndex: "serviceName", title: "Name" },
    { dataIndex: "serviceTypeName", title: "Type" },
    // { dataIndex: "reservationDetailsId", title: "Resv Id" },
    { dataIndex: "quantity", title: "QTY" },
    { dataIndex: "totalPrice", title: "Price" },
  ];

  // DEFINE MUTATION
  const updateApiAppReservationRoomsById = useMutation({
    mutationFn: async (data: CreateUpdateReservationRoomsDto) => {
      const { id } = data;
      return putApiAppReservationRoomsById({
        path: { id: id! },
        body: data,
      })
    },
    onSuccess: async (res) => {
      console.log('success', res)
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationRoomsByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const removeApiAppReservationRoomsById = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppReservationRoomsById({
        path: { id: id },
      })
    },
    onSuccess: async (res) => {
      console.log('success remove', res)
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationRoomsByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  // FUNCTIONS
  const updateServiceQty = async (i: number, j: number, add: number) => {
    const data = resvDet?.[i]?.reservationRooms?.[j] as CreateUpdateReservationRoomsDto & { selected?: boolean };
    const qty = Number(data?.quantity) + add;
    const pricePerItem = Number(data?.totalPrice) / Number(data?.quantity);
    const totalPrice = pricePerItem * qty;
    if (data) {
      data.quantity = qty;
      data.totalPrice = totalPrice;
      setValue(`reservationDetails.${i}.reservationRooms.${j}`, data)
      data.selected = true;
      await updateApiAppReservationRoomsById.mutateAsync(data);
    }
  }
  const removeService = async (i: number, j: number) => {
    const data = resvDet?.[i]?.reservationRooms?.[j] as CreateUpdateReservationRoomsDto;
    // const data = services[j];
    if (data?.id) {
      unregister(`reservationDetails.${i}.reservationRooms.${j}`)
      await removeApiAppReservationRoomsById.mutateAsync(data.id)
    }
  }
  const getPaymentStatus = (id?: string | null) => {
    const paymentStatus = paymentStatusOptions?.find(opt => opt.value == id)?.name as string;
    if(paymentStatus == "Paid") return "✅";
    // else return "Pending";
  }

  // EFFECTS
  useEffect(() => {
    setValue("payments.serviceTotalPrice", totalPrice);
  }, [totalPrice]);

  const hasSetDefaultSelected = useRef(false);
  useEffect(() => {
    if (resvDet?.length && !hasSetDefaultSelected.current) {
      resvDet.map((row, i) => {
        const services = row.reservationRooms ?? [];
        services.map((_, j) => {
          setValue(`reservationDetails.${i}.reservationRooms.${j}.selected`, true);
          hasSetDefaultSelected.current = true; // Jangan jalankan lagi
        });
      })
    }
  }, [resvDet]);

  return (
    <div className="p-4 py-3 border-2 rounded bg-gray-50 mt-3">
      <div className="grid grid-cols-1 gap-0">
        <Text as="p" className="mb-0 font-bold text-sm text-gray-800">
          Service Transactions
        </Text>
        <>
          {/* Header Table */}
          <table className="min-w-full border-collapse table-auto">
            <thead className="bg-slate-50">
              <tr>
                <th className="sticky top-0 bg-slate-50 px-2 py-1 text-center text-xs font-semibold tracking-wider text-gray-500 w-[6%] flex">
                  <div className="mr-2">
                    {!readonly &&
                      <Checkbox
                        size="sm"
                        inputClassName="h-4 w-4"
                        iconClassName="h-4 w-4"
                        onChange={(e) => {
                          resvDet.map((row, i) => {
                            const services = row.reservationRooms ?? [];
                            services.map((_, j) => {
                              setValue(`reservationDetails.${i}.reservationRooms.${j}.selected`, e.target.checked);
                            });
                          })
                        }}
                      />
                    }
                  </div>
                  No
                </th>
                {columns.map((column, key) => (
                  <th
                    key={key}
                    className="sticky top-0 bg-slate-50 px-1 py-1 text-left text-xs font-semibold tracking-wider text-gray-500 w-[22%]"
                  >
                    {column.title}
                  </th>
                ))}
                <th className="sticky top-0 w-5 bg-slate-50 px-2 py-1 text-left text-xs tracking-wider text-gray-500 w-[4%]">
                  {/* # */}
                </th>
              </tr>
            </thead>
          </table>

          {/* Scrollable Body Table */}
          <div className="h-[120px] max-h-[120px] overflow-y-auto">
            <table className="min-w-full border-collapse table-auto">
              <tbody className="divide-y divide-gray-200">
                {serviceLength === 0 && (
                  <tr>
                    <td className="p-6 text-center text-xs tracking-wider text-gray-500">
                      No Service transaction found.
                    </td>
                  </tr>
                )}

                {
                  resvDet.map((det, i) => {
                    const services = det.reservationRooms ?? [];
                    const list = services?.map((row, j) => {
                      const totalPrev = resvDet.slice(0, i).reduce((sum, r) => sum + (r.reservationRooms?.length ?? 0), 0);
                      const number = totalPrev + j + 1;
                      return (
                        <tr className="hover:bg-gray-50" key={j}>
                          <td className="px-2 text-center px-2 py-1 text-xs tracking-wider text-gray-500 w-[6%] flex">
                            <div className="mr-2">
                              {!readonly &&
                                <Checkbox
                                  size="sm"
                                  inputClassName="h-4 w-4"
                                  iconClassName="h-4 w-4"
                                  {...register(`reservationDetails.${i}.reservationRooms.${j}.selected`)}
                                />
                              }
                            </div>
                            {number}.
                          </td>
                          <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                            {row.serviceName} {getPaymentStatus(row.paymentStatusId)}
                          </td>
                          <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                            {row.serviceTypeName}
                          </td>
                          {/* <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                            {row.reservationDetailsId}
                          </td> */}
                          <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                            <div className="flex">
                              {(!readonly && can("WismaApp.ReservationRoom.Edit")) && det.status?.code == "checkin" &&
                                <ButtonMin
                                  type="button"
                                  onClick={async () => await updateServiceQty(i, j, -1)}
                                  disabled={(Number(row?.quantity) ?? 1) <= 1}
                                />
                              }
                              <div className="p-2 py-1">{row.quantity}</div>
                              {(!readonly && can("WismaApp.ReservationRoom.Edit")) && det.status?.code == "checkin" &&
                                <ButtonPlus
                                  type="button"
                                  onClick={async () => await updateServiceQty(i, j, 1)}
                                />
                              }
                            </div>
                          </td>
                          <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                            {formatCurrencyIDR(row?.totalPrice ?? 0)}
                          </td>
                          <td className="w-[4%]">
                            {(!readonly && can("WismaApp.ReservationRoom.Delete")) && det.status?.code == "checkin" &&
                              <ButtonDelete
                                onClick={async () => await removeService(i, j)}
                                title={"Are you sure?"}
                                text={
                                  "This action will delete the selected tansaction. Continue?"
                                }
                              />
                            }
                          </td>
                        </tr>
                      )
                    })
                    return list;
                  })
                }

              </tbody>
            </table>
          </div>

          {/* Footer Table */}
          <table className="min-w-full border-collapse table-auto">
            <tfoot className="">
              <tr>
                <th className="text-medium px-1 py-1 text-start tracking-wider text-gray-500" >
                  TOTAL
                </th>
                <th className="text-medium px-1 py-1 text-start tracking-wider text-gray-500 w-[29%]">
                  {formatCurrencyIDR(totalPrice)}
                </th>
              </tr>
            </tfoot>
          </table>
        </>

      </div>
    </div>
  );
}
