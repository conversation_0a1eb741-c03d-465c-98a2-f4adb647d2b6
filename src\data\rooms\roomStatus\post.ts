"use server";
import { getSession } from "@/lib/actions";
import axios from "axios";
import type {
  RoomStatus,
  RoomStatusPostResponse,
} from "@/interfaces/rooms/roomStatus";

export const postRoomStatus = async ({
  code,
  name,
  color,
}: {
  code: string;
  name: string;
  color: string;
}): Promise<RoomStatusPostResponse> => {
  try {
    const session = await getSession();

    const res = await axios<RoomStatus>({
      url: `${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room-statuses`,
      method: "POST",
      headers: {
        Authorization: "Bearer " + session?.access_token,
        "Accept-Language": "en_US",
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: {
        code: code,
        name: name,
        color: color,
      },
    });

    return {
      success: true,
      message: "Room status created successfully.",
      data: res.data,
    };
  } catch (error) {
    console.error("Failed to post room status:", error);

    return {
      success: false,
      message: "An unexpected error occurred.",
      data: null,
      error: {
        code: "ERROR_CODE",
        message: error instanceof Error ? error.message : "Unknown error",
        details: "An error occurred while posting the room status.",
      },
    };
  }
};
