"use server";

// import { getDataReservations } from "./getDatas";

// export const getDataReservation = (id: string) => {
//   const datas = getDataReservations;
//   const data = datas.find((e) => e.id == id);
//   return data
// }

import { type ReservationTypesResponse } from "@/interfaces/reservationTypes/reservationTypes";
import axios from "axios";

export const getDataReservationTypes = (id:string) => {
  return axios.get<ReservationTypesResponse>(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/reservation-types/${id}`)
    .then((response) => {
      // console.log('GET getDataReservationTypes', response.data);
      return response.data;
    })
    .catch((error) => {
      console.error("Error fetching reservations:", error);
      throw error;
    });
}