export default function TableEnhancedIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#EA7B45"
        d="M4.822 19.355H.684a.39.39 0 0 1-.391-.39V1.032a.39.39 0 0 1 .39-.39h4.139a.39.39 0 0 1 .39.39v17.933a.39.39 0 0 1-.39.39Z"
      />
      <path
        fill="#A6A6AA"
        d="M19.316 4.797H7.056a.39.39 0 0 1-.39-.39V1.031a.39.39 0 0 1 .39-.39h12.26a.39.39 0 0 1 .39.39v3.374a.39.39 0 0 1-.39.39Z"
      />
      <path
        fill="#C9EB74"
        d="M19.316 19.355H7.056a.39.39 0 0 1-.39-.39V15.59a.39.39 0 0 1 .39-.39h12.26a.39.39 0 0 1 .39.39v3.374a.39.39 0 0 1-.39.39Z"
      />
      <path
        fill="#91DAFA"
        d="M12.05 13.781H7.057a.39.39 0 0 1-.391-.39v-6.7a.39.39 0 0 1 .39-.39h4.994a.39.39 0 0 1 .39.39v6.7a.39.39 0 0 1-.39.39Z"
      />
      <path
        fill="#FFD549"
        d="M19.317 13.781h-4.994a.39.39 0 0 1-.39-.39v-6.7a.39.39 0 0 1 .39-.39h4.994a.39.39 0 0 1 .39.39v6.7a.39.39 0 0 1-.39.39Z"
      />
      <path
        fill="#E76833"
        d="M4.821.641H3.65a.39.39 0 0 1 .391.391v17.933a.39.39 0 0 1-.39.39H4.82a.39.39 0 0 0 .39-.39V1.032a.39.39 0 0 0-.39-.39Z"
      />
      <path
        fill="#848487"
        d="M19.316.642h-1.171a.39.39 0 0 1 .39.39v3.374a.39.39 0 0 1-.39.39h1.171a.39.39 0 0 0 .391-.39V1.032a.39.39 0 0 0-.39-.39Z"
      />
      <path
        fill="#B7E546"
        d="M19.316 15.2h-1.171a.39.39 0 0 1 .39.39v3.375a.39.39 0 0 1-.39.39h1.171a.39.39 0 0 0 .391-.39V15.59a.39.39 0 0 0-.39-.39Z"
      />
      <path
        fill="#6EC2FC"
        d="M12.05 6.301H10.88a.39.39 0 0 1 .39.39v6.7a.39.39 0 0 1-.39.39h1.172a.39.39 0 0 0 .39-.39v-6.7a.39.39 0 0 0-.39-.39Z"
      />
      <path
        fill="#FFD033"
        d="M19.316 6.301h-1.171a.39.39 0 0 1 .39.39v6.7a.39.39 0 0 1-.39.39h1.171a.39.39 0 0 0 .391-.39v-6.7a.39.39 0 0 0-.39-.39Z"
      />
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={0.6}
        d="M.293 8.153V1.032a.39.39 0 0 1 .39-.39h4.139a.39.39 0 0 1 .39.39v17.933a.39.39 0 0 1-.39.39H.684a.39.39 0 0 1-.391-.39v-9.64m12.85-4.528H7.058a.39.39 0 0 1-.391-.39V1.031a.39.39 0 0 1 .39-.39h12.26a.39.39 0 0 1 .39.39v3.374a.39.39 0 0 1-.39.39h-5m5 14.56H7.056a.39.39 0 0 1-.39-.391V15.59a.39.39 0 0 1 .39-.39h12.26a.39.39 0 0 1 .39.39v3.374a.39.39 0 0 1-.39.39ZM12.05 13.78H7.057a.39.39 0 0 1-.391-.39v-6.7a.39.39 0 0 1 .39-.39h4.994a.39.39 0 0 1 .39.39v6.7a.39.39 0 0 1-.39.39Zm7.267 0h-4.994a.39.39 0 0 1-.39-.39v-6.7a.39.39 0 0 1 .39-.39h4.994a.39.39 0 0 1 .39.39v6.7a.39.39 0 0 1-.39.39ZM1.556 2.227h2.347M8.38 7.895h2.347M8.38 9.325h2.347M8.38 10.757h2.347m4.92-2.863h2.347M8.38 2.047h9.613M8.38 16.613h9.613m-3.237 1.328h3.237M8.38 3.375h2.916m4.35 5.95h2.348m-2.348 1.433h2.348M1.556 3.766h2.347M1.556 5.309h1.197"
      />
    </svg>
  );
}
