"use client";
import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/tableV2";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { fetchStatusOptions } from "@/lib/hooks/config/masterStatus";
import {
  deleteApiAppGuestById,
  postApiAppGuest,
  putApiAppGuestById,
  type GuestDto,
  type CreateUpdateGuestDto,
  type FilterGroup,
  type SortInfo,
} from "@/client";
import { useMasterGuestList } from "@/lib/hooks/config/masterGuest";
import type { SelectOption } from "rizzu<PERSON>";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import { useApiMasterCompanyOptions } from "@/lib/hooks/useApiMasterCompany";
import ButtonViewImage from "@/components/container/button/button-viewImage";

export default function MasterGuest({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isDataGuest, setDataGuest] = useState<GuestDto[]>([]);
  const [isStatus, setStatus] = useState<SelectOption | undefined>();
  const [statusOptions, setStatusOptions] = useState<SelectOptionType[]>([]);
  // const [isCompany, setCompany] = useState<SelectOption | undefined>();
  const [isGender, setGender] = useState<SelectOption | undefined>();

  // PAGING
  const [sort, setSortConfig] = useState<SortInfo[] | undefined>();
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});
  const [selectFilters, setSelectFilters] = useState<Record<string, string[]>>(
    {},
  );
  const handleFilterChange = useCallback((filterGroup: FilterGroup) => {
    setFilterGroup(filterGroup);
  }, []);
  const [filterGroup, setFilterGroup] = useState<FilterGroup | undefined>(
    undefined,
  );
  const [pagination, setPagiaton] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // GET DATA
  const { isLoading, data } = useMasterGuestList(
    pagination.pageIndex,
    pagination.pageSize,
    filterGroup,
    "",
    sort,
  );

  console.log("Master Guest Data:", data);

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        status: item.status?.name ?? "",
      }));
      setDataGuest(mappedData as GuestDto[]);
    }
  }, [data]);

  const columns = [
    {
      dataIndex: "identityNumber",
      title: "Identity Number",
      filter: "text" as const,
    },
    { dataIndex: "fullname", title: "Name", filter: "text" as const },
    { dataIndex: "email", title: "Email", filter: "text" as const },
    {
      dataIndex: "dateOfBirthday",
      title: "Birth Date",
      filter: "text" as const,
    },
    { dataIndex: "gender", title: "Gender", filter: "text" as const },
    {
      dataIndex: "phoneNumber",
      title: "Phone Number",
      filter: "text" as const,
    },
    { dataIndex: "nationality", title: "Nationality", filter: "text" as const },
    { dataIndex: "city", title: "City", filter: "text" as const },
    { dataIndex: "district", title: "District", filter: "text" as const },
    { dataIndex: "companyName", title: "Company", filter: "text" as const },
    { dataIndex: "address", title: "Address", filter: "text" as const },
    { dataIndex: "status.name", title: "Status", filter: "select" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  useEffect(() => {
    const fetchStatus = async () => {
      const options = await fetchStatusOptions("Master Guest");
      setStatusOptions(options);
    };

    void fetchStatus();
  }, []);

  const { data: companyOptions } = useApiMasterCompanyOptions(0, 1000, "");

  const filterSelectTable = {
    companyName: (companyOptions ?? []).map((option) => option.label),
    "status.name": statusOptions.map((option) => option.label),
  };

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
    setStatus(undefined);
    setGender(undefined);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetGuest],
    });
  };

  const deleteMasterGuestMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppGuestById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Guest deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetGuest],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Guest will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete Guest with id:", id);
        await deleteMasterGuestMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the Guest. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const data = isDataGuest.find((e) => e.id === id);
    console.log("Selected Guest Data:", data?.companyName);

    if (data) {
      setIsEditMode(true);
      Object.entries(data).forEach(([key, value]) => {
        if (key === "status") {
          setStatus({
            label: data.status as string,
            value: String(value),
          });
        } else if (key === "gender") {
          setGender({
            label: data.gender ?? "",
            value: String(value),
          });
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createMasterGuestMutation = useMutation({
    mutationFn: async (data: CreateUpdateGuestDto) =>
      postApiAppGuest({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Guest created successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetGuest],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateMasterGuestMutation = useMutation({
    mutationFn: async (data: GuestDto) => {
      const { id, ...updateData } = data;
      return putApiAppGuestById({
        path: { id: id! },
        body: updateData as CreateUpdateGuestDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Guest updated successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetGuest],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      console.log('onSubmit')
      const data = JSON.parse(JSON.stringify(formData)) as CreateUpdateGuestDto;

      // Handle attachment
      const attachment = data.attachment?.match(/^data:(.*);base64,(.*)$/);
      if (attachment?.[1] && attachment?.[2]) {
        data.attachments = [
          {
            contentType: attachment[1],
            base64Content: attachment[2],
            fileName:
              data.identityNumber +
              "-guest-attachments-" +
              new Date().getTime() +
              ".png",
          },
        ];
      }
      data.attachment = "";

      // Format dateOfBirthday
      data.dateOfBirthday = data.dateOfBirthday
        ? new Date(data.dateOfBirthday).toISOString().split("T")[0]
        : "";

      if (isEditMode) {
        await updateMasterGuestMutation.mutateAsync(data as GuestDto);
      } else {
        // console.log("Creating Guest Data:", data);
        await createMasterGuestMutation.mutateAsync(
          data as unknown as CreateUpdateGuestDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  if (!can("WismaApp.Guest")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Config" },
            { name: "Master Guest" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              isStatus={isStatus}
              setStatus={(option) =>
                setStatus({
                  label: option.label,
                  value: String(option.value),
                })
              }
              statusOptions={statusOptions}
              // isCompany={isCompany}
              // setCompany={setCompany}
              // companyOptions={(companyOptions ?? []).map((option) => ({
              //   ...option,
              //   value: String(option.label),
              // }))}
              isGender={isGender}
              setGender={(option) =>
                setGender({
                  label: option.label,
                  value: String(option.value),
                })
              }
            />
          </div>
        )}
        <div className="rounded-lg bg-white p-4 shadow">
          <CustomTable
            columns={columns}
            dataSource={
              data?.items?.map((e) => ({
                ...e,
                action: (
                  <>
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                    {e.attachments?.length && (
                      <ButtonViewImage
                        label="Preview Attachment"
                        url={
                          e.attachments?.[0]?.streamUrl
                            ? (e.attachments[0].streamUrl.split("/").pop() ??
                              "")
                            : ""
                        }
                      />
                    )}
                  </>
                ),
              })) ?? []
            }
            rowKey="id"
            pageSize={pagination.pageSize}
            isLoading={isLoading}
            totalCount={data?.totalCount ?? 1}
            setPagiaton={setPagiaton}
            searchTerms={searchTerms}
            setSearchTerms={setSearchTerms}
            selectFilters={selectFilters}
            setSelectFilters={setSelectFilters}
            onFilterChange={handleFilterChange}
            filterSelectTable={filterSelectTable}
            onSortChange={setSortConfig}
            height={isFormVisible ? "45vh" : "70vh"}
          />
        </div>
      </div>
    </div>
  );
}
