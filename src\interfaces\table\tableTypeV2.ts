import type { SortInfo } from "@/client";
import { type Column } from "./tableType";

export interface CustomTableProps {
  columns: Column[];
  dataSource: Record<string, unknown>[];
  // dataSource: string[] | undefined;
  rowKey: string;
  pageSize: number;
  isLoading?: boolean;
  init?: () => Promise<void>;
  totalCount?: number;
  setPagiaton?: React.Dispatch<
    React.SetStateAction<{
      pageIndex: number;
      pageSize: number;
    }>
  >;
  searchTerms?: Record<string, string>;
  setSearchTerms?: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  selectFilters?: Record<string, string[]>;
  setSelectFilters?: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >;
  dateFilters?: Record<string, string>;
  setDateFilters?: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  filterSelectTable?: Record<string, string[]>;
  onSortChange?: React.Dispatch<React.SetStateAction<SortInfo[] | undefined>>;
  height?: string | number;
  setRefreshId?: React.Dispatch<React.SetStateAction<string>>;
  setCheked?: React.Dispatch<React.SetStateAction<Record<string, unknown>[]>>;
  disabledCheckLists?: (string | undefined)[];
  filterPosition?: "inside" | "top";
}
