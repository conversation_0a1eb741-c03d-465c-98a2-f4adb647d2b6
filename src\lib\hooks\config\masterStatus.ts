import { getApiMasterStatus } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "../QueryConstants";
import { getApiMasterStatusServiceByDocTypeByDocType } from "@/client";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";

export const useMasterStatus = (
  pageIndex?: number,
  pageSize?: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetStatus, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex && pageSize) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiMasterStatus({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};

export const fetchStatusOptions = async (
  docType: string,
): Promise<SelectOptionType[]> => {
  try {
    const response = await getApiMasterStatusServiceByDocTypeByDocType({
      path: { docType },
    });

    if (response?.data) {
      return response.data.map((item) => ({
        label: item.name ?? "",
        value: item.id ?? "",
      }));
    }

    return [];
  } catch (error) {
    console.error("Error fetching masterStatusData:", error);
    return [];
  }
};
