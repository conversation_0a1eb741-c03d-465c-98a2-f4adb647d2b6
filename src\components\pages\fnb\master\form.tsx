import React, { useEffect, useState } from "react";
import ButtonReset from "@/components/container/button/button-reset";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { useMasterFnbType } from "@/lib/hooks/fnb/masterFnbType";
import { Button, Input, Select, type SelectOption } from "rizzui";
import type { FormProps } from "@/interfaces/form/formType";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  setValue,
  getValues,
  watch,
  onDelete,
  handleReset,
  fnbType,
  setFnbType,
}: FormProps & {
  handleReset: () => void;
  fnbType: SelectOption | undefined;
  setFnbType: (value: SelectOption) => void;
}) {
  const { can } = useGrantedPolicies();
  const [isFnbTypeOpt, setFnbTypeOpt] = useState<SelectOptionType[]>(
    [],
  );
  const { data: masterTypeData } = useMasterFnbType();

  useEffect(() => {
    if (masterTypeData?.items) {
      const mappedTypeOptions = masterTypeData.items.map((item) => ({
        label: item.name ?? "",
        value: item.id ?? "",
      }));
      setFnbTypeOpt(mappedTypeOptions);
    }
  }, [masterTypeData]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-4 gap-2">
        <div>
          <Input
            size="sm"
            label="Name"
            disabled={isLoading}
            placeholder="Please fill in Name"
            {...register("name", { required: true })}
            error={errors.name ? "Name is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="F&B Type"
            options={isFnbTypeOpt}
            value={fnbType}
            onChange={(e: { label: string; value: string }) => {
              setFnbType(e);
              setValue("typeFoodAndBeverageId", e.value);
            }}
            disabled={isLoading}
            placeholder="Please select F&B Type"
            error={
              errors.typeFoodAndBeverageId
                ? "F&B Type is required"
                : undefined
            }
            className="w-full"
          />
        </div>
        <div>
          <CurrencyIDR
            size="sm"
            label="Price"
            name="price"
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Information"
            disabled={isLoading}
            placeholder="Please fill in Information"
            {...register("information", { required: true })}
            error={errors.information ? "Information is required" : undefined}
            className="w-full"
          />
        </div>
      </div>

      <div className="flex justify-end gap-2">
        {(can("WismaApp.ReservationFoodAndBeverages.Create") && can("WismaApp.ReservationFoodAndBeverages.Edit")) &&
          <Button
            size="sm"
            type="submit"
            disabled={isLoading}
            className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${getValues("id") ? "bg-blue-500" : "bg-green-500"
              }`}
          >
            {getValues("id") ? "Update" : "Create"}
          </Button>}
        {(getValues("id") && can("WismaApp.ReservationFoodAndBeverages.Delete")) && (
          <Button
            size="sm"
            disabled={isLoading}
            className={`rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400`}
            onClick={() => {
              onDelete(String(getValues("id")));
            }}
          >
            Delete
          </Button>
        )}
        <ButtonReset isLoading={isLoading} handleReset={handleReset} />
      </div>
    </form>
  );
}
