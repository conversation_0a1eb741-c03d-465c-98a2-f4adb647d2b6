import type { FreeTextFilterProps } from "@/interfaces/table/freeTextFilterType";
import React from "react";
import { PiMagnifyingGlass } from "react-icons/pi";

export default function FreeTextFilter({
  column,
  searchTerms,
  handleTextFilterChange,
}: FreeTextFilterProps) {
  return (
    <div className="relative">
      <span className="absolute inset-y-0 left-0 flex items-center pl-2">
        <PiMagnifyingGlass className="text-gray-400" />
      </span>
      <input
        type="text"
        placeholder="Search..."
        value={searchTerms[column.dataIndex] ?? ""}
        onChange={(e) =>
          handleTextFilterChange(column.dataIndex, e.target.value)
        }
        className="w-full rounded-md border border-gray-300 pl-8 py-1 text-xs font-normal"
      />
    </div>
  );
}
