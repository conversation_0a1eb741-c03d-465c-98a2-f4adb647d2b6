﻿import type { FilterGroup, RoomDto, SortInfo } from "@/client";
import { postApiAppRoomsList } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import type { SelectOption } from "rizzui";

export const useMasterRooms = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [QueryNames.GetRooms, pageIndex, pageSize, filter, sorting, sort],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppRoomsList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
        },
      });

      return data;
    },
  });
};

export const useMasterRoomOptions = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [QueryNames.GetRooms, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppRoomsList({
        body: {
          sorting: sorting,
          page: 1,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      // GENERATE OPTIONS
      const options: SelectOption[] & RoomDto[] =
        data?.items?.map((val) => ({
          // label: `${val.roomNumber} - ${val.roomStatus?.name}`,
          // label: `${val.roomNumber} - ${val.roomType?.name}`,
          label: `${val.roomNumber}`,
          labelType: `${val.roomNumber} - ${val.roomCode}`,
          value: val.id ?? "",
          ...val,
        })) ?? [];

      // RETURN OPTIONS
      return options;
    },
  });
};
