import { type InputProps } from "@/interfaces/form/inputType";
import React, { useEffect, useState } from "react";
import Select from "react-select";
import { type SelectOption, Text } from "rizzui";
import { get } from "lodash";

export const AutocompleteSelectV2 = (
  props: InputProps & {
    value?: SelectOption; // << Tambahkan ini
  },
) => {
  const [val, setVal] = useState<SelectOption | undefined>();

  // Daftarkan ke react-hook-form
  props.register(props.name, {
    value: val,
    required: props.required,
  });

  // Update saat props.value berubah (mode edit)
  useEffect(() => {
    if (props.value) {
      setVal(props.value);
    } else {
      const defaultVal = props.options?.find(
        (e) => e.value == props.getValues(props.name),
      );
      setVal(defaultVal);
    }
  }, [props.value, props.options]);

  return (
    <div className={props.className ?? ""}>
      {props.label && (
        <>
          {props.size === "sm" ? (
            <Text as="p" className="mb-[0.275rem] text-xs text-gray-900">
              {props.label}
            </Text>
          ) : (
            <Text as="p" className="mb-[0.375rem] font-medium text-gray-900">
              {props.label}
            </Text>
          )}
        </>
      )}
      <div className={"" + props.readOnly && "cursor-not-allowed"}>
        <Select
          options={props.options}
          value={val} // << Ini akan otomatis muncul di mode edit
          isDisabled={props.readOnly}
          isLoading={props.options?.length ? false : true}
          onChange={(newValue) => {
            if (newValue) {
              setVal(newValue);
              props.setValue(props.name, newValue.value);
            }
          }}
          classNamePrefix="rizzui-select"
          className={
            "z-51 border-red" +
            (get(props.errors, props.name) &&
              !val &&
              "[&>div>button]:border-2 [&>div>button]:border-red")
          }
          theme={(theme) => ({
            ...theme,
            borderRadius: 0,
            colors: {
              ...theme.colors,
              primary25: "#f0f0f0",
              primary: "#c5c5c5",
            },
          })}
          styles={{
            control: (base) => ({
              ...base,
              borderRadius: "0.375rem",
              boxShadow: "none",
              ...(get(props.errors, props.name) &&
                !val && {
                  border: "2px solid red",
                }),
              ...(props.size == "sm" && {
                fontSize: "0.75rem",
                minHeight: "2rem",
                lineHeight: "1rem",
                height: "2rem",
              }),
            }),
            // ... style lainnya tetap sama
          }}
        />
        {get(props.errors, props.name) && !val && (
          <Text as="small" className="text-red">
            {props.label + " is required"}
          </Text>
        )}
      </div>
    </div>
  );
};
