import React, { useEffect, useRef, useState } from "react";
import SkeletonListRoom from "@/components/container/loading/skeleton-listRoom";
import ContextMenu from "./context-menu";
import ModalUpdateRoomStatus from "./modal/room-status";
import { contrastingColor } from "@/lib/helper/format-color";
import { generateRoomRanges } from "@/lib/helper/generate-room-ranges";
import { useMasterRooms } from "@/lib/hooks/useMasterRoom";
import type { RoomDto } from "@/client";
import type { SelectOption } from "rizzui";

interface Room {
  roomNumber: string;
}

export default function ListRoom({
  selectedStatus,
  selectedType,
}: {
  selectedStatus: string | null;
  selectedType: string | null;
}) {
  const [showTooltip, setShowTooltip] = useState(false);
  const hoverTimeout = useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    hoverTimeout.current = setTimeout(() => {
      setShowTooltip(true);
    }, 2000); // Delay 2 detik
  };

  const handleMouseLeave = () => {
    if (hoverTimeout.current) {
      clearTimeout(hoverTimeout.current);
    }
    setShowTooltip(false); // langsung hilang
  };
  const [isModalOpen, setModalOpen] = useState(false);
  const [isContextMenuOpen, setContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<{
    x: number;
    y: number;
    roomId: string | null;
    roomNumber?: string | null;
    roomCode?: string | null;
    size?: string | null;
    information?: string | null;
    roomTypeId?: string | null;
    roomStatusId?: string | null;
    price?: number | null;
    roomType?: string | null;
    roomStatus?: SelectOption | null;
  }>({
    x: 0,
    y: 0,
    roomId: null,
  });

  const [dataRooms, setDataRooms] = useState<RoomDto[]>([]);
  const [pagination] = useState({
    pageIndex: 1,
    pageSize: 1000,
  });

  const { isLoading, data } = useMasterRooms(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const sortedData = [...data.items].sort((a, b) => {
        const roomNumberA = Number(a.roomNumber ?? 0);
        const roomNumberB = Number(b.roomNumber ?? 0);
        return roomNumberA - roomNumberB;
      });

      const mappedData = sortedData.map((item) => ({
        ...item,
        id: item.id ?? "",
        roomNumber: item.roomNumber ?? "",
        roomCode: item.roomCode ?? "",
        roomTypeName: item.roomType?.name ?? "",
        size: item.size ?? "",
        price: item.price ?? 0,
        roomStatusName: item.roomStatus?.name ?? "",
        information: item.information ?? "",
      }));

      setDataRooms(mappedData);
    }
  }, [data]);

  const roomRanges = isLoading
    ? Array.from({ length: 3 }, (_, index) => ({
        title: `Loading ${index + 1}`,
        range: [0, 0],
      }))
    : generateRoomRanges(
        (dataRooms ?? []).map((item) => ({
          roomNumber: item.roomNumber ?? "",
        })) as Room[],
      );

  const CONTEXT_MENU_WIDTH = 200;
  const CONTEXT_MENU_HEIGHT = 200;

  const handleContextMenu = (
    e: React.MouseEvent,
    roomId: string,
    roomNumber: string,
    roomType: string,
    roomStatus: SelectOption,
    roomCode?: string,
    size?: string,
    information?: string,
    roomTypeId?: string,
    roomStatusId?: string,
    price?: number,
  ) => {
    e.preventDefault();
    const { clientX, clientY } = e;
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    // Horizontal (kanan/kiri)
    const x =
      clientX + CONTEXT_MENU_WIDTH > windowWidth
        ? clientX - CONTEXT_MENU_WIDTH
        : clientX - 30;

    // Vertical (bawah/atas)
    const y =
      clientY + CONTEXT_MENU_HEIGHT > windowHeight
        ? clientY - CONTEXT_MENU_HEIGHT
        : clientY - 110;

    setContextMenuOpen(true);
    setContextMenuPosition({
      x,
      y,
      roomId,
      roomNumber,
      roomType,
      roomStatus,
      roomCode,
      size,
      information,
      roomTypeId,
      roomStatusId,
      price,
    });
  };

  const handleUpdateRoomStatus = () => {
    setModalOpen(true);
    setContextMenuOpen(false);
  };

  if (isLoading) {
    return <SkeletonListRoom roomRanges={roomRanges.length} />;
  }

  return (
    <div className="flex flex-col sm:gap-2 md:gap-2 xl:gap-3">
      {roomRanges.map(({ title, range }) => {
        const filteredRooms = dataRooms.filter(
          (item) =>
            Number(item?.roomNumber ?? 0) >= (range[0] ?? 0) &&
            Number(item.roomNumber ?? 0) <= (range[1] ?? 0) &&
            (selectedStatus === null ||
              item.roomStatus?.name === selectedStatus) &&
            (selectedType === null || item.roomType?.alias === selectedType),
        );

        return (
          <div key={title} className="mb-0 2xl:mb-2">
            <div className="m- flex flex-wrap gap-1 2xl:gap-x-4 2xl:gap-y-2">
              {filteredRooms.map((item) => {
                const borderColor = contrastingColor(
                  item.roomStatus?.color ?? "#000000",
                  -50,
                );

                return (
                  <div
                    key={item.id}
                    className="group relative flex w-[30px] items-center justify-between rounded-lg border px-1 shadow sm:w-[40px] md:w-[50px] xl:w-[60px] 2xl:w-[70px] 2xl:p-2 hd:w-[80px] 3xl:w-[90px]"
                    style={{
                      backgroundColor: item.roomStatus?.color ?? "#000000",
                      color: borderColor,
                      borderColor: borderColor,
                    }}
                    onContextMenu={(e) =>
                      handleContextMenu(
                        e,
                        item.id ?? "",
                        item.roomNumber ?? "",
                        item.roomType?.name ?? "",
                        {
                          label: item.roomStatus?.name ?? "",
                          value: item.roomStatus?.id ?? "",
                        },
                        item.roomCode ?? "",
                        item.size ?? "",
                        item.information ?? "",
                        item.roomType?.id ?? "",
                        item.roomStatus?.id ?? "",
                        item.price ?? 0,
                      )
                    }
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                  >
                    {/* TOOLTIP */}
                    {showTooltip && (
                      <div className="absolute left-1/2 top-0 z-50 hidden -translate-x-1/2 -translate-y-full whitespace-nowrap rounded bg-black px-2 py-1 text-[10px] text-white shadow-lg group-hover:block">
                        <div>Room Number : {item.roomNumber}</div>
                        <div>Room Type : {item.roomType?.name ?? "-"}</div>
                        <div>Room Status : {item.roomStatus?.name ?? "-"}</div>
                      </div>
                    )}

                    <div className="flex w-full flex-col gap-0">
                      <div className="flex items-center justify-between gap-0">
                        <span className="text-[5px] font-light sm:text-[6px] xl:text-[9px] 2xl:text-sm">
                          {item.roomNumber}
                        </span>
                        <span className="text-[5px] font-light sm:text-[6px] xl:text-[9px] 2xl:text-sm">
                          {item.roomStatus?.code}
                        </span>
                      </div>
                      <span
                        className="text-[5px] font-light text-gray-500 sm:text-[6px] xl:text-[9px] 2xl:text-sm"
                        style={{ color: borderColor }}
                      >
                        {item.roomType?.alias}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}
      {isContextMenuOpen && (
        <ContextMenu
          position={{ x: contextMenuPosition.x, y: contextMenuPosition.y }}
          roomId={contextMenuPosition.roomId!}
          roomStatus={contextMenuPosition.roomStatus ?? undefined}
          setContextMenuOpen={setContextMenuOpen}
          onUpdateRoomStatus={handleUpdateRoomStatus}
        />
      )}
      {isModalOpen && (
        <ModalUpdateRoomStatus
          roomId={contextMenuPosition.roomId!}
          roomNumber={contextMenuPosition.roomNumber}
          roomCode={contextMenuPosition.roomCode}
          roomType={contextMenuPosition.roomType}
          roomStatus={contextMenuPosition.roomStatus ?? undefined}
          roomSize={contextMenuPosition.size}
          roomInformation={contextMenuPosition.information}
          roomTypeId={contextMenuPosition.roomTypeId}
          roomStatusId={contextMenuPosition.roomStatusId}
          roomPrice={contextMenuPosition.price}
          isModalOpen={isModalOpen}
          setModalOpen={() => setModalOpen(false)}
        />
      )}
    </div>
  );
}
