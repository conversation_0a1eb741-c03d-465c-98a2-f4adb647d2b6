'use client';

import { useState, useMemo } from 'react';
import { filterData } from '@/utils/filter-data';

interface Data {
  id: number;
  name: string;
  // add other expected properties
}


type ColumnData = {
  title: string | JSX.Element; // Allow title to be string or JSX.Element
  dataIndex: string;
  key: string;
  width: number;
  render: (data: Data) => JSX.Element;
  onHeaderCell?: () => void;
};

export function useColumn<T extends ColumnData>(columnsData: T[]) {
  const [checkedColumns, setCheckedColumns] = useState(
    columnsData.map((column) => column.dataIndex)
  );

  const visibleColumns = useMemo(
    () => filterData(columnsData, checkedColumns),
    [columnsData, checkedColumns]
  );

  return { visibleColumns, checkedColumns, setCheckedColumns };
}
