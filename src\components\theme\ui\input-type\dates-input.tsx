import { DatePicker } from "@/components/theme/ui/datepicker";
import { useEffect, useState } from "react";
import cn from "@/utils/class-names";
import {
  type FieldErrors,
  type FieldValues,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  type UseFormWatch,
} from "react-hook-form";
import ReactDatePicker from "react-datepicker";
import { Text } from "rizzui";
import { get } from "lodash";

function formatDate(date: Date | null, type: string) {
  if (!date) return null;
  // // console.log('type', date);
  const yyyy = date.getFullYear();
  const mm = (date.getMonth() + 1).toString().padStart(2, "0");
  const dd = date.getDate().toString().padStart(2, "0");
  const hh = date.getHours().toString().padStart(2, "0");
  const mi = date.getMinutes().toString().padStart(2, "0");
  const ss = date.getSeconds().toString().padStart(2, "0");

  return type == "datetime"
    ? `${yyyy}-${mm}-${dd}T${hh}:${mi}:${ss}.000Z`
    : `${yyyy}-${mm}-${dd}T00:00:00.000Z`;
}

// Convert local date to UTC (no offset)
function toUTCDate(localDate: Date): Date {
  return new Date(
    Date.UTC(
      localDate.getFullYear(),
      localDate.getMonth(),
      localDate.getDate(),
      localDate.getHours(),
      localDate.getMinutes(),
    ),
  );
}

// Convert UTC date to local for display in browser (simulating UTC 0)
function fromUTCDate(utcDate: Date | null): Date {
  if (!utcDate) return new Date();
  if (!(utcDate instanceof Date) || isNaN(utcDate.getTime())) {
    return new Date(); // fallback
  }
  return new Date(
    utcDate.getUTCFullYear(),
    utcDate.getUTCMonth(),
    utcDate.getUTCDate(),
    utcDate.getHours(),
    utcDate.getMinutes(),
  );
}

interface DateInputProps {
  label?: string;
  name: string;
  className?: string;
  names?: string[];
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  errors: FieldErrors<FieldValues>;
  watch?: UseFormWatch<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  required?: boolean;
  readOnly?: boolean;
  inline?: boolean;
  minDate?: Date | undefined;
  maxDate?: Date | undefined;
  size?: "sm" | "md" | "lg" | "xl" | undefined;
  dateClassName?: string;
  popperPlacement?:
    | "bottom-start"
    | "top"
    | "right"
    | "bottom"
    | "left"
    | "top-start"
    | "top-end"
    | "right-start"
    | "right-end"
    | "bottom-end"
    | "left-start"
    | "left-end";
  onChange?: (date: Date | null, name: string) => void;
}

export function DateInput(props: DateInputProps) {
  const [date, setDate] = useState<Date | null>(null);

  props.register(props.name, {
    value: date,
    required: props.required,
  });
  const onChange = (e: Date | null) => {
    setDate(e);
    props.setValue(props.name, e ? formatDate(e, "date") : null);
    if (props.onChange) {
      props.onChange(e, props.name);
    }
  };
  useEffect(() => {
    if (props.getValues(props.name) && !date) {
      const defaultDate = new Date(props.getValues(props.name) as string);
      setDate(defaultDate);
    }
  }, [props]);

  return (
    <div className={props.className ?? ""}>
      {props.inline && (
        <Text as="p" className={"mb-1.5 font-medium " + ("text-" + props.size)}>
          {props.label}
        </Text>
      )}
      <div className={"" + props.readOnly && "cursor-not-allowed"}>
        <DatePicker
          popperPlacement={props.popperPlacement ?? "bottom-start"}
          selected={date}
          onChange={onChange}
          selectsStart
          // selectsRange={true}
          // minDate={new Date()}
          dateFormat="yyyy-MM-dd"
          placeholderText={props.label}
          className={cn(
            props.dateClassName,
            "date-picker-event-calendar w-full",
            props.readOnly &&
              "cursor-not-allowed rounded-md bg-[#e3e3e3b3] text-gray-700",
          )}
          readOnly={props.readOnly}
          inputProps={{
            error:
              get(props.errors, props.name) && !props.getValues(props.name)
                ? props.label + " is required"
                : undefined,
            label: props.label,
            size: props.size ?? "md",
          }}
          inline={props.inline}
          minDate={props.minDate}
          maxDate={props.maxDate}
        />
      </div>
    </div>
  );
}

export function DatetimeInput(props: DateInputProps) {
  const [date, setDate] = useState<Date | null>(null);

  props.register(props.name, {
    value: date,
    required: props.required,
  });
  const onChange = (e: Date | null) => {
    setDate(e);
    props.setValue(props.name, e ?? null);
    // props.setValue(props.name, e ? formatDate(e, "datetime") : null);
    if (props.onChange) {
      props.onChange(e, props.name);
    }
  };
  useEffect(() => {
    if (props.getValues(props.name) && !date) {
      const defaultDate = new Date(props.getValues(props.name) as string);
      setDate(defaultDate);
      // console.log(props.name + ' defaultDate', defaultDate)
    }
  }, [props]);

  return (
    <div className={props.className ?? ""}>
      {props.inline && (
        <Text as="p" className={"mb-1.5 font-medium " + ("text-" + props.size)}>
          {props.label}
        </Text>
      )}
      <DatePicker
        popperPlacement={props.popperPlacement ?? "bottom-start"}
        selected={date}
        onChange={onChange}
        selectsStart
        showTimeSelect
        dateFormat="yyyy-MM-dd, HH:mm"
        timeFormat="HH:mm"
        placeholderText={props.label}
        className={cn(
          props.dateClassName,
          "date-picker-event-calendar w-full",
          props.readOnly &&
            "cursor-not-allowed rounded-md bg-[#e3e3e3b3] text-gray-700",
        )}
        readOnly={props.readOnly}
        inputProps={{
          error:
            get(props.errors, props.name) && !props.getValues(props.name)
              ? props.label + " is required"
              : undefined,
          label: props.label,
          size: props.size ?? "md",
        }}
        inline={props.inline}
        minDate={props.minDate}
        maxDate={props.maxDate}
      />
    </div>
  );
}

type RangeDate = [Date | null, Date | null];
interface RangeDateInputProps {
  label: string;
  names: [string, string];
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  errors: FieldErrors<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  required?: boolean;
  readOnly?: boolean;
  inline?: boolean;
  className?: string;
}
export function DateRangeInput(props: RangeDateInputProps) {
  const [date, setDate] = useState<RangeDate>([null, null]);

  props.register(props.names[0], {
    value: date[0],
    required: props.required,
  });
  props.register(props.names[1], {
    value: date[1],
    required: props.required,
  });
  const onChange = (e: RangeDate) => {
    // console.log('e', e);
    setDate(e);
    props.setValue(props.names[0], e[0] ? formatDate(e[0], "date") : null);
    props.setValue(props.names[1], e[1] ? formatDate(e[1], "date") : null);
  };
  // useEffect(() => {
  //   // const tempVal = props.getValues(props.name) as RangeDate;
  //   // setDate(tempVal ? [tempVal[0], tempVal[1]]);
  //   // }, [props.getValues(props.name)]);
  // }, [props]);
  return (
    <div className={`customDatePickerWidth w-full ${props.className ?? ""}`}>
      <ReactDatePicker
        // selected={date}
        // onChange={(e) => { void onChange(e[0]) }}
        startDate={date[0]}
        endDate={date[1]}
        onChange={onChange}
        dateFormat="d MMMM yyyy"
        placeholderText={props.label}
        inline={props.inline}
        readOnly={props.readOnly}
        selectsRange={true}
        // className="w-full"
        // calendarClassName="w-full"
        // monthClassName="w-full"
        // wrapperClassName="w-full"
        // monthsShown={2}
      />
    </div>
  );
}

// export function DateTimeInput(props: any) {
//   const [value, setValue] = useState(null);
//   props.register(props.name, {
//     value: value,
//     required: props.required,
//   });
//   const onChange = (e: any) => {
//     setValue(e);
//     // // console.log(e);
//     // // console.log(formatDate(e, "datetime"));
//     props.setValue(props.name, formatDate(e, "datetime"));
//   };
//   useEffect(() => {
//     setValue(props.getValues(props.name));
//   }, [props.isUpdate]);
//   return (
//     <>
//       <Text as="p" className="mb-1">
//         {props.label}
//       </Text>
//       <DatePicker
//         popperPlacement="top-start"
//         selected={value}
//         onChange={onChange}
//         selectsStart
//         // minDate={new Date()}
//         showTimeSelect
//         dateFormat="d MMMM yyyy, HH:mm"
//         timeFormat="HH:mm"
//         placeholderText={props.label}
//         className={cn(
//           "date-picker-event-calendar",
//           props.readOnly &&
//           "!cursor-not-allowed rounded-md bg-[#e3e3e3b3] text-gray-700",
//         )}
//         readOnly={props.readOnly}
//       />
//       {/* ERROR MESSAGE */}
//       <span className="rizzui-input-error-text text-[13px] text-red">
//         {props.errors?.[props.name] &&
//           props.label + " is required"}
//       </span>
//       {/* NOTES MESSAGE */}
//       <small>
//         {!props.errors?.[props.name] && props.notes}
//       </small>
//     </>
//   );
// }
