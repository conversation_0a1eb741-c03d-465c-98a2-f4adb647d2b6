import React from "react";

export default function SkeletonListRoom({
  roomRanges,
}: {
  roomRanges?: number;
}) {
  // // console.log("SkeletonListRoom rendered with roomRanges:", roomRanges);

  return (
    <div className="flex flex-col sm:gap-2 md:gap-2 xl:gap-4 2xl:gap-8">
      {Array.from({ length: roomRanges ?? 3 }).map((_, index) => (
        <div key={index} className="mb-0 2xl:mb-2">
          <div className="flex flex-wrap gap-1 sm:gap-2 md:gap-2 xl:gap-2 2xl:gap-4">
            {Array.from({ length: roomRanges ?? 10 }).map((_, idx) => (
              <div
                key={idx}
                className="flex w-[30px] animate-pulse items-center justify-between rounded-lg border bg-gray-100 px-1 shadow sm:w-[40px] md:w-[50px] xl:w-[60px] 2xl:w-[70px] 2xl:p-2 hd:w-[80px] 3xl:w-[90px]"
              >
                <div className="flex w-full flex-col gap-0 2xl:gap-1">
                  <div className="flex items-center justify-between gap-1">
                    {/* Skeleton for roomNumber */}
                    <span className="h-2 w-8 rounded bg-gray-300 sm:h-2 sm:w-6 xl:h-2 xl:w-8 2xl:h-3 2xl:w-10"></span>
                    {/* Skeleton for RoomStatusCode */}
                    <span className="h-2 w-8 rounded bg-gray-300 sm:h-2 sm:w-6 xl:h-2 xl:w-8 2xl:h-3 2xl:w-10"></span>
                  </div>
                  {/* Skeleton for RoomTypeCode */}
                  <span className="h-2 w-12 rounded bg-gray-300 sm:h-2 sm:w-10 xl:h-2 xl:w-12 2xl:h-3 2xl:w-16"></span>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
