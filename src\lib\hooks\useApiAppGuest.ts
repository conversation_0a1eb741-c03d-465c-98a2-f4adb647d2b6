﻿import { getApiAppGuest } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import { SelectOption } from "rizzui";

export const useApiAppGuest = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetGuests, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppGuest({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};

export const useApiAppGuestOptions = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetGuests, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppGuest({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      // GENERATE OPTIONS
      const options: SelectOption[] = data?.items?.map(val => ({
        label: `${val.identityNumber} - ${val.fullname}`,
        value: val.identityNumber ?? `${val.fullname}`,
        id: val.id ?? 0,
        ...val
      })) ?? [];

      // RETURN OPTIONS
      return [
        // {label: "--Select--", value: 0, disabled: true},
        ...options
      ];
    },
  });
};
