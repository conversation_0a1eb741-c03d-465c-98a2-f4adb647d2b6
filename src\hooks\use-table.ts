import { useState, useEffect, useMemo } from 'react';
import isString from 'lodash/isString';

// type AnyObject = Record<string, any>;
type GenericObject<T> = { id: string } & Record<string, T>;

export function useTable<T, U extends GenericObject<T>>(
  initialData: U[],
  countPerPage = 10,
  initialFilterState?: Partial<U>
) {
  const [data, setData] = useState(initialData);

  /*
   * Dummy loading state.
   */
  const [isLoading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(false);
  }, []);

  /*
   * Handle row selection
   */
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const handleRowSelect = (recordKey: string) => {
    const selectedKeys = [...selectedRowKeys];
    if (selectedKeys.includes(recordKey)) {
      setSelectedRowKeys(selectedKeys.filter((key) => key !== recordKey));
    } else {
      setSelectedRowKeys([...selectedKeys, recordKey]);
    }
  };
  const handleSelectAll = () => {
    if (selectedRowKeys.length === data.length) {
      setSelectedRowKeys([]);
    } else {
      // Ensure that every record is defined and every id is a string
      const ids = data.filter((record): record is U => record !== undefined && typeof record.id === 'string').map(record => record.id);
      setSelectedRowKeys(ids);
    }
  };

  /*
   * Handle sorting
   */
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' | '' }>({
    key: "",
    direction: "",
  });

  function sortData<T>(data: T[], sortKey: keyof T, sortDirection: 'asc' | 'desc'): T[] {
    return [...data].sort((a, b) => {
      const aValue = a[sortKey];
      const bValue = b[sortKey];

      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      } else if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  const sortedData = useMemo(() => {
    const newData = data;
    if (!sortConfig.key || !sortConfig.direction) {
      return newData;
    }
    // Provide a default direction if sortConfig.direction is null
    const direction = sortConfig.direction ?? 'asc';
    return sortData(newData, sortConfig.key, direction);
  }, [sortConfig, data]);

  function handleSort(key: string) {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  }

  /*
   * Handle pagination
   */
  const [currentPage, setCurrentPage] = useState(1);
  function paginatedData(data: U[] = sortedData) {
    const start = (currentPage - 1) * countPerPage;
    const end = start + countPerPage;

    if (data.length > start) return data.slice(start, end);
    return data;
  }

  function handlePaginate(pageNumber: number) {
    setCurrentPage(pageNumber);
  }

  /*
   * Handle delete
   */
  function handleDelete(id: string | string[]) {
    const updatedData = Array.isArray(id)
      ? data.filter((item) => !id.includes(item.id))
      : data.filter((item) => item.id !== id);

    setData(updatedData);
  }

  /*
   * Handle Filters and searching
   */
  const [searchTerm, setSearchTerm] = useState('');
  type FilterValue = string | [number | Date, number | Date];

  const [filters, setFilters] = useState<Record<string, FilterValue>>(
    (initialFilterState as Record<string, FilterValue>) ?? {}
  );

  function updateFilter(columnId: string, filterValue: FilterValue) {
    if (Array.isArray(filterValue) && filterValue.length !== 2) {
      throw new Error('filterValue data must be an array of length 2');
    }

    setFilters((prevFilters) => ({
      ...prevFilters,
      [columnId]: filterValue,
    }));
  }

  function applyFilters() {
    const searchTermLower = searchTerm.toLowerCase();

    return (
      sortedData
        .filter((item) => {
          const isMatchingItem = Object.entries(filters).some(
            ([columnId, filterValue]) => {
              if (
                Array.isArray(filterValue) &&
                typeof filterValue[1] === 'object' &&
                (typeof item[columnId] === 'string' || item[columnId] instanceof Date) &&
                item[columnId] !== undefined // Ensure item[columnId] is not undefined
              ) {
                const itemValue = item[columnId];
                if (typeof itemValue === 'string' || typeof itemValue === 'number' || itemValue instanceof Date) {
                  const date = new Date(itemValue);
                  return (date >= filterValue[0] && date <= filterValue[1]);
                }
              }
              if (
                Array.isArray(filterValue) &&
                typeof filterValue[1] === 'string'
              ) {
                const itemPrice = Math.ceil(Number(item[columnId]));
                return (
                  itemPrice >= Number(filterValue[0]) &&
                  itemPrice <= Number(filterValue[1])
                );
              }
              if (isString(filterValue) && !Array.isArray(filterValue)) {
                const itemValue = item[columnId]?.toString().toLowerCase();
                if (itemValue !== filterValue.toString().toLowerCase()) {
                  return false;
                }
                return true;
              }
            }
          );
          return isMatchingItem;
        })
        // global search after running filters
        .filter((item) =>
          Object.values(item).some((value) =>
            typeof value === 'object'
              ? value &&
              Object.values(value).some(
                (nestedItem) =>
                  nestedItem &&
                  String(nestedItem).toLowerCase().includes(searchTermLower)
              )
              : value && String(value).toLowerCase().includes(searchTermLower)
          )
        )
    );
  }

  /*
   * Handle searching
   */
  function handleSearch(searchValue: string) {
    setSearchTerm(searchValue);
  }

  function searchedData() {
    if (!searchTerm) return sortedData;

    const searchTermLower = searchTerm.toLowerCase();

    return sortedData.filter((item) =>
      Object.values(item).some((value) =>
        typeof value === 'object'
          ? value &&
          Object.values(value).some(
            (nestedItem) =>
              nestedItem &&
              String(nestedItem).toLowerCase().includes(searchTermLower)
          )
          : value && String(value).toLowerCase().includes(searchTermLower)
      )
    );
  }

  /*
   * Reset search and filters
   */
  function handleReset() {
    setData(() => initialData);
    handleSearch('');
    if (initialFilterState) {
      setFilters(initialFilterState as Record<string, FilterValue>);
    }
  }

  /*
   * Set isFiltered and final filtered data
   */
  const isFiltered = applyFilters().length > 0;
  function calculateTotalItems() {
    if (isFiltered) {
      return applyFilters().length;
    }
    if (searchTerm) {
      return searchedData().length;
    }
    return sortedData.length;
  }
  const filteredAndSearchedData = isFiltered ? applyFilters() : searchedData();
  const tableData = paginatedData(filteredAndSearchedData);

  /*
   * Go to first page when data is filtered and searched
   */
  useEffect(() => {
    handlePaginate(1);
  }, [isFiltered, searchTerm]);

  // useTable returns
  return {
    isLoading,
    isFiltered,
    tableData,
    // pagination
    currentPage,
    handlePaginate,
    totalItems: calculateTotalItems(),
    // sorting
    sortConfig,
    handleSort,
    // row selection
    selectedRowKeys,
    setSelectedRowKeys,
    handleRowSelect,
    handleSelectAll,
    // searching
    searchTerm,
    handleSearch,
    // filters
    filters,
    updateFilter,
    applyFilters,
    handleDelete,
    handleReset,
  };
}
