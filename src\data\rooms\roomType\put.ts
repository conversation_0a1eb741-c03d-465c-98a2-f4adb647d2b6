"use server";
import type {
  RoomType,
  RoomTypePostResponse,
} from "@/interfaces/rooms/roomType";
import axios from "axios";
import { getSession } from "@/lib/actions";

export const putRoomType = async ({
  id,
  name,
  status,
  alias,
}: {
  id: string;
  name: string;
  status: number;
  alias: string;
}): Promise<RoomTypePostResponse> => {
  try {
    const session = await getSession();

    const res = await axios<RoomType>({
      url: `${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room-type/${id}`,
      method: "PUT",
      headers: {
        Authorization: "Bearer " + session?.access_token,
        "Accept-Language": "en_US",
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: {
        name: name,
        status: status,
        alias: alias,
      },
    });

    return {
      success: true,
      message: "Room type updated successfully.",
      data: res.data,
    };
  } catch (error) {
    console.error("Failed to update room type:", error);

    return {
      success: false,
      message: "An unexpected error occurred.",
      data: null,
      error: {
        code: "ERROR_CODE",
        message: error instanceof Error ? error.message : "Unknown error",
        details: "An error occurred while posting the room type.",
      },
    };
  }
};
