export interface Rooms {
  id: string;
  name: string;
  roomNumber: string;
  roomCode: string;
  size: string;
  information: string;
  price: number;
  roomTypeId: string;
  roomTypeName: string;
  roomTypeCode: string;
  roomStatusId: string;
  roomStatusName: string;
  roomStatusCode: string;
  roomStatusColor: string;
  lastModificationTime: string | null;
  lastModifierId: string | null;
  creationTime: string;
  creatorId: string | null;
}

export interface RoomResponse {
  items: Rooms[];
  totalCount: number;
}

export interface RoomPostSuccessResponse {
  success: true;
  message: string;
  data: Rooms;
}

export interface RoomPostErrorResponse {
  success: false;
  message: string;
  data: null;
  error: {
    code: string;
    message: string;
    details: string;
    data?: Record<string, string>;
    validationErrors?: {
      message: string;
      members: string[];
    }[];
  };
}

export interface RoomPostRequest {
  name: string;
  roomNumber: string;
  roomCode: string;
  size: string;
  information: string;
  roomTypeId: string;
  roomStatusId: string;
  price: number;
}

export interface RoomPutRequest {
  id: string;
  roomNumber: string;
  roomCode: string;
  size: string;
  information: string;
  roomTypeId: string;
  roomStatusId: string;
  price: number;
}

export type RoomPostResponse = RoomPostSuccessResponse | RoomPostErrorResponse;
