import React, { useEffect, useState } from "react";
import { postApiAppRegistrationCardGenerateDocumentAsAttachment } from "@/client";
import { useApiAttachmentStreamById } from "@/lib/hooks/useApiAppAttachmentById";
import { PiFilePdfBold } from "react-icons/pi";
import { Button, Tooltip } from "rizzui";
import ModalPreview from "./modal-preview";

export default function ViewRegistrationCard({
  itemId,
  name,
}: {
  itemId: string;
  name?: string;
}) {
  const [previewLoading, setPreviewLoading] = useState(false);
  const [blobUrl, setBlobUrl] = useState<string>();
  const [modal, setModal] = useState(false);
  const [attachmentId, setAttachmentId] = useState("");

  const handleView = async () => {
    if (!itemId) return;
    setPreviewLoading(true);

    const data = {
      documentId: itemId,
      templateId: "",
      includeDetails: true,
      useAdvancedTable: true,
      generatePdf: true,
      customFilename: name ?? "RegistrationCard",
    };

    const rc = await postApiAppRegistrationCardGenerateDocumentAsAttachment({
      body: data,
    });

    setAttachmentId(rc.data?.id ?? "");
    setModal(true);
    setPreviewLoading(false);
  };

  const { data } = useApiAttachmentStreamById(attachmentId);
  useEffect(() => {
    if (data) {
      const url = URL.createObjectURL(data as Blob);
      setBlobUrl(url);
      return () => URL.revokeObjectURL(url); // clean up memory
    }
  }, [data]);

  return (
    <>
      <Tooltip size="sm" content="Registration Card" placement="top">
        <Button
          className="bg-red-500 text-white hover:bg-red-600 hover:text-white"
          variant="text"
          onClick={handleView}
          size="sm"
          isLoading={previewLoading}
        >
          <PiFilePdfBold className="h-4 w-4 hover:text-white" />
        </Button>
      </Tooltip>

      <ModalPreview
        isOpen={modal}
        onClose={() => setModal(false)}
        blobUrl={blobUrl}
        title="Preview Registration Card"
      />
    </>
  );
}
