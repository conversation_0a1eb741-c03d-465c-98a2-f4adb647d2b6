"use client";

import React from "react";
import dynamic from "next/dynamic";
import isEmpty from "lodash/isEmpty";
import Table, { type TableProps } from "@/components/theme/ui/table";
import { Loader } from "rizzui";
import cn from "@/utils/class-names";
import type { TableFilterProps } from "@/components/theme/controlled-table/table-filter";
import type { TablePaginationProps } from "@/components/theme/controlled-table/table-pagination";
import { type DefaultRecordType } from "rc-table/lib/interface";
const TableFilter = dynamic(
  () => import("@/components/theme/controlled-table/table-filter"),
  { ssr: false },
);
const TablePagination = dynamic(
  () => import("@/components/theme/controlled-table/table-pagination"),
  { ssr: false },
);

type ControlledTableProps = {
  isLoading?: boolean;
  showLoadingText?: boolean;
  filterElement?: React.ReactElement;
  filterOptions?: TableFilterProps;
  paginatorOptions?: TablePaginationProps;
  tableFooter?: React.ReactNode;
  className?: string;
  paginatorClassName?: string;
} & TableProps;

interface RecordType {
  id: string; // or number, depending on your data structure
  // other properties
}

export default function ControlledTable({
  isLoading,
  filterElement,
  filterOptions,
  paginatorOptions,
  tableFooter,
  showLoadingText,
  paginatorClassName,
  className,
  ...tableProps
}: ControlledTableProps) {
  if (isLoading) {
    return (
      <div className="grid h-full min-h-[128px] flex-grow place-content-center items-center justify-center">
        <Loader variant="spinner" size="xl" />
        {showLoadingText ? (
          <div className="flex justify-center">
            <Loader />
          </div>
        ) : null}
      </div>
    );
  }

  return (
    <>
      {!isEmpty(filterOptions) && (
        <TableFilter {...filterOptions}>{filterElement}</TableFilter>
      )}

      <div className="relative">
        <Table
          // scroll={{ x: 1300 }}
          // @ts-expect-error update
          // eslint-disable-next-line @typescript-eslint/no-unsafe-return
          rowKey={(record: RecordType) => (record as DefaultRecordType).id}
          className={cn(className)}
          {...tableProps}
        />

        {tableFooter ? tableFooter : null}
      </div>

      {!isEmpty(paginatorOptions) && (
        <TablePagination
          paginatorClassName={paginatorClassName}
          {...paginatorOptions}
        />
      )}
    </>
  );
}
