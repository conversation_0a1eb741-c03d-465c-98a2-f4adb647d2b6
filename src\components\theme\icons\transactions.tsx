export default function TransactionIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" {...props}>
      <path
        fill="#91def5"
        d="M458.07 141.3 443.01 65l-15.08 15.07A244.5 244.5 0 0 0 256 10c-65.7 0-127.48 25.59-173.95 72.05S10 190.3 10 256h44c0-111.38 90.62-202 202-202 52.96 0 102.9 20.3 140.81 57.19l-15.04 15.04z"
      />
      <circle cx="256" cy="256" r="128" fill="#ffcb7c" />
      <path
        fill="#91def5"
        d="M53.93 370.7 68.99 447l15.08-15.07A244.5 244.5 0 0 0 256 502c65.7 0 127.48-25.59 173.95-72.05S502 321.7 502 256h-44c0 111.38-90.62 202-202 202-52.96 0-102.9-20.3-140.81-57.19l15.04-15.04z"
      />
      <path d="M256 118c-76.1 0-138 61.9-138 138s61.9 138 138 138 138-61.9 138-138-61.9-138-138-138zm0 256c-65.06 0-118-52.94-118-118s52.94-118 118-118 118 52.94 118 118-52.94 118-118 118z" />
      <path d="M272.32 245.85c-2.08-.74-4.2-1.5-6.32-2.29v-39.28c5.3 1.76 8.8 4.24 8.98 4.37a10 10 0 0 0 12.03-15.98c-.53-.4-8.8-6.54-21.01-9.06v-8.3a10 10 0 1 0-20 0v8.23c-1.33.28-2.67.6-4.02 1.02-13.12 3.93-22.91 15.14-25.58 29.23-2.43 12.9 1.72 25.36 10.84 32.51 4.76 3.73 10.66 7.14 18.76 10.73v51.09c-6.75-.54-11.12-2.25-18.5-7.07a10 10 0 1 0-10.94 16.74c11.49 7.51 19.35 9.79 29.44 10.38v8.51a10 10 0 1 0 20 0v-9.65c18.72-4.59 30.72-20.5 33.36-36.2 3.42-20.31-7.2-37.97-27.04-44.98zm-32.74-15.28c-3.17-2.5-4.55-7.62-3.52-13.06.9-4.83 4-10.58 9.94-13.15v30.38a50.07 50.07 0 0 1-6.42-4.17zm40.06 56.94c-1.2 7.13-5.82 14.66-13.64 18.41v-41.08c15.24 5.6 14.3 18.75 13.64 22.67z" />
      <path d="M64 256c0-105.87 86.13-192 192-192 46.88 0 91.38 16.78 126.39 47.47l-7.7 7.69a10 10 0 0 0 5.14 16.88l76.3 15.06a10 10 0 0 0 11.75-11.74l-15.06-76.3a10 10 0 0 0-16.89-5.14l-8.21 8.22A254.43 254.43 0 0 0 256 0c-52.29 0-102.45 15.56-145.08 45a10 10 0 1 0 11.37 16.45C161.56 34.33 207.79 20 256 20c62.02 0 120.6 23.87 164.94 67.22a10 10 0 0 0 14.06-.08l1.83-1.83 8.54 43.28-43.28-8.54 1.8-1.8a10 10 0 0 0-.1-14.23A210.83 210.83 0 0 0 256 44C142.46 44 49.47 133.73 44.23 246H20.21c1.84-44.59 16-87.17 41.24-123.71A10 10 0 0 0 45 110.92C15.56 153.55 0 203.72 0 256a10 10 0 0 0 10 10h44a10 10 0 0 0 10-10zM502 246h-44a10 10 0 0 0-10 10c0 105.87-86.13 192-192 192a190.83 190.83 0 0 1-126.39-47.47l7.7-7.69a10 10 0 0 0-5.14-16.88l-76.3-15.06a10 10 0 0 0-11.75 11.74l15.06 76.3a10 10 0 0 0 16.89 5.14l8.21-8.22A254.43 254.43 0 0 0 256 512c68.38 0 132.67-26.63 181.02-74.98C485.37 388.67 512 324.38 512 256a10 10 0 0 0-10-10zm-79.12 176.88A234.45 234.45 0 0 1 256 492a234.66 234.66 0 0 1-164.94-67.22 9.97 9.97 0 0 0-14.06.08l-1.83 1.82-8.54-43.27 43.28 8.54-1.8 1.8a10 10 0 0 0 .1 14.23A210.83 210.83 0 0 0 256 468c113.54 0 206.53-89.73 211.77-202h24.02c-2.45 59.29-26.7 114.67-68.91 156.88z" />
      <path d="M82.05 92.05c2.63 0 5.21-1.07 7.07-2.93s2.93-4.44 2.93-7.07-1.07-5.21-2.93-7.07-4.44-2.93-7.07-2.93-5.21 1.07-7.07 2.93-2.93 4.44-2.93 7.07 1.07 5.21 2.93 7.07 4.44 2.93 7.07 2.93z" />
    </svg>
  );
}
