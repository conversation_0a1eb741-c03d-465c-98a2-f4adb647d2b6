import { useApiReport } from "@/lib/hooks/useApiReport";
import { type LithiumMenuIconType } from "../lithium/lithium-menu-icons";
import type { Policy } from "@/lib/hooks/useGrantedPolicies";
import { postApiReportList } from "@/client";
import { useTranslation } from "react-i18next";

export type SubMenuItemType = {
  name: string;
  href: string;
};
export type DropdownItemType = {
  name: string;
  icon: string;
  description?: string;
  href?: string;
  subMenuItems?: SubMenuItemType[];
  permission?: Policy;
};

export type menuItem = {
  name: string;
  type: string;
  dropdownItems?: DropdownItemType[];
  href?: string;
  icon?: LithiumMenuIconType;
  permission?: Policy;
};

export type menuItems = menuItem[];

export function MenuItemsByRoles() {
  // const { data: dataReport } = await postApiReportList({
  //   body: {
  //     page: 1,
  //     maxResultCount: 20
  //   },
  // });
  const { t } = useTranslation();
  const menu: menuItems = [
    {
      name: t("t_Dashboard"),
      type: "link",
      href: "/dashboard",
    },
    {
      name: t("t_Reservation"),
      type: "link",
      href: "/reservation",
      permission: "WismaApp.Reservation",
      // icon: "",
    },
    {
      name: t("t_Checkin"),
      type: "link",
      href: "/checkin",
      permission: "WismaApp.Reservation.CheckIn",
      // icon: "",
    },
    {
      name: t("t_Checkout"),
      type: "link",
      href: "/checkout",
      permission: "WismaApp.Reservation.CheckOut",
      // icon: "",
    },
    {
      name: t("t_Rooms"),
      href: "#",
      type: "dropdownItems",
      permission: "WismaApp.Room",
      dropdownItems: [
        {
          name: t("t_RoomsMaster"),
          href: "/rooms/master",
          icon: "FileSettingsIcon",
          permission: "WismaApp.Room",
        },
        {
          name: t("t_RoomsStatus"),
          href: "/rooms/masterStatus",
          icon: "FileSettingsIcon",
          permission: "WismaApp.RoomStatus",
        },
        {
          name: t("t_RoomsType"),
          href: "/rooms/masterType",
          icon: "FileSettingsIcon",
          permission: "WismaApp.RoomType",
        },
      ],
    },
    {
      name: t("t_Services"),
      href: "#",
      type: "dropdownItems",
      permission: "WismaApp.Service",
      dropdownItems: [
        {
          name: t("t_ServicesMaster"),
          href: "/services/master",
          icon: "FileSettingsIcon",
          permission: "WismaApp.Service",
        },
        {
          name: t("t_ServicesType"),
          href: "/services/masterType",
          icon: "FileSettingsIcon",
          permission: "WismaApp.ServiceType",
        },
        {
          name: t("t_Transaction"),
          href: "/services/transaction",
          icon: "InvoiceIcon",
          permission: "WismaApp.Reservation.RoomService",
        },
      ],
    },
    {
      name: t("t_FnB"),
      href: "#",
      type: "dropdownItems",
      permission: "WismaApp.FoodAndBeverage",
      dropdownItems: [
        {
          name: t("t_FnBMaster"),
          href: "/fnb/master",
          icon: "FileSettingsIcon",
          permission: "WismaApp.FoodAndBeverage",
        },
        {
          name: t("t_FnBType"),
          href: "/fnb/masterType",
          icon: "FileSettingsIcon",
          permission: "WismaApp.FoodAndBeverageType",
        },
        {
          name: t("t_Transaction"),
          href: "/fnb/transaction",
          icon: "InvoiceIcon",
          permission: "WismaApp.Reservation.RoomFoodAndBeverage",
        },
      ],
    },
    {
      name: t("t_Config"),
      href: "#",
      type: "dropdownItems",
      permission: "WismaApp.Settings.Settings",
      dropdownItems: [
        {
          name: t("t_GuestMaster"),
          href: "/config/masterGuest",
          icon: "FileSettingsIcon",
          permission: "WismaApp.Guest",
        },
        {
          name: t("t_StatusMaster"),
          href: "/config/masterStatus",
          icon: "FileSettingsIcon",
          permission: "WismaApp.MasterStatus",
        },
        {
          name: t("t_CompanyMaster"),
          href: "/config/masterCompany",
          icon: "FileSettingsIcon",
          permission: "WismaApp.MasterCompany",
        },
        {
          name: t("t_DiningOptions"),
          href: "/config/masterDiningOption",
          icon: "FileSettingsIcon",
          permission: "WismaApp.DiningOptions",
        },
        {
          name: t("t_PaymentMethod"),
          href: "/config/masterPaymentMethod",
          icon: "FileSettingsIcon",
          permission: "WismaApp.PaymentMethod",
        },
        {
          name: t("t_ReportMaster"),
          href: "/config/masterReport",
          icon: "FileSettingsIcon",
          // permission: "WismaApp.PaymentMethod",
        },
        {
          name: t("t_DocumentTemplate"),
          href: "/config/masterDocTemplate",
          icon: "FileSettingsIcon",
          // permission: "WismaApp.PaymentMethod",
        },
      ],
    },
    {
      name: t("t_Report"),
      type: "link",
      href: "/report/list",
      permission: "WismaApp.Report.View",
    },
    // {
    //   name: "Report",
    //   href: "#",
    //   type: "dropdownItems",
    //   dropdownItems: [
    //     {
    //       name: "Room",
    //       href: "/report/room",
    //       icon: "DicesIcon",
    //     },
    //     {
    //       name: "IMIP Rental Room",
    //       href: "/report/rental-room",
    //       icon: "TableCollapsibleIcon",
    //     },
    //     {
    //       name: "House Keeping",
    //       href: "/report/house-keeping",
    //       icon: "PieChartCurrencyIcon",
    //     },
    //     {
    //       name: "Resto & F&B",
    //       href: "/report/fnb",
    //       icon: "AnalyticsCircularIcon",
    //     },
    //   ],
    // },
  ];
  // if (
  //   role.includes("WISMA Admin Divisi") ||
  //   role.includes("WISMA Superuser")
  // ) {
  //   menu.splice(3, 0, {
  //     name: "Setting",
  //     type: "link",
  //     href: "/",
  //     icon: 'PiFilmScript',
  //   });
  // }
  return menu;
}
