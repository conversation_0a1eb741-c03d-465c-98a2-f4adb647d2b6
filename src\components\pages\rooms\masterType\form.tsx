import ButtonReset from "@/components/container/button/button-reset";
import { fetchStatusOptions } from "@/data/masterStatus/option";
import React, { useEffect, useState } from "react";
import { Button, Input, Select } from "rizzui";
import type { SelectOption } from "rizzui";
import type { FormProps } from "@/interfaces/form/formType";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  setValue,
  getValues,
  onDelete,
  handleReset,
  isStatus,
  setStatus,
}: FormProps & {
  handleReset: () => void;
  isStatus: SelectOption | undefined;
  setStatus: (option: SelectOption) => void;
}) {
  const { can } = useGrantedPolicies();
  const [isStatusOpt, setStatusOpt] = useState<SelectOptionType[]>([]);
  useEffect(() => {
    const fetchData = async () => {
      const options = await fetchStatusOptions("Room Type");
      setStatusOpt(options);
    };

    void fetchData();
  }, []);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-4 gap-2">
        <div>
          <Input
            size="sm"
            label="Alias"
            disabled={isLoading}
            placeholder="Please fill in Alias"
            {...register("alias", { required: true })}
            error={errors.alias ? "Alias is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Name"
            disabled={isLoading}
            placeholder="Please fill in Name"
            {...register("name", { required: true })}
            error={errors.name ? "Name is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Status"
            disabled={isLoading}
            placeholder="Please select Status"
            {...register("statusId", { required: true })}
            error={errors.statusId ? "Status is required" : undefined}
            className="w-full"
            options={isStatusOpt}
            value={isStatus}
            onChange={(e: { label: string; value: string }) => {
              setStatus(e);
              setValue("statusId", e?.value ?? "");
            }}
          />
        </div>
        <div className="flex items-end justify-start gap-2">
          {can("WismaApp.RoomType.Create") && can("WismaApp.RoomType.Edit") && (
            <Button
              size="sm"
              type="submit"
              disabled={isLoading}
              className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${
                getValues("id") ? "bg-blue-500" : "bg-green-500"
              }`}
            >
              {getValues("id") ? "Update" : "Create"}
            </Button>
          )}
          {/* {getValues("id") && can("WismaApp.RoomType.Delete") && (
            <Button
              size="sm"
              disabled={isLoading}
              className={`rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400`}
              onClick={() => {
                onDelete(String(getValues("id")));
              }}
            >
              Delete
            </Button>
          )} */}
          <ButtonReset isLoading={isLoading} handleReset={handleReset} />
        </div>
      </div>
    </form>
  );
}
