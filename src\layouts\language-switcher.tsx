"use client";

import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import "flag-icons/css/flag-icons.min.css";
import '@/lib/i18n';

export default function LanguageSwitcher() {
  const languages = [
    { code: 'en', alias: 'en', label: 'English', flag: '🇬🇧', icon: 'gb' },
    { code: 'cn', alias: 'cn', label: '中文', flag: '🇨🇳', icon: 'cn' },
  ];
  const { i18n } = useTranslation();
  // ⏬ Ambil bahasa dari localStorage saat pertama kali load
  useEffect(() => {
    const savedLang = localStorage.getItem('lang');
    const defaultLang = 'en';

    if (!savedLang) {
      localStorage.setItem('lang', defaultLang); // ⏫ set default
      void i18n.changeLanguage(defaultLang);
    } else if (savedLang !== i18n.language) {
      void i18n.changeLanguage(savedLang);
    }
  }, [i18n]);

  const handleChangeLanguage = (lang: string) => {
    void i18n.changeLanguage(lang);
    localStorage.setItem('lang', lang); // ⏫ Simpan ke localStorage
  };

  return (
    <div className="pt-1">
        <div className="flex items-center rounded-md border border-gray-300 bg-white shadow-sm">
          {languages.map((lang) => (
            <button
              key={lang.code}
              onClick={() => handleChangeLanguage(lang.code)}
              className={`flex items-center space-x-1 rounded px-1 text-sm font-medium transition 
            ${i18n.language === lang.code
                  ? 'bg-gray-200 text-gray-700'
                  : 'hover:bg-gray-100 text-gray-700'}`}
            >
              <span className={`rounded-full fi fis fi-${lang.icon}`} />
              <span>{lang.alias}</span>
            </button>
          ))}
        </div>
      </div>
  );
}
