"use server";
import { getSession } from "@/lib/actions";
import axios from "axios";
import type {
  Rooms,
  RoomPostResponse,
  RoomPutRequest,
} from "@/interfaces/rooms/roomMaster";

export const putRoomMaster = async ({
  id,
  roomNumber,
  roomCode,
  size,
  information,
  roomTypeId,
  roomStatusId,
  price,
}: RoomPutRequest): Promise<RoomPostResponse> => {
  try {
    const session = await getSession();

    const res = await axios<Rooms>({
      url: `${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room/${id}`,
      method: "PUT",
      headers: {
        Authorization: "Bearer " + session?.access_token,
        "Accept-Language": "en_US",
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: {
        name: roomNumber,
        roomNumber: roomNumber,
        roomCode: roomCode,
        size: size,
        information: information,
        roomTypeId: roomTypeId,
        roomStatusId: roomStatusId,
        price: price,
      },
    });

    return {
      success: true,
      message: "Room updated successfully.",
      data: res.data,
    };
  } catch (error) {
    console.error("Failed to post room:", error);

    return {
      success: false,
      message: "An unexpected error occurred.",
      data: null,
      error: {
        code: "ERROR_CODE",
        message: error instanceof Error ? error.message : "Unknown error",
        details: "An error occurred while posting the room type.",
      },
    };
  }
};
