import React, { useEffect, useState } from "react";
import swal from "sweetalert";
import { useMasterStatus } from "@/lib/hooks/rooms/masterStatus/useMasterStatus";
import { Button, Input, Modal, Select, Text } from "rizzui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { putApiAppRoomById } from "@/client";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import type { SelectOption } from "rizzui";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import type { SingleValue } from "react-select";
import type { CreateUpdateRoomDto } from "@/client";
import type { ModalUpdateRoomStatusProps } from "@/interfaces/dashboad/modalRoomStatusType";
import { swalError } from "@/lib/helper/swal-error";

export default function ModalUpdateRoomStatus({
  roomId,
  roomNumber,
  roomCode,
  roomType,
  roomStatus,
  roomSize,
  roomInformation,
  roomTypeId,
  roomStatusId,
  roomPrice,
  isModalOpen,
  setModalOpen,
}: ModalUpdateRoomStatusProps) {
  const queryClient = useQueryClient();
  const [isRoomStatus, setRoomStatus] = useState<SelectOption | undefined>(
    roomStatus ?? { label: "", value: "" },
  );
  const [isRoomStatusOpt, setRoomStatusOpt] = useState<SelectOptionType[]>([]);
  const { data: masterStatusData } = useMasterStatus();

  useEffect(() => {
    if (masterStatusData?.items) {
      const mappedStatusOptions = masterStatusData.items.map((item) => ({
        label: item.name ?? "",
        value: item.id ?? "",
      }));
      setRoomStatusOpt(mappedStatusOptions);
    }
  }, [masterStatusData]);

  const updateRoomMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateRoomDto) => {
      return putApiAppRoomById({
        path: { id: roomId! },
        body: dataMutation,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room updated successfully!",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRooms] });
      setModalOpen();
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const handleSubmit = async () => {
    if (!roomId) return;

    const formData: CreateUpdateRoomDto = {
      roomNumber: roomNumber ?? "",
      roomCode: roomCode ?? "",
      size: roomSize ?? "",
      information: roomInformation ?? "",
      roomStatusId: String(isRoomStatus?.value ?? roomStatusId ?? ""),
      roomTypeId: String(roomTypeId ?? ""),
      price: roomPrice ?? 0,
    };

    await updateRoomMutation.mutateAsync(formData);
  };

  const isSaveDisabled = isRoomStatus?.value === roomStatus?.value;

  return (
    <Modal isOpen={isModalOpen} size={"sm"} onClose={setModalOpen}>
      <div className="m-auto px-7 pb-4 pt-4">
        <div className="mb-4 flex items-center justify-between">
          <Text as="b">ROOM STATUS</Text>
        </div>
        <form
          onSubmit={(event) => {
            event.preventDefault();
            void handleSubmit();
          }}
          className="space-y-4"
        >
          <div className="mb-2 flex flex-col">
            <Input
              label="Room Number"
              type="text"
              size="sm"
              value={roomNumber ?? ""}
              disabled
            />
          </div>
          <div className="mb-2 flex flex-col">
            <Input
              label="Room Type"
              type="text"
              size="sm"
              value={roomType ?? ""}
              disabled
            />
          </div>
          <div className="mb-2 flex flex-col">
            <Select
              size="sm"
              label="Room Status"
              options={isRoomStatusOpt}
              value={isRoomStatus}
              onChange={(newValue: unknown) => {
                const selectedValue = newValue as SingleValue<SelectOptionType>;
                if (selectedValue) {
                  setRoomStatus(selectedValue);
                }
              }}
              placeholder="Please select Room Status"
              className="w-full"
            />
          </div>
          <div className="my-2 flex justify-end gap-2">
            <Button
              type="button"
              size="sm"
              className="bg-red-500"
              onClick={setModalOpen}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              size="sm"
              className={`bg-green-500 ${
                isSaveDisabled ? "cursor-not-allowed text-white opacity-50" : ""
              }`}
              disabled={isSaveDisabled}
            >
              Save
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
