import Image from "next/image";
import React, { useEffect, useState } from "react";
import { PiFileImageBold, PiX } from "react-icons/pi";
import { ActionIcon, Button, Modal, Title } from "rizzui";
import { useApiAttachmentStreamById } from "@/lib/hooks/useApiAppAttachmentById";

export default function ButtonViewImage({
  url,
  label,
}: {
  url: string;
  label: string;
}) {
  const [blobUrl, setBlobUrl] = useState<string>();
  const [modal, setModal] = useState<boolean>(false);
  const { data } = useApiAttachmentStreamById(url);

  useEffect(() => {
    if (data) setBlobUrl(URL.createObjectURL(data as Blob));
  }, [data]);

  return (
    <>
      <Button
        className="h-[1.5rem] rounded-lg hover:bg-blue-100"
        variant="text"
        onClick={() => {
          setModal(true);
        }}
        title="Preview"
      >
        <PiFileImageBold
          size={20}
          className="text-blue-500"
          title="Preview"
          aria-label="Preview"
        />
      </Button>
      <Modal
        isOpen={modal}
        size={"xl"}
        onClose={() => setModal(false)}
        customSize="80vw"
      >
        <div className="m-auto px-7 pb-8 pt-6">
          <div className="mb-3 flex items-center justify-between">
            <Title as="h3">{label}</Title>
            <ActionIcon
              size="sm"
              variant="text"
              onClick={() => setModal(false)}
            >
              <PiX className="h-auto w-6" strokeWidth={1.8} />
            </ActionIcon>
          </div>
          {blobUrl && (
            <Image
              src={blobUrl}
              alt={label ?? "attachment"}
              width={800}
              height={600}
              className="mx-auto max-h-[80vh] max-w-full rounded-lg border-2"
              style={{ height: "auto", width: "auto" }}
            />
          )}
        </div>
      </Modal>
    </>
  );
}
