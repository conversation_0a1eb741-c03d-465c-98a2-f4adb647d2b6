export interface Guest {
    id?: string;
    fullname: string;
    phoneNumber: string;
    identityNumber: string;
    nationality?: string;
    companyName?: string;
}

export interface GuestResponse {
    totalCount: number,
    items: Guest[]
}

export interface GuestErrorResponse {
    response?: {
        data: {
            error: {
                details: string;
            }
        }
    },
    status?: boolean,
    error?: string
}