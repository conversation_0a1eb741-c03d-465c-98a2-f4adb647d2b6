"use client";
import React, { useState } from "react";
import ListRoomStatus from "./list-room-status";
import ListRoomType from "./list-room-type";
import ListRoomV2 from "./list-roomV2";

export default function Dashboard() {
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<string | null>(null);

  return (
    <div className="relative col-span-full mt-0 flex w-full flex-col gap-4 rounded-lg border border-gray-300 bg-white p-2 xl:gap-4 2xl:gap-6 2xl:p-6">
      <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between 2xl:gap-2">
        {/* Hallo <br /> */}
        {/* {t("greeting")} */}
        <ListRoomType setSelectedType={setSelectedType} />
        <ListRoomStatus setSelectedStatus={setSelectedStatus} />
      </div>
      <ListRoomV2 selectedStatus={selectedStatus} selectedType={selectedType} />
    </div>
  );
}
