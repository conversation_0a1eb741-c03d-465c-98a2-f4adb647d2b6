import React, { useEffect, useState } from "react";
import { ActionIcon, Button, Input, Popover, Text, Title } from "rizzui";
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input";
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";
import { DateInput, DatetimeInput } from "@/components/theme/ui/input-type/dates-input";
import { UseFormHandleSubmit, type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { useMasterRoomOptions } from "@/lib/hooks/useMasterRoom";
import { useMasterRoomTypesOptions } from "@/lib/hooks/useMasterRoomTypes";
import { FilterCondition, FilterGroup, ReservationsDto } from "@/client";
import { countDays } from "@/lib/helper/count-days";
import { PiArrowsLeftRight, PiListBold } from "react-icons/pi";
import ModalChangeRoom from "./modal-change-rooms";

export default function BookingRoom({
  register,
  errors,
  setValue,
  getValues,
  watch,
  readonly = false,
  handleSubmit,
  handlePost,
}: {
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<ReservationsDto>;
  readonly?: boolean;
  handleSubmit?: UseFormHandleSubmit<FieldValues, FieldValues>
  handlePost?: (e: FieldValues, command: string) => void;
}) {

  const [modalChangeRoom, setModalChangeRoom] = useState(false);

  const filterRooms: FilterGroup = {
    operator: "Or",
    conditions: [
      {
        fieldName: "roomStatus.name",
        operator: "Equals",
        value: "READY"
      },
      ...(watch("reservationDetails.0.roomId") ? [
        {
          fieldName: "id",
          operator: "Equals",
          value: watch("reservationDetails.0.roomId")
        }
      ] as FilterCondition[] : []),
    ]
  }

  // OPTIONS
  const { data: roomOptions } = useMasterRoomOptions(0, 1000, filterRooms);
  const { data: roomTypeOptions } = useMasterRoomTypesOptions(0, 1000);

  const paymentStatus = watch("reservationDetails.0.paymentStatus.code") as string;

  useEffect(() => {
    const selectedRoom = roomOptions?.find((opt) => opt.value == getValues('reservationDetails.0.roomId'));
    const days = getValues('reservationDetails.0.room.days') ? Number(getValues('reservationDetails.0.room.days')) : 1;
    setValue('reservationDetails.0.price', ((selectedRoom?.price as number) * days))

  }, [watch("reservationDetails.0.roomId"), watch("reservationDetails.0.room.days"), roomOptions]);

  useEffect(() => {
    if (getValues("reservationDetails.0.id")) {
      const days = countDays(new Date(getValues('reservationDetails.0.checkInDate') as string), new Date(getValues('reservationDetails.0.checkOutDate') as string));
      setValue("reservationDetails.0.room.days", days);
    }
  }, [watch("reservationDetails.0.id"), watch("reservationDetails.0.checkOutDate")]);

  return (
    <div className="p-4 py-2 border-2 rounded bg-gray-50 mt-5">
      <div className="flex justify-between">
        <Text as="p" className="mb-0 font-bold text-sm text-gray-800">
          Booking Room
        </Text>
        <div>
          <Popover placement="right-start">
            <Popover.Trigger>
              <ActionIcon title={"More Options"} variant="outline" className="p-0 h-5 w-5">
                {/* <PiDotsThreeOutlineVerticalFill className="h-5 w-5 text-gray-500" /> */}
                <PiListBold className="h-5 w-5 text-gray-500" />
              </ActionIcon>
            </Popover.Trigger>
            <Popover.Content className="z-0 min-w-[140px] px-0 py-2">
              {({ setOpen }) => (
                <div className="px-2 text-gray-900">
                  {(watch('reservationDetails.0.status.code') == "checkin") && (
                    <Button
                      variant="text"
                      className="group flex w-full items-center justify-start px-2 py-2 hover:bg-gray-100 focus:outline-none"
                      onClick={() => {
                        setModalChangeRoom(true);
                        setOpen(false);
                      }}
                      size="sm"
                    >
                      <PiArrowsLeftRight className="mr-2 h-5 w-5 text-gray-500 duration-300 group-hover:text-primary" />
                      Change Rooms
                    </Button>
                  )}
                </div>
              )}
            </Popover.Content>
          </Popover>
        </div>
      </div>
      <div className="grid grid-cols-1 gap-2">
        {/* ROOM INFORMATION */}
        <div className="grid grid-cols-12 gap-2">
          <DropdownInput
            className="col-span-2"
            label={"Type"}
            name={"reservationDetails.0.room.roomTypeId"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomTypeOptions}
            required={true}
            size="sm"
            readOnly={true}
          />
          <AutocompleteSelect
            className="col-span-1"
            label={"Room"}
            name={"reservationDetails.0.roomId"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomOptions?.filter((val) => val.roomTypeId == watch("reservationDetails.0.room.roomTypeId"))}
            required={true}
            size="sm"
            readOnly={true}
          />
          <DatetimeInput
            className="col-span-3"
            label={"Check In Date"}
            name={"reservationDetails.0.checkInDate"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            readOnly={true}
            size="sm"
          // maxDate={watch("reservationDetails.0.checkOutDate") as Date | undefined}
          />
          <DatetimeInput
            className="col-span-3"
            label={"Check Out Date"}
            name={"reservationDetails.0.checkOutDate"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            // readOnly={(paymentStatus == "pending" || readonly) ? true : false}
            readOnly={true}
            // inline={true}
            size="sm"
          // minDate={watch("reservationDetails.0.checkInDate") as Date | undefined}
          />
          <Input
            label="Days"
            size="sm"
            readOnly={true}
            // disabled={isLoading}
            placeholder="Please fill in Days"
            {...register("reservationDetails.0.room.days", { required: true })}
            // error={errors.reservationDetails?.[0].days? "Days is required" : undefined}
            className="col-span-1"
          />
          <CurrencyIDR
            label={"Rate"}
            name={"reservationDetails.0.price"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomOptions}
            required={true}
            size="sm"
            className="col-span-2"
            readOnly={(paymentStatus == "pending" || readonly) ? true : false}
          />
          {/* -{getValues('reservationDetails.0.room.price')}- */}
        </div>
      </div>
      {modalChangeRoom &&
        <ModalChangeRoom
          register={register}
          errors={errors}
          setValue={setValue}
          getValues={getValues}
          watch={watch}
          roomTypeOptions={roomTypeOptions ?? []}
          roomOptions={roomOptions ?? []}
          modal={modalChangeRoom}
          setModal={setModalChangeRoom}
          handleSubmit={handleSubmit}
          handlePost={handlePost}
        />
      }
    </div>
  );
}
