export const countDays = (startDate?: Date, endDate?: Date): number => {
  if (!startDate || !endDate) return 0;
  const START_HOUR = 14; // 2 PM
  function getReferenceDate(date: Date): Date {
    const reference = new Date(date);
    reference.setHours(START_HOUR, 0, 0, 0);

    // If the given time is before 2 PM, count it as part of the previous day
    if (date < reference) {
      reference.setDate(reference.getDate() - 1);
    }

    return reference;
  }

  const startRef = getReferenceDate(startDate);
  const endRef = getReferenceDate(endDate);

  const msPerDay = 1000 * 60 * 60 * 24;
  const dayDiff = Math.floor((endRef.getTime() - startRef.getTime()) / msPerDay);

  // Add 1 to include the starting day
  return Math.max(0, (dayDiff + 1));
}
