"use client";
import React, { useState, useEffect, useCallback } from "react";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import StatusColor from "@/components/container/color/statusColor";
import CustomTableV2 from "@/components/layout/custom-table/tableV2";
import ModalInvoice from "../../checkout/_component/modal-preview-invoice";
import { useApiAppReservationDetaislList } from "@/lib/hooks/useApiAppReservationDetails";
import { Button, Popover, Text, Tooltip } from "rizzui";
import { countDays } from "@/lib/helper/count-days";
import { countNights } from "@/lib/helper/count-nights";
import { PiArrowSquareOutBold, PiEye, PiInvoiceFill } from "react-icons/pi";
import { useRouter } from "next/navigation";
import { useMasterPaymentOptions } from "@/lib/hooks/useMasterPayments";
import { Skeleton } from "@/components/theme/ui/skeleton";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { formatDate } from "@/lib/helper/format-date";
import type { Column } from "@/interfaces/table/tableType";
import type {
  FilterCondition,
  FilterGroup,
  ReservationsDto,
  RoomTypeDto,
  SortInfo,
} from "@/client";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { fetchStatusOptions } from "@/lib/hooks/config/masterStatus";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";

export default function GuestInfo({
  wiithHeader = true,
  className,
  roomsId,
}: {
  wiithHeader?: boolean;
  className?: string;
  roomsId: string;
}) {
  const { can } = useGrantedPolicies();
  const [statusOptions, setStatusOptions] = useState<SelectOptionType[]>([]);
  const [sort, setSortConfig] = useState<SortInfo[] | undefined>();
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});
  const [selectFilters, setSelectFilters] = useState<Record<string, string[]>>(
    {},
  );
  const guestIdCondition = React.useMemo<FilterGroup["conditions"][number]>(
    () => ({
      fieldName: "roomId",
      operator: "Equals" as FilterGroup["conditions"][number]["operator"],
      value: roomsId,
    }),
    [roomsId],
  );
  const [dateFilters, setDateFilters] = useState<Record<string, string>>({});
  const handleFilterChange = useCallback(
    (newFilterGroup: FilterGroup) => {
      const otherConditions = newFilterGroup.conditions.filter(
        (cond) => cond.fieldName !== "roomId",
      );

      const mergedConditions = [guestIdCondition, ...otherConditions];

      setFilterGroup({
        operator: "And",
        conditions: mergedConditions,
      });
    },
    [guestIdCondition],
  );
  const [modalPreview, setModalPreview] = useState(false);
  const [paymentId, setPaymentId] = useState("");
  const [attachmentId, setAttachmentId] = useState("");
  const [resvDetailsId, setResvDetailsId] = useState<string>();
  const filterPayment: FilterGroup = {
    operator: "And",
    conditions: [
      ...((resvDetailsId
        ? [
            {
              fieldName: "paymentDetails.reservationDetails.id",
              operator: "Equals",
              value: resvDetailsId,
            },
          ]
        : []) as FilterCondition[]),
    ],
  };

  const [filterGroup, setFilterGroup] = useState<FilterGroup>({
    operator: "And",
    conditions: [guestIdCondition],
  });

  const [pagination, setPagiaton] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [data, setData] = useState<RoomTypeDto[]>([]);

  const { isLoading: getPaymentsLoading, data: getPayments } =
    useMasterPaymentOptions(1, 100, filterPayment);
  const { isLoading, data: dataSource } = useApiAppReservationDetaislList(
    pagination.pageIndex,
    pagination.pageSize,
    filterGroup,
    "",
    sort,
  );
  const router = useRouter();

  useEffect(() => {
    const fetchStatus = async () => {
      const options = await fetchStatusOptions("reservationDetails");
      setStatusOptions(options);
    };

    void fetchStatus();
  }, []);

  const filterSelectTable = {
    "status.name": statusOptions.map((option) => option.label),
  };

  useEffect(() => {
    if (dataSource?.items) {
      const mappedData = dataSource.items.map((item) => ({
        ...item,
        stays: countDays(
          new Date(item.checkInDate ?? 0),
          new Date(item.checkOutDate ?? 0),
        ),
        nights: countNights(
          new Date(item.checkInDate ?? 0),
          new Date(item.checkOutDate ?? 0),
        ),
        checkInDate: formatDate(item.checkInDate ?? "", "datetime"),
        checkOutDate: formatDate(item.checkOutDate ?? "", "datetime"),
        // statusBadge: (
        //   <>
        //     <StatusColor
        //       name={item?.status?.name}
        //       color={item?.status?.color}
        //     />
        //   </>
        // ),
        action: (
          <div className="flex items-center justify-center gap-2">
            {can("WismaApp.Reservation.Edit") &&
              (item?.status?.code === "checkin" ||
                item?.paymentStatus?.code === "pending") && (
                <Tooltip
                  size="sm"
                  content={
                    item?.status?.code === "checkin"
                      ? "Check Out"
                      : "Process Payments"
                  }
                  placement="top"
                >
                  <Button
                    size="sm"
                    className="bg-blue-500 text-white hover:bg-blue-700"
                    onClick={() =>
                      router.push(`/checkout/form_group/${item.id}`)
                    }
                  >
                    <PiArrowSquareOutBold className="h-4 w-4" />
                  </Button>
                </Tooltip>
              )}
            {can("WismaApp.Reservation.Edit") &&
              item?.status?.code === "checkout" &&
              item?.paymentStatus?.code === "paid" && (
                <Tooltip
                  size="sm"
                  content={"Preview Reservation"}
                  placement="top"
                >
                  <Button
                    size="sm"
                    isLoading={isLoading}
                    className="bg-gray-400 text-white hover:bg-gray-500"
                    // onClick={() => router.push(`/checkout/detail/${item.id}`)}
                    onClick={() =>
                      router.push(`/checkout/form_group/${item.id}`)
                    }
                  >
                    <PiEye className="h-4 w-4" />
                  </Button>
                </Tooltip>
              )}
            {can("WismaApp.Payment.View") && item?.paymentStatus && (
              <Popover placement="left-start">
                <Popover.Trigger>
                  <Button
                    size="sm"
                    className="bg-yellow-500 text-white hover:bg-yellow-600"
                    onClick={() => {
                      setResvDetailsId(item.id);
                    }}
                  >
                    <Tooltip size="sm" content="Payments" placement="top">
                      <span className="flex items-center">
                        <PiInvoiceFill className="h-4 w-4" />
                      </span>
                    </Tooltip>
                  </Button>
                </Popover.Trigger>
                <Popover.Content>
                  <Text className="mb-2 text-sm font-bold">Invoice List:</Text>
                  {getPaymentsLoading && <Skeleton className="h-2 w-full" />}
                  {!getPaymentsLoading &&
                    getPayments?.map((e) => (
                      <div key={e.value} className="mb-1 flex flex-col gap-2">
                        <div className="">
                          <Button
                            size="sm"
                            onClick={() => {
                              setModalPreview(true);
                              setPaymentId(String(e.value));
                              setAttachmentId(
                                String(e.paymentAttachments?.[0]?.id),
                              );
                              console.log(
                                "paymentId",
                                e.paymentAttachments?.[0]?.id,
                              );
                            }}
                          >
                            {e.paymentCode}:{" "}
                            <b>{formatCurrencyIDR(Number(e.grantTotal))}</b>
                          </Button>
                        </div>
                      </div>
                    ))}
                  {getPayments?.length === 0 && <Text>No invoice found</Text>}
                </Popover.Content>
              </Popover>
            )}
          </div>
        ),
      }));
      setData(mappedData);
    }
  }, [dataSource, isLoading, router]);

  const columns: Column[] = [
    {
      dataIndex: "guest.fullname",
      title: "Guest Name",
      filter: "text",
    },
    {
      dataIndex: "guest.identityNumber",
      title: "Identity",
      filter: "text",
    },
    {
      dataIndex: "room.roomNumber",
      title: "Room",
      filter: "text",
    },
    {
      dataIndex: "room.roomType.name",
      title: "Room Type",
      filter: "text",
    },
    {
      dataIndex: "checkInDate",
      title: "Checkin",
      filter: "date",
    },
    {
      dataIndex: "checkOutDate",
      title: "Checkout",
      filter: "date",
    },
    {
      dataIndex: "stays",
      title: "Stays",
      filter: "none",
    },
    {
      dataIndex: "nights",
      title: "Nights",
      filter: "none",
    },
    {
      dataIndex: "guest.phoneNumber",
      title: "Contact",
      filter: "text",
    },
    {
      dataIndex: "guest.companyName",
      title: "Company",
      filter: "text",
    },
    {
      dataIndex: "status.name",
      title: "Status",
      filter: "select" as const,
      render: (_: unknown, record: Record<string, unknown>) => {
        const r = record as ReservationsDto;
        return <StatusColor name={r.status?.name} color={r.status?.color} />;
      },
    },
    {
      dataIndex: "action",
      title: "Action",
      filter: "none",
    },
  ];

  if (!can("WismaApp.Reservation")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Guest Info" },
            { name: "List" },
          ]}
        />
      )}
      <div className="flex flex-col gap-4">
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTableV2
          columns={columns}
          dataSource={data ?? []}
          pageSize={pagination.pageSize}
          rowKey="id"
          isLoading={isLoading}
          totalCount={dataSource?.totalCount ?? 1}
          setPagiaton={setPagiaton}
          searchTerms={searchTerms}
          setSearchTerms={setSearchTerms}
          selectFilters={selectFilters}
          setSelectFilters={setSelectFilters}
          dateFilters={dateFilters}
          setDateFilters={setDateFilters}
          onFilterChange={handleFilterChange}
          filterSelectTable={filterSelectTable}
          onSortChange={setSortConfig}
          height="70vh"
        />
        {/* </div> */}
      </div>
      <ModalInvoice
        // isLoading={isLoading}
        modalPreview={modalPreview}
        setModalPreview={setModalPreview}
        paymentId={paymentId}
        attachmentId={attachmentId}
      />
    </div>
  );
}
