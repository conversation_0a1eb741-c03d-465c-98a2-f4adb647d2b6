apiVersion: apps/v1
kind: Deployment
metadata:
  name: wismafrontend
  namespace: wismafrontend-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wismafrontend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  revisionHistoryLimit: 3
  template:
    metadata:
      labels:
        app: wismafrontend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/hostname
                    operator: In
                    values:
                      - imprdapp28
      hostAliases:
        - ip: "**********"
          hostnames:
            - "identity.imip.co.id"
            - "api-wisma.imip.co.id"
      containers:
        - name: wismafrontend
          image: ${CI_REGISTRY_IMAGE}/wismafrontend:${CI_COMMIT_SHA}
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 300m
              memory: 512Mi
          env:
            - name: NODE_ENV
              value: production
            - name: REDIS_HOST
              value: "**********"
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: wismafrontend-prod-secrets
                  key: redis-password
                  optional: true
            - name: SESSION_SECRET
              valueFrom:
                secretKeyRef:
                  name: wismafrontend-prod-secrets
                  key: session-secret
            - name: SESSION_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: wismafrontend-prod-secrets
                  key: session-password
            # Runtime environment variables that might need to be different from build time
            - name: NEXT_PUBLIC_API_URL
              value: "https://api-wisma.imip.co.id"
            - name: NEXT_PUBLIC_APP_URL
              value: "https://wisma.imip.co.id"
            - name: NEXT_PUBLIC_IMIP_AUTH_URL
              value: "https://identity.imip.co.id"
---
apiVersion: v1
kind: Service
metadata:
  name: wismafrontend
  namespace: wismafrontend-prod
  annotations:
    service.beta.kubernetes.io/external-traffic: "OnlyLocal"
spec:
  selector:
    app: wismafrontend
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: wismafrontend-nodeport
  namespace: wismafrontend-prod
spec:
  selector:
    app: wismafrontend
  ports:
    - port: 80
      targetPort: 3000
      nodePort: 30091
  type: NodePort
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wismafrontend
  namespace: wismafrontend-prod
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
    - host: wisma.imip.co.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: wismafrontend
                port:
                  number: 80
