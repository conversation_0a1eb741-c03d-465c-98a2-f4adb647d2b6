﻿'use server'
import { sessionOptions } from '@/sessionOptions'
import {getIronSession, type IronSession} from 'iron-session'
import { cookies } from 'next/headers'
import { isTokenExpired } from './auth'
import { type RedisSession, createRedisInstance } from './redis'
import {type SessionData, defaultSession, getClientConfig} from './session-utils'
import * as client from 'openid-client'

// Define the error type for OAuth errors
interface OAuthError extends Error {
  code?: string;
  error?: string;
  error_description?: string;
  status?: number;
}

/**
 * Retrieves the current session, refreshing the access token if it has expired.
 * If an error occurs, it returns the default session.
 *
 * @returns {Promise<IronSession<SessionData>>} - The session data.
 *
 * @description
 * This function uses Redis to store and retrieve session data. The `createRedisInstance` function initializes a Redis client, and session data is managed using Redis keys.
 * This helps in maintaining session state across different instances of the application.
 *
 * The OpenID client is used to handle authentication and token management. The `refreshTokenGrant` method is used to refresh the access token when it has expired, ensuring that the session remains valid without requiring the user to re-authenticate.
 */
export async function getSession(): Promise<IronSession<SessionData>> {
  let session = await getIronSession<SessionData>( cookies(), sessionOptions)

  // Initialize a session with default values if it's empty
  if (!session.isLoggedIn && !session.code_verifier && !session.state) {
    session = Object.assign(session, defaultSession);
  }

  try {
    // Check if the access token is expired and we have a user ID
    if (session.access_token && isTokenExpired(session.access_token) &&
        session.userInfo && typeof session.userInfo === 'object' && 'sub' in session.userInfo) {
      try {
        const redisKey = `session:${session.userInfo.sub}`
        const redis = createRedisInstance()
        const clientConfig = await getClientConfig()

        // Retrieve session data from Redis
        const redisSessionData = await redis.get(redisKey)

        // If no Redis data is found, we can't refresh the token
        if (!redisSessionData) {
          throw new Error('No refresh token found in Redis');
        }

        const parsedSessionData = JSON.parse(redisSessionData) as RedisSession

        // If no refresh token is found, we can't refresh the token
        if (!parsedSessionData.refresh_token) {
          throw new Error('No refresh token found in session data');
        }

        // Refresh the access token using the refresh token
        const tokenSet = await client.refreshTokenGrant(clientConfig, parsedSessionData.refresh_token)

        // Update session with the new access token
        session.access_token = tokenSet.access_token
        await session.save()

        // Update Redis with the new session data
        const newRedisSessionData = {
          access_token: tokenSet.access_token,
          refresh_token: tokenSet.refresh_token ?? parsedSessionData.refresh_token, // Keep old refresh token if new one is not provided
        } as RedisSession

        await redis.set(redisKey, JSON.stringify(newRedisSessionData))
        await redis.quit()
      } catch (error: unknown) {
        console.error('Error refreshing token:', error);

        // Type guard for OAuth errors
        const refreshError = error as OAuthError;

        // If the refresh token is invalid or expired, clear the session
        if (
          refreshError.code === 'OAUTH_RESPONSE_BODY_ERROR' &&
          (refreshError.error === 'invalid_grant' || refreshError.error === 'invalid_token')
        ) {
          // console.log('Refresh token is invalid or expired, clearing session');

          // Clear the session data
          session.isLoggedIn = false;
          session.access_token = undefined;
          session.userInfo = undefined;

          // Save the cleared session
          await session.save();

          // Try to clear Redis data if possible
          const userInfo = session.userInfo as { sub: string } | undefined;
          if (userInfo?.sub) {
            try {
              const redisKey = `session:${userInfo.sub}`;
              const redis = createRedisInstance();
              await redis.del(redisKey);
              await redis.quit();
            } catch (redisError) {
              console.error('Error clearing Redis data:', redisError);
            }
          }
        }

        // Let the outer catch handle other errors
        throw error;
      }
    }
    return session
  } catch (error) {
    console.error('Error getting session:', error)

    // Don't modify the session if we're in a client component
    try {
      // Only reset the session if we're in a server context
      if (typeof window === 'undefined') {
        // Return a logged-out session in case of an error
        session.isLoggedIn = false;
        session.access_token = undefined;
        session.userInfo = undefined;
        await session.save();
      }
    } catch (saveError) {
      console.error('Error saving session:', saveError);
    }

    return session;
  }
}
