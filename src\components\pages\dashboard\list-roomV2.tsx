import React, { useEffect, useState } from "react";
import NavigationMenu from "./navigasi-menu";
import ModalUpdateRoomStatus from "./modal/room-status";
import SkeletonListRoom from "@/components/container/loading/skeleton-listRoom";
import { contrastingColor } from "@/lib/helper/format-color";
import { generateRoomRanges } from "@/lib/helper/generate-room-ranges";
import { useMasterRooms } from "@/lib/hooks/useMasterRoom";
import { Popover } from "rizzui";
import type { FilterGroup, RoomDto } from "@/client";
import { useApiAppReservationDetailsList } from "@/lib/hooks/useApiAppReservationDetails";
import {
  PiCakeFill,
  PiCalendarCheckBold,
  PiCalendarDotsBold,
  PiPersonSimpleRunBold,
} from "react-icons/pi";
import { isToday } from "@/lib/helper/is-today";
import { isBirthday } from "@/lib/helper/is-birthday";
import { isComing } from "@/lib/helper/is-cooming";

export default function ListRoomV2({
  selectedStatus,
  selectedType,
}: {
  selectedStatus: string | null;
  selectedType: string | null;
}) {
  const [isModalOpen, setModalOpen] = useState(false);
  const [modalRoomData, setModalRoomData] = useState<RoomDto | null>(null);
  const [dataRooms, setDataRooms] = useState<RoomDto[]>([]);
  const [openPopoverId, setOpenPopoverId] = useState<string | null>(null);
  const { isLoading, data } = useMasterRooms(1, 1000);

  const defaultFilter: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "status.code",
        operator: "In",
        value: "checkin,reserved",
      },
    ],
  };
  const { data: dataReservasi } = useApiAppReservationDetailsList(
    1,
    200,
    defaultFilter,
    "",
    [],
  );
  // console.log("dataReservasi", dataReservasi);

  const resvReserved = dataReservasi?.items?.filter(
    (e) => e.status?.code == "reserved",
  );
  const resvCheckin = dataReservasi?.items?.filter(
    (e) => e.status?.code == "checkin",
  );

  useEffect(() => {
    if (data?.items) {
      const sortedData = [...data.items].sort((a, b) => {
        return Number(a.roomNumber ?? 0) - Number(b.roomNumber ?? 0);
      });

      setDataRooms(
        sortedData.map((item) => ({
          ...item,
          id: item.id ?? "",
          roomNumber: item.roomNumber ?? "",
          roomCode: item.roomCode ?? "",
          roomTypeName: item.roomType?.name ?? "",
          size: item.size ?? "",
          price: item.price ?? 0,
          roomStatusName: item.roomStatus?.name ?? "",
          information: item.information ?? "",
        })),
      );
    }
  }, [data]);

  const roomRanges = isLoading
    ? Array.from({ length: 3 }, (_, index) => ({
        title: `Loading ${index + 1}`,
        range: [0, 0],
      }))
    : generateRoomRanges(
        dataRooms.map((item) => ({ roomNumber: item.roomNumber ?? "" })),
      );

  if (isLoading) {
    return <SkeletonListRoom roomRanges={roomRanges.length} />;
  }

  return (
    <div className="flex flex-col sm:gap-2 md:gap-2 xl:gap-3">
      {roomRanges.map(({ title, range }) => {
        const filteredRooms = dataRooms.filter(
          (item) =>
            Number(item?.roomNumber ?? 0) >= (range[0] ?? 0) &&
            Number(item.roomNumber ?? 0) <= (range[1] ?? 0) &&
            (selectedStatus === null ||
              item.roomStatus?.name === selectedStatus) &&
            (selectedType === null || item.roomType?.alias === selectedType),
        );

        return (
          <div key={title} className="mb-0 2xl:mb-2">
            <div className="m- flex flex-wrap gap-1 2xl:gap-x-4 2xl:gap-y-2">
              {filteredRooms.map((item) => {
                const borderColor = contrastingColor(
                  item.roomStatus?.color ?? "#000000",
                  -50,
                );

                // const reservationIds: string[] =
                //   dataReservasi?.items
                //     ?.filter((resv) => resv.roomId === item.id)
                //     .map((resv) => resv.id)
                //     .filter((id): id is string => typeof id === "string") ?? [];
                const reservationInfos =
                  dataReservasi?.items
                    ?.filter((resv) => resv.roomId === item.id)
                    .map((resv) => ({
                      id: resv.id,
                      guestName: resv.guest?.fullname ?? "",
                    }))
                    .filter(
                      (info): info is { id: string; guestName: string } =>
                        typeof info.id === "string",
                    ) ?? [];

                const itemIdStr = String(item.id);

                // VARIABLES FOR ICONS
                const reservedList = resvReserved?.filter(
                  (e) => e.roomId == item.id,
                );
                const checkinList = resvCheckin?.filter(
                  (e) => e.roomId == item.id,
                );

                // GET CONDITIONS
                // const isCheckinTomorrow = reservedList?.some((e) => e.checkInDate && isTomorrow(e.checkInDate)) && item.roomStatus?.code == 'VR'
                const isReserved = reservedList?.some(
                  (e) => e.checkInDate && isComing(e.checkInDate),
                );
                const isCheckinToday =
                  reservedList?.some(
                    (e) => e.checkInDate && isToday(e.checkInDate),
                  ) && item.roomStatus?.code == "VR";
                const isCheckoutToday =
                  checkinList?.some(
                    (e) => e.checkOutDate && isToday(e.checkOutDate),
                  ) && item.roomStatus?.code == "OVC";
                const isGuestBirthday = checkinList?.some((e) =>
                  isBirthday(e.guest?.dateOfBirthday),
                );

                return (
                  <Popover
                    key={itemIdStr}
                    placement="right"
                    isOpen={openPopoverId === itemIdStr}
                    setIsOpen={(open) =>
                      setOpenPopoverId(open ? itemIdStr : null)
                    }
                  >
                    <Popover.Trigger>
                      <div
                        onContextMenu={(e) => {
                          e.preventDefault();
                          setOpenPopoverId(itemIdStr);
                        }}
                        onClick={(e) => e.preventDefault()}
                        className="group relative flex w-[30px] items-center justify-between rounded-lg border px-1 shadow sm:w-[40px] md:w-[50px] xl:w-[60px] 2xl:w-[70px] 2xl:p-2 hd:w-[80px] 3xl:w-[90px]"
                        style={{
                          backgroundColor: item.roomStatus?.color ?? "#000000",
                          color: borderColor,
                          borderColor: borderColor,
                          cursor: "context-menu",
                        }}
                      >
                        <div className="flex w-full flex-col gap-0">
                          <div className="flex items-center justify-between gap-0">
                            <span className="text-[5px] font-light sm:text-[6px] xl:text-[9px] 2xl:text-sm">
                              {item.roomNumber}
                            </span>
                            <span className="text-[5px] font-light sm:text-[6px] xl:text-[9px] 2xl:text-sm">
                              {item.roomStatus?.code}
                            </span>
                          </div>
                          <div className="flex items-center justify-between gap-0">
                            <span
                              className="text-[5px] font-light text-gray-500 sm:text-[6px] xl:text-[9px] 2xl:text-sm"
                              style={{ color: borderColor }}
                            >
                              {item.roomType?.alias}
                            </span>
                            <span className="flex text-[5px] font-light sm:text-[6px] xl:text-[9px] 2xl:text-sm">
                              {isCheckinToday && (
                                <PiCalendarCheckBold className="h-5 w-5" />
                              )}
                              {isReserved && (
                                <PiCalendarDotsBold className="h-5 w-5" />
                              )}
                              {isCheckoutToday && (
                                <PiPersonSimpleRunBold className="h-5 w-5" />
                              )}
                              {isGuestBirthday && (
                                <PiCakeFill className="h-5 w-5" />
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Popover.Trigger>
                    <Popover.Content className="p-0">
                      <NavigationMenu
                        roomId={item.id!}
                        roomStatus={item.roomStatus}
                        dataReservasi={resvCheckin}
                        reservationInfos={reservationInfos}
                        onUpdateRoomStatus={() => {
                          setModalRoomData(item);
                          setModalOpen(true);
                        }}
                      />
                    </Popover.Content>
                  </Popover>
                );
              })}
            </div>
          </div>
        );
      })}
      {isModalOpen && modalRoomData && (
        <ModalUpdateRoomStatus
          roomId={modalRoomData.id}
          roomNumber={modalRoomData.roomNumber}
          roomCode={modalRoomData.roomCode}
          roomType={modalRoomData.roomType?.name ?? ""}
          roomStatus={
            modalRoomData.roomStatus
              ? {
                  value: modalRoomData.roomStatus.id ?? "",
                  label: modalRoomData.roomStatus.name ?? "",
                }
              : undefined
          }
          roomSize={modalRoomData.size}
          roomInformation={modalRoomData.information}
          roomTypeId={modalRoomData.roomTypeId}
          roomStatusId={modalRoomData.roomStatusId}
          roomPrice={modalRoomData.price}
          isModalOpen={isModalOpen}
          setModalOpen={() => setModalOpen(false)}
        />
      )}
    </div>
  );
}
