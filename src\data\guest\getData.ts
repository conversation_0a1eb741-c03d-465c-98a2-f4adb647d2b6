"use server";
import { type GuestResponse } from "@/interfaces/reservations/guest";
import axios from "axios";


export const getDataGuests = () => {
  return axios.get<GuestResponse>(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/guest`)
    .then((response) => {
      // console.log('GET getDataGuests', response.data);
      return response.data;
    })
    .catch((error) => {
      console.error("Error fetching reservations:", error);
      throw error;
    });
}

