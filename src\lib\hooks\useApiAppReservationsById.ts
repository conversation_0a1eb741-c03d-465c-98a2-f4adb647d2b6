﻿import { getApiAppReservationsById } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";

export const useApiAppReservationsById = (
  id: string,
  refreshId?: string
) => {
  return useQuery({
    queryKey: [QueryNames.GetReservationsById, id, refreshId],
    queryFn: async () => {
      const { data } = await getApiAppReservationsById({
        path: { id } // Replace with your reservation ID
      });
      return data;
    },
    enabled: id !== "", // 👈 Query hanya jalan jika id tidak kosong
  });
};