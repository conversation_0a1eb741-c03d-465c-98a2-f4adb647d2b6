import { type TForm } from "@/lib/custom-types";
import { useEffect, useState } from "react";
import {
  type Control,
  FieldValues,
  UseFieldArrayRemove,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  useFieldArray,
} from "react-hook-form";
import { PiTrash } from "react-icons/pi";
import { Button, Checkbox, Input, Select, Text, type SelectOption } from "rizzui";

export function MasterReportExcelHeader({
  register,
  setValue,
  index,
  remove,
  control
}: {
  register: UseFormRegister<FieldValues>;
  setValue?: UseFormSetValue<FieldValues>;
  getValues?: UseFormGetValues<FieldValues>;
  control?: Control<FieldValues>;
  index?: number;
  remove?: UseFieldArrayRemove;
}) {

  return (
    <div>
      <Text as="b" className="col-span-4">
        Excel Header Config
      </Text>
      <div className="col-span-full mb-1 grid grid-cols-4 gap-2 rounded-lg text-xs">
        <>
          <Input
            label={"Title"}
            placeholder="Title"
            size="sm"
            {...register(`excelHeaderConfig.title`,)}
          />
          <Input
            label={"Sub Title"}
            placeholder="Sub Title"
            size="sm"
            {...register(`excelHeaderConfig.subTitle`,)}
          />
          <Input
            label={"Date Format"}
            placeholder="Date Format"
            size="sm"
            {...register(`excelHeaderConfig.dateFormat`,)}
          />
          <div>
            <Text as="p" className={"mb-1.5 font-medium text-xs"}>
              Show Generated Date
            </Text>
            <Checkbox
              size="lg"
              {...register('excelHeaderConfig.showGeneratedDate')}
            />
          </div>
          <Input
            label={"Font Size"}
            placeholder="Font Size"
            size="sm"
            {...register(`excelHeaderConfig.titleStyle.fontSize`,)}
          />
          <Input
            label={"Horizontal Alignment"}
            placeholder="Horizontal Alignment"
            size="sm"
            {...register(`excelHeaderConfig.titleStyle.horizontalAlignment`,)}
          />
          <Input
            label={"Font Color"}
            placeholder="Font Color"
            size="sm"
            {...register(`excelHeaderConfig.titleStyle.fontColor`,)}
          />
          <div>
            <Text as="p" className={"mb-1.5 font-medium text-xs"}>
              Bold
            </Text>
            <Checkbox
              size="lg"
              {...register('excelHeaderConfig.titleStyle.bold')}
            />
          </div>
        </>
      </div>
    </div>
  );
}
