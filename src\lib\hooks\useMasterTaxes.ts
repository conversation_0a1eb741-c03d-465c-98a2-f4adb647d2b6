﻿import { postApiAppTaxList, type FilterGroup, type SortInfo } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import type { SelectOption } from "rizzui";

export const useMasterTaxes = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [
      QueryNames.postApiAppTaxList,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
    ],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppTaxList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      return data;
    },
  });
};

export const useMasterTaxesOptions = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [
      QueryNames.postApiAppTaxList,
      pageIndex,
      pageSize,
      filter,
      sorting,
    ],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppTaxList({
        body: {
          sorting: sorting,
          page: 1,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      // GENERATE OPTIONS
      const options: SelectOption[] =
        data?.items?.map((val) => ({
          label: `${val.name}`,
          value: val.id ?? "",
          ...val,
        })) ?? [];

      // RETURN OPTIONS
      return options;
    },
  });
};
