export default function DicesIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#68E7FD"
        d="m5.138 12.932-3.321-1.995 3.32-1.996 3.322 1.996-3.321 1.996Z"
      />
      <path
        fill="#00C1FC"
        d="M1.817 10.938v4.088l3.32 1.996v-4.089l-3.32-1.996Z"
      />
      <path
        fill="#00DCFC"
        d="m8.459 10.938-3.321 1.995v4.089l3.32-1.996v-4.089Z"
      />
      <path
        fill="#68E7FD"
        d="m14.861 12.932-3.321-1.995 3.321-1.996 3.321 1.996-3.321 1.996Z"
      />
      <path
        fill="#00C1FC"
        d="M11.54 10.938v4.088l3.321 1.996v-4.089l-3.321-1.996Z"
      />
      <path
        fill="#00DCFC"
        d="m18.182 10.938-3.32 1.995v4.089l3.32-1.996v-4.089Z"
      />
      <path
        fill="#00E499"
        d="m10 15.62-3.321-1.996 3.32-1.995 3.322 1.995L10 15.62Z"
      />
      <path
        fill="#00B594"
        d="M6.679 13.621v4.089l3.32 1.996v-4.09l-3.32-1.995Z"
      />
      <path
        fill="#00D38E"
        d="M13.321 13.621 10 15.617v4.088l3.321-1.995v-4.089Z"
      />
      <path
        fill="#FF94BE"
        d="M10 4.284 6.679 2.289 9.999.293l3.322 1.996L10 4.284Z"
      />
      <path fill="#FF5597" d="M6.679 2.29v4.088l3.32 1.996v-4.09L6.68 2.29Z" />
      <path fill="#FF77AB" d="M13.321 2.29 10 4.284v4.089l3.321-1.996V2.289Z" />
      <path
        fill="#FFEA76"
        d="M4.74 7.159 1.417 5.164 4.74 3.168 8.06 5.164 4.74 7.159Z"
      />
      <path fill="#FFC25E" d="M1.418 5.16v4.09l3.321 1.995v-4.09L1.42 5.16Z" />
      <path fill="#FFD250" d="M8.06 5.16 4.74 7.156v4.089l3.32-1.996V5.16Z" />
      <path
        fill="#FFEA76"
        d="m15.26 7.159-3.32-1.995 3.32-1.996 3.321 1.996-3.32 1.995Z"
      />
      <path fill="#FFC25E" d="M11.94 5.16v4.09l3.32 1.995v-4.09L11.94 5.16Z" />
      <path fill="#FFD250" d="m18.58 5.16-3.32 1.996v4.089l3.32-1.996V5.16Z" />
      <path
        fill="#FFAB56"
        d="M10 9.843 6.679 7.847l3.32-1.995 3.322 1.995L10 9.843Z"
      />
      <path fill="#FF7E3D" d="M6.679 7.844v4.089l3.32 1.995V9.84L6.68 7.844Z" />
      <path fill="#FF982A" d="M13.321 7.844 10 9.839v4.09l3.321-1.996v-4.09Z" />
      <path
        fill="#000"
        d="m18.732 4.91-3.321-1.995a.293.293 0 0 0-.302 0l-1.495.899V2.288a.29.29 0 0 0-.142-.25L10.15.041a.293.293 0 0 0-.302 0l-.925.556a.293.293 0 1 0 .302.502L10 .635l2.752 1.653L10 3.942 7.248 2.288l.98-.59a.293.293 0 0 0-.301-.501l-1.4.84a.293.293 0 0 0-.141.251v1.526L4.89 2.915a.293.293 0 0 0-.302 0l-3.32 1.996a.293.293 0 0 0-.143.25v4.09c0 .102.054.198.143.25l1.186.713-.788.474a.293.293 0 0 0-.142.25v4.09c0 .102.054.198.142.25l.847.51a.293.293 0 1 0 .302-.503l-.705-.423v-3.405l.162.097L4.845 13.1v3.405l-1.035-.622a.293.293 0 0 0-.302.503l1.48.888a.294.294 0 0 0 .301 0l1.097-.659v1.097c0 .102.054.198.142.25l3.32 1.996a.293.293 0 0 0 .303 0l3.32-1.995a.293.293 0 0 0 .143-.251v-1.097l1.097.66a.294.294 0 0 0 .301 0l3.321-1.996a.293.293 0 0 0 .142-.251v-1.534a.293.293 0 1 0-.585 0v1.368l-2.736 1.643V13.1l2.574-1.546.161-.097v.863a.293.293 0 1 0 .586 0v-1.381a.293.293 0 0 0-.142-.251l-.787-.474 1.186-.712a.293.293 0 0 0 .142-.251V5.16c0-.099-.05-.194-.142-.25ZM10 15.277l-2.752-1.653.835-.503 1.766 1.061a.293.293 0 0 0 .302 0l1.765-1.06.836.502L10 15.276Zm0-9.084 2.752 1.653L10 9.5 7.248 7.845 10 6.192ZM5.032 7.323l2.723-1.636.012-.007v1.17l-1.24.744a.293.293 0 0 0-.141.252v2.07l-1.354.812V7.323Zm1.94 4.446V8.363l2.735 1.644v3.405L6.972 11.77Zm3.32 1.643v-3.405l2.736-1.644v3.406l-2.735 1.643Zm3.322-5.567a.294.294 0 0 0-.142-.25l-1.24-.746V5.68l.589.354 2.146 1.29v3.405l-1.353-.813v-2.07Zm1.646-4.337 2.752 1.654-2.752 1.653-2.752-1.653 2.752-1.654Zm-3.472 1.403a.293.293 0 0 0-.142.25v1.336l-1.353-.813V4.45l2.735-1.644v1.36l-1.24.745ZM6.972 2.806 9.707 4.45v1.234l-1.354.813V5.162a.293.293 0 0 0-.142-.251l-1.24-.745v-1.36Zm-2.233.702 2.753 1.654-2.753 1.653-2.752-1.653L4.74 3.508ZM1.711 5.68l2.736 1.643v3.405L1.71 9.085V5.68Zm2.878 5.817c.071.043.146.043.142.042a.293.293 0 0 0 .16-.042l1.495-.898v1.244l-1.248.75-2.752-1.654.636-.383 1.567.941Zm.842 5.008V13.1l1.31-.787.774.465-.987.593a.293.293 0 0 0-.142.251v2.31l-.955.573Zm1.54-2.365.59.354 2.146 1.29v3.405l-2.735-1.643V14.14Zm6.057 3.406-2.735 1.643v-3.405l2.146-1.29.589-.354v3.406Zm.586-3.924a.304.304 0 0 0-.142-.25l-.987-.594.774-.465 1.31.787v3.405l-.955-.573v-2.31Zm4-2.683-2.753 1.653-1.247-.75V10.6l1.495.898a.294.294 0 0 0 .302 0l1.566-.94.637.382Zm.674-1.854-2.735 1.643V7.323l2.147-1.29.588-.354v3.406Z"
      />
    </svg>
  );
}
