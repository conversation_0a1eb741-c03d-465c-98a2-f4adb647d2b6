export default function TicketIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="45"
      height="44"
      viewBox="0 0 45 44"
      fill="none"
      {...props}
    >
      <path
        d="M33.8063 5.09016C25.3522 5.09016 19.5466 0.710938 15.473 0.710938C11.3993 0.710938 2.84375 3.05272 2.84375 21.793C2.84375 40.5334 14.3534 43.2834 18.8341 43.2834C39.816 43.2846 48.7785 5.09016 33.8063 5.09016Z"
        fill="#FFFCE2"
      />
      <path
        d="M37.3255 6.11198C36.3147 6.11198 35.4922 5.28942 35.4922 4.27865C35.4922 3.26787 36.3147 2.44531 37.3255 2.44531C38.3363 2.44531 39.1589 3.26787 39.1589 4.27865C39.1589 5.28942 38.3363 6.11198 37.3255 6.11198ZM37.3255 3.66753C36.9882 3.66753 36.7144 3.94131 36.7144 4.27865C36.7144 4.61598 36.9882 4.88976 37.3255 4.88976C37.6629 4.88976 37.9366 4.61598 37.9366 4.27865C37.9366 3.94131 37.6629 3.66753 37.3255 3.66753Z"
        fill="#C9B500"
      />
      <path
        d="M33.3524 33.309H10.7413C9.39076 33.309 8.29688 32.2151 8.29688 30.8646V28.7257H8.90799C10.5959 28.7257 11.9635 27.358 11.9635 25.6701C11.9635 23.9823 10.5959 22.6146 8.90799 22.6146H8.29688V20.4757C8.29688 19.1251 9.39076 18.0312 10.7413 18.0312H33.3524C34.703 18.0312 35.7969 19.1251 35.7969 20.4757V22.6146H35.1858C33.4979 22.6146 32.1302 23.9823 32.1302 25.6701C32.1302 27.358 33.4979 28.7257 35.1858 28.7257H35.7969V30.8646C35.7969 32.2139 34.703 33.309 33.3524 33.309Z"
        fill="white"
      />
      <path
        d="M33.6597 18.0286L31.4561 11.5252C31.2935 11.0424 30.8364 10.6953 30.2986 10.6953C30.1483 10.6953 30.0041 10.7222 29.8708 10.7723L10.4375 18.0286H33.6597Z"
        fill="#FFF59A"
      />
      <path
        d="M11.0469 30.8646V28.7257H11.658C13.3459 28.7257 14.7135 27.358 14.7135 25.6701C14.7135 23.9823 13.3459 22.6146 11.658 22.6146H11.0469V20.4757C11.0469 19.1251 12.1408 18.0312 13.4913 18.0312H10.7413C9.39076 18.0312 8.29688 19.1251 8.29688 20.4757V22.6146H8.90799C10.5959 22.6146 11.9635 23.9823 11.9635 25.6701C11.9635 27.358 10.5959 28.7257 8.90799 28.7257H8.29688V30.8646C8.29688 32.2151 9.39076 33.309 10.7413 33.309H13.4913C12.1408 33.309 11.0469 32.2139 11.0469 30.8646Z"
        fill="#D5DBE1"
      />
      <path
        d="M31.4561 11.5252C31.2935 11.0424 30.8364 10.6953 30.2986 10.6953C30.1483 10.6953 30.0041 10.7222 29.8708 10.7723L10.4375 18.0286H15.6319L31.6357 12.0532L31.4561 11.5252Z"
        fill="#C9B500"
      />
      <path
        d="M33.355 34.2205H10.7439C8.88981 34.2205 7.38281 32.7135 7.38281 30.8594V28.7205C7.38281 28.2145 7.79348 27.8038 8.29948 27.8038H8.91059C10.09 27.8038 11.0495 26.8444 11.0495 25.6649C11.0495 24.4855 10.09 23.526 8.91059 23.526H8.29948C7.79348 23.526 7.38281 23.1154 7.38281 22.6094V20.4705C7.38281 18.6164 8.88981 17.1094 10.7439 17.1094H33.355C35.2091 17.1094 36.7161 18.6164 36.7161 20.4705V22.6094C36.7161 23.1154 36.3055 23.526 35.7995 23.526H35.1884C34.0089 23.526 33.0495 24.4855 33.0495 25.6649C33.0495 26.2357 33.2719 26.7735 33.6765 27.1768C34.081 27.5802 34.6176 27.8026 35.1871 27.8026H35.7983C36.3043 27.8026 36.7149 28.2133 36.7149 28.7193V30.8582C36.7161 32.7135 35.2091 34.2205 33.355 34.2205ZM9.21615 29.6262V30.8594C9.21615 31.7015 9.90181 32.3872 10.7439 32.3872H33.355C34.1971 32.3872 34.8828 31.7015 34.8828 30.8594V29.6249C33.938 29.554 33.058 29.1507 32.3797 28.4736C31.6293 27.7232 31.2161 26.7258 31.2161 25.6649C31.2161 23.5774 32.8344 21.8614 34.8828 21.7037V20.4705C34.8828 19.6284 34.1971 18.9427 33.355 18.9427H10.7439C9.90181 18.9427 9.21615 19.6284 9.21615 20.4705V21.7037C11.2646 21.8602 12.8828 23.5774 12.8828 25.6649C12.8828 27.7525 11.2646 29.4685 9.21615 29.6262Z"
        fill="black"
      />
      <path
        d="M17.1562 17.4141H18.9896V20.4696H17.1562V17.4141Z"
        fill="black"
      />
      <path
        d="M17.1562 23.2188H18.9896V28.1076H17.1562V23.2188Z"
        fill="black"
      />
      <path
        d="M17.1562 30.8594H18.9896V33.9149H17.1562V30.8594Z"
        fill="black"
      />
      <path
        d="M33.6553 18.9473C33.2728 18.9473 32.9171 18.7065 32.7875 18.3252L30.5839 11.8217C30.5484 11.7178 30.4458 11.6139 30.2942 11.6139L10.7533 18.8898C10.2815 19.067 9.75232 18.8275 9.57387 18.352C9.39665 17.8778 9.63743 17.3498 10.1117 17.1726L29.545 9.91626C30.6462 9.50682 31.943 10.1228 32.3194 11.2338L34.5231 17.7373C34.6856 18.2176 34.4278 18.737 33.9487 18.8996C33.8521 18.9314 33.7531 18.9473 33.6553 18.9473Z"
        fill="black"
      />
    </svg>
  );
}
