import React from "react";
import { PiPlusCircle } from "react-icons/pi";

interface ButtonPlusProps {
  onClick: () => void;
  disabled?: boolean;
  type?: "button" | "submit" | "reset" | undefined;
}

export default function ButtonPlus({ onClick, disabled, type }: ButtonPlusProps) {
  return (
    <button onClick={onClick} disabled={disabled} type={type}>
      <PiPlusCircle className="h-5 w-5 text-green-500 transition-transform duration-200 hover:scale-125 hover:text-green-700" />
    </button>
  );
}
