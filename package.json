{"name": "ef-core", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev", "lint": "next lint", "gen-api": "curl -k https://api-wisma-dev.imip.co.id/swagger/v1/swagger.json -o swagger.json && npx @hey-api/openapi-ts -i swagger.json -o src/client -c @hey-api/client-fetch", "start": "next start", "openapi-ts": "openapi-ts"}, "dependencies": {"@auth/core": "^0.30.0", "@auth/prisma-adapter": "^1.4.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.26.12", "@handsontable/react": "^15.2.0", "@handsontable/react-wrapper": "^15.3.0", "@headlessui/react": "^1.7.19", "@hookform/resolvers": "^3.3.4", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^5.10.2", "@radix-ui/react-slot": "^1.0.2", "@t3-oss/env-nextjs": "^0.10.1", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.25.0", "@trpc/client": "next", "@trpc/react-query": "next", "@trpc/server": "next", "@types/prismjs": "^1.26.4", "@types/react-html-parser": "^2.0.6", "@uploadthing/react": "^6.5.1", "autoprefixer": "^10.4.19", "axios": "^1.6.8", "clsx": "^2.1.1", "dayjs": "^1.11.10", "file-type": "^19.0.0", "flag-icons": "^7.5.0", "framer-motion": "^12.5.0", "handsontable": "^15.2.0", "i18next": "^25.2.1", "i18next-http-backend": "^3.0.2", "ioredis": "^5.6.1", "iron-session": "^8.0.4", "jotai": "^2.8.0", "jwt-decode": "^4.0.0", "laravel-echo": "^1.18.0", "loadash": "^1.0.0", "lodash": "^4.17.21", "nanoid": "^5.0.7", "next": "^14.2.1", "next-auth": "5.0.0-beta.17", "next-i18next": "^15.4.2", "next-themes": "^0.3.0", "nextjs-toploader": "^1.6.12", "openid-client": "^6.4.2", "pretty-bytes": "^6.1.1", "prismjs": "^1.29.0", "pspdfkit": "^2024.8.2", "pusher-js": "8.4.0-rc2", "rc-pagination": "^4.0.4", "rc-rate": "^2.12.0", "rc-slider": "^10.6.2", "rc-table": "^7.45.4", "react": "18.2.0", "react-big-calendar": "^1.11.6", "react-datepicker": "^6.9.0", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.3", "react-hot-toast": "^2.4.1", "react-html-parser": "^2.0.2", "react-i18next": "^15.5.3", "react-icons": "^5.1.0", "react-quill": "^2.0.0", "react-select": "^5.8.0", "react-simple-code-editor": "^0.13.1", "react-use": "^17.5.0", "recharts": "^2.12.6", "rimraf": "^5.0.5", "rizzui": "0.8.4", "server-only": "^0.0.1", "sharp": "^0.33.3", "simplebar-react": "2.3.3", "superjson": "^2.2.1", "sweetalert": "^2.1.2", "sweetalert2-react": "^0.8.3", "tailwind-merge": "^2.3.0", "uploadthing": "^6.10.1", "uuid": "^9.0.1", "zod": "^3.23.3"}, "devDependencies": {"@hey-api/client-fetch": "^0.10.0", "@hey-api/openapi-ts": "^0.66.5", "@types/eslint": "^8.56.2", "@types/lodash": "^4.17.1", "@types/node": "^20.11.20", "@types/react": "^18.2.57", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "eslint-config-next": "^14.1.3", "postcss": "^8.4.34", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "prisma": "^5.10.2", "tailwindcss": "^3.4.1", "typescript": "^5.4.2"}, "ct3aMetadata": {"initVersion": "7.32.0"}}