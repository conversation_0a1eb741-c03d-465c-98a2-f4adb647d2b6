import { InputProps } from "@/interfaces/form/inputType";
import React, { useState, useEffect } from "react";
import { PiCaretDownBold, PiCheckBold } from "react-icons/pi";
import { Button, SelectOption } from "rizzui";

type OptionType = {
	value: string;
	label: string;
};

interface OptionProps {
	disable: boolean;
	value?: string[];
	options: SelectOption[];
	// onChange?: (selectedOptions: string[]) => void;
	maxShown?: number;
}

export default function MultipleSelect({
	label,
	disable,
	options,
	maxShown = 3,
	setValue,
	getValues,
	name,
}: InputProps & OptionProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

	const toggleDropdown = () => setIsOpen(!isOpen);

	const handleOptionClick = (value: string) => {
		const newSelectedOptions = selectedOptions.includes(value)
			? selectedOptions.filter((item) => item !== value)
			: [...selectedOptions, value];

		setSelectedOptions(newSelectedOptions);
	};

	const handleOutsideClick: EventListener = (event: Event) => {
		const mouseEvent = event as MouseEvent;
		const target = mouseEvent.target as HTMLElement;
		if (!target.closest(".dropdown")) {
			setIsOpen(false);
		}
	};

	useEffect(() => {
		document.addEventListener("mousedown", handleOutsideClick);
		return () => {
			document.removeEventListener("mousedown", handleOutsideClick);
		};
	}, []);

	useEffect(() => {
		if(isOpen == false){
			setValue(name, selectedOptions);
		}
	}, [isOpen]);

	useEffect(() => {
		const val = getValues(name) as string[] ?? []
		setSelectedOptions(val);
	}, [getValues(name)]);

	return (
		<div className="dropdown relative">
			<label className="mb-1.5 block text-sm font-medium text-gray-700">
				{label}
			</label>
			<Button
				type="button"
				variant="outline"
				disabled={disable}
				onClick={toggleDropdown}
				className="flex w-full items-center justify-between rounded-md border-[1.5px] border-gray-200 bg-white px-4 py-2 text-left shadow-sm"
			>
				{selectedOptions.length > 0 ? (
					selectedOptions.length > maxShown
						? selectedOptions.slice(0, maxShown).map(v => options.find(o => o.value === v)?.label).join(", ") + `, ${selectedOptions.length - maxShown} More...`
						: selectedOptions.map(v => options.find(o => o.value === v)?.label).join(", ")
				) : (
					<span className="font-normal text-gray-400">Select...</span>
				)}
				<span
					className={`transform transition-transform duration-300 ${isOpen ? "rotate-180" : ""}`}
				>
					<PiCaretDownBold />
				</span>
			</Button>
			{isOpen && (
				<div className="absolute dropdown-select mt-2 w-full rounded-md border border-gray-300 bg-white shadow-lg max-h-[400px] overflow-y-auto">
					{options.map((option) => (
						<div
							key={option.value}
							onClick={() => handleOptionClick(option.value as string)}
							className={`cursor-pointer px-4 py-2 hover:bg-gray-300 ${selectedOptions.includes(option.value as string) ? "bg-gray-100" : ""}`}
						>
							<div className="flex">
								<span className="text-gray-500 w-5">
									{selectedOptions.includes(option.value as string) && (
										<PiCheckBold />
									)}
								</span>
								{option.label}
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);
}
