export interface SelectFilterProps {
  column: { dataIndex: string };
  dataSource: Record<string, string[]> | undefined;
  tempSelectFilters: Record<string, string[]>;
  setTempSelectFilters: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >;
  handleSelectFilterChange: (dataIndex: string, values: string[]) => void;
  selectFilters: Record<string, string>;
  setSelectFilters: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >;
  showDropdown: string | null;
  setShowDropdown: React.Dispatch<React.SetStateAction<string | null>>;
}
