import { getIronSession } from 'iron-session'
import { cookies } from 'next/headers'
import { type NextRequest, NextResponse } from 'next/server'
import { type SessionData } from './lib/session-utils'
import { sessionOptions } from './sessionOptions'

export async function middleware(request: NextRequest) {
  const cookieStore = cookies()
  const session = await getIronSession<SessionData>(
    cookieStore,
    sessionOptions,
  );

  // console.log("session", session)

  if (!session) {
    // Create an absolute URL for the redirect
    const loginUrl = new URL('/auth/login', request.url)
    return NextResponse.redirect(loginUrl)
  }
  // if (!session.tenantId && request.nextUrl.pathname !== '/auth/set-tenant') {
  //   return NextResponse.redirect(new URL('/auth/set-tenant', request.url))
  // }
}

export const config = {
  runtime: 'nodejs',
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
