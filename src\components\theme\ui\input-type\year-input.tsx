import { DatePicker } from "@/components/theme/ui/datepicker";
import { useEffect, useState } from "react";
import cn from "@/utils/class-names";
import {
  UseFormUnregister,
  type FieldErrors,
  type FieldValues,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  type UseFormWatch,
} from "react-hook-form";
import ReactDatePicker from "react-datepicker";
import { Text } from "rizzui";
import { get } from "lodash";

function formatDate(date: Date | null, type: string) {
  if (!date) return null;
  // // console.log('type', date);
  const yyyy = date.getFullYear();
  const mm = (date.getMonth() + 1).toString().padStart(2, "0");
  const dd = date.getDate().toString().padStart(2, "0");
  const hh = date.getHours().toString().padStart(2, "0");
  const mi = date.getMinutes().toString().padStart(2, "0");
  const ss = date.getSeconds().toString().padStart(2, "0");

  let formatedDate = "";
  switch (type) {
    case 'datetime':
      formatedDate = `${yyyy}-${mm}-${dd}T${hh}:${mi}:${ss}.000Z`
      break;
    case 'month':
      formatedDate = `${mm}`
      break;
    case 'year':
      formatedDate = `${yyyy}`
      break;
    default:
      formatedDate = `${yyyy}-${mm}-${dd}T00:00:00.000Z`;
      break;
  }
  return formatedDate;
}

// Convert local date to UTC (no offset)
function toUTCDate(localDate: Date): Date {
  return new Date(
    Date.UTC(
      localDate.getFullYear(),
      localDate.getMonth(),
      localDate.getDate(),
      localDate.getHours(),
      localDate.getMinutes(),
    ),
  );
}

// Convert UTC date to local for display in browser (simulating UTC 0)
function fromUTCDate(utcDate: Date | null): Date {
  if (!utcDate) return new Date();
  if (!(utcDate instanceof Date) || isNaN(utcDate.getTime())) {
    return new Date(); // fallback
  }
  return new Date(
    utcDate.getUTCFullYear(),
    utcDate.getUTCMonth(),
    utcDate.getUTCDate(),
    utcDate.getHours(),
    utcDate.getMinutes(),
  );
}

interface DateInputProps {
  label?: string;
  name: string;
  className?: string;
  names?: string[];
  register: UseFormRegister<FieldValues>;
  unregister: UseFormUnregister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  errors: FieldErrors<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  required?: boolean;
  readOnly?: boolean;
  inline?: boolean;
  minDate?: Date | undefined;
  maxDate?: Date | undefined;
  size?: "sm" | "md" | "lg" | "xl" | undefined;
  dateClassName?: string;
  popperPlacement?:
  | "bottom-start"
  | "top"
  | "right"
  | "bottom"
  | "left"
  | "top-start"
  | "top-end"
  | "right-start"
  | "right-end"
  | "bottom-end"
  | "left-start"
  | "left-end";
  onChange?: (date: Date | null, name: string) => void;
}

export function YearInput(props: DateInputProps) {
  const [date, setDate] = useState<Date | null>(null);

  const onChange = (e: Date | null) => {
    console.log('onChange', e);
    setDate(e);
    props.setValue(props.name, e ? formatDate(e, "year") : null);
  };
  // useEffect(() => {
  //   if (props.getValues(props.name) && !date) {
  //     console.log('date', date)
  //     const defaultDate = new Date(`${props.getValues(props.name) as string}-01-01`);
  //     setDate(defaultDate);
  //     console.log('setDate(defaultDate)', defaultDate)
  //   }
  //   // else {
  //   //   console.log('setDate(null)')
  //   //   setDate(null);
  //   // }
  // }, [props.getValues(props.name)]);

  useEffect(() => {
    console.log('watch', props.watch(props.name))
    if (!props.getValues(props.name)) {
      props.register(props.name, {
        value: date,
        required: props.required,
      });
      setDate(null);
    }
  }, [props.watch(props.name)]);

  return (
    <div className={props.className ?? ""}>
      {props.inline && (
        <Text as="p" className={"mb-1.5 font-medium " + ("text-" + props.size)}>
          {props.label}
        </Text>
      )}
      <div className={"" + props.readOnly && "cursor-not-allowed"}>
        <DatePicker
          popperPlacement={props.popperPlacement ?? "bottom-start"}
          selected={date}
          onChange={onChange}
          selectsStart
          showYearPicker
          dateFormat="yyyy"
          placeholderText={props.label}
          className={cn(
            props.dateClassName,
            "date-picker-event-calendar w-full",
            props.readOnly &&
            "cursor-not-allowed rounded-md bg-[#e3e3e3b3] text-gray-700",
          )}
          readOnly={props.readOnly}
          inputProps={{
            error:
              get(props.errors, props.name) && !props.getValues(props.name)
                ? props.label + " is required"
                : undefined,
            label: props.label,
            size: props.size ?? "md",
          }}
          inline={props.inline}
          minDate={props.minDate}
          maxDate={props.maxDate}
        />
      </div>
    </div>
  );
}
