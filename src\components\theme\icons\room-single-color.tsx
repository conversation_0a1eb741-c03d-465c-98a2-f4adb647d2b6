export default function RoomSingleIconColor({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      data-name="Layer 3"
      viewBox="0 0 64 64"
      {...props}
    >
      <path fill="#914027" d="M8 7h48v22H8z" />
      <g fill="#b7533e">
        <path d="M14 7h6v18h-6zM26 7h6v18h-6zM50 7h6v22h-6zM38 7h6v18h-6z" />
      </g>
      <path fill="#914027" d="M2 42h60v8H2z" />
      <path
        fill="#c1cfe8"
        d="M60 42v-8.4a8.6 8.6 0 0 0-3.83-7.15A8.61 8.61 0 0 0 51.39 25H12.61a8.61 8.61 0 0 0-4.78 1.45A8.6 8.6 0 0 0 4 33.6V42z"
      />
      <path
        fill="#798499"
        d="m12 29 2.21-.89A15.58 15.58 0 0 1 20 27a15.57 15.57 0 0 1 5.79 1.11L28 29V17l-2.21.89A15.57 15.57 0 0 1 20 19a15.58 15.58 0 0 1-5.79-1.11L12 17zM36 29l2.21-.89A15.58 15.58 0 0 1 44 27a15.57 15.57 0 0 1 5.79 1.11L52 29V17l-2.21.89A15.57 15.57 0 0 1 44 19a15.58 15.58 0 0 1-5.79-1.11L36 17z"
      />
      <path fill="#666" d="m8 50-5 7h6l7-7zM56 50l5 7h-6l-7-7z" />
      <path fill="#95afd1" d="M60 39v3H4v-3a3 3 0 0 1 3-3h50a3 3 0 0 1 3 3z" />
      <path
        fill="#8a96a8"
        d="M20 19a15.58 15.58 0 0 1-5.79-1.11l-.5-.2-.41 5.37a2.85 2.85 0 0 0 3.53 2.98 14.74 14.74 0 0 1 8.24.32l2.93.97V17l-2.21.89A15.57 15.57 0 0 1 20 19zM44 19a15.58 15.58 0 0 1-5.79-1.11l-.5-.2-.41 5.37a2.85 2.85 0 0 0 3.53 2.98 14.74 14.74 0 0 1 8.24.32l2.93.97V17l-2.21.89A15.57 15.57 0 0 1 44 19z"
      />
      <path fill="#9e4832" d="M12 48h50v-6H4.26A8 8 0 0 0 12 48z" />
      <path d="M62 41h-1v-7.4a9.58 9.58 0 0 0-4-7.78V10a4 4 0 0 0-4-4H11a4 4 0 0 0-4 4v15.82a9.58 9.58 0 0 0-4 7.78V41H2a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h4.06l-3.87 5.42A1 1 0 0 0 3 58h6a1 1 0 0 0 .7-.3l6.71-6.7H47.6l6.7 6.7a1 1 0 0 0 .71.3h6a1 1 0 0 0 .81-1.58L57.94 51H62a1 1 0 0 0 1-1v-8a1 1 0 0 0-1-1zm-7-31v14.72a9.59 9.59 0 0 0-2-.57V17a1 1 0 0 0-1.37-.93l-.63.25V8h2a2 2 0 0 1 2 2zm-19.56 6.17A1 1 0 0 0 35 17v7h-2V8h4v8.32l-.63-.25a1 1 0 0 0-.93.1zm-6.88 0a1 1 0 0 0-.93-.1l-.63.25V8h4v16h-2v-7a1 1 0 0 0-.44-.83zM25 17.1a14.55 14.55 0 0 1-4 .85V8h4zm-6 .85a14.55 14.55 0 0 1-4-.85V8h4zm-5.16.86a16.64 16.64 0 0 0 12.32 0l.84-.33v9.04l-.84-.33a16.64 16.64 0 0 0-12.32 0l-.84.33v-9.04zM49 17.1a14.55 14.55 0 0 1-4 .85V8h4zm-6 .85a14.55 14.55 0 0 1-4-.85V8h4zm-6 .53.84.33a16.64 16.64 0 0 0 12.32 0l.84-.33v9.04l-.84-.33a16.64 16.64 0 0 0-12.32 0l-.84.33zM11 8h2v8.32l-.63-.25A1 1 0 0 0 11 17v7.15a9.59 9.59 0 0 0-2 .57V10a2 2 0 0 1 2-2zM8.39 27.28a7.62 7.62 0 0 1 2.61-1.1V29a1 1 0 0 0 1.37.93l2.22-.89a14.63 14.63 0 0 1 10.82 0l2.22.89A1 1 0 0 0 29 29v-3h6v3a1 1 0 0 0 1.37.93l2.22-.89a14.63 14.63 0 0 1 10.82 0l2.22.89A1 1 0 0 0 53 29v-2.82a7.58 7.58 0 0 1 6 7.42v1.95a3.96 3.96 0 0 0-2-.55H7a3.96 3.96 0 0 0-2 .56V33.6a7.6 7.6 0 0 1 3.39-6.32zM5 39a2 2 0 0 1 2-2h50a2 2 0 0 1 2 2v2H5zm3.59 17H4.94l3.58-5h5.07zm50.47 0H55.4l-5-5h5.07zM61 49H3v-6h58z" />
    </svg>
  );
}
