export default function MoneyInHand({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -2 512 512" {...props}>
      <path
        fill="#f3a334"
        d="M360.32 7.65a98.15 98.15 0 0 0-87.28 53.22 98.12 98.12 0 0 1 28.26-4.14c54.21 0 98.16 43.95 98.16 98.16 0 16.2-3.92 31.48-10.87 44.95 40.43-12.14 69.89-49.64 69.89-94.03 0-54.2-43.94-98.16-98.16-98.16zm0 0"
      />

      <path
        fill="#e87e04"
        d="M301.57 36.2c-3.7 0-7.34.17-10.95.5a98.51 98.51 0 0 0-17.59 24.17 98.15 98.15 0 0 1 28.27-4.14c54.21 0 98.16 43.95 98.16 98.16 0 16.2-3.93 31.48-10.87 44.95a97.66 97.66 0 0 0 26.92-12.87C437.03 111.3 379.8 36.2 301.57 36.2zm0 0"
      />
      <path
        fill="#ffe183"
        d="M313.97 57.55c48.23 6.21 85.49 47.43 85.49 97.34 0 1.79-.06 3.56-.15 5.32a66.84 66.84 0 0 0 27.95-54.4c0-59.18-71.13-88.76-113.3-48.26zm0 0"
      />
      <path
        fill="#ffc964"
        d="M339.17 42.31a66.96 66.96 0 0 0-25.2 15.24 98.17 98.17 0 0 1 85.5 97.34 87.69 87.69 0 0 1-.16 5.29v.03a67.35 67.35 0 0 0 19.56-21.95c-6.24-44.92-37.76-81.87-79.7-95.95zm0 0"
      />
      <path
        fill="#f3a334"
        d="M400 154.63c0 54.35-44.07 98.42-98.43 98.42-54.36 0-98.43-44.07-98.43-98.43 0-54.36 44.07-98.42 98.43-98.42 54.36 0 98.43 44.06 98.43 98.42zm0 0"
      />
      <path
        fill="#e87e04"
        d="M223.14 154.63c0-50.99 38.77-92.92 88.43-97.93-3.29-.34-6.62-.5-10-.5-54.36 0-98.43 44.06-98.43 98.42s44.07 98.43 98.43 98.43c3.38 0 6.71-.17 10-.5-49.66-5.01-88.43-46.94-88.43-97.93zm0 0"
      />
      <path
        fill="#ffe183"
        d="M368.7 154.63a67.12 67.12 0 1 1-134.25 0 67.12 67.12 0 0 1 134.24 0zm0 0"
      />
      <path
        fill="#ffc964"
        d="M254.45 154.63a67.14 67.14 0 0 1 57.12-66.38 67.12 67.12 0 1 0 0 132.76 67.14 67.14 0 0 1-57.12-66.38zm0 0"
      />
      <path
        fill="#72bdf6"
        d="M122.07 305.16c-26.47 9.98-81.75 30.8-107.8 40.63a10.64 10.64 0 0 0-6.21 13.71l50.99 135.3c2.07 5.5 8.2 8.28 13.7 6.2l18.75-7.05 89.06-33.56zm0 0"
      />
      <path
        fill="#4b98d4"
        d="M170.04 438.55 54.29 482.17l4.76 12.63c2.07 5.5 8.2 8.28 13.7 6.2l18.75-7.05 89.06-33.57-8.5-22.58zm0 0"
      />
      <path
        fill="#f0a479"
        d="M362 301.73s-1.3-25.42-28.05-25.42c-74.08 0-83.47-1.28-99.77 4.93l-59.24 24.5 45.23 120.01c46.67-19.34 28.49-16.46 114.41-16.46 10.26-.49 32.92-3.92 50.02-23.53l.02-.02L494.3 267.9c3.48-3.97 20.15-25.89 1.4-44.3-20.88-19.24-42.6 2.46-44.48 4.17z"
      />
      <path
        fill="#e8956b"
        d="m198.67 295.92-23.73 9.81 45.23 120.02 23.72-9.82zm0 0"
      />
      <path
        fill="#e4e3e1"
        d="m160.65 290.63-40.4 15.22 58.5 155.22 40.39-15.22a9.39 9.39 0 0 0 5.48-12.1L172.74 296.1a9.38 9.38 0 0 0-12.1-5.47zm0 0"
      />
      <path
        fill="#d1d0cf"
        d="m224.62 433.75-5.7-15.11a10.27 10.27 0 0 1-4.36 3.13l-44.33 16.71 8.51 22.6 40.4-15.23a9.39 9.39 0 0 0 5.48-12.1zm0 0"
      />
      <path d="M301.43 260.27c39.71 0 74.36-22.02 92.41-54.48a105.42 105.42 0 0 0 71.58-88.18 7.5 7.5 0 0 0-14.91-1.7 90.42 90.42 0 0 1-48.17 70.06c2.21-7.09 3.68-14.5 4.35-22.13a74.65 74.65 0 0 0 28.2-58.31c0-63.9-75.52-97.88-123.35-56.1a106.77 106.77 0 0 0-22.53.25C335-9.3 430.11 9.96 448.43 83.57a7.5 7.5 0 0 0 14.56-3.62c-23.12-92.9-148.66-109.3-195-25.58-41.92 14.02-72.22 53.66-72.22 100.24 0 58.26 47.4 105.66 105.66 105.66zM419.9 105.53c0 13.8-4.85 27.07-13.42 37.58l-.02-.16c-4.77-43.25-35.49-77.8-74.57-89.54 39.42-21.7 88.01 7.02 88.01 52.12zM301.43 63.95a90.38 90.38 0 0 1 37.28 8.02 90.84 90.84 0 0 1 53.38 82.64c0 49.87-40.56 90.66-90.66 90.66-49.99 0-90.66-40.67-90.66-90.66 0-50.02 40.71-90.66 90.66-90.66zm0 0" />
      <path d="M301.43 229.05c41.05 0 74.44-33.4 74.44-74.44s-33.4-74.44-74.44-74.44S227 113.57 227 154.6s33.4 74.44 74.44 74.44zm0-133.88c32.78 0 59.44 26.67 59.44 59.44s-26.66 59.44-59.44 59.44c-32.77 0-59.43-26.67-59.43-59.44s26.66-59.44 59.43-59.44zm0 0" />
      <path d="M281.16 170.54c-3.41 0-6.17 4.62-6.17 7.7 0 6.18 10.03 13.34 23.36 13.67V195c0 1.43 1.65 2.75 3.75 2.75 1.87 0 3.74-1.32 3.74-2.75v-3.42c12.35-1.98 20.83-9.8 20.83-23.36 0-14.99-11.02-19.94-20.83-23.58v-19.4c7.39.56 9.92 4.08 13 4.08 4.09 0 5.85-5.07 5.85-7.6 0-6.4-11.68-8.49-18.85-8.7v-2.87c0-1.44-1.87-2.76-3.74-2.76-2.1 0-3.75 1.32-3.75 2.76v3.08c-11.02 1.44-21.37 7.5-21.37 21.5 0 14.1 11.34 18.4 21.37 22.14v21.93c-9.7-1.1-13-8.26-17.19-8.26zm23.8-11.02c4.74 2.09 8.27 4.73 8.27 10.13 0 5.18-3.42 8.05-8.27 9.04zm-14.54-26.23c0-4.19 3.3-6.83 8.81-7.72v16.64c-5.07-1.98-8.81-4.07-8.81-8.92zM446.49 221.04l-80.12 66.44c-4.47-9.84-14.08-19.67-32.3-19.67-12.41 0-23-.04-32.12-.07-45.86-.15-55.42-.18-70.31 5.49l-.2.07-50.84 21.03-.7-1.88a16.88 16.88 0 0 0-21.77-9.84L11.76 337.77a18.13 18.13 0 0 0-10.59 23.37l50.99 135.3a18.13 18.13 0 0 0 23.38 10.6l146.38-55.17a16.88 16.88 0 0 0 9.85-21.76l-.66-1.75 6.24-2.67c17.61-7.55 21.8-9.34 37.83-9.88a7.5 7.5 0 0 0-.5-15c-18.85.64-25 3.28-43.24 11.09l-5.63 2.4-39.92-105.93 51.18-21.16c12.24-4.64 20.35-4.6 64.83-4.46 9.13.03 19.74.06 32.17.06 8.16 0 13.83 2.8 17.34 8.58a24.44 24.44 0 0 1 3.2 9.44c-.21 2.96-1.96 16.63-15.42 18.92-20.55 3.49-64.88 9.83-65.32 9.9a7.5 7.5 0 1 0 2.12 14.85c.45-.07 44.97-6.44 65.71-9.96 19-3.23 26.05-19.43 27.6-30l86.84-72 .25-.22.35-.32c8.63-8.08 21.51-15.31 33.92-3.97 14.02 13.9 1.14 30.44-1.81 33.86L379.24 379.65l-.16.18c-15.36 17.61-36.14 20.53-44.55 20.96-9.35 0-17.46-.04-24.55-.08h-.04a7.5 7.5 0 0 0-.04 15c7.16.04 15.35.08 24.81.08h.36c10.2-.5 35.98-4 55.23-26L500 271.92l.08-.09c34.52-39.6-12.92-88.82-53.6-50.8zM70.25 492.99a3.13 3.13 0 0 1-4.05-1.83l-50.99-135.3c-.6-1.63.2-3.44 1.84-4.05L116 314.5l53.2 141.19-98.96 37.29zm147.48-57.6c.36.97-.11 2.06-1.1 2.44l-33.38 12.58-53.2-141.18 33.38-12.58c.98-.37 2.06.12 2.43 1.1l51.87 137.65zm0 0" />
    </svg>
  );
}
