import type { PaginationProps } from "@/interfaces/table/paginationType";
import React from "react";
import { Pi<PERSON>aretLeft, PiCaretRight } from "react-icons/pi";
import { Button } from "rizzui";

export default function PaginationTable({
  totalPages,
  pageNumber,
  setPageNumber,
}: PaginationProps) {
  const pageNumberChange = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages) {
      setPageNumber(newPage);
    }
  };

  const RenderPageNumbers = () => {
    const pageNumbers = [];
    for (let i = 1; i <= totalPages; i++) {
      if (
        i === 1 ||
        i === totalPages ||
        (i >= pageNumber - 1 && i <= pageNumber + 1)
      ) {
        pageNumbers.push(
          <Button
            key={i}
            size="sm"
            onClick={() => pageNumberChange(i)}
            className={` ${
              i === pageNumber
                ? "bg-black text-white"
                : "bg-transparent text-black hover:bg-gray-200 hover:text-white"
            }`}
          >
            {i}
          </Button>,
        );
      } else if (i === pageNumber - 2 || i === pageNumber + 2) {
        pageNumbers.push(
          <span key={i} className="px-2">
            ...
          </span>,
        );
      }
    }
    return pageNumbers;
  };

  return (
    <div className="mt-4 flex justify-end space-x-2">
      <Button
        onClick={() => pageNumberChange(pageNumber - 1)}
        disabled={pageNumber === 1}
        className="disabled:cursor-not-allowed"
        variant="outline"
        size="sm"
      >
        <PiCaretLeft />
      </Button>
      <RenderPageNumbers />
      <Button
        onClick={() => pageNumberChange(pageNumber + 1)}
        disabled={pageNumber === totalPages}
        className="disabled:cursor-not-allowed"
        variant="outline"
        size="sm"
      >
        <PiCaretRight />
      </Button>
    </div>
  );
}
