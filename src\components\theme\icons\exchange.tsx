export default function ExchangeIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.9394 5.25L12.2197 6.96967C11.9268 7.26257 11.9268 7.73745 12.2197 8.03033C12.5126 8.3232 12.9875 8.3232 13.2803 8.03033L16.2803 5.03033C16.5732 4.73744 16.5732 4.26257 16.2803 3.96967L13.2803 0.969668C12.9875 0.676777 12.5126 0.676777 12.2197 0.969668C11.9268 1.26257 11.9268 1.73744 12.2197 2.03033L13.9394 3.75H5.25C4.25544 3.75 3.30161 4.14509 2.59835 4.84835C1.89509 5.55161 1.5 6.50544 1.5 7.5V8.25C1.5 8.66423 1.83579 9 2.25 9C2.66421 9 3 8.66423 3 8.25V7.5C3 6.90326 3.23705 6.33097 3.65901 5.90901C4.08097 5.48705 4.65326 5.25 5.25 5.25H13.9394Z"
        fill="currentColor"
      />
      <path
        d="M5.78033 11.0303C6.07322 10.7374 6.07322 10.2625 5.78033 9.96968C5.48743 9.6768 5.01257 9.6768 4.71967 9.96968L1.71967 12.9697C1.42678 13.2625 1.42678 13.7374 1.71967 14.0303L4.71967 17.0303C5.01257 17.3232 5.48743 17.3232 5.78033 17.0303C6.07322 16.7374 6.07322 16.2625 5.78033 15.9697L4.06066 14.25H12.75C13.7446 14.25 14.6984 13.8549 15.4016 13.1516C16.1049 12.4484 16.5 11.4946 16.5 10.5V9.75C16.5 9.33577 16.1642 9 15.75 9C15.3358 9 15 9.33577 15 9.75V10.5C15 11.0967 14.7629 11.669 14.341 12.091C13.919 12.5129 13.3467 12.75 12.75 12.75H4.06066L5.78033 11.0303Z"
        fill="currentColor"
      />
    </svg>
  );
}
