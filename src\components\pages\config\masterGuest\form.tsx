import React from "react";
import ButtonReset from "@/components/container/button/button-reset";
import { Button, Input, Select } from "rizzui";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { UploadFile } from "@/components/theme/ui/file-upload/upload-file";
import type { SelectOption } from "rizzui";
import type { FormProps } from "@/interfaces/form/formType";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import { DateInput } from "@/components/theme/ui/input-type/dates-input";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  setValue,
  getValues,
  onDelete,
  handleReset,
  watch,
  isStatus,
  setStatus,
  statusOptions,
  // isCompany,
  // setCompany,
  // companyOptions,
  isGender,
  setGender,
}: FormProps & {
  handleReset: () => void;
  isStatus: SelectOption | undefined;
  setStatus: (option: SelectOption) => void;
  statusOptions: SelectOptionType[];
  // isCompany: SelectOption | undefined;
  // setCompany: (option: SelectOption) => void;
  // companyOptions: SelectOptionType[];
  isGender: SelectOption | undefined;
  setGender: (option: SelectOption) => void;
}) {
  const { can } = useGrantedPolicies();
  const genderOptions = [
    { label: "Pria", value: "pria" },
    { label: "Wanita", value: "wanita" },
  ];

  // console.log("isCompany nya", isCompany);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-5 gap-2">
        <div>
          <Input
            size="sm"
            label="Identity Number"
            disabled={isLoading}
            placeholder="Please fill in Identity Number"
            {...register("identityNumber", { required: true })}
            error={
              errors.identityNumber ? "Identity Number is required" : undefined
            }
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Full Name"
            disabled={isLoading}
            placeholder="Please fill in Full Name"
            {...register("fullname", { required: true })}
            error={errors.fullname ? "Full Name is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Email"
            disabled={isLoading}
            placeholder="Please fill in Email"
            {...register("email", { required: true })}
            error={errors.email ? "Email is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <DateInput
            className="col-span-3"
            label={"Birth Date"}
            name={"dateOfBirthday"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            size="sm"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Gender"
            disabled={isLoading}
            placeholder="Please select Gender"
            {...register("gender", { required: true })}
            error={errors.gender ? "Gender is required" : undefined}
            className="w-full"
            options={genderOptions}
            value={isGender}
            onChange={(e: { label: string; value: string }) => {
              setGender(e);
              setValue("gender", e?.value ?? "");
            }}
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Phone Number"
            disabled={isLoading}
            placeholder="Please fill in Phone Number"
            {...register("phoneNumber", { required: true })}
            error={errors.phoneNumber ? "Phone Number is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Nationality"
            disabled={isLoading}
            placeholder="Please fill in Nationality"
            {...register("nationality", { required: true })}
            error={errors.nationality ? "Nationality is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="City"
            disabled={isLoading}
            placeholder="Please fill in City"
            {...register("city", { required: true })}
            error={errors.identityNumber ? "City is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="District"
            disabled={isLoading}
            placeholder="Please fill in District"
            {...register("district", { required: true })}
            error={errors.district ? "District is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Company"
            disabled={isLoading}
            placeholder="Please fill in Company"
            {...register("companyName", { required: true })}
            error={errors.companyName ? "Company is required" : undefined}
            className="w-full"
          />
          {/* <AutocompleteSelectV2
            label="Company"
            size="sm"
            name="companyName"
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={companyOptions}
            value={isCompany}
            required={true}
          /> */}
        </div>
        <div>
          <Input
            size="sm"
            label="Address"
            disabled={isLoading}
            placeholder="Please fill in Address"
            {...register("address", { required: true })}
            error={errors.address ? "Address is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Status"
            disabled={isLoading}
            placeholder="Please select Status"
            {...register("statusId", { required: true })}
            error={errors.statusId ? "Status is required" : undefined}
            className="w-full"
            options={statusOptions}
            value={isStatus}
            onChange={(e: { label: string; value: string }) => {
              setStatus(e);
              setValue("statusId", e?.value ?? "");
            }}
          />
        </div>
        <div>
          <UploadFile
            label={"Attachment"}
            name={"attachment"}
            setValue={setValue}
            getValues={getValues}
            register={register}
            errors={errors}
            accept="application/image"
            // required={true}
            size="sm"
          />
        </div>
        <div className="flex items-end justify-start gap-2">
          {can("WismaApp.Guest.Create") && can("WismaApp.Guest.Edit") && (
            <Button
              size="sm"
              type="submit"
              disabled={isLoading}
              className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${
                getValues("id") ? "bg-blue-500" : "bg-green-500"
              }`}
            >
              {getValues("id") ? "Update" : "Create"}
            </Button>
          )}
          {/* {getValues("id") && can("WismaApp.Guest.Delete") && (
            <Button
              size="sm"
              disabled={isLoading}
              className={`rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400`}
              onClick={() => {
                onDelete(String(getValues("id")));
              }}
            >
              Delete
            </Button>
          )} */}
          <ButtonReset isLoading={isLoading} handleReset={handleReset} />
        </div>
      </div>
    </form>
  );
}
