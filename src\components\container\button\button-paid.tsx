"use client";
import React from "react";
import { Popover, Title, Text, Button } from "rizzui";
import { PiCurrencyCircleDollarBold, PiCurrencyDollar, PiTrash } from "react-icons/pi";

export default function ButtonPaid({
  onClick,
  title,
  text,
}: {
  onClick?: () => void;
  title?: string;
  text?: string;
}) {
  return (
    <Popover placement="left-start">
      <Popover.Trigger>
        <button type="button" className="rounded-md px-2 py-1 text-xs text-white">
          <PiCurrencyCircleDollarBold className="h-4 w-4 text-green-500 transition-transform duration-200 hover:scale-125 hover:text-green-700" />
        </button>
      </Popover.Trigger>
      <Popover.Content>
        {({ setOpen }) => (
          <div className="flex w-56 flex-col items-center justify-center">
            <Title as="h6">{title}</Title>
            <Text className="text-center">{text}</Text>
            <div className="mt-2 flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                No
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  setOpen(false);
                  onClick?.();
                }}
                className="bg-red-500 text-white hover:bg-red-600"
              >
                Yes
              </Button>
            </div>
          </div>
        )}
      </Popover.Content>
    </Popover>
  );
}
