export default function FolderLockIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" {...props}>
      <path
        fill="#FFC250"
        d="M.293 11.239h18.085V2.872L1.341 2.867c-.579 0-1.048.47-1.048 1.048v7.324Z"
      />
      <path
        fill="#FFE07D"
        d="m12.223 2.357-.915 2c-.17.374-.543.613-.953.613H1.341c-.579 0-1.048.47-1.048 1.048v9.654c0 .579.47 1.048 1.048 1.048l15.99-.005c.578 0 1.047-.47 1.047-1.048V2.792c0-.579-.47-1.048-1.049-1.048h-4.153c-.41 0-.783.24-.953.613Z"
      />
      <path
        fill="#FFD064"
        d="M18.377 2.792c0-.579-.47-1.048-1.048-1.048H14.8c.571.01 1.031.475 1.031 1.048l.001 12.875c0 .579-.469 1.048-1.047 1.048h2.544c.58 0 1.048-.469 1.048-1.048V2.792Z"
      />
      <path
        fill="#4073C8"
        d="M19.293 13.12a.413.413 0 0 1-.392-.28 3.616 3.616 0 0 0-.186-.45.414.414 0 0 1 .078-.475.414.414 0 0 0 0-.586l-.665-.666a.414.414 0 0 0-.585 0 .414.414 0 0 1-.477.079 3.595 3.595 0 0 0-.448-.186.413.413 0 0 1-.28-.392.414.414 0 0 0-.415-.414h-.94a.414.414 0 0 0-.415.414.413.413 0 0 1-.28.392 3.601 3.601 0 0 0-.449.186.414.414 0 0 1-.476-.078.414.414 0 0 0-.586 0l-.665.665a.414.414 0 0 0 0 .585.414.414 0 0 1 .079.477 3.632 3.632 0 0 0-.186.448.413.413 0 0 1-.392.28.414.414 0 0 0-.414.415v.94c0 .23.185.415.414.415.177 0 .335.112.392.28.052.155.114.304.186.449.079.159.047.35-.079.476a.414.414 0 0 0 0 .586l.665.665a.414.414 0 0 0 .586 0 .414.414 0 0 1 .476-.079c.145.072.294.134.449.186.168.057.28.215.28.392 0 .229.186.414.415.414h.94a.414.414 0 0 0 .414-.414c0-.177.113-.335.28-.392a3.61 3.61 0 0 0 .45-.186.414.414 0 0 1 .475.079.414.414 0 0 0 .586 0l.665-.665a.414.414 0 0 0 0-.586.414.414 0 0 1-.078-.476 3.61 3.61 0 0 0 .186-.449.413.413 0 0 1 .392-.28.414.414 0 0 0 .414-.415v-.94a.414.414 0 0 0-.414-.414Zm-3.84 2.59a1.706 1.706 0 1 1 0-3.412 1.706 1.706 0 0 1 0 3.412Z"
      />
      <path
        fill="#F2F9FF"
        d="M15.453 15.71a1.706 1.706 0 1 1 0-3.411 1.706 1.706 0 0 1 0 3.411Z"
      />
      <path
        fill="#3B66C1"
        d="M19.293 13.12a.413.413 0 0 1-.392-.28 3.632 3.632 0 0 0-.186-.45.414.414 0 0 1 .078-.475.414.414 0 0 0 0-.586l-.665-.666a.414.414 0 0 0-.585 0 .414.414 0 0 1-.477.079 3.595 3.595 0 0 0-.448-.186.413.413 0 0 1-.28-.392.414.414 0 0 0-.415-.414h-.94a.414.414 0 0 0-.408.344 4.223 4.223 0 0 1 2.584 3.91 4.223 4.223 0 0 1-2.584 3.91.414.414 0 0 0 .408.344h.94a.414.414 0 0 0 .415-.414c0-.177.112-.335.28-.392.154-.052.304-.114.448-.186a.414.414 0 0 1 .477.079.414.414 0 0 0 .586 0l.665-.665a.414.414 0 0 0 0-.586.414.414 0 0 1-.079-.476c.072-.145.134-.294.186-.449a.413.413 0 0 1 .392-.28.414.414 0 0 0 .414-.414v-.941a.414.414 0 0 0-.414-.414Z"
      />
      <path
        fill="#000"
        d="M5.667 8.318H2.426a.293.293 0 1 0 0 .586h3.241a.293.293 0 1 0 0-.586Zm0-1.457H2.426a.293.293 0 1 0 0 .586h3.241a.293.293 0 1 0 0-.586Zm13.626 5.966a.12.12 0 0 1-.114-.082 3.92 3.92 0 0 0-.201-.484.12.12 0 0 1 .023-.14.708.708 0 0 0 0-1l-.665-.665a.708.708 0 0 0-1 0 .12.12 0 0 1-.14.024 3.907 3.907 0 0 0-.484-.201.12.12 0 0 1-.082-.115.708.708 0 0 0-.707-.707h-.94a.708.708 0 0 0-.708.707.12.12 0 0 1-.08.114 3.911 3.911 0 0 0-.486.201.12.12 0 0 1-.139-.023.708.708 0 0 0-1 0l-.665.665a.708.708 0 0 0 0 1 .12.12 0 0 1 .023.14c-.077.156-.145.32-.2.484a.12.12 0 0 1-.115.082.708.708 0 0 0-.707.707v.94c0 .39.317.708.707.708a.12.12 0 0 1 .114.08c.056.166.124.33.201.486a.12.12 0 0 1-.023.139.707.707 0 0 0 0 1l.665.665a.707.707 0 0 0 1 0 .12.12 0 0 1 .14-.023c.156.077.319.145.484.2a.12.12 0 0 1 .081.115c0 .39.318.707.708.707h.94c.39 0 .707-.317.707-.707a.12.12 0 0 1 .082-.114c.165-.056.328-.124.485-.201a.12.12 0 0 1 .139.023.707.707 0 0 0 1 0l.665-.665a.707.707 0 0 0 0-1 .12.12 0 0 1-.023-.14c.077-.156.145-.319.2-.484a.12.12 0 0 1 .115-.081c.39 0 .707-.318.707-.707v-.941a.708.708 0 0 0-.707-.707Zm.121 1.647a.121.121 0 0 1-.121.122.706.706 0 0 0-.67.48c-.047.14-.105.278-.17.411a.706.706 0 0 0 .133.814.121.121 0 0 1 0 .172l-.665.665a.121.121 0 0 1-.171 0 .706.706 0 0 0-.814-.134 3.333 3.333 0 0 1-.412.17.706.706 0 0 0-.48.67.121.121 0 0 1-.12.121h-.941a.121.121 0 0 1-.122-.121.706.706 0 0 0-.48-.67 3.32 3.32 0 0 1-.411-.17.706.706 0 0 0-.814.134.121.121 0 0 1-.171 0l-.666-.666a.121.121 0 0 1 0-.171.706.706 0 0 0 .134-.813 3.329 3.329 0 0 1-.17-.413.706.706 0 0 0-.67-.48.121.121 0 0 1-.121-.12v-.941c0-.067.054-.122.121-.122a.706.706 0 0 0 .67-.48c.047-.14.104-.278.17-.411a.706.706 0 0 0-.134-.814.121.121 0 0 1 0-.171l.665-.665a.121.121 0 0 1 .172 0 .706.706 0 0 0 .813.133c.134-.065.272-.123.413-.17a.706.706 0 0 0 .48-.67c0-.067.054-.121.12-.121h.941c.067 0 .121.054.121.121 0 .304.193.573.48.67.14.047.28.105.412.17.272.135.599.081.814-.133a.121.121 0 0 1 .171 0l.665.665a.121.121 0 0 1 0 .171.706.706 0 0 0-.133.814c.065.133.123.271.17.412.097.287.366.48.67.48.067 0 .121.054.121.12v.941Z"
      />
      <path
        fill="#000"
        d="M15.453 12.006a2 2 0 0 0-1.999 1.999 2 2 0 0 0 1.999 1.998 2 2 0 0 0 1.998-1.998 2 2 0 0 0-1.998-2Zm0 3.411a1.414 1.414 0 0 1-1.413-1.412c0-.78.634-1.413 1.413-1.413s1.412.634 1.412 1.413c0 .778-.633 1.412-1.412 1.412Z"
      />
      <path
        fill="#000"
        d="m10.697 16.424-9.356.003a.755.755 0 0 1-.755-.755V6.018c0-.416.339-.755.755-.755h9.014c.523 0 1.002-.307 1.22-.783l.915-2.001a.757.757 0 0 1 .686-.441h4.153c.417 0 .755.337.755.754l.001 6.628a.293.293 0 0 0 .586 0V2.792c0-.74-.6-1.34-1.342-1.34h-4.153c-.523 0-1.002.307-1.22.783l-.155.34-10.46-.002C.6 2.573 0 3.173 0 3.913v11.759c0 .741.6 1.341 1.341 1.341l9.356-.003a.293.293 0 0 0 0-.586ZM1.341 3.159l10.192.002-.491 1.075a.757.757 0 0 1-.687.441H1.341c-.28 0-.54.087-.755.234v-.997c0-.418.338-.755.755-.755Z"
      />
    </svg>
  );
}
