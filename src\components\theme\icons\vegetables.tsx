export default function VegetablesIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#FFA426"
        d="M10 15.625a4.688 4.688 0 1 0 0-9.375 4.688 4.688 0 0 0 0 9.375Z"
      />
      <path
        fill="#FE8205"
        d="M6.563 10.938a4.685 4.685 0 0 1 4.062-4.641 4.687 4.687 0 1 0 0 9.281 4.685 4.685 0 0 1-4.063-4.64Z"
      />
      <path
        fill="#FFC247"
        d="M10 6.25c-.21.002-.418.017-.625.047a4.682 4.682 0 0 1 0 9.28A4.686 4.686 0 1 0 10 6.25Z"
      />
      <path
        fill="#A5E887"
        d="M11.797 4.637c.484-.481 1.357-2.137.875-2.621-.483-.485-2.14.39-2.622.874a1.236 1.236 0 0 0 1.747 1.747Z"
      />
      <path
        fill="#86C983"
        d="M11.797 3.7a1.236 1.236 0 0 1-2.017-.406 1.236 1.236 0 0 0 2.018 1.344c.467-.467 1.297-2.031.912-2.569a4.668 4.668 0 0 1-.913 1.631Z"
      />
      <path
        fill="#98BA5C"
        d="m12.096 3.034-.442-.442-2.279 2.28a2.952 2.952 0 0 0-2.188-.81v.626c2.46 0 2.5 2.398 2.5 2.5V7.5h.626v-.312a3.872 3.872 0 0 0-.547-1.824l2.33-2.33Z"
      />
      <path
        fill="#F5E3A7"
        d="M10 14.375a3.442 3.442 0 0 1-3.438-3.438h.625A2.816 2.816 0 0 0 10 13.75v.625ZM6.562 9.687h.625v.626h-.625v-.626Zm5.313-1.25h.625v.626h-.625v-.626Zm1.25 1.25h.625v.626h-.625v-.626Zm-1.25 0h.625v.626h-.625v-.626Z"
      />
      <path
        fill="#A5E887"
        d="M4.063 9.375c.533.03 1.06.132 1.565.303.525.16.935.322.935.322l-.457.228-.793.397.625.938-1.875-.626-1.875.626.625-.938-.794-.397L1.562 10s1.463-.584 2.407-.622c.031-.003.062-.003.094-.003Z"
      />
      <path
        fill="#FF475A"
        d="M7.813 12.813c0 .373-.075.743-.22 1.087l-1.343.163a1.563 1.563 0 0 0-1.562 1.562H3.125a2.813 2.813 0 0 1-1.106-5.396l.794.396-.625.938 1.875-.625 1.875.625-.625-.938.793-.396a2.81 2.81 0 0 1 1.707 2.584Z"
      />
      <path
        fill="#F03049"
        d="M6.106 10.854a2.807 2.807 0 0 1 1.685 2.293 2.788 2.788 0 0 0-1.685-2.918l-.793.396.312.47.481-.241Zm-3.918 1.334 1.874-.626 1.875.626-.535-.804-1.34-.447-1.339.447-.536.803Zm-1.05-.738a2.8 2.8 0 0 1 .88-.596l.482.24.313-.469-.794-.396A2.798 2.798 0 0 0 .33 13.125c.07-.634.354-1.225.807-1.675Z"
      />
      <path
        fill="#FFC247"
        d="M16.563 15.625a2.505 2.505 0 0 1-2.5 2.5c-.105 0-.21-.007-.313-.022a1.914 1.914 0 0 1-.263-.047l-1.612-.194-.625-.075-3.438-.412-1.562-.188a1.563 1.563 0 1 1 0-3.125l1.344-.162 1.468-.175.626-.075 2.812-.338 1.563-.187a2.495 2.495 0 0 1 2.187 1.294c.206.368.314.784.313 1.206Z"
      />
      <path
        fill="#A5E887"
        d="M19.688 15v1.25H17.5l1.25.625-.313.938-2.437-.61a2.493 2.493 0 0 0 0-3.156l.25-.063 2.188-.547.312.938L17.5 15h2.188Z"
      />
      <path
        fill="#FF9500"
        d="M7.188 17.3v-1.675h.625v1.75l-.625-.075Zm2.5-3.65v1.663h-.626v-1.588l.626-.075Zm2.187 2.287v1.926l-.625-.075v-1.85h.625Zm1.25-2.7V15H12.5v-1.688l.625-.075Zm.625 4.866v-1.228h.625v1.231l-.625-.003Zm-4.688-2.166h.626v.626h-.626v-.625Zm4.688-.312h.625v.625h-.625v-.625Z"
      />
      <path
        fill="#98BA5C"
        d="M3.75 10.313a5.42 5.42 0 0 0-.088-.877 2.102 2.102 0 0 0-.445-1.09l.441-.442c.346.4.561.896.619 1.42.06.327.093.657.098.989H3.75Z"
      />
      <path
        fill="#FFA426"
        d="M16.563 15.625a2.505 2.505 0 0 1-2.5 2.5c-.105 0-.21-.007-.313-.022a1.914 1.914 0 0 1-.263-.047l-1.612-.194-.625-.075-3.438-.412-1.562-.188a1.547 1.547 0 0 1-1.484-2.03A1.565 1.565 0 0 0 6.25 16.25l1.563.188 3.437.412.625.075 1.613.194c.086.021.174.037.262.047.104.015.208.022.313.022A2.485 2.485 0 0 0 16 16.265a2.52 2.52 0 0 0 .516-1.1c.03.151.045.305.047.459Z"
      />
      <path fill="#FE8205" d="M7.813 16.437v.938l-.625-.075v-.938l.625.075Z" />
      <path fill="#FF9500" d="M9.688 13.65v1.663h-.626v-1.588l.626-.075Z" />
      <path fill="#FE8205" d="M11.875 16.925v.937l-.625-.075v-.937l.625.075Z" />
      <path fill="#FF9500" d="M13.125 13.237V15H12.5v-1.688l.625-.075Z" />
      <path
        fill="#FE8205"
        d="M14.375 17.17v.937l-.625-.003v-.938c.103.015.208.022.313.022.104 0 .208-.006.312-.019Z"
      />
      <path
        fill="#FF9500"
        d="M9.063 15.938h.624v.624h-.624v-.625Zm4.687-.313h.625v.625h-.625v-.625Z"
      />
      <path
        fill="#98BA5C"
        d="M15.416 12.584a6.78 6.78 0 0 0-1.144.284 3.805 3.805 0 0 0-.7.316l-.76.09-1.456.176-.418-.42.896-.896c.672-.672.894-2.238.894-2.238.188.187.412.333.66.429a1.564 1.564 0 0 0 .462 1.137c.204.206.462.35.744.416v.003c.129.031.26.046.394.044.095.247.24.471.428.659Z"
      />
      <path
        fill="#7A9E51"
        d="M14.988 11.925a1.526 1.526 0 0 1-.394-.044v-.003c-.036-.008-.071-.022-.106-.031-.307.06-.608.14-.904.242a3.805 3.805 0 0 0-.7.315l-.759.09-.739.09-.448.447.418.419 1.457-.175.759-.09c.222-.13.457-.235.7-.316a6.78 6.78 0 0 1 1.144-.285 1.904 1.904 0 0 1-.429-.66Z"
      />
      <path
        fill="#A5E887"
        d="M19.225 8.328a1.59 1.59 0 0 1 0 2.241c-.167.165-.369.29-.59.366a1.9 1.9 0 0 1-3.647.99 1.527 1.527 0 0 1-.394-.044v-.003a1.543 1.543 0 0 1-.744-.415 1.563 1.563 0 0 1-.462-1.138 1.898 1.898 0 0 1 .134-3.59c.277-.085.57-.104.856-.057.076-.222.2-.424.366-.59a1.59 1.59 0 0 1 2.24 0c.032.031.053.068.082.1a1.584 1.584 0 0 1 2.059 2.06c.031.027.069.05.1.08Z"
      />
      <path
        fill="#86C983"
        d="M19.225 8.328c-.031-.031-.069-.053-.1-.081a1.583 1.583 0 0 0-2.06-2.06c-.028-.03-.05-.068-.08-.1a1.586 1.586 0 0 0-1.855-.274c.141.072.272.164.386.275.03.03.053.068.08.1a1.584 1.584 0 0 1 2.06 2.059c.031.028.069.05.1.081a1.59 1.59 0 0 1 0 2.24c-.166.166-.368.29-.59.366a1.892 1.892 0 0 1-1.14 2.057 1.896 1.896 0 0 0 2.608-2.057c.222-.075.424-.2.591-.365a1.59 1.59 0 0 0 0-2.24Z"
      />
    </svg>
  );
}
