export default function MagnifyingWithBarIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#F99B8C"
        d="m11.509 12.782.494 1.098 2.07 2.377.386.081v.362l2.074 2.382a1.808 1.808 0 0 0 2.127.457s.266-.42.42-.576c.743-.742.43-1.784-.364-2.47l-5.943-4.895-1.264 1.184Z"
      />
      <path
        fill="#EA8378"
        d="M19.707 17.9a1.8 1.8 0 0 1-1.046 1.64 1.802 1.802 0 0 0-.457-2.127l-4.786-4.159a.486.486 0 0 0-.648.006c-.242.223-.499.43-.767.621l-.75-.86.913-.854.913-.854.799.694 2.378 2.068.078.29.365.096 2.383 2.073c.415.359.625.862.625 1.367Z"
      />
      <path
        fill="#F5F8F9"
        d="m13.412 15.65 1.284-1.284.845-.947a.273.273 0 0 0 0-.387l-.594-.585a.273.273 0 0 0-.387 0l-1.046.892-1.207 1.206a.273.273 0 0 0 0 .387l.718.718c.107.107.28.107.386 0Z"
      />
      <path
        fill="#E8EDF2"
        d="m15.649 13.412-.073.072-.88.88-1.182-1.027 1.03-1.03a.274.274 0 0 1 .387 0l.718.718a.273.273 0 0 1 0 .387Z"
      />
      <path
        fill="#FEB3A7"
        d="M7.427 14.188c3.94 0 6.496-2.822 6.496-6.761 0-2.463-.886-4.407-2.508-5.916a7.134 7.134 0 0 0-9.918 9.882c1.5 1.751 3.458 2.795 5.93 2.795Z"
      />
      <path
        fill="#F99B8C"
        d="M14.56 7.426a7.134 7.134 0 0 1-13.063 3.967 7.134 7.134 0 0 0 9.897-9.897 7.127 7.127 0 0 1 3.166 5.93Z"
      />
      <path
        fill="#F5F8F9"
        d="M7.427 12.895a5.469 5.469 0 1 0 0-10.938 5.469 5.469 0 0 0 0 10.938Z"
      />
      <path
        fill="#F99B8C"
        d="M14.56 7.426a7.134 7.134 0 0 1-13.063 3.967 7.134 7.134 0 0 0 9.897-9.897 7.127 7.127 0 0 1 3.166 5.93Z"
      />
      <path
        fill="#F5F8F9"
        d="M7.364 12.673c3.02 0 5.397-2.227 5.397-5.247 0-.628.029-1.231-.166-1.793A5.471 5.471 0 0 0 1.958 7.426a5.471 5.471 0 0 0 3.8 5.209c.526.169 1.024.038 1.606.038Z"
      />
      <path
        fill="#E8EDF2"
        d="M12.895 7.425a5.469 5.469 0 0 1-7.261 5.168 7.134 7.134 0 0 0 6.96-6.96c.195.561.301 1.165.301 1.792Z"
      />
      <path
        fill="#A8D3D8"
        d="M4.515 5.486h5.824a.56.56 0 0 0 0-1.119H4.515a.56.56 0 1 0 0 1.12Zm0 2.5h5.824a.56.56 0 0 0 0-1.119H4.515a.56.56 0 1 0 0 1.12Zm0 2.5h5.824a.56.56 0 0 0 0-1.119H4.515a.56.56 0 1 0 0 1.12Z"
      />
      <path
        stroke="#000"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={0.6}
        d="m14.376 12.462-1.103-.954M15.58 13.5l2.263 1.959m1.034.896.206.179c.414.359.625.862.625 1.367a1.807 1.807 0 0 1-3.174 1.182L13.5 15.578m-1.991-2.3.952 1.1m.95 1.27 2.238-2.237a.273.273 0 0 0 0-.386l-.718-.718a.273.273 0 0 0-.387 0l-2.237 2.237a.273.273 0 0 0 0 .387l.718.718c.107.107.28.107.387 0Zm3.052-1.367-2.182 2.182m-6.17-1.933a7.093 7.093 0 0 0 3.399-1.251 7.172 7.172 0 0 0 1.767-1.768A7.102 7.102 0 0 0 14.56 7.43a7.104 7.104 0 0 0-1.584-4.484m-.958-.978a7.134 7.134 0 1 0-5.275 12.56"
      />
      <path
        stroke="#000"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={0.6}
        d="M1.985 7.98a5.47 5.47 0 1 0 .033-1.367"
      />
      <path
        stroke="#000"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={0.6}
        d="M4.515 5.486h5.824a.56.56 0 0 0 0-1.119H4.515a.56.56 0 1 0 0 1.12Zm0 2.5h5.824a.56.56 0 0 0 0-1.119H4.515a.56.56 0 1 0 0 1.12Zm0 2.5h5.824a.56.56 0 0 0 0-1.119H4.515a.56.56 0 1 0 0 1.12Zm2.912-6.474v1.83m1.562.67v1.83m-3.125.666v1.83"
      />
    </svg>
  );
}
