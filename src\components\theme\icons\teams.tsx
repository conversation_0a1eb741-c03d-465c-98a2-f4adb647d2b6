export default function TeamsIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <g clipPath="url(#teams)">
        <path
          fill="#827DDC"
          d="M12 24c6.627 0 12-5.373 12-12S18.627 0 12 0 0 5.373 0 12s5.373 12 12 12Z"
        />
        <path
          fill="#6E64C3"
          d="M16.574 8.95 15.19 7.566a1.402 1.402 0 0 0-.923-.41l-1.77-1.77-8.132 1.427V17l6.983 6.982c.216.012.433.018.652.018 6.095 0 11.128-4.544 11.898-10.43l-5.06-5.058a1.392 1.392 0 0 0-2.264.438Z"
        />
        <path
          fill="#302C89"
          d="m4.365 17 8.132 1.426V5.386L4.365 6.813V17Zm8.718-.735a1.771 1.771 0 0 0 2.883-1.378V10.63h-2.883v5.635Zm1.112-6.297a1.407 1.407 0 1 0-1.112-2.269v1.723c.257.332.66.546 1.112.546Zm5.411 1.575h-3.058v3.76a1.748 1.748 0 0 0 2.58.099c.33-.333.512-.783.507-1.25l-.029-2.61Zm-1.751-.656a1.392 1.392 0 1 0 0-2.783 1.392 1.392 0 0 0 0 2.783Z"
        />
        <path
          fill="#fff"
          d="M10.035 10.448H8.822v3.545H8.09v-3.545H6.877V9.72h3.158v.728Z"
        />
      </g>
      <defs>
        <clipPath id="teams">
          <path fill="#fff" d="M0 0h24v24H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}
