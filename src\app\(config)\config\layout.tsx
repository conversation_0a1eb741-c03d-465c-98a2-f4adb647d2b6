import LayoutProvider from "@/layouts/dashboard-layout-provider";
import { getSession } from "@/lib/actions";
import { redirect } from "next/navigation";

type LayoutProps = {
  children: React.ReactNode;
};

export default async function DefaultLayout({ children }: LayoutProps) {
  const session = await getSession();
  if (!session.isLoggedIn) {
    redirect("/auth/login");
  }

  return <LayoutProvider>{children}</LayoutProvider>;
}
