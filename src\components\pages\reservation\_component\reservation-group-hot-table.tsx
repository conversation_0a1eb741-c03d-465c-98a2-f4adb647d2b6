import React, { forwardRef, useEffect, useImperative<PERSON><PERSON>le, useRef, useState } from "react";
import { HotTable } from "@handsontable/react-wrapper";
// import "handsontable/dist/handsontable.full.min.css";
import 'handsontable/styles/handsontable.css';
import 'handsontable/styles/ht-theme-main.css';
import { type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import { type Reservation } from "@/interfaces/reservations/reservation";
import type Handsontable from "handsontable";
import { ActionIcon, Button } from "rizzui";
import { type ReservationDetail } from "@/interfaces/reservations/reservationDetail";
import { countNights } from "@/lib/helper/count-nights";
import { CellSettings, type ColumnSettings } from "node_modules/handsontable/settings";
import { createRoot } from "react-dom/client";
import { registerAllCellTypes } from "handsontable/cellTypes";
import { useMasterRoomTypesOptions } from "@/lib/hooks/useMasterRoomTypes";
import { useMasterRoomOptions } from "@/lib/hooks/useMasterRoom";
import { useApiAppGuestOptions } from "@/lib/hooks/useApiAppGuest";
import { type RoomDto, type FilterGroup, type ReservationDetailsDto, type RoomStatusDto, CreateReservationGuestDto } from "@/client";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { formatDate } from "@/lib/helper/format-date";
import { CopyPaste, registerPlugin } from "handsontable/plugins";
import { PiPlusCircleBold, PiTrash, PiUserCirclePlus } from "react-icons/pi";
import { countDays } from "@/lib/helper/count-days";
// import ModalAddGuest from "./modal-add-guests";

interface HotTableWithInstance extends React.ComponentRef<typeof HotTable> {
  hotInstance: Handsontable.Core;
}
export type ReservationGroupHotTableRef = {
  getValuess: () => Promise<ReservationDetailsDto[] | undefined>;
};

// ON TESTING JADI MASIH TERHAMBUR

export const ReservationGroupHotTable = forwardRef<ReservationGroupHotTableRef, {
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<Reservation>;
}>(({ isLoading, register, setValue, getValues, watch, errors }, ref) => {

  // const [modalAddGuest, setModalAddGuest] = useState(false);
  // OPTIONS
  const filterRooms: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "roomStatus.name",
        operator: "Equals",
        value: "READY"
      }
    ]
  }
  const { data: guestOptions } = useApiAppGuestOptions(0, 1000, "",)
  const { data: roomOptions } = useMasterRoomOptions(0, 1000, filterRooms);
  const { data: roomTypeOptions } = useMasterRoomTypesOptions(0, 1000, "",);

  const hotRef = useRef<HotTableWithInstance>(null);
  registerAllCellTypes();
  registerPlugin(CopyPaste)
  // registerAllPlugins()

  // Struktur kolom untuk pemetaan data
  const columns: ColumnSettings[] = [
    {
      type: "text", width: 35, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const container = document.createElement("div");
        td.appendChild(container);
        createRoot(container).render(
          <center>{String(row + 1)}</center>
        );
      },
    },
    {
      data: "guestIdentityNumber",
      type: "autocomplete",
      width: 200,
      source: guestOptions?.map(opt => opt.label) ?? [],
      validator: (value: string, callback: (valid: boolean) => void) => {
        callback(!!value && value.trim().length > 0); // wajib diisi, tidak boleh kosong
      },
    },
    {
      data: "guestFullname", type: "text", width: 160,
      // renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
      //   td.innerHTML = "";
      //   const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
      //   const identityNumber = rowData.guestIdentityNumber;
      //   const fullName = guestOptions?.find(opt => opt.label === identityNumber)?.fullname as string ?? rowData.guestFullname;
      //   rowData.guestFullname = fullName;
      //   td.textContent = fullName;
      // },
      validator: (value: string, callback: (valid: boolean) => void) => {
        callback(!!value && value.trim().length > 0); // wajib diisi, tidak boleh kosong
      },
    },
    {
      data: "guestCompanyName", type: "text", width: 160,
      // renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
      //   td.innerHTML = "";
      //   const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
      //   const identityNumber = rowData.guestIdentityNumber;
      //   const companyName = guestOptions?.find(opt => opt.label === identityNumber)?.companyName as string ?? rowData.guestCompanyName;
      //   td.textContent = companyName;
      // }
      validator: (value: string, callback: (valid: boolean) => void) => {
        callback(!!value && value.trim().length > 0); // wajib diisi, tidak boleh kosong
      },
    },
    {
      data: "guestNationality", type: "text", width: 160,
      validator: (value: string, callback: (valid: boolean) => void) => {
        callback(!!value && value.trim().length > 0); // wajib diisi, tidak boleh kosong
      },
      // renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
      //   td.innerHTML = "";
      //   const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
      //   const identityNumber = rowData.guestIdentityNumber;
      //   const nationality = guestOptions?.find(opt => opt.label === identityNumber)?.nationality as string ?? rowData.guestNationality;
      //   td.textContent = nationality;
      // }
    },
    {
      data: "guestCity", type: "text", width: 160,
      validator: (value: string, callback: (valid: boolean) => void) => {
        callback(!!value && value.trim().length > 0); // wajib diisi, tidak boleh kosong
      },
      // renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
      //   td.innerHTML = "";
      //   const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
      //   const identityNumber = rowData.guestIdentityNumber;
      //   const city = guestOptions?.find(opt => opt.label === identityNumber)?.city as string ?? rowData.guestCity;
      //   td.textContent = city;
      // }
    },
    {
      data: "guestPhoneNumber", type: "text", width: 140,
      validator: (value: string, callback: (valid: boolean) => void) => {
        callback(!!value && value.trim().length > 0); // wajib diisi, tidak boleh kosong
      },
      // renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
      //   td.innerHTML = "";
      //   const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
      //   const identityNumber = rowData.guestIdentityNumber;
      //   const phoneNumber = guestOptions?.find(opt => opt.label === identityNumber)?.phoneNumber as string ?? rowData.guestPhoneNumber;
      //   td.textContent = phoneNumber;
      // }
    },
    {
      data: "checkInDate",
      type: "date",
      width: 140,
      dateFormat: 'YYYY-MM-DD',
      correctFormat: true,
    },
    {
      data: "checkOutDate",
      type: "date",
      width: 140,
      dateFormat: 'YYYY-MM-DD',
      correctFormat: true,
    },
    {
      data: "roomTypeName",
      type: "select",
      width: 160,
      selectOptions: roomTypeOptions?.map(opt => opt.label) ?? [],
    },
    {
      data: "roomName",
      type: "select",
      width: 140,
      selectOptions: (visualRow, visualColumn, prop) => {
        const hot = hotRef.current?.hotInstance;
        if (!hot) return [];

        const rowData = hot.getSourceDataAtRow(visualRow) as Record<string, string>;
        const selectedRoomType = rowData?.roomTypeName;

        const selectedRoom = (watch('reservationDetails') as ReservationDetailsDto[])?.map(e => e.roomId) ?? [];
        const roomOptionsLabel = (roomOptions ?? [])
          .filter((opt: RoomDto) => !selectedRoom.includes(opt.id))
          .filter((opt: RoomDto) => opt.roomType?.name === selectedRoomType)
          // .map((opt: RoomDto) => opt.roomCode)
          .map((opt: RoomDto) => opt.roomNumber)
          .filter((val): val is string => typeof val === 'string'); // ✅ Filter out null/undefined;

        return roomOptionsLabel;
      }
    },
    {
      data: "roomStatus", type: "text", width: 140, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
        const roomName = rowData?.roomName;
        const roomStatus = roomOptions?.find(opt => opt.label === roomName)?.roomStatus as RoomStatusDto ?? {} as RoomStatusDto;
        td.textContent = roomStatus?.name ?? "N/A";
      }
    },
    {
      data: "price", type: "text", width: 140, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
        const nights = rowData?.nights ?? 1;
        const roomName = rowData?.roomName;
        const roomPrice = roomOptions?.find(opt => opt.label === roomName)?.price as number ?? 0;
        td.textContent = formatCurrencyIDR(Number(roomPrice) * Number(nights)) ?? "N/A";
      }
    },
    {
      data: "nights", type: "text", width: 100, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        td.textContent = String(instance.getDataAtCell(row, instance.propToCol("nights")) || "1");
      }
    },
    {
      data: "arrivalDate",
      type: "text",
      width: 140,
      readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        td.textContent = formatDate(watch('arrivalDate') as Date, "date");
      }
    },
    {
      width: 30,
      readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const container = document.createElement("div");
        td.appendChild(container);
        createRoot(container).render(
          <button style={{ cursor: "pointer" }} onClick={() => { instance.alter("remove_row", row); }}>
            {/* ❌ */}
            <PiTrash className="h-4 w-4 text-red-500 transition-transform duration-200 hover:scale-125 hover:text-red-700" />
          </button>
        );
      },
    }
  ];

  const [data, setData] = useState<ReservationDetail[]>([{} as ReservationDetail]);

  // Menambahkan baris baru dengan default values
  const addRow = () => {
    setData((prevData) => {
      // // console.log("prevData", prevData);
      if (prevData)
        // Mengambil data dari getValues dan menambahkan baris baru
        return [...prevData, {}] as ReservationDetail[]
      return [{} as ReservationDetail];
    });
  };

  // Mengambil nilai dalam format objek
  const validateTable = (): Promise<boolean> => {
    return new Promise((resolve) => {
      if (hotRef.current) {
        const hot = hotRef.current.hotInstance;
        hot.validateCells((isValid) => {
          resolve(isValid);
        });
      }
    });
  };
  const getValuess = async () => {
    let form = {} as ReservationDetailsDto[];
    if (hotRef.current) {
      const hot = hotRef.current.hotInstance; // ✅ Property 'hotInstance' does not exist on type 'HotTable'.ts(2339)
      const tableData = hot.getData() as [];
      const isValid = await validateTable();
      // console.log('isValid', isValid)
      if(!isValid) return undefined;
      // Konversi array of arrays ke array of objects
      const formattedData = tableData.map((row) =>
        Object.fromEntries(columns.map((col, index) => [col.data, row[index] ?? ""])) as Record<string, string> // Unsafe return of an `any` typed value.eslint@typescript-eslint/no-unsafe-return
      );
      // SET FORM VALUE
      form = formattedData.map((value, key) => {
        const room = roomOptions?.find(opt => opt.label === value.roomName);
        // console.log('value', value)
        const guestId = guestOptions?.find(opt => opt.label === value.guestIdentityNumber)?.id as string ?? null;
        const guestData: CreateReservationGuestDto = {};
        if (guestId === null) {
          guestData.identityNumber = value.guestIdentityNumber
          guestData.fullname = value.guestFullname
          guestData.companyName = value.guestCompanyName
          guestData.nationality = value.guestNationality
          guestData.city = value.guestCity
          guestData.phoneNumber = value.guestPhoneNumber
          guestData.email = ""
          guestData.district = ""
        }
        return {
          checkInDate: `${value.checkInDate}T00:00:00.000Z`,
          checkOutDate: `${value.checkOutDate}T00:00:00.000Z`,
          price: Number(room?.price ?? 0) * Number(value?.nights ?? "1"),
          guestId: guestId,
          roomId: room?.id as string ?? "",
          statusId: getValues('reservationDetailsStatus') as string ?? "",
          rfid: "",
          // guestData: guestData
          ...(Object.keys(guestData).length > 0 && { guestData }),
        }
      })
      console.log(form)
      // setValue("reservationDetails", form)
    }
    // const reservationDetails = await getValuess();
    return form;
  };

  useEffect(() => {
    let tempDatas = getValues("reservationDetails") as ReservationDetail[];
    tempDatas = tempDatas?.map((e) => ({
      ...e,
    }));
    setData(tempDatas);
    console.log('reservationDetails', getValues("reservationDetails"))
  }, [watch])

  const handleAfterChange = async (changes: Handsontable.CellChange[] | null, source: Handsontable.ChangeSource) => {
    if (source === "edit") {
      if (changes) {
        if (hotRef.current) {
          const hot = hotRef.current.hotInstance; // ✅ Property 'hotInstance' does not exist on type 'HotTable'.ts(2339)
          for (const [row, prop, oldVal, newVal] of changes) {
            // set nights value
            if (prop === 'checkInDate' || prop === 'checkOutDate') {
              const rowData = hot.getSourceDataAtRow(row) as ReservationDetailsDto;
              const dateIn = new Date(rowData?.checkInDate ?? "");
              const dateOut = new Date(rowData?.checkOutDate ?? "");

              const nights = countDays(dateIn, dateOut);
              hot.setDataAtCell(row, hot.propToCol("nights"), nights, "nights-computed");
            }
            if (prop === 'guestIdentityNumber') {
              const guest = guestOptions?.find(opt => opt.label === newVal);
              if (guest) {
                hot.setDataAtCell(row, hot.propToCol("guestFullname"), guest.fullname as string, "guestFullname-computed");
                hot.setDataAtCell(row, hot.propToCol("guestCompanyName"), guest.companyName as string, "guestCompanyName-computed");
                hot.setDataAtCell(row, hot.propToCol("guestNationality"), guest.nationality as string, "guestNationaliy-computed");
                hot.setDataAtCell(row, hot.propToCol("guestCity"), guest.city as string, "guestCity-computed");
                hot.setDataAtCell(row, hot.propToCol("guestPhoneNumber"), guest.phoneNumber as string, "guestPhoneNumber-computed");
              }
            }
          }
        }
      }
    }
    // const allData = getValues();
    // // console.log('allData', allData);
  };

  // console.log(hotRef.current?.hotInstance?.getPlugin('CopyPaste'));


  useImperativeHandle(ref, () => ({
    getValuess,
  }));
  return (
    <div>
      <h5>Guest Informations
        {/* <ActionIcon
          size="sm"
          variant="text"
          onClick={() => setModalAddGuest(true)}
        >
          <PiUserCirclePlus className="h-auto w-6" strokeWidth={1.8} />
        </ActionIcon> */}
      </h5>
      <div className="overflow-scrollable">
        <HotTable
          ref={hotRef}
          colHeaders={["No", "Identity", "Name", "Company", "Nationality", "City", "Contact", "Check In", "Check Out", "Room Type", "Room Number", "Room Status", "Rate", "Nights", "Arr. Date", "#"]}
          data={data ?? []}
          columns={columns}
          autoWrapRow={true}
          autoWrapCol={true}
          height="360"
          fixedColumnsLeft={4}
          licenseKey="non-commercial-and-evaluation"
          copyPaste={true}
          copyable={true}
          afterChange={handleAfterChange}
          autoColumnSize={true}
        // plugins={['CopyPaste']}
        // Aktifkan fitur copy-paste
        // settings={{
        //   copyPaste: true
        // }}
        />
      </div>
      <div className="mt-6 flex gap-2">
        <Button
          onClick={addRow}
        // size="sm"
        >
          <PiPlusCircleBold className="w-5 h-5 mr-2" />
          Add Row
        </Button>
        {/* <Button onClick={getValuess} size="sm">Get Values</Button> */}
      </div>

      {/* <ModalAddGuest modal={modalAddGuest} setModal={setModalAddGuest} /> */}
    </div>
  );
})
ReservationGroupHotTable.displayName = "ReservationGroupHotTable";