export default function AnalyticsCircularIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M15.7036 10.4021C15.7036 13.774 12.9702 16.5074 9.59839 16.5074C6.22656 16.5074 3.49316 13.774 3.49316 10.4021C3.49316 7.03031 6.2266 4.29688 9.59839 4.29688C12.9702 4.29688 15.7036 7.03031 15.7036 10.4021Z"
        fill="#EDEDED"
      />
      <path
        d="M9.59887 1.15625C4.49306 1.15625 0.354004 5.2953 0.354004 10.4011C0.354004 15.5069 4.49306 19.646 9.59887 19.646C14.7047 19.646 18.8437 15.5069 18.8437 10.4011C18.8437 5.2953 14.7046 1.15625 9.59887 1.15625ZM9.59887 15.2436C6.92438 15.2436 4.7563 13.0756 4.7563 10.4011C4.7563 7.72659 6.92438 5.55851 9.59887 5.55851C12.2734 5.55851 14.4414 7.72659 14.4414 10.4011C14.4414 13.0756 12.2733 15.2436 9.59887 15.2436Z"
        fill="#C6C6C6"
      />
      <path
        d="M9.59839 3.35938C5.70824 3.35938 2.55469 6.51292 2.55469 10.4031C2.55469 14.2932 5.70824 17.4468 9.59839 17.4468C13.4885 17.4468 16.6421 14.2932 16.6421 10.4031C16.6421 6.51292 13.4885 3.35938 9.59839 3.35938ZM9.59839 15.2456C6.9239 15.2456 4.75582 13.0776 4.75582 10.4031C4.75582 7.72859 6.9239 5.5605 9.59839 5.5605C12.2729 5.5605 14.441 7.72859 14.441 10.4031C14.441 13.0776 12.2729 15.2456 9.59839 15.2456Z"
        fill="#969696"
      />
      <path
        d="M10.5247 0.35942C15.3488 0.814146 19.1881 4.65336 19.6428 9.47745C19.6914 9.99366 19.2842 10.4396 18.7658 10.4396H13.5001C13.276 10.4396 13.0876 10.2711 13.0629 10.0483C13.0611 10.032 13.0592 10.0157 13.0571 9.99936C12.8585 8.40628 11.5959 7.14363 10.0028 6.94495C9.98648 6.94292 9.97016 6.94101 9.95375 6.93917C9.73098 6.91452 9.5625 6.72609 9.5625 6.50195V1.23641C9.56258 0.717935 10.0085 0.310749 10.5247 0.35942Z"
        fill="#FF6029"
      />
      <path
        d="M16.6424 10.4397H13.5001C13.276 10.4397 13.0876 10.2712 13.0629 10.0484C13.0611 10.0321 13.0592 10.0158 13.0571 9.99944C12.8585 8.40636 11.5959 7.1437 10.0028 6.94503C9.98648 6.943 9.97016 6.94109 9.95375 6.93925C9.73098 6.9146 9.5625 6.72617 9.5625 6.50203V3.35984C9.57473 3.3598 9.58684 3.35938 9.59906 3.35938C13.4892 3.35938 16.6428 6.51292 16.6428 10.4031C16.6428 10.4153 16.6424 10.4274 16.6424 10.4397Z"
        fill="#B7441C"
      />
      <path
        d="M19.102 11.7328C18.7867 14.0062 17.6584 16.0962 15.925 17.6179C14.1754 19.1541 11.9281 20 9.59709 20C7.03362 20 4.62358 19.0017 2.81093 17.1891C0.998279 15.3764 0 12.9664 0 10.4029C0 8.07918 0.841326 5.83711 2.36906 4.08973C3.8821 2.35911 5.96221 1.22767 8.22639 0.903721C8.39885 0.87876 8.55897 0.998955 8.58369 1.17161C8.60842 1.34423 8.48846 1.50423 8.3158 1.52892C3.93511 2.15571 0.631561 5.97066 0.631561 10.4029C0.631561 15.3465 4.65351 19.3684 9.59705 19.3684C14.0485 19.3684 17.8658 16.0485 18.4764 11.646C18.5004 11.4733 18.6597 11.3526 18.8326 11.3767C19.0054 11.4006 19.126 11.56 19.102 11.7328ZM8.21225 3.1459C4.73421 3.80575 2.20984 6.85781 2.20984 10.4029C2.20984 14.4763 5.52378 17.7902 9.59705 17.7902C13.1629 17.7902 16.2183 15.2485 16.862 11.7465C16.8935 11.575 16.78 11.4104 16.6085 11.3789C16.437 11.3475 16.2724 11.4608 16.2408 11.6324C15.6522 14.8345 12.8581 17.1587 9.59705 17.1587C5.87198 17.1587 2.8414 14.1281 2.8414 10.403C2.8414 7.1609 5.14964 4.36985 8.32994 3.76641C8.50131 3.73391 8.61385 3.56864 8.58135 3.39727C8.54881 3.22594 8.38366 3.11352 8.21225 3.1459ZM8.35701 6.02824C8.52483 5.98078 8.62241 5.80625 8.57491 5.63844C8.52744 5.47067 8.35291 5.37313 8.1851 5.42055C5.96803 6.0477 4.41964 8.09656 4.41964 10.4029C4.41964 13.2578 6.74221 15.5803 9.59705 15.5803C11.9237 15.5803 13.9772 14.0146 14.5909 11.7728C14.6369 11.6046 14.5379 11.4308 14.3697 11.3848C14.2015 11.3386 14.0277 11.4378 13.9817 11.606C13.4429 13.5741 11.6399 14.9487 9.59705 14.9487C7.09046 14.9487 5.0512 12.9095 5.0512 10.4029C5.05124 8.37777 6.41065 6.57883 8.35701 6.02824ZM19.6877 10.3629C19.4607 10.6123 19.1371 10.7554 18.7998 10.7554H13.5135C13.1268 10.7554 12.8031 10.4657 12.7606 10.0815C12.759 10.0664 12.7572 10.0516 12.7554 10.0367C12.5731 8.57503 11.425 7.42687 9.96326 7.24461C9.9485 7.24277 9.93354 7.24101 9.91857 7.23933C9.53424 7.19683 9.24455 6.8732 9.24455 6.48648V1.20017C9.24455 0.86294 9.38764 0.539308 9.63705 0.312238C9.88611 0.0855977 10.2211 -0.0263161 10.5559 0.00524632C15.5539 0.476378 19.5235 4.44594 19.9946 9.44402C20.0263 9.77898 19.9144 10.1139 19.6877 10.3629ZM19.3659 9.50331C18.9232 4.8068 15.1932 1.07681 10.4967 0.634073C10.4783 0.632354 10.46 0.631456 10.4419 0.631456C10.3013 0.631456 10.1682 0.68294 10.0623 0.779346C9.94229 0.888526 9.87623 1.03802 9.87623 1.20013V6.48648C9.87623 6.55074 9.92428 6.60449 9.98795 6.61156C10.0057 6.61351 10.0236 6.61566 10.0414 6.61785C11.7904 6.83594 13.1641 8.20972 13.3822 9.95859C13.3844 9.97644 13.3865 9.99433 13.3885 10.0122C13.3955 10.0758 13.4493 10.1238 13.5136 10.1238H18.7999C18.9621 10.1238 19.1116 10.0577 19.2207 9.93773C19.3295 9.8182 19.3811 9.66394 19.3659 9.50331ZM10.861 3.1234C10.6892 3.0938 10.5258 3.20903 10.4961 3.3809C10.4665 3.55278 10.5818 3.71614 10.7536 3.74579C13.5428 4.22684 15.7506 6.42215 16.2475 9.20855C16.2748 9.36148 16.4079 9.46898 16.5581 9.46898C16.5765 9.46898 16.5951 9.46734 16.6139 9.46402C16.7856 9.43339 16.8999 9.26941 16.8693 9.09769C16.3259 6.05035 13.9113 3.64946 10.861 3.1234Z"
        fill="black"
      />
    </svg>
  );
}
