"use client";

import <PERSON><PERSON>eader<PERSON>ustom from "@/app/shared/page-header-custom";
import React, { useEffect, useState } from "react";
import Payments from "./_component/payments";
import CheckoutServices from "./_component/services";
import LoadingScreen from "@/components/theme/ui/loading-screen";
import ModalInvoice from "./_component/modal-preview-invoice";
import ReservationDetail from "./_component/reservation-detail";
import BookingRoom from "./_component/booking-room";
import CheckoutFnB from "./_component/fnb";
import { useForm } from "react-hook-form";
import { Button, Title } from "rizzui";
import { useRouter } from "next/navigation";
import { randStr } from "@/lib/helper/generate-random-string";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { useApiAppReservationDetailsById } from "@/lib/hooks/useApiAppReservationDetailsById";
import { useApiAppReservationsById } from "@/lib/hooks/useApiAppReservationsById";
import { useMasterPaymentDetailsOptions } from "@/lib/hooks/useMasterPaymentDetailss";
import { useMasterPaymentOptions } from "@/lib/hooks/useMasterPayments";
import {
  type PaymentDetailsDto,
  type FilterGroup,
  type ReservationFoodAndBeveragesDto,
  type ReservationRoomsDto,
  type PaymentsDto,
} from "@/client";

export default function DetailCheckout({
  wiithHeader = true,
  className,
  id,
}: {
  wiithHeader?: boolean;
  className?: string;
  id: string;
}) {
  const {
    register,
    unregister,
    setValue,
    getValues,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  // DEFINE HOOKS
  // DEFINE VARIABLES
  const [idResv, setIdResv] = useState<string>("");
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const { isLoading: isLoadingDetails, data: dataSourceDet } =
    useApiAppReservationDetailsById(id, refreshId);
  const { isLoading: isLoadingResv, data: dataSourceResv } =
    useApiAppReservationsById(idResv);
  const isLoading = isLoadingDetails || isLoadingResv;

  // DEFINE MODAL STATE
  const [modalPreview, setModalPreview] = useState<boolean>(false);
  const [paymentId, setPaymentId] = useState<string>("");
  const [attachmentId, setAttachmentId] = useState<string>("");
  const router = useRouter();

  const filterPayment: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "paymentDetails.reservationDetails.id",
        operator: "Equals",
        value: id,
      },
    ],
  };
  const filterPaymentDetails: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "reservationDetails.id",
        operator: "Equals",
        value: id,
      },
    ],
  };
  const { data: dataPayments } = useMasterPaymentOptions(1, 100, filterPayment);
  const { data: dataPaymentDetails } = useMasterPaymentDetailsOptions(
    0,
    100,
    filterPaymentDetails,
  );
  const paymentsRooms = dataPaymentDetails?.find(
    (pay: PaymentDetailsDto) => pay.sourceType == 1,
  );

  // TRANSFORM PAYMENTS TO FNV
  const fnb =
    dataPaymentDetails
      ?.filter((pay: PaymentDetailsDto) => pay.sourceType == 2)
      .flat()
      .map((e: PaymentDetailsDto) => {
        return {
          totalPrice: 3960000,
          quantity: 3,
          foodAndBeverageName:
            e.reservationFoodAndBeverages?.foodAndBeverageName,
          foodAndBeverageTypeName:
            e.reservationFoodAndBeverages?.foodAndBeverageTypeName,
          // "id": e.id,
        };
      }) ?? [];
  // TRANSFORM PAYMENTS TO SERVICES
  const service =
    dataPaymentDetails
      ?.filter((pay: PaymentDetailsDto) => pay.sourceType == 3)
      .flat()
      .map((e: PaymentDetailsDto) => {
        return {
          totalPrice: 3960000,
          quantity: 3,
          serviceName: e.reservationRoom?.serviceName,
          serviceTypeName: e.reservationRoom?.serviceTypeName,
          // "id": e.id,
        };
      }) ?? [];

  // FNB & SERVICES
  const fnbs = watch(
    "reservationDetails.0.reservationFoodAndBeverages",
  ) as (ReservationFoodAndBeveragesDto & { selected: boolean })[];
  const selectedFnbs = fnbs?.map((fnb) => {
    return { ...fnb, selected: true };
  });
  const services = watch(
    "reservationDetails.0.reservationRooms",
  ) as (ReservationRoomsDto & { selected: boolean })[];
  const selectedServices = services?.map((service) => {
    return { ...service, selected: true };
  });

  // DEFINE EFFECTS
  // set idResv to reservationId from data source
  useEffect(() => {
    setIdResv(dataSourceDet?.reservationId ?? "");
  }, [dataSourceDet]);
  // set setValue from data source
  useEffect(() => {
    // SET RESERVATIONS
    Object.entries(dataSourceResv ?? {}).forEach(([key, value]) => {
      setValue(key, value);
    });
    unregister("reservationDetails");
    // SET RESERVATIONS DETAILS
    Object.entries(dataSourceDet ?? {}).forEach(([key, value]) => {
      setValue(`reservationDetails.0.${key}`, value);
    });
    const fnbs = watch(
      "reservationDetails.0.reservationFoodAndBeverages",
    ) as (ReservationFoodAndBeveragesDto & { selected: boolean })[];
    // const unpaidFnbs = fnbs?.filter((fnb) => fnb.paymentStatus?.code !== "paid");
    setValue("reservationDetails.0.reservationFoodAndBeverages", fnbs);

    const services = watch(
      "reservationDetails.0.reservationRooms",
    ) as (ReservationRoomsDto & { selected: boolean })[];
    // const unpaidServices = services?.filter((serv) => serv.paymentStatus?.code !== "paid");
    setValue("reservationDetails.0.reservationRooms", services);
  }, [idResv, dataSourceResv]);

  // RE SET VALUE FOR RESERVATION DETAILS, FNB, AND SERVICES
  useEffect(() => {
    if (paymentsRooms?.reservationDetails) {
      setValue("reservationDetails.0", paymentsRooms?.reservationDetails);
      setValue("reservationDetails.0.price", paymentsRooms?.amount);
    }
    if (fnb) {
      setValue("reservationDetails.0.reservationFoodAndBeverages", fnb);
    }
    if (service) {
      setValue("reservationDetails.0.reservationRooms", service);
    }
    console.log("dataPaymentDetails", [paymentsRooms, fnb, service]);
  }, [dataPaymentDetails]);

  // SET PRICE FROM PAYMENT DETAILS
  useEffect(() => {
    setValue("reservationDetails.0.price", dataPaymentDetails?.[0]?.amount);
  }, [watch("reservationDetails.0.price")]);

  useEffect(() => {
    // SET PAID AMOUNT
    const paidAmount = dataPayments?.reduce((total, item: PaymentsDto) => {
      return total + Number(item.paidAmount);
    }, 0);
    setValue("payments.paidAmount", paidAmount);
    // SET PAID TAX
    const paidTax = dataPayments?.reduce((total, item: PaymentsDto) => {
      return total + Number(item.vatAmount);
    }, 0);
    setValue("payments.paidTax", paidTax);
    // SET GRANT TOTAL
    const grantTotal = dataPayments?.reduce((total, item: PaymentsDto) => {
      return total + Number(item.grantTotal);
    }, 0);
    setValue("payments.grantTotal", grantTotal);
    // SET PAYMENT METHOD NAME
    const paymentMethodName = [
      ...new Set(dataPayments?.map((p) => p.paymentMethod?.name)),
    ].join(", ");
    setValue("payments.paymentMethodName", paymentMethodName);
  }, [watch("payments.paidAmount"), watch("payments.paidTax"), dataPayments]);

  return (
    <div className={"mb-2 @container " + className}>
      {isLoading && <LoadingScreen />}
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Reservation", href: "/reservation" },
            { name: "Checkout", href: "/checkout" },
            { name: getValues("reservationCode") as string, href: "" },
          ]}
        >
          {/* <Button
            type="button"
            size="sm"
            variant="outline"
            className="rounded-lg px-4 py-2"
            onClick={() => console.log(getValues())}
          >
            Get Values
          </Button> */}
          <div className="flex gap-2">
            <b className="mt-1">Invoice:</b>
            {dataPayments?.map((e) => (
              <>
                <Button
                  key={e.value}
                  type="button"
                  size="sm"
                  onClick={() => {
                    setModalPreview(true);
                    setPaymentId(String(e.value));
                    setAttachmentId(String(e.paymentAttachments?.[0]?.id));
                  }}
                >
                  {e.paymentCode}:{" "}
                  <b>{formatCurrencyIDR(Number(e.grantTotal))}</b>
                </Button>
              </>
            ))}
            <ModalInvoice
              // isLoading={isLoading}
              modalPreview={modalPreview}
              setModalPreview={setModalPreview}
              paymentId={paymentId}
              attachmentId={attachmentId}
            />
          </div>
        </PageHeaderCustom>
      )}
      <form className="space-y-4">
        <div className="grid grid-cols-5 gap-8">
          <div className="col-span-2">
            <ReservationDetail
              isLoading={false}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
            />
          </div>
          <div className="col-span-3">
            <BookingRoom
              isLoading={false}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              readonly={true}
            />
            <CheckoutFnB
              register={register}
              unregister={unregister}
              setValue={setValue}
              fnbs={selectedFnbs}
              readonly={true}
            />
            <CheckoutServices
              register={register}
              unregister={unregister}
              setValue={setValue}
              services={selectedServices}
              readonly={true}
            />
          </div>
        </div>
        {/* BUTTON */}
        <div className="grid grid-cols-5 gap-8">
          <div className="col-span-2"></div>
          <div className="col-span-3">
            <Payments
              isLoading={false}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              selectedFnbs={selectedFnbs}
              selectedServices={selectedServices}
              isDetail={true}
            />
            <div className="mt-3 rounded border-2 bg-gray-50 px-4 py-2">
              <div className="flex justify-between">
                <div className="flex gap-2">
                  <Title as="h6" className="mb-0 p-1 text-gray-600">
                    {formatCurrencyIDR(Number(watch("payments.grantTotal")))}
                  </Title>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    isLoading={isLoading}
                    onClick={() => router.back()}
                    className="rounded-lg px-4 py-2"
                  >
                    Quit
                  </Button>
                </div>
              </div>
              <div className="flex gap-2"></div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
