import React from "react";

export default function SkeletonTable({
  rows = 5,
  columns = 5,
}: {
  rows?: number;
  columns?: number;
}) {
  return (
    <div className="animate-pulse">
      <table className="min-w-full border-collapse divide-y divide-gray-200">
        {/* <thead className="bg-slate-50">
          <tr>
            {Array.from({ length: columns }).map((_, index) => (
              <th
                key={index}
                className="px-4 py-2 text-left text-xs font-semibold uppercase tracking-wider text-gray-500"
              >
                <div className="h-4 w-24 rounded bg-gray-300"></div>
              </th>
            ))}
          </tr>
        </thead> */}
        <tbody className="divide-y divide-gray-200 bg-white">
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <tr key={rowIndex}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <td key={colIndex} className="px-4 py-2">
                  <div className="h-4 w-full rounded bg-gray-300"></div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
