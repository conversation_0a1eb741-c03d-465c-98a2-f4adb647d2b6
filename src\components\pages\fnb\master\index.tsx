"use client";
import React, { useEffect, useState } from "react";
import Form from "./form";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useForm } from "react-hook-form";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useMasterFnb } from "@/lib/hooks/fnb/masterFnb";
import {
  deleteApiAppFoodAndBeverageById,
  postApiAppFoodAndBeverage,
  putApiAppFoodAndBeverageById,
  type CreateUpdateFoodAndBeverageDto,
  type FoodAndBeverageDto,
} from "@/client";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import type { SelectOption } from "rizzui";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function MasterFnB({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isDataFnB, setDataFnB] = useState<FoodAndBeverageDto[]>([]);
  const [fnbType, setFnbType] = useState<SelectOption | undefined>();

  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 1000,
  });

  const { isLoading, data } = useMasterFnb(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
        price: item.price ?? 0,
        information: item.information ?? "",
        typeFoodAndBeverageId: item.typeFoodAndBeverageId ?? "",
        typeFoodAndBeverageName: item.typeFoodAndBeverageName ?? "",
      }));
      setDataFnB(mappedData);
    }
  }, [data]);

  const columns = [
    { dataIndex: "name", title: "Name", filter: "text" as const },
    {
      dataIndex: "typeFoodAndBeverageName",
      title: "Type",
      filter: "select" as const,
    },
    { dataIndex: "price", title: "Price", filter: "text" as const },
    { dataIndex: "information", title: "Information", filter: "text" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setFnbType(undefined);
    setIsEditMode(false);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetFnb],
    });
  };

  const deleteFnbMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppFoodAndBeverageById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "F&B deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetFnb],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "F&B will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete f&b with id:", id);
        await deleteFnbMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the f&b. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const data = isDataFnB.find((e) => e.id === id);
    if (data) {
      setIsEditMode(true);
      Object.entries(data).map(([key, value], _index) => {
        if (key === "typeFoodAndBeverageName") {
          setFnbType({
            label: data.typeFoodAndBeverageName ?? "",
            value: data.typeFoodAndBeverageId ?? "",
          });
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createFnbMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateFoodAndBeverageDto) =>
      postApiAppFoodAndBeverage({
        body: dataMutation,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "F&B Created Successfully",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetFnb],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateFnbMutation = useMutation({
    mutationFn: async (dataMutation: FoodAndBeverageDto) => {
      const { id, ...updateData } = dataMutation;

      return putApiAppFoodAndBeverageById({
        path: { id: id! },
        body: updateData as CreateUpdateFoodAndBeverageDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "F&B Updated Successfully",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetFnbs],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      if (isEditMode) {
        // console.log("Updating f&b:", formData);
        await updateFnbMutation.mutateAsync(formData);
      } else {
        formData.usageTime = Number(formData.usageTime);
        const { ...dataMutation } = formData;
        // console.log("Creating f&b:", dataMutation);
        await createFnbMutation.mutateAsync(
          dataMutation as unknown as CreateUpdateFoodAndBeverageDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  if (!can("WismaApp.ReservationFoodAndBeverages.View"))
    return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "F&B" },
            { name: "Master F&B" },
          ]}
        >
          {can("WismaApp.ReservationFoodAndBeverages.Create") &&
            can("WismaApp.ReservationFoodAndBeverages.Edit") && (
              <ButtonForm
                isLoading={isLoading}
                isFormVisible={isFormVisible}
                setIsFormVisible={(visible) => {
                  if (visible) {
                    handleReset();
                  }
                  setIsFormVisible(visible);
                }}
              />
            )}
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              fnbType={fnbType}
              setFnbType={setFnbType}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  price: formatCurrencyIDR(e.price ?? 0),
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
