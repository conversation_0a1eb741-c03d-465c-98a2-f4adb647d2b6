import { ReservationsDto } from "@/client";
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { DatetimeInput } from "@/components/theme/ui/input-type/dates-input";
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input";
import { countDays } from "@/lib/helper/count-days";
import React, { useEffect, useState } from "react";
import { FieldErrors, FieldValues, UseFormGetValues, UseFormHandleSubmit, UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import { PiArrowsLeftRight, PiArrowSquareOut, PiX } from "react-icons/pi";
import { ActionIcon, Button, Input, Modal, SelectOption, Text, Title } from "rizzui";

export default function ModalChangeRoom({
  register,
  errors,
  setValue,
  getValues,
  watch,
  roomTypeOptions,
  roomOptions,
  modal,
  setModal,
  handleSubmit,
  handlePost,
}: {
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<ReservationsDto>;
  readonly?: boolean;
  roomTypeOptions: SelectOption[];
  roomOptions: SelectOption[];
  modal: boolean;
  setModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleSubmit?: UseFormHandleSubmit<FieldValues, FieldValues>
  handlePost?: (e: FieldValues, command: string) => void;
}) {

  useEffect(() => {
    setValue("changeRoom.checkInDate", new Date())
    setValue("changeRoom.checkOutDate", getValues("reservationDetails.0.checkOutDate"))
    setValue("changeRoom.guestId", getValues("reservationDetails.0.guestId"))
    setValue("changeRoom.reservationId", getValues("reservationDetails.0.reservationId"))
    setValue("changeRoom.rfid", getValues("reservationDetails.0.rfid"))
    setValue("changeRoom.room.roomTypeId", getValues("reservationDetails.0.room.roomTypeId"))

    // Cleanup saat komponen di-unmount
    return () => {
      // setValue("changeRoom.checkInDate", undefined);
      // setValue("changeRoom.checkOutDate", undefined);
      // setValue("changeRoom.room.days", undefined);
      // setValue("changeRoom.price", undefined);
    };
  }, []);

  useEffect(() => {
    const days = countDays(new Date(getValues('changeRoom.checkInDate') as string), new Date(getValues('changeRoom.checkOutDate') as string));
    setValue("changeRoom.room.days", days);
    const selectedRoom = roomOptions?.find((opt) => opt.value == getValues('changeRoom.roomId'));
    // const days = getValues('changeRoom.room.days') ? Number(getValues('changeRoom.room.days')) : 1;
    setValue('changeRoom.price', ((selectedRoom?.price as number) * days))

  }, [watch("changeRoom.roomId"), roomOptions]);

  return (
    <>
      {/* PREVIEW FORM */}
      <Modal
        isOpen={modal}
        size={"xl"}
        onClose={() => setModal(false)}
        customSize="60vw"
      >
        <div className="m-auto px-7 pb-8 pt-6">
          <div className="mb-3 flex items-center justify-between">
            <Title as="h3">Change Rooms To</Title>
            <ActionIcon
              size="sm"
              variant="text"
              onClick={() => setModal(false)}
            >
              <PiX className="h-auto w-6" strokeWidth={1.8} />
            </ActionIcon>
          </div>
          <div className="mb-3">
            <div className="grid grid-cols-1 gap-2">
              {/* ROOM INFORMATION */}
              <div className="grid grid-cols-12 gap-2">
                <DropdownInput
                  className="col-span-2"
                  label={"Type"}
                  name={"changeRoom.room.roomTypeId"}
                  register={register}
                  setValue={setValue}
                  errors={errors}
                  watch={watch}
                  getValues={getValues}
                  options={roomTypeOptions}
                  required={true}
                  size="sm"
                // readOnly={true}
                />
                <AutocompleteSelect
                  className="col-span-1"
                  label={"Room"}
                  name={"changeRoom.roomId"}
                  register={register}
                  setValue={setValue}
                  errors={errors}
                  watch={watch}
                  getValues={getValues}
                  options={roomOptions?.filter((val) => val.roomTypeId == watch("changeRoom.room.roomTypeId"))}
                  required={true}
                  size="sm"
                // readOnly={true}
                />
                <DatetimeInput
                  className="col-span-3"
                  label={"Check In Date"}
                  name={"changeRoom.checkInDate"}
                  register={register}
                  setValue={setValue}
                  errors={errors}
                  watch={watch}
                  getValues={getValues}
                  required={true}
                  readOnly={true}
                  size="sm"
                />
                <DatetimeInput
                  className="col-span-3"
                  label={"Check Out Date"}
                  name={"changeRoom.checkOutDate"}
                  register={register}
                  setValue={setValue}
                  errors={errors}
                  watch={watch}
                  getValues={getValues}
                  required={true}
                  readOnly={true}
                  size="sm"
                />
                <Input
                  label="Days"
                  size="sm"
                  readOnly={true}
                  placeholder="Please fill in Days"
                  {...register("changeRoom.room.days", { required: true })}
                  className="col-span-1"
                />
                <CurrencyIDR
                  label={"Rate"}
                  name={"changeRoom.price"}
                  register={register}
                  setValue={setValue}
                  errors={errors}
                  watch={watch}
                  getValues={getValues}
                  options={roomOptions}
                  required={true}
                  size="sm"
                  className="col-span-2"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              // type="submit"
              size="sm"
              // isLoading={isLoadingResvDetOptions}
              onClick={async () => {
                if (handleSubmit && handlePost)
                  await handleSubmit((e) => handlePost(getValues(), "changeRoom"))();
              }}
              className="rounded-lg bg-blue-500 px-4 py-2 text-white"
            >
              Accept
            </Button>
            <Button
              type="button"
              size="sm"
              variant="outline"
              onClick={() => setModal(false)}
              className="rounded-lg px-4 py-2 disabled:bg-gray-400"
            >
              Quit
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}
