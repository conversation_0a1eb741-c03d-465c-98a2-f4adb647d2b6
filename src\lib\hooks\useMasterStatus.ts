﻿import { getApiMasterStatus } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import { type SelectOption } from "rizzui";

export const useMasterStatus = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetStatus, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiMasterStatus({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};

export const useMasterStatusOptions = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetStatus, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiMasterStatus({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      // GENERATE OPTIONS
      const options: SelectOption[] = data?.items?.map(val => ({
        label: val.name ?? "",
        value: val.id ?? "",
        id: val.id ?? 0,
        ...val
      })) ?? [];

      // RETURN OPTIONS
      return [
        // {label: "--Select--", value: 0, disabled: true},
        ...options
      ];
    },
  });
};
