import { contrastingColor } from "@/lib/helper/format-color";
import { useMasterStatus } from "@/lib/hooks/rooms/masterStatus/useMasterStatus";
import React, { useState } from "react";

export default function ListRoomStatus({
  setSelectedStatus,
}: {
  setSelectedStatus: (status: string | null) => void;
}) {
  const [activeStatus, setActiveStatus] = useState<string | null>(null);
  const { data: masterStatusData } = useMasterStatus();

  return (
    <div className="flex flex-wrap gap-2">
      {masterStatusData?.items?.map((item) => {
        const isActive = activeStatus === item.name;
        const borderColor = isActive
          ? contrastingColor(item.color ?? "#000000", -50)
          : "transparent";

        return (
          <div
            key={item.id}
            className="xl:w-18 flex w-16 cursor-pointer items-center justify-center rounded-lg border p-0 shadow-sm 2xl:w-16 2xl:p-3"
            style={{
              backgroundColor: item.color!,
              color: contrastingColor(item.color ?? "#000000", -50),
              borderColor: borderColor,
            }}
            onClick={() => {
              const newStatus =
                activeStatus === item.name ? null : (item.name ?? null);
              setActiveStatus(newStatus);
              setSelectedStatus(newStatus);
            }}
            title={item.name!}
          >
            <span className="text-[5px] font-normal sm:text-[7px] md:text-[9px] xl:text-[8px] 2xl:text-xs">
              {item.code}
            </span>
          </div>
        );
      })}
      <button
        className={`xl:w-18 flex w-16 cursor-pointer items-center justify-center rounded-lg bg-gray-200 p-1 shadow-sm 2xl:w-16 2xl:p-3 ${
          activeStatus === null ? "border-gray-400" : "border-transparent"
        }`}
        onClick={() => {
          setActiveStatus(null);
          setSelectedStatus(null);
        }}
        title="ALL STATUS"
      >
        <span className="text-[5px] font-normal sm:text-[7px] md:text-[9px] xl:text-[8px] 2xl:text-xs">
          ALL
        </span>
      </button>
    </div>
  );
}
