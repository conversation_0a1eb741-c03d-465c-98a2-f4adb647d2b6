"use client";

import { updateThemeColor } from "@/utils/update-theme-color";
import { ThemeProvider as NextThemeProvider } from "next-themes";
import { presetLight } from "@/config/color-presets";
import { useEffect } from "react";

export function ThemeProvider({ children }: React.PropsWithChildren<object>) {
  useEffect(() => {
    updateThemeColor(
      presetLight.lighter,
      presetLight.light,
      presetLight.default,
      presetLight.dark,
      presetLight.foreground,
    );
  }, []);
  return (
    <NextThemeProvider
      enableSystem={false}
      defaultTheme="light"
      disableTransitionOnChange
    >
      {children}
    </NextThemeProvider>
  );
}
