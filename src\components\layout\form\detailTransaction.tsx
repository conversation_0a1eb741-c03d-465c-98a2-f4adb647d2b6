// "use client";

import React from "react";
import SectionHeader from "@/components/container/header/sectionHeader";
import TransactionTable from "../custom-table/tableTransaction";
import { PiCardholderBold } from "react-icons/pi";
// import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";
import type { DetailTransactionProps } from "@/interfaces/transaction/detailTransactionType";
import MultipleSelect from "@/components/theme/ui/input-type/multiple-select";

export default function DetailTransaction({
  label,
  name,
  columns,
  data,
  register,
  errors,
  setValue,
  getValues,
  watch,
  options,
  onDelete,
  onQtyChange,
  onDateChange,
}: DetailTransactionProps) {
  return (
    <div className="rounded-lg border border-gray-300 bg-white p-4">
      <div className="flex flex-col gap-2">
        <SectionHeader title="Detail Transaction" icon={<PiCardholderBold />} />
        <div>
          {/* <AutocompleteSelect
            label={label}
            name={name}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={options}
            required={true}
          /> */}
          <MultipleSelect
            label={label}
            name={name}
            disable={false}
            options={options}
            setValue={setValue}
            getValues={getValues}
            register={register}
            errors={errors}
            watch={watch}
          />
        </div>
        <div className="pt-2">
          <TransactionTable
            columns={columns}
            dataSource={data}
            rowKey="id"
            pageSize={100}
            onDelete={onDelete}
            onQtyChange={onQtyChange}
            onDateChange={onDateChange}
            isLoading={false}
            register={register}
            errors={errors}
            setValue={setValue}
            getValues={getValues}
            watch={watch}
          />
        </div>
      </div>
    </div>
  );
}
