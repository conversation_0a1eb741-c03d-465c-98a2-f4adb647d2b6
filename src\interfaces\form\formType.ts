import type {
  FieldValues,
  UseFormRegister,
  UseFormGetValues,
  FieldErrors,
  UseFormWatch,
  Control,
} from "react-hook-form";

export interface FormProps {
  isLoading: boolean;
  onSubmit: (data: Record<string, string | number | File>) => void;
  register: UseFormRegister<FieldValues>;
  errors: FieldErrors<FieldValues>;
  handleSubmit: (
    callback: (data: Record<string, string | number>) => void,
  ) => (event: React.FormEvent) => void;
  setValue: (name: string, value: string | number) => void;
  getValues: UseFormGetValues<FieldValues>;
  setIsEditMode: (isEditMode: boolean) => void;
  watch: UseFormWatch<FieldValues>;
  onDelete: (id: string) => void;
  control?: Control<FieldValues>;
}
