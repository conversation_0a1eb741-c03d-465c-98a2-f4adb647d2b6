import React, { useEffect, useRef } from "react";
import type { ContextMenuProps } from "@/interfaces/dashboad/contextMenuType";

export default function ContextMenu({
  position,
  roomId,
  roomStatus,
  setContextMenuOpen,
  onUpdateRoomStatus,
}: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setContextMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [setContextMenuOpen]);

  const isUpdateDisabled = roomStatus?.label === "OVC IN STAY";

  return (
    <>
      <div
        ref={menuRef}
        className="w-50 absolute z-50 rounded-md bg-white shadow-lg"
        style={{
          top: position.y,
          left: position.x,
        }}
      >
        <ul className="divide-y divide-gray-200">
          <li
            className="mt-1 cursor-pointer px-4 py-2 text-xs font-normal hover:bg-gray-100"
            onClick={() => {
              // console.log("List NEW RESERVATION : ", roomId)
            }}
          >
            NEW RESERVATION
          </li>
          <li
            className="cursor-pointer px-4 py-2 text-xs font-normal hover:bg-gray-100"
            onClick={() => {
              window.location.href = `/config/masterGuest/info/${roomId}`;
            }}
          >
            GUEST INFO
          </li>
          <li
            className={`px-4 py-2 text-xs font-normal ${
              isUpdateDisabled
                ? "cursor-default text-gray-400"
                : "cursor-pointer hover:bg-gray-100"
            }`}
            onClick={!isUpdateDisabled ? onUpdateRoomStatus : undefined}
          >
            UPDATE ROOM STATUS
          </li>
          <li
            className="mb-1 cursor-pointer px-4 py-2 text-xs font-normal hover:bg-gray-100"
            onClick={() => {
              // console.log("List CHECK OUT : ", roomId)
            }}
          >
            CHECK OUT
          </li>
        </ul>
      </div>
    </>
  );
}
