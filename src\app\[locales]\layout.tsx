import type { Metada<PERSON> } from "next";
import { ThemeProvider } from "@/components/theme/theme-provider";
import { inter, lexendDeca } from "@/app/fonts";
import GlobalDrawer from "@/app/shared/drawer-views/container";
import GlobalModal from "@/app/shared/modal-views/container";
import { cn } from "@/utils/class-names";
import NextProgress from "@/components/theme/next-progress";
import { Toaster } from "react-hot-toast";
import "@/styles/globals.css";
import ReactQueryProviders from "@/components/theme/QueryClientProvider";

export const metadata: Metadata = {
  title: "WISMA",
  description: "IMIP WISMA",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      dir="ltr" // 💡 Prevent next-themes hydration warning
      suppressHydrationWarning
      // className="h-screen w-screen overflow-hidden"
      className="h-screen w-screen"
    >
      <body
        // 💡 Prevent hydration warnings caused by third-party extensions, such as Grammarly.
        suppressHydrationWarning
        className={cn(inter.variable, lexendDeca.variable, "font-inter")}
      >
        <ReactQueryProviders>
          <ThemeProvider>
            <NextProgress />
            {children}
            <Toaster />
            <GlobalDrawer />
            <GlobalModal />
          </ThemeProvider>
        </ReactQueryProviders>
      </body>
    </html>
  );
}
