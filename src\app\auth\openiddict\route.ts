import { clientConfig, siteConfig } from '@/config'
import { getSession } from '@/lib/actions'
import { createRedisInstance, type RedisSession } from '@/lib/redis'
import { getClientConfig } from '@/lib/session-utils'
import { headers } from 'next/headers'
import { type NextRequest } from 'next/server'

// Define types for OpenID Connect configuration and responses
interface OpenIdServerMetadata {
  issuer?: string
  token_endpoint?: string
  authorization_endpoint?: string
  userinfo_endpoint?: string
  [key: string]: unknown
}

// Used for type checking but not directly referenced
// eslint-disable-next-line @typescript-eslint/no-unused-vars
type OpenIdClientConfig = {
  serverMetadata: () => OpenIdServerMetadata
}

interface TokenResponse {
  access_token: string
  refresh_token?: string
  id_token?: string
  token_type: string
  expires_in?: number
  [key: string]: unknown
}

// Used for type checking but not directly referenced
// eslint-disable-next-line @typescript-eslint/no-unused-vars
type TokenSet = {
  access_token: string
  refresh_token?: string
  id_token?: string
  token_type: string
  expires_at?: number
  claims: () => Record<string, unknown>
}

interface UserInfo {
  sub: string
  name?: string
  given_name?: string
  email?: string
  email_verified?: boolean
  [key: string]: unknown
}

interface AuthCodeGrantOptions {
  pkceCodeVerifier: string
  expectedState: string
}

// Error types for better type safety
interface OAuthError {
  error: string
  error_description?: string
  [key: string]: unknown
}

interface OAuthWWWAuthenticateError extends Error {
  code: string
  status?: number
  cause?: unknown
  headers?: Record<string, string>
  wwwAuthenticate?: string
  error?: string
}

// Type guard for OAuthError - used in the error parsing logic
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function isOAuthError(obj: unknown): obj is OAuthError {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'error' in obj &&
    typeof (obj as Record<string, unknown>).error === 'string'
  )
}

// Type guard for OAuthWWWAuthenticateError
function isOAuthWWWAuthenticateError(obj: unknown): obj is OAuthWWWAuthenticateError {
  return (
    obj instanceof Error &&
    'code' in obj &&
    (obj as Record<string, unknown>).code === 'OAUTH_WWW_AUTHENTICATE_CHALLENGE'
  )
}

/**
 * Custom function to fetch user information from the userinfo endpoint
 */
async function customFetchUserInfo(config: { serverMetadata: () => OpenIdServerMetadata }, accessToken: string): Promise<UserInfo> {
  try {
    const userInfoEndpoint = config.serverMetadata().userinfo_endpoint
    if (!userInfoEndpoint) {
      throw new Error('No userinfo endpoint found in server metadata')
    }

    const response = await fetch(userInfoEndpoint, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Userinfo request failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      })
      throw new Error(`Userinfo request failed: ${response.status} ${response.statusText}`)
    }

    return response.json() as Promise<UserInfo>
  } catch (error) {
    console.error('Error fetching user info:', error)
    throw error
  }
}

/**
 * Helper function to check if the token endpoint is accessible
 */
async function checkTokenEndpoint(config: { serverMetadata: () => OpenIdServerMetadata }) {
  try {
    const metadata = config.serverMetadata()
    // console.log('OpenID configuration:', {
    //   issuer: metadata.issuer,
    //   tokenEndpoint: metadata.token_endpoint,
    //   authorizationEndpoint: metadata.authorization_endpoint,
    //   userInfoEndpoint: metadata.userinfo_endpoint,
    // })

    if (!metadata.token_endpoint) {
      console.error('Token endpoint not found in server metadata')
      return false
    }

    return true
  } catch (error) {
    console.error('Error checking token endpoint:', error)
    return false
  }
}

/**
 * Custom function to handle the authorization code grant with explicit client authentication
 */
async function customAuthorizationCodeGrant(
  config: { serverMetadata: () => OpenIdServerMetadata },
  url: URL,
  options: AuthCodeGrantOptions
): Promise<TokenResponse> {
  const code = url.searchParams.get('code')
  if (!code) {
    throw new Error('No authorization code found in URL')
  }

  const tokenEndpoint = config.serverMetadata().token_endpoint
  if (!tokenEndpoint) {
    throw new Error('No token endpoint found in server metadata')
  }

  const params = new URLSearchParams()
  params.append('grant_type', 'authorization_code')
  params.append('code', code)
  params.append('redirect_uri', clientConfig.redirect_uri)

  // Ensure code_verifier is properly passed
  if (options.pkceCodeVerifier) {
    // console.log('Using PKCE code_verifier with length:', options.pkceCodeVerifier.length)
    params.append('code_verifier', options.pkceCodeVerifier)
  } else {
    console.error('No PKCE code_verifier provided')
    throw new Error('Missing PKCE code_verifier')
  }

  // Add client authentication
  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    Authorization:
      'Basic ' +
      Buffer.from(`${clientConfig.client_id}:${clientConfig.client_secret}`).toString('base64'),
  }

  // console.log('Making token request to:', tokenEndpoint)
  // console.log('Request parameters:', Object.fromEntries(params))

  const response = await fetch(tokenEndpoint, {
    method: 'POST',
    headers,
    body: params,
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error('Token request failed:', {
      status: response.status,
      statusText: response.statusText,
      error: errorText,
    })

    // Parse the error response if it's JSON
    try {
      const errorJson = JSON.parse(errorText) as OAuthError
      if (
        errorJson.error === 'invalid_grant' &&
        errorJson.error_description?.includes('code_verifier')
      ) {
        throw new Error(`PKCE verification failed: ${errorJson.error_description ?? 'Unknown error'}`)
      } else if (
        errorJson.error === 'invalid_grant' &&
        errorJson.error_description?.includes('expired')
      ) {
        throw new Error(`Authorization code expired: ${errorJson.error_description ?? 'Unknown error'}`)
      }
    } catch {
      // If it's not valid JSON, continue with a generic error
    }

    throw new Error(`Token request failed: ${response.status} ${response.statusText}`)
  }

  const tokenData = await response.json() as TokenResponse
  return tokenData
}

/**
 * Handles the GET request for OpenID Connect authentication.
 *
 * This function performs the following steps:
 * 1. Retrieves the current session.
 * 2. Fetches the OpenID client configuration.
 * 3. Constructs the current URL from the request headers.
 * 4. Performs the authorization code grant flow to obtain tokens.
 * 5. Updates the session with the access token and user information.
 * 6. Saves the session.
 * 7. Stores the access and refresh tokens in Redis.
 * 8. Redirects the user to the post-login route.
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    const session = await getSession()
    const openIdClientConfig = await getClientConfig()

    // Check if the token endpoint is accessible
    await checkTokenEndpoint(openIdClientConfig)

    const headerList = headers();
    const host = headerList.get('x-forwarded-host') ?? headerList.get('host') ?? 'localhost'
    const protocol = headerList.get('x-forwarded-proto') ?? 'https'
    const currentUrl = new URL(
      `${protocol}://${host}${request.nextUrl.pathname}${request.nextUrl.search}`
    )

    // Check if the code is present in the URL
    const code = currentUrl.searchParams.get('code')
    if (!code) {
      console.error('No authorization code found in the URL')
      return Response.redirect(`${process.env.NEXT_PUBLIC_APP_URL}${siteConfig.baseLinks.login}`)
    }

    // Check if we have the required PKCE code verifier and state
    if (!session.code_verifier || !session.state) {
      console.error('Missing PKCE code verifier or state in session')
      return Response.redirect(`${process.env.NEXT_PUBLIC_APP_URL}${siteConfig.baseLinks.login}`)
    }

    // Check received state against stored state
    const receivedState = currentUrl.searchParams.get('state')
    if (receivedState !== session.state) {
      console.error('State mismatch, possible CSRF attack')
      return Response.redirect(`${process.env.NEXT_PUBLIC_APP_URL}${siteConfig.baseLinks.login}`)
    }

    let tokenSet
    try {
      // console.log('Attempting authorization code grant with:', {
      //   url: currentUrl.toString(),
      //   codeVerifierLength: session.code_verifier?.length || 0,
      //   stateLength: session.state?.length || 0,
      // })

      // Use custom function instead of the library's authorizationCodeGrant
      const tokenData = await customAuthorizationCodeGrant(openIdClientConfig, currentUrl, {
        pkceCodeVerifier: session.code_verifier,
        expectedState: session.state,
      })

      // Create a tokenSet-like object from the response
      tokenSet = {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        id_token: tokenData.id_token,
        token_type: tokenData.token_type,
        expires_at: tokenData.expires_in
          ? Math.floor(Date.now() / 1000) + tokenData.expires_in
          : undefined,
        claims: () => {
          if (tokenData.id_token) {
            // Simple JWT parsing
            const parts = tokenData.id_token.split('.')
            if (parts.length === 3) {
              const payload = JSON.parse(Buffer.from(parts[1] ?? '', 'base64').toString()) as Record<string, unknown>
              return payload
            }
          }
          return {}
        },
      }
    } catch (error: unknown) {
      if (isOAuthWWWAuthenticateError(error)) {
        console.error('Authentication challenge received:', {
          error: error.message,
          status: error.status,
          cause: error.cause,
          headers: error.headers,
          challenge: error.wwwAuthenticate,
        })

        // Check if this is an invalid_grant error that might be due to expired code
        if (error.error === 'invalid_grant') {
          // console.log('Invalid grant error detected, redirecting to login')
          return Response.redirect(
            `${process.env.NEXT_PUBLIC_APP_URL}${siteConfig.baseLinks.login}`
          )
        }

        // Check if this is an invalid_client error that might be due to client authentication issues
        if (error.error === 'invalid_client') {
          console.error('Invalid client error detected, check client credentials')
          return new Response(
            JSON.stringify({
              error: 'Authentication configuration error',
              message: 'There is an issue with the client authentication. Please contact support.',
            }),
            {
              status: 500,
              headers: { 'Content-Type': 'application/json' },
            }
          )
        }

        // For other WWW-Authenticate challenges, redirect to log in
        return Response.redirect(`${process.env.NEXT_PUBLIC_APP_URL}${siteConfig.baseLinks.login}`)
      }

      // Handle PKCE related errors
      if (
        error instanceof Error &&
        error.message &&
        (error.message.includes('code_verifier') ||
          error.message.includes('PKCE verification failed') ||
          error.message.includes('Authorization code expired'))
      ) {
        console.error('PKCE verification error:', error.message)
        return Response.redirect(`${process.env.NEXT_PUBLIC_APP_URL}${siteConfig.baseLinks.login}`)
      }

      // For other errors, rethrow to be caught by the outer try/catch
      throw error
    }

    const { access_token, refresh_token } = tokenSet
    session.isLoggedIn = true
    session.access_token = access_token
    // We could extract claims from tokenSet.claims() if needed

    // Call the userinfo endpoint to get user info using our custom function
    const userinfo = await customFetchUserInfo(openIdClientConfig, access_token)

    // store userinfo in session
    session.userInfo = {
      sub: userinfo.sub,
      name: userinfo.given_name ?? userinfo.name ?? '',
      email: userinfo.email ?? '',
      email_verified: userinfo.email_verified ?? false,
    }

    await session.save()

    const redisSessionData = {
      access_token: access_token,
      refresh_token: refresh_token,
    } as RedisSession

    const redis = createRedisInstance()
    const redisKey = `session:${session.userInfo.sub}`
    await redis.set(redisKey, JSON.stringify(redisSessionData))
    await redis.quit()
    return Response.redirect(clientConfig.post_login_route)
  } catch (error) {
    console.error('Authentication error:', error)
    // Return a more user-friendly error page
    return new Response(
      JSON.stringify({
        error: 'Authentication failed',
        message: 'There was an error during the authentication process. Please try again.',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}
