import Echo from "laravel-echo";
import Pusher from "pusher-js";


declare global {
  interface Window {
    Pusher: typeof Pusher;
  }
}

// Assign Pusher to the window object
if (typeof window !== 'undefined') {
  window.Pusher = Pusher;
}
export function initializeEcho() {
  const echo = new Echo({
    broadcaster: 'pusher',
    wsHost: "realtime-pusher.ably.io",
    key: 'Vvlh2Q.fzndew', // Replace with your Pusher key
    wsPort: "443",
    cluster: 'mt1', // Replace with your Pusher cluster
    forceTLS: false,
    encrypted: true,
  });

  return echo;
}