import { type InputProps } from "@/interfaces/form/inputType";
import React, { useEffect, useState } from "react";
import Select from "react-select";
import { type SelectOption, Text } from "rizzui"; // Adjust this import based on your setup
import { get } from "lodash";

export const AutocompleteSelect = (props: InputProps) => {
  const [val, setVal] = useState<SelectOption>();

  props.register(props.name, {
    value: val,
    required: props.required,
  });

  const optionsKey = props.options?.map((opt) => opt.value).join(",");
  useEffect(() => {
    const defaultVal = props.options?.find(
      (e) => e.value == props.getValues(props.name),
    );
    // console.log(`${props.name} GET`, props.getValues(props.name));
    // console.log(`${props.name} VAL`, defaultVal);
    setVal(defaultVal);
  }, [props.watch(props.name), optionsKey]);
  return (
    <div className={props.className ?? ""}>
      {props.label && (
        <>
          {props.size === "sm" ? (
            <Text as="p" className="mb-[0.275rem] text-xs text-gray-900">
              {props.label}
            </Text>
          ) : (
            <Text as="p" className="mb-[0.375rem] font-medium text-gray-900">
              {props.label}
            </Text>
          )}
        </>
      )}
      <div className={"" + props.readOnly && "cursor-not-allowed"}>
        <Select
          options={props.options}
          value={val}
          isDisabled={props.readOnly}
          isLoading={props.options?.length ? false : true}
          onInputChange={(newValue) => {
            if (newValue) {
              props.setValue(props.name, newValue);
              props.onChange && props.onChange();
            }
          }}
          onChange={(newValue, _actionMeta) => {
            if (newValue) {
              setVal(newValue);
              props.setValue(props.name, newValue.value);
            }
          }}
          classNamePrefix="rizzui-select"
          className={
            "z-51 border-red" +
            (get(props.errors, props.name) && !val && "[&>div>button]:border-2 [&>div>button]:border-red")
          }
          theme={(theme) => ({
            ...theme,
            borderRadius: 0,
            colors: {
              ...theme.colors,
              primary25: "#f0f0f0",
              primary: "#c5c5c5",
            },
          })}
          styles={{
            control: (base, _state) => ({
              ...base,
              borderRadius: "0.375rem",
              boxShadow: "none",
              ...(get(props.errors, props.name) &&
                !val && {
                border: "2px solid red",
              }),
              ...(props.size == "sm" && {
                fontSize: "0.75rem",
                minHeight: "2rem",
                lineHeight: "1rem",
                height: "2rem",
              }),
            }),
            valueContainer: (base, _state) => ({
              ...base,
              padding: "2px 0px 2px 8px !important",
            }),
            loadingIndicator: (base, _state) => ({
              // ...base,
              visibility: "hidden",
            }),
            loadingMessage: (base, _state) => ({
              // ...base,
              visibility: "hidden",
            }),
            indicatorsContainer: (base, _state) => ({
              // ...base,
              visibility: "hidden",
            }),
            menuList: (base, _state) => ({
              ...base,
              ...(props.size == "sm" && {
                padding: "0px",
                fontSize: "0.75rem",
              }),
              height: props.optionsHeight ?? "400px",
            }),
            dropdownIndicator: (base, _state) => ({
              ...base,
              ...(props.size == "sm" && {
                padding: "0px",
                fontSize: "0.75rem",
              }),
            }),
            input: (base) => ({
              ...base,
              appearance: "none",
              boxShadow: "none",
              borderColor: "transparent",
              height: "20px",
              "&:focus": {
                boxShadow: "none !important",
                outline: "none",
                borderColor: "transparent",
              },
            }),
          }}
        />
        {get(props.errors, props.name) && !val && (
          <Text as="small" className="text-red">
            {props.label + " is required"}
          </Text>
        )}
      </div>
    </div>
  );
};
