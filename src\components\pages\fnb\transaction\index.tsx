"use client";

import React, { useEffect, useState } from "react";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import DetailTransaction from "@/components/layout/form/detailTransaction";
import GuestInformation from "@/components/layout/form/guestInformation";
import { useForm } from "react-hook-form";
import { useMasterFnbOption } from "@/lib/hooks/fnb/masterFnb";
import { useApiAppReservationDetailOptions } from "@/lib/hooks/useApiAppReservationDetails";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { formatDate } from "@/lib/helper/format-date";
import {
  deleteApiAppReservationFoodAndBeveragesById,
  postApiAppReservationFoodAndBeverages,
  postApiAppReservationFoodAndBeveragesList,
  putApiAppReservationFoodAndBeveragesById,
  type FilterGroup,
  type CreateUpdateReservationFoodAndBeveragesDto,
  type ReservationDetailsDto,
} from "@/client";
import type { ItemTransactionType } from "@/interfaces/form/itemTransactionType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";
import { randStr } from "@/lib/helper/generate-random-string";

export default function TransactionFnB({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [guestData, setGuestData] = useState<ReservationDetailsDto>();
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const { data: masterFnbOpt } = useMasterFnbOption(0, 1000, "");
  const { data: reservDetailOpt } = useApiAppReservationDetailOptions(
    0,
    1000,
    "",
    refreshId,
  ) as { data: ReservationDetailsDto[] };
  const [guestOptions, setGuestOptions] = useState(
    (reservDetailOpt ?? [])
      .filter((item) => item?.status?.name === "Check In")
      .map((item) => ({
        value: item.id!,
        label: item.guest?.fullname ?? "Unknown Guest",
      })),
  );

  console.log("masterFnbOpt", masterFnbOpt);

  const [itemTransaction, setItemTransaction] = useState<ItemTransactionType[]>(
    [],
  );

  const columns = [
    { dataIndex: "date", title: "Date" },
    { dataIndex: "name", title: "Name" },
    { dataIndex: "type", title: "Type" },
    { dataIndex: "qty", title: "QTY" },
    { dataIndex: "price", title: "Price" },
  ];

  useEffect(() => {
    if (reservDetailOpt) {
      const filteredOptions = reservDetailOpt.filter(
        (item) => item?.status?.name === "Check In",
      );

      setGuestOptions(
        filteredOptions.map((item) => ({
          value: item.id!,
          label: `${item.room?.roomNumber} - ${item.guest?.fullname}`,
        })),
      );
    }
  }, [reservDetailOpt]);

  const handleSelectGuest = (selectedId: string) => {
    setRefreshId(randStr());
    const selectedGuest = reservDetailOpt?.find(
      (item) => item.id === selectedId,
    );
    const transformedData: Record<
      string,
      {
        id?: string;
        foodAndBeverageId: string;
        name: string;
        type: string;
        qty: number;
        price: number;
        unitPrice: number;
        transactionDate?: string;
      }
    > = {};

    if (selectedGuest) {
      setGuestData(selectedGuest);
      const reservFnb = selectedGuest?.reservationFoodAndBeverages;

      if (reservFnb && reservFnb.length > 0) {
        reservFnb.forEach((item) => {
          const key = item.id ?? "";
          const quantity = item.quantity ?? 0;
          const totalPrice = item.totalPrice ?? 0;
          const unitPrice = quantity > 0 ? totalPrice / quantity : 0;

          transformedData[key] = {
            id: key,
            foodAndBeverageId: item.foodAndBeverageId ?? "",
            name: item.foodAndBeverageName ?? "",
            type: item.foodAndBeverageTypeName ?? "",
            qty: quantity,
            price: totalPrice,
            unitPrice: unitPrice,
            transactionDate: formatDate(item.transactionDate ?? "", "datetime"),
          };
        });

        setItemTransaction(Object.values(transformedData));
      } else {
        setItemTransaction([]);
      }
    }
  };

  useEffect(() => {
    const selectedRfid = getValues("fnbGuestInfo") as string;
    if (selectedRfid) {
      handleSelectGuest(selectedRfid);
    }
  }, [watch("fnbGuestInfo")]);

  const createApiAppReservationFoodAndBeverages = useMutation({
    mutationFn: async (data: CreateUpdateReservationFoodAndBeveragesDto) =>
      postApiAppReservationFoodAndBeverages({
        body: data,
      }),
    onSuccess: async () => {
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.getApiAppReservationFoodAndBeverages],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateApiAppReservationFoodAndBeveragesById = useMutation({
    mutationFn: async (data: CreateUpdateReservationFoodAndBeveragesDto) => {
      const { id, ...updateData } = data;
      return putApiAppReservationFoodAndBeveragesById({
        path: { id: id! },
        body: updateData,
      });
    },
    onSuccess: async (res) => {
      console.log("success", res);
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationFoodAndBeveragesByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const removeApiAppReservationFoodAndBeveragesById = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppReservationFoodAndBeveragesById({
        path: { id: id },
      });
    },
    onSuccess: async (res) => {
      console.log("success remove", res);
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationFoodAndBeveragesByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  // UPDATE QTY RESERVATION FOOD AND BEVERAGE
  async function handleQtyChange(key: number, newQty: number) {
    const tempData = itemTransaction;
    try {
      if (tempData?.[key]) {
        const qty = Number(tempData[key]?.qty) + newQty;
        const price = Number(tempData[key].unitPrice) * qty;
        tempData[key].qty = qty;
        tempData[key].price = price;
        setItemTransaction([...tempData]);

        const transformedData: CreateUpdateReservationFoodAndBeveragesDto = {
          id: tempData[key]?.id ?? "",
          reservationDetailsId: guestData?.id ?? "",
          foodAndBeverageId: tempData[key]?.foodAndBeverageId ?? "",
          quantity: qty,
          totalPrice: price,
          transactionDate: tempData[key]?.transactionDate ?? "",
        };
        await updateApiAppReservationFoodAndBeveragesById.mutateAsync(
          transformedData,
        );
      }
    } catch (error) {
      console.error("Error in handleQtyChange:", error);
      const err = error as { details?: { error?: { message?: string } } };
      await swal({
        title: err.details?.error?.message ?? "An error occurred",
        text: "Please try again later",
        icon: "error",
      });
    }
  }

  // UPDATE DATE RESERVATION FOOD AND BEVERAGE
  async function handleDateChange(key: number, newDate: string | Date) {
    const tempData = itemTransaction;
    try {
      if (tempData?.[key]) {
        tempData[key].transactionDate = formatDate(newDate, "datetime");
        setItemTransaction([...tempData]);

        const transformedData: CreateUpdateReservationFoodAndBeveragesDto = {
          id: tempData[key]?.id ?? "",
          reservationDetailsId: guestData?.id ?? "",
          foodAndBeverageId: tempData[key]?.foodAndBeverageId ?? "",
          quantity: tempData[key]?.qty ?? 0,
          totalPrice: tempData[key]?.price ?? 0,
          transactionDate:
            typeof newDate === "string" ? newDate : newDate.toISOString(),
        };

        await updateApiAppReservationFoodAndBeveragesById.mutateAsync(
          transformedData,
        );
      }
    } catch (error) {
      console.error("Error in function handleDateChange:", error);
      const err = error as { details?: { error?: { message?: string } } };
      await swal({
        title: err.details?.error?.message ?? "An error occurred",
        text: "Please try again later",
        icon: "error",
      });
    }
  }

  // CREATE RESERVATION FOOD AND BEVERAGE
  async function handleSave(selectedId: string) {
    setRefreshId(randStr());
    const selectedItem = (masterFnbOpt ?? []).find(
      (item) => item.id === selectedId,
    );
    const reservationDetailsId = guestData?.id;
    if (!selectedItem || !reservationDetailsId) return;

    try {
      const quantity = 1;
      const price = selectedItem?.price as number;
      const newData = {
        id: undefined,
        reservationDetailsId: reservationDetailsId,
        foodAndBeverageId: selectedItem?.id as string,
        paymentStatusId: null,
        quantity,
        totalPrice: quantity * price,
        transactionDate: new Date(
          Date.now() + 8 * 60 * 60 * 1000,
        ).toISOString(),
      };
      await createApiAppReservationFoodAndBeverages.mutateAsync(newData);

      const filter: FilterGroup = {
        operator: "And",
        conditions: [
          {
            fieldName: "ReservationDetailsId",
            operator: "Equals",
            value: reservationDetailsId,
          },
        ],
      };

      const dataReservFnb = await postApiAppReservationFoodAndBeveragesList({
        body: {
          filterGroup: filter,
          maxResultCount: 100,
        },
      });

      console.log("dataReservFnb", dataReservFnb.data?.items);

      const dataTransaction = (dataReservFnb.data?.items ?? []).map((item) => ({
        id: item.id,
        foodAndBeverageId: item.foodAndBeverageId,
        name: item.foodAndBeverageName ?? "",
        type: item.foodAndBeverageTypeName ?? "",
        qty: item.quantity ?? 0,
        price: item.totalPrice ?? 0,
        unitPrice: (item.totalPrice ?? 0) / (item.quantity ?? 1),
        transactionDate: formatDate(item.transactionDate ?? "", "datetime"),
      }));

      console.log("dataTransaction", dataTransaction);

      setItemTransaction(dataTransaction);
    } catch (error) {
      console.error("Error in handleSave:", error);
      const err = error as { details?: { error?: { message?: string } } };
      await swal({
        title: err.details?.error?.message ?? "An error occurred",
        text: "Please try again later",
        icon: "error",
      });
    }
  }

  // DELETE RESERVATION FOOD AND BEVERAGE BY ID
  const handleDelete = async (key: number) => {
    // const confirmation = (await swal({
    //   title: "Remove",
    //   text: "Remove Transaction List?",
    //   icon: "info",
    //   buttons: ["Cancel", "Yes"],
    // })) as unknown as boolean;
    // if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

    const tempData = itemTransaction;

    try {
      if (tempData?.[key]) {
        const id = tempData[key].id;
        await removeApiAppReservationFoodAndBeveragesById.mutateAsync(id!);
        const newData = itemTransaction.filter((_, index) => index !== key);
        setItemTransaction(newData);
      }
    } catch (error: unknown) {
      console.error("Error in handleDelete:", error);
      const err = error as { details?: { error?: { message?: string } } };
      await swal({
        title: err.details?.error?.message ?? "An error occurred",
        text: "Please try again later",
        icon: "error",
      });
    }
  };

  useEffect(() => {
    const selectedId = getValues("fnbTransaction") as string[];
    try {
      if (selectedId.length) {
        selectedId.forEach((id) => {
          void handleSave(id);
        });
        setValue("fnbTransaction", []);
      }
    } catch (error) {
      console.error("Error in handleSave:", error);
    }
  }, [watch("fnbTransaction")]);

  if (!can("WismaApp.ReservationFoodAndBeverages.Create"))
    return <AccessDeniedLayout />;
  return (
    <div className={"mb-10 mt-4 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "F&B" },
            { name: "Transaction F&B" },
          ]}
        />
      )}
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-3">
          <GuestInformation
            label="F&B"
            name="fnbGuestInfo"
            register={register}
            errors={errors}
            setValue={setValue}
            getValues={getValues}
            watch={watch}
            options={guestOptions}
            data={guestData}
          />
        </div>
        <div className="col-span-9">
          <DetailTransaction
            label="F&B"
            name="fnbTransaction"
            columns={columns}
            data={itemTransaction as unknown as Record<string, unknown>[]}
            register={register}
            errors={errors}
            setValue={setValue}
            getValues={getValues}
            watch={watch}
            options={guestData ? (masterFnbOpt ?? []) : []}
            onDelete={handleDelete}
            onQtyChange={handleQtyChange}
            onDateChange={handleDateChange}
          />
        </div>
      </div>
    </div>
  );
}
