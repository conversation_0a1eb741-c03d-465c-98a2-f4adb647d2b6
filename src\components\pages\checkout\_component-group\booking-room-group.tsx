import React, { useEffect, useRef, useState } from "react";
import { ActionIcon, Button, Checkbox, Input, Popover, Text, Title } from "rizzui";
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input";
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";
import { DateInput, DatetimeInput } from "@/components/theme/ui/input-type/dates-input";
import { UseFormHandleSubmit, type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { useMasterRoomOptions } from "@/lib/hooks/useMasterRoom";
import { useMasterRoomTypesOptions } from "@/lib/hooks/useMasterRoomTypes";
import { FilterCondition, FilterGroup, ReservationDetailsDto, ReservationsDto } from "@/client";
import { countDays } from "@/lib/helper/count-days";
import { PiArrowsLeftRight, PiListBold } from "react-icons/pi";
import ModalChangeRoom from "../_component/modal-change-rooms";

export default function BookingRoom({
  register,
  errors,
  setValue,
  getValues,
  watch,
  readonly = false,
  handleSubmit,
  handlePost,
}: {
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<ReservationsDto>;
  readonly?: boolean;
  handleSubmit?: UseFormHandleSubmit<FieldValues, FieldValues>
  handlePost?: (e: FieldValues, command: string) => void;
}) {

  const [modalChangeRoom, setModalChangeRoom] = useState(false);

  const resvDet = (watch("reservationDetails") ?? []) as (ReservationDetailsDto & { selected?: boolean })[];
  const isGroup = resvDet?.length > 1;
  const roomsIds = resvDet?.map((e) => e.roomId).filter(id => id !== undefined) ?? [];
  const filterRooms: FilterGroup = {
    operator: "Or",
    conditions: [
      {
        fieldName: "roomStatus.name",
        operator: "Equals",
        value: "READY"
      },
      ...(roomsIds.length > 0 ? [
        {
          fieldName: "id",
          operator: "In",
          value: roomsIds.join(","),
        }
      ] as FilterCondition[] : []),
    ]
  }

  // OPTIONS
  const { data: roomOptions } = useMasterRoomOptions(0, 1000, filterRooms);
  const { data: roomTypeOptions } = useMasterRoomTypesOptions(0, 1000);

  // const paymentStatus = watch(`reservationDetails.0.paymentStatus.code`) as string;

  useEffect(() => {
    resvDet?.map((row, i) => {
      const isExtraBed = Boolean(getValues(`reservationDetails.${i}.isExtraBed`));
      const selectedRoom = roomOptions?.find((opt) => opt.value == getValues(`reservationDetails.${i}.roomId`));
      const price = isExtraBed ? 500000 : selectedRoom?.price as number;
      const days = getValues(`reservationDetails.${i}.days`) ? Number(getValues(`reservationDetails.${i}.days`)) : 1;
      setValue(`reservationDetails.${i}.price`, (price * days))
    })
  }, [resvDet, roomOptions]);

  useEffect(() => {
    resvDet?.map((row, i) => {
      const days = countDays(new Date(getValues(`reservationDetails.${i}.checkInDate`) as string), new Date(getValues(`reservationDetails.${i}.checkOutDate`) as string));
      setValue(`reservationDetails.${i}.days`, days);
    })
  }, [resvDet]);

  const hasSetDefaultSelected = useRef(false);
  useEffect(() => {
    if (resvDet?.length && resvDet[0]?.id && !hasSetDefaultSelected.current) {
      resvDet.map((row, i) => {
        setValue(`reservationDetails.${i}.selected`, true);
        hasSetDefaultSelected.current = true; // Jangan jalankan lagi
      })
    }
  }, [resvDet]);


  // EFFECTS
  const totalPrice = resvDet?.reduce((total, item) => {
    const price = item.selected ? Number(item.price) : 0;
    return total + price;
  }, 0);
  useEffect(() => {
    setValue("payments.roomTotalPrice", totalPrice);
  }, [totalPrice]);

  return (
    <div className="p-4 py-2 border-2 rounded bg-gray-50 mt-5">
      <div className="flex justify-between">
        <Text as="p" className="mb-0 font-bold text-sm text-gray-800">
          Booking Room
        </Text>
        {!isGroup && (watch('reservationDetails.0.status.code') == "checkin") && (
          <div>
            <Popover placement="right-start">
              <Popover.Trigger>
                <ActionIcon title={"More Options"} variant="outline" className="p-0 h-5 w-5">
                  <PiListBold className="h-5 w-5 text-gray-500" />
                </ActionIcon>
              </Popover.Trigger>
              <Popover.Content className="z-0 min-w-[140px] px-0 py-2">
                {({ setOpen }) => (
                  <div className="px-2 text-gray-900">
                    <Button
                      variant="text"
                      className="group flex w-full items-center justify-start px-2 py-2 hover:bg-gray-100 focus:outline-none"
                      onClick={() => {
                        setModalChangeRoom(true);
                        setOpen(false);
                      }}
                      size="sm"
                    >
                      <PiArrowsLeftRight className="mr-2 h-5 w-5 text-gray-500 duration-300 group-hover:text-primary" />
                      Change Rooms
                    </Button>
                  </div>
                )}
              </Popover.Content>
            </Popover>
          </div>
        )}
      </div>
      <div className="h-[0px] px-2">
        <Checkbox
          size="sm"
          inputClassName="h-4 w-4"
          iconClassName="h-4 w-4"
          onChange={(e) => {
            resvDet.map((row, i) => {
              setValue(`reservationDetails.${i}.selected`, e.target.checked);
            })
          }}
        />
      </div>
      <div className="grid grid-cols-1 gap-2">
        {/* ROOM INFORMATION */}
        {resvDet?.map((row, i) => (
          <div className="grid grid-cols-12 gap-2" key={i}>
            <div className="flex col-span-2">
              <div className={`mx-2 ${i == 0 ? "pt-7" : "pt-1"}`}>
                <Checkbox
                  // size="lg"
                  inputClassName="h-4 w-4"
                  iconClassName="h-4 w-4"
                  {...register(`reservationDetails.${i}.selected`)}
                />
              </div>
              <DropdownInput
                className="w-full"
                label={i == 0 ? "Type" : ""}
                name={`reservationDetails.${i}.room.roomTypeId`}
                register={register}
                setValue={setValue}
                errors={errors}
                watch={watch}
                getValues={getValues}
                options={roomTypeOptions}
                required={true}
                size="sm"
                readOnly={true}
              />
            </div>
            <AutocompleteSelect
              className="col-span-1"
              label={i == 0 ? "Room" : ""}
              name={`reservationDetails.${i}.roomId`}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
              options={roomOptions?.filter((val) => val.roomTypeId == watch(`reservationDetails.${i}.room.roomTypeId`))}
              required={true}
              size="sm"
              readOnly={true}
            />
            <DatetimeInput
              className="col-span-3"
              label={i == 0 ? "Check In Date" : ""}
              name={`reservationDetails.${i}.checkInDate`}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
              required={true}
              readOnly={true}
              size="sm"
            // maxDate={watch(`reservationDetails.${i}.checkOutDate") as Date | undefined}
            />
            <DatetimeInput
              className="col-span-3"
              label={i == 0 ? "Check Out Date" : ""}
              name={`reservationDetails.${i}.checkOutDate`}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
              required={true}
              // readOnly={(paymentStatus == "pending" || readonly) ? true : false}
              readOnly={true}
              // inline={true}
              size="sm"
            // minDate={watch(`reservationDetails.${i}.checkInDate`) as Date | undefined}
            />
            <Input
              label={i == 0 ? "Days" : ""}
              size="sm"
              readOnly={true}
              // disabled={isLoading}
              placeholder="Please fill in Days"
              {...register(`reservationDetails.${i}.days`, { required: true })}
              // error={errors.reservationDetails?.[0].days ? "Days is required" : undefined}
              className="col-span-1"
            />
            <CurrencyIDR
              label={i == 0 ? "Rate" : ""}
              name={`reservationDetails.${i}.price`}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
              options={roomOptions}
              required={true}
              size="sm"
              className="col-span-2"
            // readOnly={(paymentStatus == "pending" || readonly) ? true : false}
            />
          </div>
        ))}
      </div>
      {modalChangeRoom &&
        <ModalChangeRoom
          register={register}
          errors={errors}
          setValue={setValue}
          getValues={getValues}
          watch={watch}
          roomTypeOptions={roomTypeOptions ?? []}
          roomOptions={roomOptions ?? []}
          modal={modalChangeRoom}
          setModal={setModalChangeRoom}
          handleSubmit={handleSubmit}
          handlePost={handlePost}
        />
      }
    </div>
  );
}
