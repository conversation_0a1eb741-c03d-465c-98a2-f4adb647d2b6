export function contrastingColor(hexColor: string, amount: number): string {
  const color = hexColor.replace("#", "");

  const r = Math.max(
    Math.min(parseInt(color.substring(0, 2), 16) + amount, 255),
    0,
  );
  const g = Math.max(
    Math.min(parseInt(color.substring(2, 4), 16) + amount, 255),
    0,
  );
  const b = Math.max(
    Math.min(parseInt(color.substring(4, 6), 16) + amount, 255),
    0,
  );

  return `#${r.toString(16).padStart(2, "0")}${g
    .toString(16)
    .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
}

export function stripAlphaFromHex(hexColor: string): string {
  const color = hexColor.replace("#", "");
  return `#${color.substring(0, 6)}`;
}

export function addAlphaComponentColor(hexColor: string): string {
  return hexColor.toUpperCase() + "42";
}
