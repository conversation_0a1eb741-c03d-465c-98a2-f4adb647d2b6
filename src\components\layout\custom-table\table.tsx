import React, { useState, useEffect } from "react";
import PaginationTable from "./pagination";
import ButtonReset from "@/components/container/button/button-reset";
import PageSizeSelector from "./pageSizeSelector";
import { renderCellValue } from "@/lib/client/helper";
import type { CustomTableProps } from "@/interfaces/table/tableType";
import { formatTitle } from "@/lib/helper/format-title";
import { Empty, Text } from "rizzui";
import SelectFilter from "./filter/select";
import FreeTextFilter from "./filter/freeText";
import Sorting from "./filter/sorting";
import SkeletonTable from "@/components/container/loading/skeleton-table";

export default function CustomTable({
  columns,
  dataSource,
  rowKey,
  pageSize,
  isLoading = false,
  init,
}: CustomTableProps) {
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});
  const [selectFilters, setSelectFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [filteredData, setFilteredData] = useState(dataSource);
  const [sortedData, setSortedData] = useState(filteredData);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(pageSize);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [columnOrder, setColumnOrder] = useState(columns);
  const [draggedColumnIndex, setDraggedColumnIndex] = useState<string | null>(
    null,
  );
  const [showDropdown, setShowDropdown] = useState<string | null>(null);
  const [tempSelectFilters, setTempSelectFilters] = useState<
    Record<string, string[]>
  >({});

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        event.target instanceof HTMLElement &&
        !event.target.closest(".relative")
      ) {
        setShowDropdown(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const filtered = dataSource.filter((item) =>
      columns.every((column) => {
        if (column.filter === "none") return true;

        const searchValue = searchTerms[column.dataIndex] ?? "";
        const selectValue = selectFilters[column.dataIndex] ?? [];

        const value = column.dataIndex
          .split(".")
          .reduce<Record<
            string,
            unknown
          > | null>((acc, key) => (acc && typeof acc === "object" && key in acc ? (acc[key] as Record<string, unknown> | null) : null), item as Record<string, unknown> | null);
        if (column.filter === "text" && value) {
          // eslint-disable-next-line @typescript-eslint/no-base-to-string
          return value
            ?.toString()
            .toLowerCase()
            .includes(searchValue.toLowerCase());
        }

        if (column.filter === "select") {
          return (
            selectValue.length === 0 ||
            (typeof value === "string" && selectValue.includes(value))
          );
        }

        return true;
      }),
    );

    setFilteredData(filtered);
    setCurrentPage(1);
  }, [searchTerms, selectFilters, dataSource, columns]);

  useEffect(() => {
    if (!sortConfig) {
      setSortedData(filteredData);
    }
  }, [filteredData, sortConfig]);

  const handleSort = (key: string) => {
    if (key === "action") return;
    setSortConfig((prev) => {
      if (prev?.key === key) {
        return { key, direction: prev.direction === "asc" ? "desc" : "asc" };
      }
      return { key, direction: "asc" };
    });
  };

  const paginatedData = sortedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  const handleTextFilterChange = (dataIndex: string, value: string) => {
    setSearchTerms((prev) => ({
      ...prev,
      [dataIndex]: value,
    }));
  };

  const handleSelectFilterChange = (dataIndex: string, values: string[]) => {
    setSelectFilters((prev) => ({
      ...prev,
      [dataIndex]: values,
    }));
  };

  const handleReset = () => {
    setSearchTerms({});
    setSelectFilters({});
    setSortConfig(null);
    setTempSelectFilters({});
    setFilteredData(dataSource);
    setSortedData(dataSource);
    setCurrentPage(1);
    if (init) {
      void init();
    }
  };

  const handleDragStart = (
    e: React.DragEvent<HTMLTableHeaderCellElement>,
    dataIndex: string,
  ) => {
    e.dataTransfer.setData("text/plain", dataIndex);
    setDraggedColumnIndex(dataIndex);
  };

  const handleDrop = (
    e: React.DragEvent<HTMLTableHeaderCellElement>,
    targetDataIndex: string,
  ) => {
    const draggedDataIndex = e.dataTransfer.getData("text/plain");

    const draggedIndex = columnOrder.findIndex(
      (col) => col.dataIndex === draggedDataIndex,
    );
    const targetIndex = columnOrder.findIndex(
      (col) => col.dataIndex === targetDataIndex,
    );

    if (draggedIndex === -1 || targetIndex === -1) return;

    const updatedColumnOrder = [...columnOrder];
    const [draggedColumn] = updatedColumnOrder.splice(draggedIndex, 1);
    if (draggedColumn) {
      updatedColumnOrder.splice(targetIndex, 0, draggedColumn);
    }

    setColumnOrder(updatedColumnOrder);
    setDraggedColumnIndex(null);
  };

  return (
    <div className="custom-table">
      <div className="rounded-lg border border-gray-300">
        <div className="custom-scrollbar max-h-[410px] min-h-[225px] overflow-y-auto">
          {isLoading ? (
            <SkeletonTable rows={pageSize} columns={columns.length} />
          ) : (
            <table className="min-w-full border-collapse divide-y divide-gray-200">
              <thead className="bg-slate-50">
                <tr>
                  <th className="sticky top-0 bg-slate-50 px-4 py-2 text-center text-xs font-semibold uppercase tracking-wider text-gray-500">
                    NO
                  </th>
                  {columnOrder.map((column) => (
                    <th
                      key={column.dataIndex}
                      className={`sticky top-0 bg-slate-50 px-4 py-2 text-left text-xs font-semibold uppercase tracking-wider text-gray-500 ${
                        column.dataIndex === "action" ? "" : "cursor-move"
                      } ${
                        draggedColumnIndex === column.dataIndex
                          ? "bg-slate-300"
                          : ""
                      }`}
                      draggable={column.dataIndex !== "action"}
                      onDragStart={(e) => handleDragStart(e, column.dataIndex)}
                      onDragOver={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, column.dataIndex)}
                    >
                      <div className="flex items-center gap-1">
                        <span
                          onClick={() =>
                            column.dataIndex !== "action" &&
                            handleSort(column.dataIndex)
                          }
                          className={
                            column.dataIndex !== "action"
                              ? "cursor-move"
                              : "cursor-pointer"
                          }
                        >
                          {formatTitle(column.title)}
                        </span>
                        {column.dataIndex !== "action" && (
                          <Sorting
                            column={column}
                            sortConfig={sortConfig}
                            handleSort={handleSort}
                            filteredData={filteredData}
                            onSortedData={setSortedData}
                          />
                        )}
                      </div>
                      {column.filter !== "none" && (
                        <>
                          {column.filter === "text" && (
                            <FreeTextFilter
                              column={column}
                              searchTerms={searchTerms}
                              handleTextFilterChange={handleTextFilterChange}
                            />
                          )}
                          {column.filter === "select" && (
                            <SelectFilter
                              column={column}
                              dataSource={dataSource}
                              tempSelectFilters={tempSelectFilters}
                              setTempSelectFilters={setTempSelectFilters}
                              handleSelectFilterChange={
                                handleSelectFilterChange
                              }
                              searchTerms={searchTerms}
                              setSearchTerms={setSearchTerms}
                              showDropdown={showDropdown}
                              setShowDropdown={setShowDropdown}
                            />
                          )}
                        </>
                      )}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {paginatedData.length === 0 ? (
                  <tr>
                    <td
                      colSpan={columns.length + 1}
                      className="px-4 py-2 text-center text-sm text-gray-500"
                    >
                      <Empty />
                      <Text className="mt-2 text-center">
                        No Data Available.
                      </Text>
                    </td>
                  </tr>
                ) : (
                  paginatedData.map((item, i) => (
                    <tr
                      key={String(item[rowKey])}
                      className="transition-colors hover:bg-gray-50"
                    >
                      <td className="px-4 py-1 text-center text-xs">
                        {(currentPage - 1) * itemsPerPage + i + 1}.
                      </td>
                      {columnOrder.map((column) => {
                        const value = column.dataIndex
                          .split(".")
                          .reduce<Record<
                            string,
                            unknown
                          > | null>((acc, key) => (acc && typeof acc === "object" && key in acc ? (acc[key] as Record<string, unknown>) : null), item as Record<string, string | number>);

                        return (
                          <td
                            key={column.dataIndex}
                            className="justify-center whitespace-nowrap px-4 py-1 text-xs"
                          >
                            {column.render
                              ? column.render(value, item)
                              : renderCellValue(value)}
                          </td>
                        );
                      })}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <div className="mt-4">
          <ButtonReset isLoading={isLoading} handleReset={handleReset} />
        </div>
        <PageSizeSelector
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
        />
        <PaginationTable
          totalPages={Math.ceil(filteredData.length / itemsPerPage)}
          pageNumber={currentPage}
          setPageNumber={setCurrentPage}
        />
      </div>
    </div>
  );
}
