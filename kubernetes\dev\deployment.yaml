apiVersion: apps/v1
kind: Deployment
metadata:
  name: wismafrontend
  namespace: wismafrontend-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wismafrontend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  revisionHistoryLimit: 3
  template:
    metadata:
      labels:
        app: wismafrontend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/hostname
                    operator: In
                    values:
                      - k8s-worker1
      hostAliases:
        - ip: "**********"
          hostnames:
            - "api-identity.imip.co.id"
            - "api-identity-dev.imip.co.id"
            - "api-wisma-dev.imip.co.id"
            - "api-wisma.imip.co.id"
      containers:
        - name: wismafrontend
          image: ${CI_REGISTRY_IMAGE}/wismafrontend:${CI_COMMIT_SHA}
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 300m
              memory: 512Mi
          env:
            - name: NODE_ENV
              value: development
            - name: REDIS_HOST
              value: "**********"
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: wismafrontend-dev-secrets
                  key: redis-password
                  optional: true
            - name: SESSION_SECRET
              valueFrom:
                secretKeyRef:
                  name: wismafrontend-dev-secrets
                  key: session-secret
            - name: SESSION_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: wismafrontend-dev-secrets
                  key: session-password
            # Runtime environment variables that might need to be different from build time
            - name: NEXT_PUBLIC_API_URL
              value: "https://api-wisma-dev.imip.co.id"
            - name: NEXT_PUBLIC_APP_URL
              value: "https://wisma-dev.imip.co.id"
            - name: NEXT_PUBLIC_IMIP_AUTH_URL
              value: "https://api-identity-dev.imip.co.id"
            - name: NEXT_PUBLIC_STACK_ENV
              value: "development"
---
apiVersion: v1
kind: Service
metadata:
  name: wismafrontend
  namespace: wismafrontend-dev
  annotations:
    service.beta.kubernetes.io/external-traffic: "OnlyLocal"
spec:
  selector:
    app: wismafrontend
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: wismafrontend-nodeport
  namespace: wismafrontend-dev
spec:
  selector:
    app: wismafrontend
  ports:
    - port: 80
      targetPort: 3000
      nodePort: 30090
  type: NodePort
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wismafrontend
  namespace: wismafrontend-dev
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
    - host: wisma-dev.imip.co.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: wismafrontend
                port:
                  number: 80
