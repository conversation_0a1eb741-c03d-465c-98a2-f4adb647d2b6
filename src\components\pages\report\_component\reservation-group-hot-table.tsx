import React, { useRef } from "react";
import { HotTable } from "@handsontable/react-wrapper";
// import "handsontable/dist/handsontable.full.min.css";
import 'handsontable/styles/handsontable.css';
import 'handsontable/styles/ht-theme-main.css';
import Handsontable from "handsontable";
import { CellProperties, type ColumnSettings } from "node_modules/handsontable/settings";
import { createRoot } from "react-dom/client";
import { registerAllCellTypes } from "handsontable/cellTypes";
import { Settings } from "handsontable/plugins/nestedHeaders";
import { Empty } from "rizzui";
import LoadingCard from "@/components/theme/ui/loading-card";

interface HotTableWithInstance extends React.ComponentRef<typeof HotTable> {
  hotInstance: Handsontable.Core;
}
type RowRenderer = (instance: Handsontable.Core, td: HTMLTableCellElement, row: number, column: number, prop: string | number, value: Record<string, string>, cellProperties: CellProperties) => void;

// ON TESTING JADI MASIH TERHAMBUR
export function ReportRoomHotTable({
  isLoading,
  data,
  title,
  columns,
  nestedHeaders,
  withNumber,
}: {
  isLoading: boolean;
  data: Record<string, string | number | null>[];
  title: string;
  columns: Handsontable.ColumnSettings[];
  nestedHeaders?: Settings;
  withNumber?: boolean;
}) {
  const hotRef = useRef<HotTableWithInstance>(null);
  registerAllCellTypes();
  // console.log('data', data)

  // Struktur kolom untuk pemetaan data
  const columnss: ColumnSettings[] = [
    ...(withNumber ?
      [{
        title: "No", type: "text", width: 35, readOnly: true,
        renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
          td.innerHTML = "";
          const container = document.createElement("div");
          td.appendChild(container);
          createRoot(container).render(
            <center>{String(row + 1)}</center>
          );
        },
      }] : []),
    ...columns
  ];
  const colHeaders = columns.map((e) => e.title ?? "N/A")
  const lastRowRenderer = (instance: Handsontable.Core, td: HTMLTableCellElement, row: number, column: number, prop: string | number, value: Record<string, string>, cellProperties: CellProperties) => {
    Handsontable.renderers.TextRenderer(instance, td, row, column, prop, value, cellProperties);
    td.style.fontWeight = 'bold';
    // td.style.color = 'green';
    td.style.background = '#f6f3f4';
  };
  return (
    <div className="p-4 pr-0 border rounded bg-gray-50">
      <h5>{title}</h5>
      <div className="overflow-auto relative" style={{ width: "100%", minHeight: "65vh" }}>
      {/* <div className="overflow-auto relative" style={{ width: "100%"}}> */}
        <LoadingCard isLoading={isLoading} />
        {!data.length &&
          <Empty className="mt-10" text="No Data" />
        }
        {data.length ?
          <HotTable
            ref={hotRef}
            colHeaders={nestedHeaders ? undefined : colHeaders}
            nestedHeaders={nestedHeaders ?? undefined}
            data={data}
            columns={columnss}
            licenseKey="non-commercial-and-evaluation"
            copyPaste={true}
            className="htCenter"
            cells={(row, col) => {
              const cellProperties: { renderer?: RowRenderer, style?: string } = {};
              if (row === data.length - 1) {
                // cellProperties.style = 'background-color: red !important';
                // cellProperties.className = 'htCenter bg-gray-300';
                cellProperties.renderer = lastRowRenderer;
              }
              return cellProperties;
            }}
          />
          : <></>}
      </div>
    </div>
  );
}
