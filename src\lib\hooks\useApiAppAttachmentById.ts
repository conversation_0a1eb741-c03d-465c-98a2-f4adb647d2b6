﻿import { getApiAttachmentStreamById } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";

export const useApiAttachmentStreamById = (
  id: string,
  refreshId?: string
) => {
  return useQuery({
    queryKey: [QueryNames.GetReservationsById, id, refreshId],
    queryFn: async () => {
      const { data } = await getApiAttachmentStreamById({
        path: { id } // Replace with your reservation ID
      });
      return data;
    },
    enabled: !!id, // 👈 hanya fetch jika id tidak falsy (null, undefined, "")
  });
};