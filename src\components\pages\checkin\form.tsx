'use client';

import <PERSON><PERSON>eader<PERSON>ustom from "@/app/shared/page-header-custom";
import React, { useEffect, useState } from "react";
import { type FieldValue, useForm } from "react-hook-form";
import { type Reservation } from "@/interfaces/reservations/reservation";
import ReservationDetail from "./_component/reservation-detail";
import { useApiAppReservationDetailsById } from "@/lib/hooks/useApiAppReservationDetailsById";
import { useApiAppReservationsById } from "@/lib/hooks/useApiAppReservationsById";
import BookingRoom from "./_component/booking-room";
import { type CreateUpdateReservationDetailsDto, putApiAppReservationDetailsById, type ReservationDetailsDto, type ReservationsDto } from "@/client";
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import swal from "sweetalert";
import { useRouter } from "next/navigation";
import { preventEnter } from "@/lib/helper/prevent-enter";
import LoadingScreen from "@/components/theme/ui/loading-screen";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { formatDate } from "@/lib/helper/format-date";
import { swalError } from "@/lib/helper/swal-error";

export default function FormCheckin({
  wiithHeader = true,
  className,
  id,
}: {
  wiithHeader?: boolean;
  className?: string;
  id: string;
}) {
  const {
    register,
    unregister,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  // DEFINE HOOKS
  const queryClient = useQueryClient();
  const router = useRouter();

  // DEFINE VARIABLES
  const { isLoading: isLoadingDetails, data: dataSourceDet } = useApiAppReservationDetailsById(id);
  const { isLoading: isLoadingResvDetOptions, data: statusResvDetOptions } = useMasterStatusByDocTypeOptions("reservationDetails")
  const isLoading = (isLoadingDetails || isLoadingResvDetOptions)
  const [postLoading, setPostLoading] = useState<boolean>(false);
  const headerCode = `${String(getValues('reservationCode'))} (${String(getValues('reservationDetails.0.marketCode'))})`;

  // DEFINE MUTATIONS
  const updateReservationDetailsMutation = useMutation({
    mutationFn: async (dataMutation: ReservationDetailsDto) => {
      const { id, ...updateData } = dataMutation;

      return putApiAppReservationDetailsById({
        path: { id: id! },
        body: updateData as CreateUpdateReservationDetailsDto,
      });
    },
    onSuccess: async () => {
      setPostLoading(false);
      await swal({
        title: "Success",
        text: "Check In Successfully",
        icon: "success",
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.putApiAppReservationDetailsById] });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  // DEFINCE ACTIONS
  const handlePost = async (submitData: FieldValue<Reservation>) => {
    const confirmation = (await swal({
      title: "Checkin",
      text: "Checkin Reservation?",
      icon: "info",
      buttons: ["Cancel", "Yes"],
    })) as unknown as boolean;
    if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

    setPostLoading(true);
    // // console.log("submit data", submitData);
    const resv = JSON.parse(JSON.stringify(submitData)) as ReservationsDto;
    const resvDet = resv.reservationDetails?.[0];
    const statusId = statusResvDetOptions?.find(opt => opt.code == "checkin")?.value // code fore Check In
    const data: ReservationDetailsDto = {
      "id": resvDet?.id,
      "reservationId": resvDet?.reservationId,
      "roomId": resvDet?.roomId,
      "statusId": statusId as string,
      "guestId": resvDet?.guestId,
      // "checkInDate": resvDet?.checkInDate,
      "checkInDate": formatDate(new Date(), "datetime"),
      "checkOutDate": resvDet?.checkOutDate,
      "rfid": resvDet?.rfid,
      "price": resvDet?.price,
    }
    // // console.log("POST data", data);
    await updateReservationDetailsMutation.mutateAsync(data);
    router.push('/checkout');
    setPostLoading(false);
  }

  // DEFINE USE EFFECT
  // set setValue from data source
  useEffect(() => {
    Object.entries(dataSourceDet?.reservation ?? {}).forEach(([key, value]) => {
      setValue(key, value);
    });
    unregister("reservationDetails");
    Object.entries(dataSourceDet ?? {}).forEach(([key, value]) => {
      setValue(`reservationDetails.0.${key}`, value);
    });
    setValue('reservationDetails.0.checkInDate', new Date())
  }, [dataSourceDet])
  
  // --PERMISSION--
  const { can } = useGrantedPolicies();
  if(!can("WismaApp.Reservation.Edit")) return <AccessDeniedLayout />; 
  return (
    <div className={"mb-10 mt-4 @container " + className}>
      {(isLoading || postLoading) && <LoadingScreen />}
      {wiithHeader && (
        <>
          <PageHeaderCustom
            breadcrumb={[
              { name: "Reservation", href: "/reservation" },
              { name: "Checkin", href: "/checkin" },
              { name: headerCode, href: "" },
            ]}
          >
          </PageHeaderCustom>
        </>
      )}
      {/* <form onSubmit={handleSubmit((e)=> postDataReservations(JSON.parse(JSON.stringify(e))))} className="space-y-4"> */}
      <form onSubmit={handleSubmit(handlePost)} onKeyDown={preventEnter} className="space-y-4">
        <div className="grid grid-cols-3 gap-8">
          <div className="col-span-1">
            <ReservationDetail
              isLoading={isLoadingResvDetOptions}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
            />
          </div>
          <div className="col-span-2">
            <BookingRoom
              isLoading={false}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch} />
            {/* BUTTON */}
          </div>
        </div>
      </form>
    </div>
  );
}
