"use client";
import React, { useEffect, useState } from "react";
import <PERSON>Header<PERSON>ustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useMasterDiningOption } from "@/lib/hooks/config/masterDiningOption";
import {
  deleteApiMasterDiningOptionsById,
  postApiMasterDiningOptions,
  putApiMasterDiningOptionsById,
  type CreateUpdateDiningOptionsDto,
  type DiningOptionsDto,
} from "@/client";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function MasterDiningOption({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isDataDiningOption, setDataDiningOption] = useState<
    DiningOptionsDto[]
  >([]);

  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 1000,
  });

  const { isLoading, data } = useMasterDiningOption(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
      }));
      setDataDiningOption(mappedData as DiningOptionsDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "name", title: "Name", filter: "text" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetDiningOption],
    });
  };

  const deleteMasterDiningOptionMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiMasterDiningOptionsById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Dining option deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetDiningOption],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Dining Option will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete Dining Option with id:", id);
        await deleteMasterDiningOptionMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the Dining Option. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const data = isDataDiningOption.find((e) => e.id === id);
    if (data) {
      setIsEditMode(true);
      Object.entries(data).map(([key, value], _index) => {
        setValue(key, value);
      });
    }
  };

  const createMasterDiningOptionMutation = useMutation({
    mutationFn: async (data: CreateUpdateDiningOptionsDto) =>
      postApiMasterDiningOptions({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Dining option created successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetDiningOption],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateMasterDiningOptionMutation = useMutation({
    mutationFn: async (data: DiningOptionsDto) => {
      const { id, ...updateData } = data;
      return putApiMasterDiningOptionsById({
        path: { id: id! },
        body: updateData as CreateUpdateDiningOptionsDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Dining option updated successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetDiningOption],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      if (isEditMode) {
        // console.log("Updating Dining Option:", formData);
        await updateMasterDiningOptionMutation.mutateAsync(
          formData as DiningOptionsDto,
        );
      } else {
        // console.log("Creating Dining Option:", formData);
        await createMasterDiningOptionMutation.mutateAsync(
          formData as CreateUpdateDiningOptionsDto,
        );
      }
      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  if (!can("WismaApp.DiningOptions.View")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Config" },
            { name: "Master Dining Option" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
