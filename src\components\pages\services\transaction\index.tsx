"use client";

import React, { useEffect, useState } from "react";
import PageHeader<PERSON>ustom from "@/app/shared/page-header-custom";
import DetailTransaction from "@/components/layout/form/detailTransaction";
import GuestInformation from "@/components/layout/form/guestInformation";
import { formatDate } from "@/lib/helper/format-date";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useMasterServiceOption } from "@/lib/hooks/services/masterServices";
import { useApiAppReservationDetailOptions } from "@/lib/hooks/useApiAppReservationDetails";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import {
  deleteApiAppReservationRoomsById,
  postApiAppReservationRooms,
  postApiAppReservationRoomsList,
  putApiAppReservationRoomsById,
  type CreateUpdateReservationRoomsDto,
  type ReservationDetailsDto,
} from "@/client";
import type { FilterGroup } from "@/client";
import type { ItemTransactionType } from "@/interfaces/form/itemTransactionType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";
import { randStr } from "@/lib/helper/generate-random-string";

export default function TransactionService({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const [guestData, setGuestData] = useState<ReservationDetailsDto>();
  const { data: masterServiceOpt } = useMasterServiceOption(0, 1000, "");
  const { data: reservDetailOpt } = useApiAppReservationDetailOptions(
    0,
    1000,
    "",
    refreshId,
  ) as { data: ReservationDetailsDto[] };
  const [guestOptions, setGuestOptions] = useState(
    (reservDetailOpt ?? [])
      .filter((item) => item?.status?.name === "Check In")
      .map((item) => ({
        value: item.id!,
        label: item.guest?.fullname ?? "Unknown Guest",
      })),
  );

  const [itemTransaction, setItemTransaction] = useState<ItemTransactionType[]>(
    [],
  );

  const columns = [
    { dataIndex: "date", title: "Date" },
    { dataIndex: "name", title: "Name" },
    { dataIndex: "qty", title: "Usage Time" },
    { dataIndex: "time", title: "Time" },
    { dataIndex: "price", title: "Price" },
  ];

  useEffect(() => {
    if (reservDetailOpt) {
      const filteredOptions = reservDetailOpt.filter(
        (item) => item?.status?.name === "Check In",
      );

      setGuestOptions(
        filteredOptions.map((item) => ({
          value: item.id!,
          label: `${item.room?.roomNumber} - ${item.guest?.fullname}`,
        })),
      );
    }
  }, [reservDetailOpt]);

  const handleSelectGuest = (selectedId: string) => {
    setRefreshId(randStr());
    const selectedGuest = reservDetailOpt?.find(
      (item) => item.id === selectedId,
    );
    const transformedData: Record<
      string,
      {
        id?: string;
        serviceId: string;
        name: string;
        type: string;
        qty: number;
        price: number;
        unitPrice: number;
        time: string;
        transactionDate: string;
      }
    > = {};

    if (selectedGuest) {
      setGuestData(selectedGuest);
      const reservService = selectedGuest?.reservationRooms;

      if (reservService && reservService.length > 0) {
        reservService.forEach((item) => {
          const key = item.id ?? "";
          const quantity = item.quantity ?? 0;
          const totalPrice = item.totalPrice ?? 0;
          const unitPrice = quantity > 0 ? totalPrice / quantity : 0;

          transformedData[key] = {
            id: key,
            serviceId: item.serviceId ?? "",
            name: item.serviceName ?? "",
            type: item.serviceTypeName ?? "",
            qty: quantity,
            price: totalPrice,
            unitPrice: unitPrice,
            time: "Jam",
            transactionDate: formatDate(item.transactionDate ?? "", "datetime"),
          };
        });

        setItemTransaction(Object.values(transformedData));
      } else {
        setItemTransaction([]);
      }
    }
  };

  useEffect(() => {
    const selectedRfid = getValues("serviceGuestInfo") as string;
    if (selectedRfid) {
      handleSelectGuest(selectedRfid);
    }
  }, [watch("serviceGuestInfo")]);

  const createApiAppReservationRooms = useMutation({
    mutationFn: async (data: CreateUpdateReservationRoomsDto) =>
      postApiAppReservationRooms({
        body: data,
      }),
    onSuccess: async () => {
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.getApiAppReservationRooms],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateApiAppReservationRoomsById = useMutation({
    mutationFn: async (data: CreateUpdateReservationRoomsDto) => {
      const { id } = data;
      return putApiAppReservationRoomsById({
        path: { id: id! },
        body: data,
      });
    },
    onSuccess: async (res) => {
      console.log("success", res);
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationRoomsByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const removeApiAppReservationRoomsById = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppReservationRoomsById({
        path: { id: id },
      });
    },
    onSuccess: async (res) => {
      console.log("success remove", res);
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationRoomsByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  // UPDATE QTY RESERVATION ROOMS
  async function handleQtyChange(key: number, newQty: number) {
    const tempData = itemTransaction;

    try {
      if (tempData?.[key]) {
        const qty = Number(tempData[key]?.qty) + newQty;
        const price = Number(tempData[key].unitPrice) * qty;
        tempData[key].qty = qty;
        tempData[key].price = price;
        setItemTransaction([...tempData]);

        const transformedData: CreateUpdateReservationRoomsDto = {
          id: tempData[key]?.id ?? "",
          reservationDetailsId: guestData?.id ?? "",
          serviceId: tempData[key]?.serviceId ?? "",
          quantity: qty,
          totalPrice: price,
          transactionDate: tempData[key]?.transactionDate ?? "",
        };

        await updateApiAppReservationRoomsById.mutateAsync(transformedData);
      }
    } catch (error) {
      console.error("Error in handleQtyChange:", error);
      const err = error as { details?: { error?: { message?: string } } };
      await swal({
        title: err.details?.error?.message ?? "An error occurred",
        text: "Please try again later",
        icon: "error",
      });
    }
  }

  // UPDATE DATE RESERVATION ROOMS
  async function handleDateChange(key: number, newDate: string | Date) {
    const tempData = itemTransaction;
    try {
      if (tempData?.[key]) {
        tempData[key].transactionDate = formatDate(newDate, "datetime");
        setItemTransaction([...tempData]);

        const transformedData: CreateUpdateReservationRoomsDto = {
          id: tempData[key]?.id ?? "",
          reservationDetailsId: guestData?.id ?? "",
          serviceId: tempData[key]?.serviceId ?? "",
          quantity: tempData[key]?.qty ?? 0,
          totalPrice: tempData[key]?.price ?? 0,
          transactionDate:
            typeof newDate === "string" ? newDate : newDate.toISOString(),
        };

        await updateApiAppReservationRoomsById.mutateAsync(transformedData);
      }
    } catch (error) {
      console.error("Error in function handleDateChange:", error);
      const err = error as { details?: { error?: { message?: string } } };
      await swal({
        title: err.details?.error?.message ?? "An error occurred",
        text: "Please try again later",
        icon: "error",
      });
    }
  }

  // CREATE RESERVATION ROOMS
  async function handleSave(selectedId: string) {
    const selectedItem = (masterServiceOpt ?? []).find(
      (item) => item.id === selectedId,
    );
    const reservationDetailsId = guestData?.id;
    if (!selectedItem || !reservationDetailsId) return;

    try {
      const quantity = 1;
      const price = selectedItem?.price as number;
      const newData = {
        id: undefined,
        reservationDetailsId: reservationDetailsId,
        serviceId: selectedItem?.id as string,
        paymentStatusId: null,
        quantity,
        price: price,
        totalPrice: quantity * price,
        transactionDate: new Date(
          Date.now() + 8 * 60 * 60 * 1000,
        ).toISOString(),
      };
      await createApiAppReservationRooms.mutateAsync(newData);

      const filter: FilterGroup = {
        operator: "And",
        conditions: [
          {
            fieldName: "ReservationDetailsId",
            operator: "Equals",
            value: reservationDetailsId,
          },
        ],
      };

      const dataReservRooms = await postApiAppReservationRoomsList({
        body: {
          filterGroup: filter,
          maxResultCount: 100,
        },
      });

      const dataTransaction = (dataReservRooms.data?.items ?? []).map(
        (item) => ({
          id: item.id,
          serviceId: item.serviceId,
          name: item.serviceName ?? "",
          type: item.serviceTypeName ?? "",
          qty: item.quantity ?? 0,
          price: item.totalPrice ?? 0,
          unitPrice: (item.totalPrice ?? 0) / (item.quantity ?? 1),
          time: "Jam",
          transactionDate: formatDate(item.transactionDate ?? "", "datetime"),
        }),
      );

      setItemTransaction(dataTransaction);
    } catch (error) {
      console.error("Error in handleSave:", error);
      const err = error as { details?: { error?: { message?: string } } };
      await swal({
        title: err.details?.error?.message ?? "An error occurred",
        text: "Please try again later",
        icon: "error",
      });
    }
  }

  // DELETE RESERVATION ROOMS BY ID
  const handleDelete = async (key: number) => {
    // const confirmation = (await swal({
    //   title: "Remove",
    //   text: "Remove Transaction List?",
    //   icon: "info",
    //   buttons: ["Cancel", "Yes"],
    // })) as unknown as boolean;
    // if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

    const tempData = itemTransaction;

    try {
      if (tempData?.[key]) {
        const id = tempData[key].id;
        await removeApiAppReservationRoomsById.mutateAsync(id!);
        const newData = itemTransaction.filter((_, index) => index !== key);
        setItemTransaction(newData);
      }
    } catch (error: unknown) {
      console.error("Error in handleDelete:", error);
      const err = error as { details?: { error?: { message?: string } } };
      await swal({
        title: err.details?.error?.message ?? "An error occurred",
        text: "Please try again later",
        icon: "error",
      });
    }
  };

  useEffect(() => {
    const selectedId = getValues("serviceTransaction") as string[];
    try {
      if (selectedId.length) {
        selectedId.forEach((id) => {
          void handleSave(id);
        });
        setValue("serviceTransaction", []);
      }
    } catch (error) {
      console.error("Error in handleSave:", error);
    }
  }, [watch("serviceTransaction")]);

  if (!can("WismaApp.ReservationRoom.Create")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-10 mt-4 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Service" },
            { name: "Service Transaction" },
          ]}
        />
      )}
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-3">
          <GuestInformation
            label="Services"
            name="serviceGuestInfo"
            register={register}
            errors={errors}
            setValue={setValue}
            getValues={getValues}
            watch={watch}
            options={guestOptions}
            data={guestData}
          />
        </div>
        <div className="col-span-9">
          <DetailTransaction
            label="Services"
            name="serviceTransaction"
            columns={columns}
            data={itemTransaction as unknown as Record<string, unknown>[]}
            register={register}
            errors={errors}
            setValue={setValue}
            getValues={getValues}
            watch={watch}
            options={guestData ? (masterServiceOpt ?? []) : []}
            onDelete={handleDelete}
            onQtyChange={handleQtyChange}
            onDateChange={handleDateChange}
          />
        </div>
      </div>
    </div>
  );
}
