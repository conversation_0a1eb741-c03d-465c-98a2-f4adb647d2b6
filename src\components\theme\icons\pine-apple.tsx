export default function PineAppleIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" {...props}>
      <path
        fill="#5EC62E"
        d="M7.733 5.572a14.36 14.36 0 0 0-1.87-.63h-.009C4.528 5.139 3.4 6.06 2.76 7.469a.149.149 0 0 0 .038.175.138.138 0 0 0 .167.013 11.176 11.176 0 0 1 3.946-1.535l.004-.001.004-.003c.256-.195.53-.366.815-.51l.04-.019-.041-.017Z"
      />
      <path
        fill="#4EAA18"
        d="M3.531 7.241c.494-1.088 1.28-1.884 2.222-2.282-1.282.228-2.37 1.138-2.993 2.51a.149.149 0 0 0 .038.175.138.138 0 0 0 .167.014c.183-.115.368-.223.556-.327a.15.15 0 0 1 .01-.09Z"
      />
      <path
        fill="#5EC62E"
        d="M8.208 5.373a11.602 11.602 0 0 0-1.883-2.021l-.005-.004c-1.613-.637-3.443-.317-5.02.878a.178.178 0 0 0-.06.208.17.17 0 0 0 .172.113 14.24 14.24 0 0 1 6.307 1.062l.009.004.008-.004c.15-.076.306-.145.463-.205l.025-.01-.016-.021Z"
      />
      <path
        fill="#4EAA18"
        d="M7.733 4.766a14.229 14.229 0 0 0-5.628-.801.17.17 0 0 1-.172-.114.19.19 0 0 1-.01-.044c-.211.125-.42.264-.623.418a.178.178 0 0 0-.06.208.17.17 0 0 0 .172.113A14.24 14.24 0 0 1 7.719 5.61l.009.003.008-.004c.15-.075.305-.144.463-.205l.025-.01-.016-.02c-.151-.208-.31-.41-.475-.607Z"
      />
      <path
        fill="#5EC62E"
        d="M8.076 3.432v-.004l-.002-.003c-.825-1.548-2.459-2.292-4.37-1.989a.145.145 0 0 0-.122.126.14.14 0 0 0 .082.15 11.5 11.5 0 0 1 4.512 3.685l.009.012.014-.005c.123-.048.25-.091.378-.13l.016-.004-.004-.048a3.217 3.217 0 0 0-.007-.081v-.004l-.002-.003a4.6 4.6 0 0 1-.504-1.702Z"
      />
      <path
        fill="#4EAA18"
        d="M8.242 4.295a11.476 11.476 0 0 0-3.807-2.811.14.14 0 0 1-.082-.11 5.35 5.35 0 0 0-.648.062.145.145 0 0 0-.123.126.14.14 0 0 0 .082.15 11.5 11.5 0 0 1 4.512 3.685l.009.012.014-.005c.123-.048.25-.091.378-.13l.016-.004-.004-.048a3.217 3.217 0 0 0-.007-.081v-.004l-.002-.003a4.938 4.938 0 0 1-.338-.839Z"
      />
      <path
        fill="#5EC62E"
        d="M14.695 3.936c-2.214-1.416-3.973-.432-3.978-.431-.439.482-.852 1.002-1.226 1.547l-.024.036.043-.005c.317-.035.64-.04.961-.016h.005l.004-.002c1.326-.55 2.72-.84 4.146-.86a.138.138 0 0 0 .133-.102.149.149 0 0 0-.064-.167Z"
      />
      <path
        fill="#4EAA18"
        d="M10.718 3.506c-.44.481-.852 1.002-1.226 1.546l-.025.037.043-.005c.318-.035.64-.04.961-.017h.005l.004-.001.057-.023a13.5 13.5 0 0 1 1.71-1.869c-.878 0-1.424.297-1.53.332Z"
      />
      <path
        fill="#5EC62E"
        d="M14.155.42c-3.346-.244-4.688 2-4.692 2.002-.267.884-.428 1.8-.479 2.72l-.001.027.025-.006a5.24 5.24 0 0 1 .5-.08h.01l.005-.008A14.24 14.24 0 0 1 14.236.75a.17.17 0 0 0 .082-.188.178.178 0 0 0-.163-.142Z"
      />
      <path
        fill="#4EAA18"
        d="M10.26 2.361c.055-.195.14-.298.165-.34.531-.746 1.153-1.158 2.012-1.494-1.232.257-2.271.907-2.97 1.89-.043.178-.4 1.212-.483 2.726l-.001.026.025-.005c.166-.035.334-.062.5-.08l.01-.001.005-.008c.108-.157.219-.311.333-.464.06-.76.196-1.515.404-2.25Z"
      />
      <path
        fill="#5EC62E"
        d="M10.79-.572a.145.145 0 0 0-.171-.04C8.191.469 7.342 2.919 8.597 5.252l.007.014.016-.004c.128-.037.259-.07.388-.098l.015-.003.001-.015a11.5 11.5 0 0 1 1.78-5.547.14.14 0 0 0-.014-.17Z"
      />
      <path
        fill="#5EC62E"
        d="M10.79-.572a.145.145 0 0 0-.171-.04C8.191.469 7.342 2.919 8.597 5.252l.007.014.016-.004c.128-.037.259-.07.388-.098l.015-.003.001-.015a11.5 11.5 0 0 1 1.78-5.547.14.14 0 0 0-.014-.17Z"
      />
      <path
        fill="#4EAA18"
        d="M10.807-.547a.145.145 0 0 0-.188-.064C8.191.469 7.342 2.919 8.597 5.25l.007.014.016-.004c.128-.037.259-.07.388-.098l.015-.003.001-.015c.012-.217.03-.433.055-.648C8.303 2.55 8.933.563 10.807-.547Z"
      />
      <path
        fill="#FFC850"
        d="M8.577 5.194c-2.812.832-4.431 3.814-3.599 6.625l1.235 4.171c.832 2.812 3.814 4.431 6.626 3.6 2.811-.833 4.43-3.815 3.598-6.626l-1.234-4.172c-.833-2.811-3.814-4.43-6.626-3.598Z"
      />
      <path
        fill="#F9B428"
        d="m6.984 15.762-1.235-4.171c-.794-2.682.643-5.519 3.218-6.497a5.35 5.35 0 0 0-.39.1c-2.812.832-4.431 3.814-3.599 6.625l1.235 4.171c.832 2.812 3.814 4.432 6.625 3.6a5.33 5.33 0 0 0 .382-.13c-2.693.581-5.442-1.016-6.236-3.698Z"
      />
      <path
        fill="#ED9D0F"
        d="M16.435 15.98c.06-.199.107-.402.143-.608l-1.934-1.05 1.421-2.616-.342-1.156-2.607-1.416 1.159-2.128a5.325 5.325 0 0 0-.427-.477l-1.262 2.317-2.85-1.548 1.22-2.25a5.308 5.308 0 0 0-.648-.068L9.207 7.01 7.123 5.878a5.292 5.292 0 0 0-.524.402l2.32 1.26-1.538 2.837-2.464-1.34c-.051.206-.09.414-.116.624l2.293 1.246-1.538 2.836-.01-.005.247.833.005-.01 2.838 1.541L7.5 18.186c.145.149.298.288.459.419l1.205-2.215L12 17.929l-1.015 1.867c.227.015.456.015.686 0l.858-1.58 1.536.835c.194-.117.38-.246.556-.385l-1.804-.98 1.54-2.834 2.079 1.129Zm-.78-4.781-1.54 2.835-2.83-1.537 1.543-2.834 2.826 1.536Zm-6.206-3.37 2.849 1.547-1.543 2.833-2.844-1.545L9.45 7.828ZM6.086 14.03l1.538-2.836 2.843 1.544-1.543 2.834-2.838-1.542Zm6.2 3.368L9.454 15.86l1.543-2.833 2.83 1.537-1.54 2.835Z"
      />
    </svg>
  );
}
