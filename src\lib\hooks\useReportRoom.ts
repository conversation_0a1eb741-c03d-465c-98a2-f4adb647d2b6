﻿import { getApiAppRoomStatus, getApiAppRoomType } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import { type SelectOption } from "rizzui";

export const useReportRoomDaily = (
  filter?: string,
  sorting?: string,
) => {
  // return useQuery({
  //   queryKey: [QueryNames.GetRoomStatus, pageIndex, pageSize, filter, sorting],
  //   queryFn: async () => {
  //     let skip = 0;
  //     if (pageIndex && pageSize) {
  //       skip = pageIndex * pageSize;
  //     }
  //     const { data } = await getApiAppRoomStatus({
  //       query: {
  //         MaxResultCount: pageSize,
  //         SkipCount: skip,
  //         Sorting: sorting,
  //       },
  //     });

  //     return data;
  //   },
  // });

  return [
    { "type": "OVS全景套房", "total": "6", "mod": "0", "mtc": "0", "ls": "3", "tamu_ci": "2", "sisa": "1", "persen_kosong": "17%" },
    { "type": "ES", "total": "2", "mod": "0", "mtc": "0", "ls": "0", "tamu_ci": "2", "sisa": "0", "persen_kosong": "0%" },
    { "type": "DS", "total": "5", "mod": "0", "mtc": "0", "ls": "5", "tamu_ci": "0", "sisa": "0", "persen_kosong": "0%" },
    { "type": "OVK", "total": "59", "mod": "0", "mtc": "1", "ls": "33", "tamu_ci": "21", "sisa": "4", "persen_kosong": "7%" },
    { "type": "DK", "total": "9", "mod": "0", "mtc": "0", "ls": "0", "tamu_ci": "9", "sisa": "0", "persen_kosong": "0%" },
    { "type": "BK", "total": "45", "mod": "5", "mtc": "0", "ls": "0", "tamu_ci": "38", "sisa": "2", "persen_kosong": "4%" },
    { "type": "合计", "total": "126", "mod": "5", "mtc": "1", "ls": "41", "tamu_ci": "72", "sisa": "7", "persen_kosong": "6%" }
  ]
};

export const useReportRoomMonthly = (
  filter?: string,
  sorting?: string,
) => {
  // return useQuery({
  //   queryKey: [QueryNames.GetRoomStatus, pageIndex, pageSize, filter, sorting],
  //   queryFn: async () => {
  //     let skip = 0;
  //     if (pageIndex && pageSize) {
  //       skip = pageIndex * pageSize;
  //     }
  //     const { data } = await getApiAppRoomStatus({
  //       query: {
  //         MaxResultCount: pageSize,
  //         SkipCount: skip,
  //         Sorting: sorting,
  //       },
  //     });

  //     return data;
  //   },
  // });

  return [
    { "date": "1/1/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 40, "tamu": 19, "sisa": 62, "persen_kosong": "49%" },
    { "date": "1/2/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 40, "tamu": 21, "sisa": 60, "persen_kosong": "48%" },
    { "date": "1/3/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 20, "sisa": 60, "persen_kosong": "48%" },
    { "date": "1/4/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 13, "sisa": 67, "persen_kosong": "53%" },
    { "date": "1/5/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 18, "sisa": 62, "persen_kosong": "49%" },
    { "date": "1/6/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 23, "sisa": 57, "persen_kosong": "45%" },
    { "date": "1/7/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 20, "sisa": 60, "persen_kosong": "48%" },
    { "date": "1/8/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 44, "sisa": 36, "persen_kosong": "29%" },
    { "date": "1/9/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 40, "sisa": 40, "persen_kosong": "32%" },
    { "date": "1/10/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 42, "sisa": 38, "persen_kosong": "30%" },
    { "date": "1/11/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 51, "sisa": 29, "persen_kosong": "23%" },
    { "date": "1/12/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 44, "sisa": 36, "persen_kosong": "29%" },
    { "date": "1/13/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": 58, "sisa": 22, "persen_kosong": "17%" },
    { "date": "1/14/2025", "total_kamar": 127, "mod_mtc": 5, "ls": 41, "tamu": 72, "sisa": 9, "persen_kosong": "7%" },
    { "date": "1/15/2025", "total_kamar": 127, "mod_mtc": 5, "ls": 41, "tamu": 80, "sisa": 1, "persen_kosong": "1%" },
    { "date": "1/16/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/17/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/18/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/19/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/20/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/21/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/22/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/23/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/24/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/25/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/26/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/27/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/28/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/29/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/30/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null },
    { "date": "1/31/2025", "total_kamar": 126, "mod_mtc": 5, "ls": 41, "tamu": null, "sisa": null, "persen_kosong": null }
  ]
};

export const useReportRoomRental = (
  filter?: string,
  sorting?: string,
) => {
  // return useQuery({
  //   queryKey: [QueryNames.GetRoomStatus, pageIndex, pageSize, filter, sorting],
  //   queryFn: async () => {
  //     let skip = 0;
  //     if (pageIndex && pageSize) {
  //       skip = pageIndex * pageSize;
  //     }
  //     const { data } = await getApiAppRoomStatus({
  //       query: {
  //         MaxResultCount: pageSize,
  //         SkipCount: skip,
  //         Sorting: sorting,
  //       },
  //     });

  //     return data;
  //   },
  // });

  return [
    { "Date": "2024-01-31", "RoomNo.": "8201", "Company": "PT.GCNS", "GuestName": "方毅", "DocumentName": "FANG YI", "GuestID": "E43744290", "Nationality": "CHN", "IDAddress": "HENAN/河南", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8206", "Company": "PT.GCNS", "GuestName": "王鑫", "DocumentName": "WANG XIN", "GuestID": "E34520894", "Nationality": "CHN", "IDAddress": "HUBEI", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8207", "Company": "PT.ITSS", "GuestName": "秦华祥", "DocumentName": "QIN HUAXIANG", "GuestID": "E64548814", "Nationality": "CHN", "IDAddress": "CHONGQING", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8208", "Company": "PT.IRNC", "GuestName": "谭本江", "DocumentName": "TAN BENJIANG", "GuestID": "E18132523", "Nationality": "CHN", "IDAddress": "CHONGQING", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8209", "Company": "PT.ITSS", "GuestName": "林玉才", "DocumentName": "LIN YUCAI", "GuestID": "E78996033", "Nationality": "CHN", "IDAddress": "HENAN", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8210", "Company": "PT.PMKI", "GuestName": "董金军", "DocumentName": "DONG JINJUN", "GuestID": "E37093296", "Nationality": "CHN", "IDAddress": "ANHUI", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8212", "Company": "PT.DSI", "GuestName": "刘浩", "DocumentName": "LIU HAO", "GuestID": "EJ2854612", "Nationality": "CHN", "IDAddress": "TIANJIN", "DateIn": "2021-03-16", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8216", "Company": "PT.SMI", "GuestName": "梅雄峰", "DocumentName": "MEI XIONGFENG", "GuestID": "E27866846", "Nationality": "CHN", "IDAddress": "HUBEI", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8217", "Company": "", "GuestName": "赵元", "DocumentName": "ZHAO YUAN", "GuestID": "EG5795050", "Nationality": "CHN", "IDAddress": "GANSU", "DateIn": "2024-01-24", "DateOut": "2024-02-07", "LengthStay": 14, "RoomPrice": "800000 盾/夜", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8218", "Company": "", "GuestName": "ZULKIFLI ARMAN", "DocumentName": "ZULKIFLI ARMAN", "GuestID": "****************", "Nationality": "IDN", "IDAddress": "KABUPATEN BOGOR", "DateIn": "2024-01-29", "DateOut": "2024-02-02", "LengthStay": 4, "RoomPrice": "1380000 盾/夜", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8220", "Company": "PT.IMIP", "GuestName": "项炳舜", "DocumentName": "XIANG BINGSHUN", "GuestID": "G60172059", "Nationality": "CHN", "IDAddress": "ZHEJIANG", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8221", "Company": "PT.DCI", "GuestName": "李建伟", "DocumentName": "LI JIANWEI", "GuestID": "E83881723", "Nationality": "CHN", "IDAddress": "HEBEI", "DateIn": "2023-07-01", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8222", "Company": "PT.ITSS", "GuestName": "许绍飞", "DocumentName": "XU SHAOFEI", "GuestID": "E72768468", "Nationality": "CHN", "IDAddress": "ZHEJIANG", "DateIn": "Long Stay", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" },
    { "Date": "2024-01-31", "RoomNo.": "8223", "Company": "PT.DCI", "GuestName": "熊周民", "DocumentName": "XIONG ZHOUMIN", "GuestID": "EJ2852374", "Nationality": "CHN", "IDAddress": "TIANJIN", "DateIn": "2023-09-01", "DateOut": "Long Stay", "LengthStay": "Long Stay", "RoomPrice": "10000 元/月", "TotalwithVAT": "VAT", "PaymentCash": "", "PaymentBank": "", "InvoiceNo": "", "Remark": "" }
  ]
};


export const useReportHouseKeeping = (
  filter?: string,
  sorting?: string,
) => {
  // return useQuery({
  //   queryKey: [QueryNames.GetRoomStatus, pageIndex, pageSize, filter, sorting],
  //   queryFn: async () => {
  //     let skip = 0;
  //     if (pageIndex && pageSize) {
  //       skip = pageIndex * pageSize;
  //     }
  //     const { data } = await getApiAppRoomStatus({
  //       query: {
  //         MaxResultCount: pageSize,
  //         SkipCount: skip,
  //         Sorting: sorting,
  //       },
  //     });

  //     return data;
  //   },
  // });

  return [
    { "RoomNumber": "8201", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "方毅", "NameofDocument": "FANG YI", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8202", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "11,200,000" },
    { "RoomNumber": "8203", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "11,040,000" },
    { "RoomNumber": "8205", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10,400,000" },
    { "RoomNumber": "8206", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "王鑫", "NameofDocument": "WANG XIN", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8207", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "秦华祥", "NameofDocument": "QIN HUAXIANG", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8208", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "谭本江", "NameofDocument": "TAN BENJIANG", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8209", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "林玉才", "NameofDocument": "LIN YUCAI", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8210", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "董金军", "NameofDocument": "DONG JINJUN", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8211", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "16,800,000" },
    { "RoomNumber": "8212", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "刘浩", "NameofDocument": "LIU HAO", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8215", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "16,000,000" },
    { "RoomNumber": "8216", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "梅雄峰", "NameofDocument": "MEI XIONGFENG", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8217", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "16,800,000" },
    { "RoomNumber": "8218", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "20,700,000" },
    { "RoomNumber": "8219", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "0" },
    { "RoomNumber": "8201", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "方毅", "NameofDocument": "FANG YI", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8202", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "11,200,000" },
    { "RoomNumber": "8203", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "11,040,000" },
    { "RoomNumber": "8205", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10,400,000" },
    { "RoomNumber": "8206", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "王鑫", "NameofDocument": "WANG XIN", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8207", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "秦华祥", "NameofDocument": "QIN HUAXIANG", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8208", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "谭本江", "NameofDocument": "TAN BENJIANG", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8209", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "林玉才", "NameofDocument": "LIN YUCAI", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8210", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "董金军", "NameofDocument": "DONG JINJUN", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8211", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "16,800,000" },
    { "RoomNumber": "8212", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "刘浩", "NameofDocument": "LIU HAO", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8215", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "16,000,000" },
    { "RoomNumber": "8216", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "梅雄峰", "NameofDocument": "MEI XIONGFENG", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "10000" },
    { "RoomNumber": "8217", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "16,800,000" },
    { "RoomNumber": "8218", "RoomType": "OVK", "StandardAmount": "1,380,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "20,700,000" },
    { "RoomNumber": "8219", "RoomType": "BK", "StandardAmount": "800,000", "EmployeesRooms": "", "NameofDocument": "", "01.01": 325, "01.02": 322.5, "01.03": 322.5, "01.04": 322.5, "01.05": 322.5, "01.06": 322.5, "01.07": 322.5, "01.08": 322.5, "01.09": 322.5, "01.10": 322.5, "01.11": 322.5, "01.12": 322.5, "01.13": 322.5, "01.14": 322.5, "01.15": 322.5, "01.16": 322.5, "01.17": 322.5, "01.18": 322.5, "01.19": 322.5, "01.20": 322.5, "01.21": 322.5, "01.22": 322.5, "01.23": 322.5, "01.24": 322.5, "01.25": 322.5, "01.26": 322.5, "01.27": 322.5, "01.28": 322.5, "01.29": 322.5, "01.30": 322.5, "01.31": 322.5, "Subtotal": "0" },
  ]
};


export const useReportFnb = (
  filter?: string,
  sorting?: string,
) => {
  // return useQuery({
  //   queryKey: [QueryNames.GetRoomStatus, pageIndex, pageSize, filter, sorting],
  //   queryFn: async () => {
  //     let skip = 0;
  //     if (pageIndex && pageSize) {
  //       skip = pageIndex * pageSize;
  //     }
  //     const { data } = await getApiAppRoomStatus({
  //       query: {
  //         MaxResultCount: pageSize,
  //         SkipCount: skip,
  //         Sorting: sorting,
  //       },
  //     });

  //     return data;
  //   },
  // });

  return [
    { "signature": "YQD202411-011", "05.01": "35,700,000.00"    , "05.02": "", "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "IMIP202412002", "05.01": "6,000,000.00"     , "05.02": "", "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "IMIP202412003", "05.01": "1,540,000.00"     , "05.02": "", "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "WBJD202411-072", "05.01": "", "05.02": ""   , "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "WBJD202411-066", "05.01": "13,284,000.00"   , "05.02": "", "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "XZJD202410-079-1", "05.01": "77,418,000.00" , "05.02": "", "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "WBJD202411-067", "05.01": "3,982,000.00"    , "05.02": "", "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "WBJD202411-074-1", "05.01": "660,000.00"    , "05.02": "", "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "WBJD202411-068", "05.01": "11,986,000.00"   , "05.02": "", "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
    { "signature": "YQD202412-001", "05.01": "", "05.02": ""    , "05.03": "", "05.04": "", "05.05": "", "05.06": "", "05.07": "", "05.08": "", "05.09": "", "05.10": "", "05.11": "", "05.12": "", "05.13": "", "05.14": "", "05.15": "", "05.16": "", "05.17": "", "05.18": "", "05.19": "", "05.20": "", "05.21": "", "05.22": "", "05.23": "", "05.24": "", "05.25": "", "05.26": "", "05.27": "", "05.28": "", "05.29": "", "05.30": "", "05.31": "", },
  ]
};