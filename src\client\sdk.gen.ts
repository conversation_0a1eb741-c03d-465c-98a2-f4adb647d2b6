// This file is auto-generated by @hey-api/openapi-ts

import {
  type Options as ClientOptions,
  type TDataShape,
  type Client,
  formDataBodySerializer,
} from "@hey-api/client-fetch";
import type {
  GetApiAbpApplicationConfigurationData,
  GetApiAbpApplicationConfigurationResponse,
  GetApiAbpApplicationConfigurationError,
  GetApiAbpMultiTenancyTenantsByNameByNameData,
  GetApiAbpMultiTenancyTenantsByNameByNameResponse,
  GetApiAbpMultiTenancyTenantsByNameByNameError,
  GetApiAbpMultiTenancyTenantsByIdByIdData,
  GetApiAbpMultiTenancyTenantsByIdByIdResponse,
  GetApiAbpMultiTenancyTenantsByIdByIdError,
  PostApiAccountRegisterData,
  PostApiAccountRegisterResponse,
  PostApiAccountRegisterError,
  PostApiAccountSendPasswordResetCodeData,
  PostApiAccountSendPasswordResetCodeError,
  PostApiAccountVerifyPasswordResetTokenData,
  PostApiAccountVerifyPasswordResetTokenResponse,
  PostApiAccountVerifyPasswordResetTokenError,
  PostApiAccountResetPasswordData,
  PostApiAccountResetPasswordError,
  GetApiAttachmentData,
  GetApiAttachmentResponse,
  GetApiAttachmentError,
  GetApiAttachmentDownloadByIdData,
  GetApiAttachmentDownloadByIdError,
  GetApiAttachmentStreamByIdData,
  GetApiAttachmentStreamByIdError,
  PostApiAttachmentUploadData,
  PostApiAttachmentUploadResponse,
  PostApiAttachmentUploadError,
  GetApiAttachmentByReferenceData,
  GetApiAttachmentByReferenceResponse,
  GetApiAttachmentByReferenceError,
  DeleteApiAttachmentByIdData,
  DeleteApiAttachmentByIdResponse,
  DeleteApiAttachmentByIdError,
  PostApiAttachmentBulkDownloadData,
  PostApiAttachmentBulkDownloadError,
  PostApiAttachmentUploadFormData,
  PostApiAttachmentUploadFormResponse,
  PostApiAttachmentUploadFormError,
  GetApiExchangeRatesData,
  GetApiMasterCompanyData,
  GetApiMasterCompanyResponse,
  GetApiMasterCompanyError,
  PostApiMasterCompanyData,
  PostApiMasterCompanyResponse,
  PostApiMasterCompanyError,
  DeleteApiMasterCompanyByIdData,
  DeleteApiMasterCompanyByIdError,
  GetApiMasterCompanyByIdData,
  GetApiMasterCompanyByIdResponse,
  GetApiMasterCompanyByIdError,
  PutApiMasterCompanyByIdData,
  PutApiMasterCompanyByIdResponse,
  PutApiMasterCompanyByIdError,
  GetApiMasterDiningOptionsData,
  GetApiMasterDiningOptionsResponse,
  GetApiMasterDiningOptionsError,
  PostApiMasterDiningOptionsData,
  PostApiMasterDiningOptionsResponse,
  PostApiMasterDiningOptionsError,
  DeleteApiMasterDiningOptionsByIdData,
  DeleteApiMasterDiningOptionsByIdError,
  GetApiMasterDiningOptionsByIdData,
  GetApiMasterDiningOptionsByIdResponse,
  GetApiMasterDiningOptionsByIdError,
  PutApiMasterDiningOptionsByIdData,
  PutApiMasterDiningOptionsByIdResponse,
  PutApiMasterDiningOptionsByIdError,
  PostApiDocumentGenerateRcData,
  PostApiDocumentGenerateRcError,
  GetApiDocumentTemplatesAllData,
  GetApiDocumentTemplatesAllResponse,
  GetApiDocumentTemplatesAllError,
  GetApiDocumentTemplatesByTypeByDocumentTypeData,
  GetApiDocumentTemplatesByTypeByDocumentTypeResponse,
  GetApiDocumentTemplatesByTypeByDocumentTypeError,
  DeleteApiDocumentTemplatesByIdData,
  DeleteApiDocumentTemplatesByIdError,
  GetApiDocumentTemplatesByIdData,
  GetApiDocumentTemplatesByIdResponse,
  GetApiDocumentTemplatesByIdError,
  PutApiDocumentTemplatesByIdData,
  PutApiDocumentTemplatesByIdResponse,
  PutApiDocumentTemplatesByIdError,
  PostApiDocumentTemplatesData,
  PostApiDocumentTemplatesResponse,
  PostApiDocumentTemplatesError,
  PostApiDocumentTemplatesUploadData,
  PostApiDocumentTemplatesUploadResponse,
  PostApiDocumentTemplatesUploadError,
  PostApiDocumentConvertDocxToPdfData,
  PostApiDocumentConvertDocxToPdfError,
  GetApiAppDocumentTemplatesData,
  GetApiAppDocumentTemplatesResponse,
  GetApiAppDocumentTemplatesError,
  GetApiAppDocumentTemplatesByTypeData,
  GetApiAppDocumentTemplatesByTypeResponse,
  GetApiAppDocumentTemplatesByTypeError,
  GetApiAppDocumentByIdTemplateByIdData,
  GetApiAppDocumentByIdTemplateByIdResponse,
  GetApiAppDocumentByIdTemplateByIdError,
  PostApiAppDocumentTemplateData,
  PostApiAppDocumentTemplateResponse,
  PostApiAppDocumentTemplateError,
  PostApiAppDocumentUploadTemplateData,
  PostApiAppDocumentUploadTemplateResponse,
  PostApiAppDocumentUploadTemplateError,
  DeleteApiAppDocumentByIdTemplateData,
  DeleteApiAppDocumentByIdTemplateError,
  PutApiAppDocumentByIdTemplateData,
  PutApiAppDocumentByIdTemplateResponse,
  PutApiAppDocumentByIdTemplateError,
  PostApiAppDocumentConvertDocxToPdfData,
  PostApiAppDocumentConvertDocxToPdfResponse,
  PostApiAppDocumentConvertDocxToPdfError,
  PostApiDocxToPdfConvertData,
  PostApiDocxToPdfConvertBase64Data,
  PostApiAccountDynamicClaimsRefreshData,
  PostApiAccountDynamicClaimsRefreshError,
  GetApiChangeLogData,
  PostApiChangeLogEntityData,
  GetApiChangeLogEntityLatestData,
  GetApiChangeLogEntityLatestResponse,
  GetApiChangeLogEntityTypeData,
  GetApiChangeLogTimeRangeData,
  GetApiChangeLogPropertyHistoryData,
  GetApiChangeLogEntityChangesData,
  PostApiAppFoodAndBeverageListData,
  PostApiAppFoodAndBeverageListResponse,
  PostApiAppFoodAndBeverageListError,
  GetApiAppFoodAndBeverageData,
  GetApiAppFoodAndBeverageResponse,
  GetApiAppFoodAndBeverageError,
  PostApiAppFoodAndBeverageData,
  PostApiAppFoodAndBeverageResponse,
  PostApiAppFoodAndBeverageError,
  DeleteApiAppFoodAndBeverageByIdData,
  DeleteApiAppFoodAndBeverageByIdError,
  GetApiAppFoodAndBeverageByIdData,
  GetApiAppFoodAndBeverageByIdResponse,
  GetApiAppFoodAndBeverageByIdError,
  PutApiAppFoodAndBeverageByIdData,
  PutApiAppFoodAndBeverageByIdResponse,
  PutApiAppFoodAndBeverageByIdError,
  PostApiAppGuestListData,
  PostApiAppGuestListResponse,
  PostApiAppGuestListError,
  GetApiAppGuestData,
  GetApiAppGuestResponse,
  GetApiAppGuestError,
  PostApiAppGuestData,
  PostApiAppGuestResponse,
  PostApiAppGuestError,
  DeleteApiAppGuestByIdData,
  DeleteApiAppGuestByIdError,
  GetApiAppGuestByIdData,
  GetApiAppGuestByIdResponse,
  GetApiAppGuestByIdError,
  PutApiAppGuestByIdData,
  PutApiAppGuestByIdResponse,
  PutApiAppGuestByIdError,
  PostApiAppGuestUploadAttachmentsByGuestIdData,
  PostApiAppGuestUploadAttachmentsByGuestIdError,
  GetApiHealthKubernetesData,
  PostApiInvoiceGenerateData,
  PostApiInvoiceGenerateError,
  PostApiInvoiceGenerateWismaData,
  PostApiInvoiceGenerateWismaError,
  PostApiInvoiceGenerateWismaDownloadData,
  PostApiInvoiceGenerateWismaDownloadError,
  GetApiInvoiceTemplateDataByPaymentIdData,
  GetApiInvoiceTemplateDataByPaymentIdResponse,
  GetApiInvoiceTemplateDataByPaymentIdError,
  PostApiAppInvoiceDocumentGenerateInvoiceData,
  PostApiAppInvoiceDocumentGenerateInvoiceResponse,
  PostApiAppInvoiceDocumentGenerateInvoiceError,
  GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdData,
  GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdResponse,
  GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdError,
  PostApiAppInvoiceDocumentGenerateWismaInvoiceData,
  PostApiAppInvoiceDocumentGenerateWismaInvoiceResponse,
  PostApiAppInvoiceDocumentGenerateWismaInvoiceError,
  PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentData,
  PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentResponse,
  PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentError,
  PostApiAccountLoginData,
  PostApiAccountLoginResponse,
  PostApiAccountLoginError,
  GetApiAccountLogoutData,
  GetApiAccountLogoutError,
  PostApiAccountCheckPasswordData,
  PostApiAccountCheckPasswordResponse,
  PostApiAccountCheckPasswordError,
  PostApiAppMasterStatusListData,
  PostApiAppMasterStatusListResponse,
  PostApiAppMasterStatusListError,
  GetApiMasterStatusData,
  GetApiMasterStatusResponse,
  GetApiMasterStatusError,
  PostApiMasterStatusData,
  PostApiMasterStatusResponse,
  PostApiMasterStatusError,
  DeleteApiMasterStatusByIdData,
  DeleteApiMasterStatusByIdError,
  GetApiMasterStatusByIdData,
  GetApiMasterStatusByIdResponse,
  GetApiMasterStatusByIdError,
  PutApiMasterStatusByIdData,
  PutApiMasterStatusByIdResponse,
  PutApiMasterStatusByIdError,
  GetApiMasterStatusServiceByDocTypeByDocTypeData,
  GetApiMasterStatusServiceByDocTypeByDocTypeResponse,
  GetApiMasterStatusServiceByDocTypeByDocTypeError,
  GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeData,
  GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeResponse,
  GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeError,
  GetApiMasterStatusServicePagedByDocTypeByDocTypeData,
  GetApiMasterStatusServicePagedByDocTypeByDocTypeResponse,
  GetApiMasterStatusServicePagedByDocTypeByDocTypeError,
  PostApiAppPaymentDetailsListData,
  PostApiAppPaymentDetailsListResponse,
  PostApiAppPaymentDetailsListError,
  GetApiAppPaymentDetailsData,
  GetApiAppPaymentDetailsResponse,
  GetApiAppPaymentDetailsError,
  PostApiAppPaymentDetailsData,
  PostApiAppPaymentDetailsResponse,
  PostApiAppPaymentDetailsError,
  DeleteApiAppPaymentDetailsByIdData,
  DeleteApiAppPaymentDetailsByIdError,
  GetApiAppPaymentDetailsByIdData,
  GetApiAppPaymentDetailsByIdResponse,
  GetApiAppPaymentDetailsByIdError,
  PutApiAppPaymentDetailsByIdData,
  PutApiAppPaymentDetailsByIdResponse,
  PutApiAppPaymentDetailsByIdError,
  GetApiAppPaymentGuestsData,
  GetApiAppPaymentGuestsResponse,
  GetApiAppPaymentGuestsError,
  PostApiAppPaymentGuestsData,
  PostApiAppPaymentGuestsResponse,
  PostApiAppPaymentGuestsError,
  DeleteApiAppPaymentGuestsByIdData,
  DeleteApiAppPaymentGuestsByIdError,
  GetApiAppPaymentGuestsByIdData,
  GetApiAppPaymentGuestsByIdResponse,
  GetApiAppPaymentGuestsByIdError,
  PutApiAppPaymentGuestsByIdData,
  PutApiAppPaymentGuestsByIdResponse,
  PutApiAppPaymentGuestsByIdError,
  GetApiMasterPaymentMethodData,
  GetApiMasterPaymentMethodResponse,
  GetApiMasterPaymentMethodError,
  PostApiMasterPaymentMethodData,
  PostApiMasterPaymentMethodResponse,
  PostApiMasterPaymentMethodError,
  DeleteApiMasterPaymentMethodByIdData,
  DeleteApiMasterPaymentMethodByIdError,
  GetApiMasterPaymentMethodByIdData,
  GetApiMasterPaymentMethodByIdResponse,
  GetApiMasterPaymentMethodByIdError,
  PutApiMasterPaymentMethodByIdData,
  PutApiMasterPaymentMethodByIdResponse,
  PutApiMasterPaymentMethodByIdError,
  PostApiAppPaymentsListData,
  PostApiAppPaymentsListResponse,
  PostApiAppPaymentsListError,
  GetApiAppPaymentsData,
  GetApiAppPaymentsResponse,
  GetApiAppPaymentsError,
  PostApiAppPaymentsData,
  PostApiAppPaymentsResponse,
  PostApiAppPaymentsError,
  DeleteApiAppPaymentsByIdData,
  DeleteApiAppPaymentsByIdError,
  GetApiAppPaymentsByIdData,
  GetApiAppPaymentsByIdResponse,
  GetApiAppPaymentsByIdError,
  PutApiAppPaymentsByIdData,
  PutApiAppPaymentsByIdResponse,
  PutApiAppPaymentsByIdError,
  PostApiAppPaymentsByPaymentIdCleanupAttachmentsData,
  PostApiAppPaymentsByPaymentIdCleanupAttachmentsResponse,
  PostApiAppPaymentsByPaymentIdCleanupAttachmentsError,
  GetApiAccountMyProfileData,
  GetApiAccountMyProfileResponse,
  GetApiAccountMyProfileError,
  PutApiAccountMyProfileData,
  PutApiAccountMyProfileResponse,
  PutApiAccountMyProfileError,
  PostApiAccountMyProfileChangePasswordData,
  PostApiAccountMyProfileChangePasswordError,
  PostApiAppRegistrationCardGenerateDocumentAsAttachmentData,
  PostApiAppRegistrationCardGenerateDocumentAsAttachmentResponse,
  PostApiAppRegistrationCardGenerateDocumentAsAttachmentError,
  GetApiReportData,
  GetApiReportResponse,
  GetApiReportError,
  PostApiReportData,
  PostApiReportResponse,
  PostApiReportError,
  GetApiReportActiveData,
  GetApiReportActiveResponse,
  GetApiReportActiveError,
  DeleteApiReportByIdData,
  DeleteApiReportByIdError,
  GetApiReportByIdData,
  GetApiReportByIdResponse,
  GetApiReportByIdError,
  PutApiReportByIdData,
  PutApiReportByIdResponse,
  PutApiReportByIdError,
  PostApiReportPreviewData,
  PostApiReportPreviewResponse,
  PostApiReportPreviewError,
  PostApiReportExportCsvData,
  PostApiReportExportCsvError,
  PostApiReportExportExcelData,
  PostApiReportExportExcelError,
  PostApiReportExportExcelCustomData,
  PostApiReportExportExcelCustomError,
  GetApiReportByReportIdParametersData,
  GetApiReportByReportIdParametersResponse,
  GetApiReportByReportIdParametersError,
  GetApiReportByReportIdExcelHeaderData,
  GetApiReportByReportIdExcelHeaderResponse,
  GetApiReportByReportIdExcelHeaderError,
  PutApiReportByReportIdExcelHeaderData,
  PutApiReportByReportIdExcelHeaderError,
  GetApiReportByReportIdDebugHeaderData,
  GetApiReportByReportIdDebugHeaderError,
  PostApiReportByReportIdTestHeaderData,
  PostApiReportByReportIdTestHeaderError,
  PostApiReportExportExcelConditionalFormattingTestData,
  PostApiReportExportExcelConditionalFormattingTestError,
  PostApiReportListData,
  PostApiReportListResponse,
  PostApiReportListError,
  PostApiReportByReportIdTestPivotData,
  PostApiReportByReportIdTestPivotError,
  GetApiAppReportroomsData,
  GetApiAppReportroomsResponse,
  GetApiAppReportroomsError,
  PostApiAppReportroomsReportByDateData,
  PostApiAppReportroomsReportByDateResponse,
  PostApiAppReportroomsReportByDateError,
  GetApiAppReportroomsRoomTypesData,
  GetApiAppReportroomsRoomTypesResponse,
  GetApiAppReportroomsRoomTypesError,
  GetApiAppReportRoomsReportRoomsData,
  GetApiAppReportRoomsReportRoomsResponse,
  GetApiAppReportRoomsReportRoomsError,
  GetApiAppReportRoomsRoomTypesReportByDateData,
  GetApiAppReportRoomsRoomTypesReportByDateResponse,
  GetApiAppReportRoomsRoomTypesReportByDateError,
  PostApiAppReservationDetailsListData,
  PostApiAppReservationDetailsListResponse,
  PostApiAppReservationDetailsListError,
  DeleteApiAppReservationDetailsByIdData,
  DeleteApiAppReservationDetailsByIdError,
  GetApiAppReservationDetailsByIdData,
  GetApiAppReservationDetailsByIdResponse,
  GetApiAppReservationDetailsByIdError,
  PutApiAppReservationDetailsByIdData,
  PutApiAppReservationDetailsByIdResponse,
  PutApiAppReservationDetailsByIdError,
  GetApiAppReservationDetailsData,
  GetApiAppReservationDetailsResponse,
  GetApiAppReservationDetailsError,
  PostApiAppReservationDetailsData,
  PostApiAppReservationDetailsResponse,
  PostApiAppReservationDetailsError,
  PostApiAppReservationFoodAndBeveragesListData,
  PostApiAppReservationFoodAndBeveragesListResponse,
  PostApiAppReservationFoodAndBeveragesListError,
  GetApiAppReservationFoodAndBeveragesData,
  GetApiAppReservationFoodAndBeveragesResponse,
  GetApiAppReservationFoodAndBeveragesError,
  PostApiAppReservationFoodAndBeveragesData,
  PostApiAppReservationFoodAndBeveragesResponse,
  PostApiAppReservationFoodAndBeveragesError,
  DeleteApiAppReservationFoodAndBeveragesByIdData,
  DeleteApiAppReservationFoodAndBeveragesByIdError,
  GetApiAppReservationFoodAndBeveragesByIdData,
  GetApiAppReservationFoodAndBeveragesByIdResponse,
  GetApiAppReservationFoodAndBeveragesByIdError,
  PutApiAppReservationFoodAndBeveragesByIdData,
  PutApiAppReservationFoodAndBeveragesByIdResponse,
  PutApiAppReservationFoodAndBeveragesByIdError,
  PostApiAppReservationFoodAndBeveragesCreateManyData,
  PostApiAppReservationFoodAndBeveragesCreateManyResponse,
  PostApiAppReservationFoodAndBeveragesCreateManyError,
  PutApiAppReservationFoodAndBeveragesUpdateManyData,
  PutApiAppReservationFoodAndBeveragesUpdateManyResponse,
  PutApiAppReservationFoodAndBeveragesUpdateManyError,
  PostApiAppReservationRoomsListData,
  PostApiAppReservationRoomsListResponse,
  PostApiAppReservationRoomsListError,
  DeleteApiAppReservationRoomsByIdData,
  DeleteApiAppReservationRoomsByIdError,
  GetApiAppReservationRoomsByIdData,
  GetApiAppReservationRoomsByIdResponse,
  GetApiAppReservationRoomsByIdError,
  PutApiAppReservationRoomsByIdData,
  PutApiAppReservationRoomsByIdResponse,
  PutApiAppReservationRoomsByIdError,
  GetApiAppReservationRoomsData,
  GetApiAppReservationRoomsResponse,
  GetApiAppReservationRoomsError,
  PostApiAppReservationRoomsData,
  PostApiAppReservationRoomsResponse,
  PostApiAppReservationRoomsError,
  PostApiAppReservationRoomsCreateManyData,
  PostApiAppReservationRoomsCreateManyResponse,
  PostApiAppReservationRoomsCreateManyError,
  PostApiAppReservationsListData,
  PostApiAppReservationsListResponse,
  PostApiAppReservationsListError,
  GetApiAppReservationsData,
  GetApiAppReservationsResponse,
  GetApiAppReservationsError,
  PostApiAppReservationsData,
  PostApiAppReservationsResponse,
  PostApiAppReservationsError,
  PostApiAppReservationsCreateWithDetailsData,
  PostApiAppReservationsCreateWithDetailsError,
  PutApiAppReservationsByIdUpdateWithDetailsData,
  PutApiAppReservationsByIdUpdateWithDetailsResponse,
  PutApiAppReservationsByIdUpdateWithDetailsError,
  DeleteApiAppReservationsByIdData,
  DeleteApiAppReservationsByIdError,
  GetApiAppReservationsByIdData,
  GetApiAppReservationsByIdResponse,
  GetApiAppReservationsByIdError,
  PutApiAppReservationsByIdData,
  PutApiAppReservationsByIdResponse,
  PutApiAppReservationsByIdError,
  GetApiAppReservationsByIdWithDetailsData,
  GetApiAppReservationsByIdWithDetailsResponse,
  GetApiAppReservationsByIdWithDetailsError,
  GetApiAppReservationTypesData,
  GetApiAppReservationTypesResponse,
  GetApiAppReservationTypesError,
  PostApiAppReservationTypesData,
  PostApiAppReservationTypesResponse,
  PostApiAppReservationTypesError,
  DeleteApiAppReservationTypesByIdData,
  DeleteApiAppReservationTypesByIdError,
  GetApiAppReservationTypesByIdData,
  GetApiAppReservationTypesByIdResponse,
  GetApiAppReservationTypesByIdError,
  PutApiAppReservationTypesByIdData,
  PutApiAppReservationTypesByIdResponse,
  PutApiAppReservationTypesByIdError,
  GetApiAppRoomData,
  GetApiAppRoomResponse,
  GetApiAppRoomError,
  PostApiAppRoomData,
  PostApiAppRoomResponse,
  PostApiAppRoomError,
  DeleteApiAppRoomByIdData,
  DeleteApiAppRoomByIdError,
  GetApiAppRoomByIdData,
  GetApiAppRoomByIdResponse,
  GetApiAppRoomByIdError,
  PutApiAppRoomByIdData,
  PutApiAppRoomByIdResponse,
  PutApiAppRoomByIdError,
  PostApiAppRoomsListData,
  PostApiAppRoomsListResponse,
  PostApiAppRoomsListError,
  GetApiAppRoomStatusData,
  GetApiAppRoomStatusResponse,
  GetApiAppRoomStatusError,
  PostApiAppRoomStatusData,
  PostApiAppRoomStatusResponse,
  PostApiAppRoomStatusError,
  DeleteApiAppRoomStatusByIdData,
  DeleteApiAppRoomStatusByIdError,
  GetApiAppRoomStatusByIdData,
  GetApiAppRoomStatusByIdResponse,
  GetApiAppRoomStatusByIdError,
  PutApiAppRoomStatusByIdData,
  PutApiAppRoomStatusByIdResponse,
  PutApiAppRoomStatusByIdError,
  GetApiAppRoomStatusLogsData,
  GetApiAppRoomStatusLogsResponse,
  GetApiAppRoomStatusLogsError,
  PostApiAppRoomStatusLogsData,
  PostApiAppRoomStatusLogsResponse,
  PostApiAppRoomStatusLogsError,
  DeleteApiAppRoomStatusLogsByIdData,
  DeleteApiAppRoomStatusLogsByIdError,
  GetApiAppRoomStatusLogsByIdData,
  GetApiAppRoomStatusLogsByIdResponse,
  GetApiAppRoomStatusLogsByIdError,
  PutApiAppRoomStatusLogsByIdData,
  PutApiAppRoomStatusLogsByIdResponse,
  PutApiAppRoomStatusLogsByIdError,
  GetApiAppRoomTypeData,
  GetApiAppRoomTypeResponse,
  GetApiAppRoomTypeError,
  PostApiAppRoomTypeData,
  PostApiAppRoomTypeResponse,
  PostApiAppRoomTypeError,
  DeleteApiAppRoomTypeByIdData,
  DeleteApiAppRoomTypeByIdError,
  GetApiAppRoomTypeByIdData,
  GetApiAppRoomTypeByIdResponse,
  GetApiAppRoomTypeByIdError,
  PutApiAppRoomTypeByIdData,
  PutApiAppRoomTypeByIdResponse,
  PutApiAppRoomTypeByIdError,
  PostApiAppServicesListData,
  PostApiAppServicesListResponse,
  PostApiAppServicesListError,
  GetApiAppServicesData,
  GetApiAppServicesResponse,
  GetApiAppServicesError,
  PostApiAppServicesData,
  PostApiAppServicesResponse,
  PostApiAppServicesError,
  DeleteApiAppServicesByIdData,
  DeleteApiAppServicesByIdError,
  GetApiAppServicesByIdData,
  GetApiAppServicesByIdResponse,
  GetApiAppServicesByIdError,
  PutApiAppServicesByIdData,
  PutApiAppServicesByIdResponse,
  PutApiAppServicesByIdError,
  GetApiAppServiceTypesData,
  GetApiAppServiceTypesResponse,
  GetApiAppServiceTypesError,
  PostApiAppServiceTypesData,
  PostApiAppServiceTypesResponse,
  PostApiAppServiceTypesError,
  DeleteApiAppServiceTypesByIdData,
  DeleteApiAppServiceTypesByIdError,
  GetApiAppServiceTypesByIdData,
  GetApiAppServiceTypesByIdResponse,
  GetApiAppServiceTypesByIdError,
  PutApiAppServiceTypesByIdData,
  PutApiAppServiceTypesByIdResponse,
  PutApiAppServiceTypesByIdError,
  GetApiStatusSettingsData,
  GetApiStatusSettingsResponse,
  GetApiStatusSettingsError,
  PatchApiStatusSettingsData,
  PatchApiStatusSettingsResponse,
  PatchApiStatusSettingsError,
  PutApiStatusSettingsData,
  PutApiStatusSettingsResponse,
  PutApiStatusSettingsError,
  GetApiAppStatusSettingsData,
  GetApiAppStatusSettingsResponse,
  GetApiAppStatusSettingsError,
  PutApiAppStatusSettingsData,
  PutApiAppStatusSettingsResponse,
  PutApiAppStatusSettingsError,
  PostApiAppTaxListData,
  PostApiAppTaxListResponse,
  PostApiAppTaxListError,
  GetApiAppTaxData,
  GetApiAppTaxResponse,
  GetApiAppTaxError,
  PostApiAppTaxData,
  PostApiAppTaxResponse,
  PostApiAppTaxError,
  DeleteApiAppTaxByIdData,
  DeleteApiAppTaxByIdError,
  GetApiAppTaxByIdData,
  GetApiAppTaxByIdResponse,
  GetApiAppTaxByIdError,
  PutApiAppTaxByIdData,
  PutApiAppTaxByIdResponse,
  PutApiAppTaxByIdError,
  DeleteApiMultiTenancyTenantsByIdData,
  DeleteApiMultiTenancyTenantsByIdError,
  GetApiMultiTenancyTenantsByIdData,
  GetApiMultiTenancyTenantsByIdResponse,
  GetApiMultiTenancyTenantsByIdError,
  PutApiMultiTenancyTenantsByIdData,
  PutApiMultiTenancyTenantsByIdResponse,
  PutApiMultiTenancyTenantsByIdError,
  GetApiMultiTenancyTenantsData,
  GetApiMultiTenancyTenantsResponse,
  GetApiMultiTenancyTenantsError,
  PostApiMultiTenancyTenantsData,
  PostApiMultiTenancyTenantsResponse,
  PostApiMultiTenancyTenantsError,
  DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData,
  DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringError,
  GetApiMultiTenancyTenantsByIdDefaultConnectionStringData,
  GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponse,
  GetApiMultiTenancyTenantsByIdDefaultConnectionStringError,
  PutApiMultiTenancyTenantsByIdDefaultConnectionStringData,
  PutApiMultiTenancyTenantsByIdDefaultConnectionStringError,
  GetApiTestAuthorizationPublicData,
  GetApiTestAuthorizationAuthenticatedData,
  GetApiTestAuthorizationAdminOnlyData,
  GetApiTestAuthorizationWismaRoomData,
  GetApiOptionsData,
  GetApiCronTickersData,
  GetApiTimeTickersData,
  GetApiTimeTickersGraphDataRangeData,
  GetApiTimeTickersGraphDataData,
  GetApiCronTickersGraphDataRangeData,
  GetApiCronTickersGraphDataRangeIdData,
  GetApiCronTickersGraphDataData,
  GetApiCronTickerOccurrencesCronTickerIdData,
  GetApiCronTickerOccurrencesCronTickerIdGraphDataData,
  PostApiTickerCancelData,
  DeleteTimeTickerDeleteData,
  DeleteApiCronTickerDeleteData,
  DeleteApiCronTickerOccurrenceDeleteData,
  GetApiTickerRequestIdData,
  GetApiTickerFunctionsData,
  PutApiTimeTickerUpdateData,
  PostApiTimeTickerAddData,
  PostApiCronTickerAddData,
  PutCronTickerUpdateData,
  GetApiTickerHostNextTickerData,
  PostApiTickerHostStopData,
  PostApiTickerHostStartData,
  PostApiTickerHostRestartData,
  GetApiTickerHostStatusData,
  GetApiTickerStatusesGetLastWeekData,
  GetApiTickerStatusesGetData,
  GetApiTickerMachineJobsData,
  GetApiAppTypeFoodAndBeverageData,
  GetApiAppTypeFoodAndBeverageResponse,
  GetApiAppTypeFoodAndBeverageError,
  PostApiAppTypeFoodAndBeverageData,
  PostApiAppTypeFoodAndBeverageResponse,
  PostApiAppTypeFoodAndBeverageError,
  DeleteApiAppTypeFoodAndBeverageByIdData,
  DeleteApiAppTypeFoodAndBeverageByIdError,
  GetApiAppTypeFoodAndBeverageByIdData,
  GetApiAppTypeFoodAndBeverageByIdResponse,
  GetApiAppTypeFoodAndBeverageByIdError,
  PutApiAppTypeFoodAndBeverageByIdData,
  PutApiAppTypeFoodAndBeverageByIdResponse,
  PutApiAppTypeFoodAndBeverageByIdError,
  GetApiAppUserSynchronizationHealthData,
  GetApiAppUserSynchronizationHealthResponse,
  GetApiAppUserSynchronizationHealthDetailedData,
  GetApiAppUserSynchronizationHealthDetailedResponse,
} from "./types.gen";
import { client as _heyApiClient } from "./client.gen";

export type Options<
  TData extends TDataShape = TDataShape,
  ThrowOnError extends boolean = boolean,
> = ClientOptions<TData, ThrowOnError> & {
  /**
   * You can provide a client instance returned by `createClient()` instead of
   * individual options. This might be also useful if you want to implement a
   * custom client.
   */
  client?: Client;
  /**
   * You can pass arbitrary values through the `meta` object. This can be
   * used to access values that aren't defined as part of the SDK function.
   */
  meta?: Record<string, unknown>;
};

export const getApiAbpApplicationConfiguration = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAbpApplicationConfigurationData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAbpApplicationConfigurationResponse,
    GetApiAbpApplicationConfigurationError,
    ThrowOnError
  >({
    url: "/api/abp/application-configuration",
    ...options,
  });
};

export const getApiAbpMultiTenancyTenantsByNameByName = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAbpMultiTenancyTenantsByNameByNameData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAbpMultiTenancyTenantsByNameByNameResponse,
    GetApiAbpMultiTenancyTenantsByNameByNameError,
    ThrowOnError
  >({
    url: "/api/abp/multi-tenancy/tenants/by-name/{name}",
    ...options,
  });
};

export const getApiAbpMultiTenancyTenantsByIdById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAbpMultiTenancyTenantsByIdByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAbpMultiTenancyTenantsByIdByIdResponse,
    GetApiAbpMultiTenancyTenantsByIdByIdError,
    ThrowOnError
  >({
    url: "/api/abp/multi-tenancy/tenants/by-id/{id}",
    ...options,
  });
};

export const postApiAccountRegister = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAccountRegisterData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAccountRegisterResponse,
    PostApiAccountRegisterError,
    ThrowOnError
  >({
    url: "/api/account/register",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAccountSendPasswordResetCode = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAccountSendPasswordResetCodeData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiAccountSendPasswordResetCodeError,
    ThrowOnError
  >({
    url: "/api/account/send-password-reset-code",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAccountVerifyPasswordResetToken = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAccountVerifyPasswordResetTokenData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAccountVerifyPasswordResetTokenResponse,
    PostApiAccountVerifyPasswordResetTokenError,
    ThrowOnError
  >({
    url: "/api/account/verify-password-reset-token",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAccountResetPassword = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAccountResetPasswordData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiAccountResetPasswordError,
    ThrowOnError
  >({
    url: "/api/account/reset-password",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAttachment = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAttachmentData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAttachmentResponse,
    GetApiAttachmentError,
    ThrowOnError
  >({
    url: "/api/attachment",
    ...options,
  });
};

export const getApiAttachmentDownloadById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAttachmentDownloadByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    unknown,
    GetApiAttachmentDownloadByIdError,
    ThrowOnError
  >({
    url: "/api/attachment/download/{id}",
    ...options,
  });
};

export const getApiAttachmentStreamById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAttachmentStreamByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    unknown,
    GetApiAttachmentStreamByIdError,
    ThrowOnError
  >({
    url: "/api/attachment/stream/{id}",
    ...options,
  });
};

export const postApiAttachmentUpload = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAttachmentUploadData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAttachmentUploadResponse,
    PostApiAttachmentUploadError,
    ThrowOnError
  >({
    url: "/api/attachment/upload",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAttachmentByReference = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAttachmentByReferenceData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAttachmentByReferenceResponse,
    GetApiAttachmentByReferenceError,
    ThrowOnError
  >({
    url: "/api/attachment/by-reference",
    ...options,
  });
};

export const deleteApiAttachmentById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiAttachmentByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    DeleteApiAttachmentByIdResponse,
    DeleteApiAttachmentByIdError,
    ThrowOnError
  >({
    url: "/api/attachment/{id}",
    ...options,
  });
};

export const postApiAttachmentBulkDownload = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAttachmentBulkDownloadData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiAttachmentBulkDownloadError,
    ThrowOnError
  >({
    url: "/api/attachment/bulk-download",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAttachmentUploadForm = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAttachmentUploadFormData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAttachmentUploadFormResponse,
    PostApiAttachmentUploadFormError,
    ThrowOnError
  >({
    ...formDataBodySerializer,
    url: "/api/attachment/upload-form",
    ...options,
    headers: {
      "Content-Type": null,
      ...options?.headers,
    },
  });
};

export const getApiExchangeRates = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiExchangeRatesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/exchange-rates",
      ...options,
    },
  );
};

export const getApiMasterCompany = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiMasterCompanyData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiMasterCompanyResponse,
    GetApiMasterCompanyError,
    ThrowOnError
  >({
    url: "/api/master/company",
    ...options,
  });
};

export const postApiMasterCompany = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiMasterCompanyData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiMasterCompanyResponse,
    PostApiMasterCompanyError,
    ThrowOnError
  >({
    url: "/api/master/company",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiMasterCompanyById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiMasterCompanyByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiMasterCompanyByIdError,
    ThrowOnError
  >({
    url: "/api/master/company/{id}",
    ...options,
  });
};

export const getApiMasterCompanyById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiMasterCompanyByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMasterCompanyByIdResponse,
    GetApiMasterCompanyByIdError,
    ThrowOnError
  >({
    url: "/api/master/company/{id}",
    ...options,
  });
};

export const putApiMasterCompanyById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiMasterCompanyByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiMasterCompanyByIdResponse,
    PutApiMasterCompanyByIdError,
    ThrowOnError
  >({
    url: "/api/master/company/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiMasterDiningOptions = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiMasterDiningOptionsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiMasterDiningOptionsResponse,
    GetApiMasterDiningOptionsError,
    ThrowOnError
  >({
    url: "/api/master/dining-options",
    ...options,
  });
};

export const postApiMasterDiningOptions = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiMasterDiningOptionsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiMasterDiningOptionsResponse,
    PostApiMasterDiningOptionsError,
    ThrowOnError
  >({
    url: "/api/master/dining-options",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiMasterDiningOptionsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiMasterDiningOptionsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiMasterDiningOptionsByIdError,
    ThrowOnError
  >({
    url: "/api/master/dining-options/{id}",
    ...options,
  });
};

export const getApiMasterDiningOptionsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiMasterDiningOptionsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMasterDiningOptionsByIdResponse,
    GetApiMasterDiningOptionsByIdError,
    ThrowOnError
  >({
    url: "/api/master/dining-options/{id}",
    ...options,
  });
};

export const putApiMasterDiningOptionsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiMasterDiningOptionsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiMasterDiningOptionsByIdResponse,
    PutApiMasterDiningOptionsByIdError,
    ThrowOnError
  >({
    url: "/api/master/dining-options/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiDocumentGenerateRc = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiDocumentGenerateRcData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiDocumentGenerateRcError,
    ThrowOnError
  >({
    url: "/api/document/generate-rc",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiDocumentTemplatesAll = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiDocumentTemplatesAllData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiDocumentTemplatesAllResponse,
    GetApiDocumentTemplatesAllError,
    ThrowOnError
  >({
    url: "/api/document/templates/all",
    ...options,
  });
};

export const getApiDocumentTemplatesByTypeByDocumentType = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    GetApiDocumentTemplatesByTypeByDocumentTypeData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiDocumentTemplatesByTypeByDocumentTypeResponse,
    GetApiDocumentTemplatesByTypeByDocumentTypeError,
    ThrowOnError
  >({
    url: "/api/document/templates/by-type/{documentType}",
    ...options,
  });
};

export const deleteApiDocumentTemplatesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiDocumentTemplatesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiDocumentTemplatesByIdError,
    ThrowOnError
  >({
    url: "/api/document/templates/{id}",
    ...options,
  });
};

export const getApiDocumentTemplatesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiDocumentTemplatesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiDocumentTemplatesByIdResponse,
    GetApiDocumentTemplatesByIdError,
    ThrowOnError
  >({
    url: "/api/document/templates/{id}",
    ...options,
  });
};

export const putApiDocumentTemplatesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiDocumentTemplatesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiDocumentTemplatesByIdResponse,
    PutApiDocumentTemplatesByIdError,
    ThrowOnError
  >({
    url: "/api/document/templates/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiDocumentTemplates = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiDocumentTemplatesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiDocumentTemplatesResponse,
    PostApiDocumentTemplatesError,
    ThrowOnError
  >({
    url: "/api/document/templates",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiDocumentTemplatesUpload = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiDocumentTemplatesUploadData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiDocumentTemplatesUploadResponse,
    PostApiDocumentTemplatesUploadError,
    ThrowOnError
  >({
    ...formDataBodySerializer,
    url: "/api/document/templates/upload",
    ...options,
    headers: {
      "Content-Type": null,
      ...options?.headers,
    },
  });
};

export const postApiDocumentConvertDocxToPdf = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiDocumentConvertDocxToPdfData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiDocumentConvertDocxToPdfError,
    ThrowOnError
  >({
    url: "/api/document/convert-docx-to-pdf",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppDocumentTemplates = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAppDocumentTemplatesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppDocumentTemplatesResponse,
    GetApiAppDocumentTemplatesError,
    ThrowOnError
  >({
    url: "/api/app/document/templates",
    ...options,
  });
};

export const getApiAppDocumentTemplatesByType = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAppDocumentTemplatesByTypeData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppDocumentTemplatesByTypeResponse,
    GetApiAppDocumentTemplatesByTypeError,
    ThrowOnError
  >({
    url: "/api/app/document/templates-by-type",
    ...options,
  });
};

export const getApiAppDocumentByIdTemplateById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppDocumentByIdTemplateByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppDocumentByIdTemplateByIdResponse,
    GetApiAppDocumentByIdTemplateByIdError,
    ThrowOnError
  >({
    url: "/api/app/document/{id}/template-by-id",
    ...options,
  });
};

export const postApiAppDocumentTemplate = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppDocumentTemplateData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppDocumentTemplateResponse,
    PostApiAppDocumentTemplateError,
    ThrowOnError
  >({
    url: "/api/app/document/template",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppDocumentUploadTemplate = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppDocumentUploadTemplateData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppDocumentUploadTemplateResponse,
    PostApiAppDocumentUploadTemplateError,
    ThrowOnError
  >({
    url: "/api/app/document/upload-template",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppDocumentByIdTemplate = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppDocumentByIdTemplateData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppDocumentByIdTemplateError,
    ThrowOnError
  >({
    url: "/api/app/document/{id}/template",
    ...options,
  });
};

export const putApiAppDocumentByIdTemplate = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppDocumentByIdTemplateData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppDocumentByIdTemplateResponse,
    PutApiAppDocumentByIdTemplateError,
    ThrowOnError
  >({
    url: "/api/app/document/{id}/template",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppDocumentConvertDocxToPdf = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppDocumentConvertDocxToPdfData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppDocumentConvertDocxToPdfResponse,
    PostApiAppDocumentConvertDocxToPdfError,
    ThrowOnError
  >({
    url: "/api/app/document/convert-docx-to-pdf",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiDocxToPdfConvert = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiDocxToPdfConvertData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    ...formDataBodySerializer,
    url: "/api/docx-to-pdf/convert",
    ...options,
    headers: {
      "Content-Type": null,
      ...options?.headers,
    },
  });
};

export const postApiDocxToPdfConvertBase64 = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiDocxToPdfConvertBase64Data, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/docx-to-pdf/convert-base64",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAccountDynamicClaimsRefresh = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAccountDynamicClaimsRefreshData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiAccountDynamicClaimsRefreshError,
    ThrowOnError
  >({
    url: "/api/account/dynamic-claims/refresh",
    ...options,
  });
};

export const getApiChangeLog = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiChangeLogData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/change-log",
      ...options,
    },
  );
};

export const postApiChangeLogEntity = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiChangeLogEntityData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/change-log/entity",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiChangeLogEntityLatest = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiChangeLogEntityLatestData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiChangeLogEntityLatestResponse,
    unknown,
    ThrowOnError
  >({
    url: "/api/change-log/entity/latest",
    ...options,
  });
};

export const getApiChangeLogEntityType = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiChangeLogEntityTypeData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/change-log/entity-type",
      ...options,
    },
  );
};

export const getApiChangeLogTimeRange = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiChangeLogTimeRangeData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/change-log/time-range",
      ...options,
    },
  );
};

export const getApiChangeLogPropertyHistory = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiChangeLogPropertyHistoryData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/change-log/property-history",
      ...options,
    },
  );
};

export const getApiChangeLogEntityChanges = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiChangeLogEntityChangesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/change-log/entity-changes",
      ...options,
    },
  );
};

export const postApiAppFoodAndBeverageList = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppFoodAndBeverageListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppFoodAndBeverageListResponse,
    PostApiAppFoodAndBeverageListError,
    ThrowOnError
  >({
    url: "/api/app/food-and-beverage/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppFoodAndBeverage = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppFoodAndBeverageData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppFoodAndBeverageResponse,
    GetApiAppFoodAndBeverageError,
    ThrowOnError
  >({
    url: "/api/app/food-and-beverage",
    ...options,
  });
};

export const postApiAppFoodAndBeverage = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppFoodAndBeverageData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppFoodAndBeverageResponse,
    PostApiAppFoodAndBeverageError,
    ThrowOnError
  >({
    url: "/api/app/food-and-beverage",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppFoodAndBeverageById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppFoodAndBeverageByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppFoodAndBeverageByIdError,
    ThrowOnError
  >({
    url: "/api/app/food-and-beverage/{id}",
    ...options,
  });
};

export const getApiAppFoodAndBeverageById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppFoodAndBeverageByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppFoodAndBeverageByIdResponse,
    GetApiAppFoodAndBeverageByIdError,
    ThrowOnError
  >({
    url: "/api/app/food-and-beverage/{id}",
    ...options,
  });
};

export const putApiAppFoodAndBeverageById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppFoodAndBeverageByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppFoodAndBeverageByIdResponse,
    PutApiAppFoodAndBeverageByIdError,
    ThrowOnError
  >({
    url: "/api/app/food-and-beverage/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppGuestList = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppGuestListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppGuestListResponse,
    PostApiAppGuestListError,
    ThrowOnError
  >({
    url: "/api/app/guest/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppGuest = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppGuestData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppGuestResponse,
    GetApiAppGuestError,
    ThrowOnError
  >({
    url: "/api/app/guest",
    ...options,
  });
};

export const postApiAppGuest = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppGuestData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppGuestResponse,
    PostApiAppGuestError,
    ThrowOnError
  >({
    url: "/api/app/guest",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppGuestById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiAppGuestByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppGuestByIdError,
    ThrowOnError
  >({
    url: "/api/app/guest/{id}",
    ...options,
  });
};

export const getApiAppGuestById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppGuestByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppGuestByIdResponse,
    GetApiAppGuestByIdError,
    ThrowOnError
  >({
    url: "/api/app/guest/{id}",
    ...options,
  });
};

export const putApiAppGuestById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppGuestByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppGuestByIdResponse,
    PutApiAppGuestByIdError,
    ThrowOnError
  >({
    url: "/api/app/guest/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppGuestUploadAttachmentsByGuestId = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PostApiAppGuestUploadAttachmentsByGuestIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).post<
    unknown,
    PostApiAppGuestUploadAttachmentsByGuestIdError,
    ThrowOnError
  >({
    url: "/api/app/guest/upload-attachments/{guestId}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiHealthKubernetes = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiHealthKubernetesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/health/kubernetes",
      ...options,
    },
  );
};

export const postApiInvoiceGenerate = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiInvoiceGenerateData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiInvoiceGenerateError,
    ThrowOnError
  >({
    url: "/api/invoice/generate",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiInvoiceGenerateWisma = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiInvoiceGenerateWismaData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiInvoiceGenerateWismaError,
    ThrowOnError
  >({
    url: "/api/invoice/generate-wisma",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiInvoiceGenerateWismaDownload = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiInvoiceGenerateWismaDownloadData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiInvoiceGenerateWismaDownloadError,
    ThrowOnError
  >({
    url: "/api/invoice/generate-wisma-download",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiInvoiceTemplateDataByPaymentId = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiInvoiceTemplateDataByPaymentIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiInvoiceTemplateDataByPaymentIdResponse,
    GetApiInvoiceTemplateDataByPaymentIdError,
    ThrowOnError
  >({
    url: "/api/invoice/template-data/{paymentId}",
    ...options,
  });
};

export const postApiAppInvoiceDocumentGenerateInvoice = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppInvoiceDocumentGenerateInvoiceData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppInvoiceDocumentGenerateInvoiceResponse,
    PostApiAppInvoiceDocumentGenerateInvoiceError,
    ThrowOnError
  >({
    url: "/api/app/invoice-document/generate-invoice",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppInvoiceDocumentInvoiceTemplateDataByPaymentId = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdResponse,
    GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdError,
    ThrowOnError
  >({
    url: "/api/app/invoice-document/invoice-template-data/{paymentId}",
    ...options,
  });
};

export const postApiAppInvoiceDocumentGenerateWismaInvoice = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    PostApiAppInvoiceDocumentGenerateWismaInvoiceData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppInvoiceDocumentGenerateWismaInvoiceResponse,
    PostApiAppInvoiceDocumentGenerateWismaInvoiceError,
    ThrowOnError
  >({
    url: "/api/app/invoice-document/generate-wisma-invoice",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachment = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentResponse,
    PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentError,
    ThrowOnError
  >({
    url: "/api/app/invoice-document/generate-wisma-invoice-as-attachment",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAccountLogin = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAccountLoginData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAccountLoginResponse,
    PostApiAccountLoginError,
    ThrowOnError
  >({
    url: "/api/account/login",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAccountLogout = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAccountLogoutData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    unknown,
    GetApiAccountLogoutError,
    ThrowOnError
  >({
    url: "/api/account/logout",
    ...options,
  });
};

export const postApiAccountCheckPassword = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAccountCheckPasswordData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAccountCheckPasswordResponse,
    PostApiAccountCheckPasswordError,
    ThrowOnError
  >({
    url: "/api/account/check-password",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppMasterStatusList = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppMasterStatusListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppMasterStatusListResponse,
    PostApiAppMasterStatusListError,
    ThrowOnError
  >({
    url: "/api/app/master-status/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiMasterStatus = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiMasterStatusData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiMasterStatusResponse,
    GetApiMasterStatusError,
    ThrowOnError
  >({
    url: "/api/master/status",
    ...options,
  });
};

export const postApiMasterStatus = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiMasterStatusData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiMasterStatusResponse,
    PostApiMasterStatusError,
    ThrowOnError
  >({
    url: "/api/master/status",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiMasterStatusById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiMasterStatusByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiMasterStatusByIdError,
    ThrowOnError
  >({
    url: "/api/master/status/{id}",
    ...options,
  });
};

export const getApiMasterStatusById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiMasterStatusByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMasterStatusByIdResponse,
    GetApiMasterStatusByIdError,
    ThrowOnError
  >({
    url: "/api/master/status/{id}",
    ...options,
  });
};

export const putApiMasterStatusById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiMasterStatusByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiMasterStatusByIdResponse,
    PutApiMasterStatusByIdError,
    ThrowOnError
  >({
    url: "/api/master/status/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiMasterStatusServiceByDocTypeByDocType = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    GetApiMasterStatusServiceByDocTypeByDocTypeData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMasterStatusServiceByDocTypeByDocTypeResponse,
    GetApiMasterStatusServiceByDocTypeByDocTypeError,
    ThrowOnError
  >({
    url: "/api/master/status-service/by-doc-type/{docType}",
    ...options,
  });
};

export const getApiMasterStatusServiceByIdAndDocTypeByIdByDocType = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeResponse,
    GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeError,
    ThrowOnError
  >({
    url: "/api/master/status-service/by-id-and-doc-type/{id}/{docType}",
    ...options,
  });
};

export const getApiMasterStatusServicePagedByDocTypeByDocType = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    GetApiMasterStatusServicePagedByDocTypeByDocTypeData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMasterStatusServicePagedByDocTypeByDocTypeResponse,
    GetApiMasterStatusServicePagedByDocTypeByDocTypeError,
    ThrowOnError
  >({
    url: "/api/master/status-service/paged-by-doc-type/{docType}",
    ...options,
  });
};

export const postApiAppPaymentDetailsList = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppPaymentDetailsListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppPaymentDetailsListResponse,
    PostApiAppPaymentDetailsListError,
    ThrowOnError
  >({
    url: "/api/app/payment-details/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppPaymentDetails = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppPaymentDetailsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppPaymentDetailsResponse,
    GetApiAppPaymentDetailsError,
    ThrowOnError
  >({
    url: "/api/app/payment-details",
    ...options,
  });
};

export const postApiAppPaymentDetails = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppPaymentDetailsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppPaymentDetailsResponse,
    PostApiAppPaymentDetailsError,
    ThrowOnError
  >({
    url: "/api/app/payment-details",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppPaymentDetailsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppPaymentDetailsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppPaymentDetailsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payment-details/{id}",
    ...options,
  });
};

export const getApiAppPaymentDetailsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppPaymentDetailsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppPaymentDetailsByIdResponse,
    GetApiAppPaymentDetailsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payment-details/{id}",
    ...options,
  });
};

export const putApiAppPaymentDetailsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppPaymentDetailsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppPaymentDetailsByIdResponse,
    PutApiAppPaymentDetailsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payment-details/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppPaymentGuests = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppPaymentGuestsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppPaymentGuestsResponse,
    GetApiAppPaymentGuestsError,
    ThrowOnError
  >({
    url: "/api/app/payment-guests",
    ...options,
  });
};

export const postApiAppPaymentGuests = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppPaymentGuestsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppPaymentGuestsResponse,
    PostApiAppPaymentGuestsError,
    ThrowOnError
  >({
    url: "/api/app/payment-guests",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppPaymentGuestsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppPaymentGuestsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppPaymentGuestsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payment-guests/{id}",
    ...options,
  });
};

export const getApiAppPaymentGuestsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppPaymentGuestsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppPaymentGuestsByIdResponse,
    GetApiAppPaymentGuestsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payment-guests/{id}",
    ...options,
  });
};

export const putApiAppPaymentGuestsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppPaymentGuestsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppPaymentGuestsByIdResponse,
    PutApiAppPaymentGuestsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payment-guests/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiMasterPaymentMethod = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiMasterPaymentMethodData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiMasterPaymentMethodResponse,
    GetApiMasterPaymentMethodError,
    ThrowOnError
  >({
    url: "/api/master/payment-method",
    ...options,
  });
};

export const postApiMasterPaymentMethod = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiMasterPaymentMethodData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiMasterPaymentMethodResponse,
    PostApiMasterPaymentMethodError,
    ThrowOnError
  >({
    url: "/api/master/payment-method",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiMasterPaymentMethodById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiMasterPaymentMethodByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiMasterPaymentMethodByIdError,
    ThrowOnError
  >({
    url: "/api/master/payment-method/{id}",
    ...options,
  });
};

export const getApiMasterPaymentMethodById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiMasterPaymentMethodByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMasterPaymentMethodByIdResponse,
    GetApiMasterPaymentMethodByIdError,
    ThrowOnError
  >({
    url: "/api/master/payment-method/{id}",
    ...options,
  });
};

export const putApiMasterPaymentMethodById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiMasterPaymentMethodByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiMasterPaymentMethodByIdResponse,
    PutApiMasterPaymentMethodByIdError,
    ThrowOnError
  >({
    url: "/api/master/payment-method/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppPaymentsList = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppPaymentsListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppPaymentsListResponse,
    PostApiAppPaymentsListError,
    ThrowOnError
  >({
    url: "/api/app/payments/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppPayments = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppPaymentsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppPaymentsResponse,
    GetApiAppPaymentsError,
    ThrowOnError
  >({
    url: "/api/app/payments",
    ...options,
  });
};

export const postApiAppPayments = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppPaymentsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppPaymentsResponse,
    PostApiAppPaymentsError,
    ThrowOnError
  >({
    url: "/api/app/payments",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppPaymentsById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiAppPaymentsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppPaymentsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payments/{id}",
    ...options,
  });
};

export const getApiAppPaymentsById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppPaymentsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppPaymentsByIdResponse,
    GetApiAppPaymentsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payments/{id}",
    ...options,
  });
};

export const putApiAppPaymentsById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppPaymentsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppPaymentsByIdResponse,
    PutApiAppPaymentsByIdError,
    ThrowOnError
  >({
    url: "/api/app/payments/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppPaymentsByPaymentIdCleanupAttachments = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    PostApiAppPaymentsByPaymentIdCleanupAttachmentsData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).post<
    PostApiAppPaymentsByPaymentIdCleanupAttachmentsResponse,
    PostApiAppPaymentsByPaymentIdCleanupAttachmentsError,
    ThrowOnError
  >({
    url: "/api/app/payments/{paymentId}/cleanup-attachments",
    ...options,
  });
};

export const getApiAccountMyProfile = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAccountMyProfileData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAccountMyProfileResponse,
    GetApiAccountMyProfileError,
    ThrowOnError
  >({
    url: "/api/account/my-profile",
    ...options,
  });
};

export const putApiAccountMyProfile = <ThrowOnError extends boolean = false>(
  options?: Options<PutApiAccountMyProfileData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).put<
    PutApiAccountMyProfileResponse,
    PutApiAccountMyProfileError,
    ThrowOnError
  >({
    url: "/api/account/my-profile",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAccountMyProfileChangePassword = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAccountMyProfileChangePasswordData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiAccountMyProfileChangePasswordError,
    ThrowOnError
  >({
    url: "/api/account/my-profile/change-password",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppRegistrationCardGenerateDocumentAsAttachment = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    PostApiAppRegistrationCardGenerateDocumentAsAttachmentData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppRegistrationCardGenerateDocumentAsAttachmentResponse,
    PostApiAppRegistrationCardGenerateDocumentAsAttachmentError,
    ThrowOnError
  >({
    url: "/api/app/registration-card/generate-document-as-attachment",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiReport = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiReportData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiReportResponse,
    GetApiReportError,
    ThrowOnError
  >({
    url: "/api/report",
    ...options,
  });
};

export const postApiReport = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiReportData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiReportResponse,
    PostApiReportError,
    ThrowOnError
  >({
    url: "/api/report",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiReportActive = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiReportActiveData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiReportActiveResponse,
    GetApiReportActiveError,
    ThrowOnError
  >({
    url: "/api/report/active",
    ...options,
  });
};

export const deleteApiReportById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiReportByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiReportByIdError,
    ThrowOnError
  >({
    url: "/api/report/{id}",
    ...options,
  });
};

export const getApiReportById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiReportByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiReportByIdResponse,
    GetApiReportByIdError,
    ThrowOnError
  >({
    url: "/api/report/{id}",
    ...options,
  });
};

export const putApiReportById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiReportByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiReportByIdResponse,
    PutApiReportByIdError,
    ThrowOnError
  >({
    url: "/api/report/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiReportPreview = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiReportPreviewData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiReportPreviewResponse,
    PostApiReportPreviewError,
    ThrowOnError
  >({
    url: "/api/report/preview",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiReportExportCsv = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiReportExportCsvData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiReportExportCsvError,
    ThrowOnError
  >({
    url: "/api/report/export/csv",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiReportExportExcel = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiReportExportExcelData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiReportExportExcelError,
    ThrowOnError
  >({
    url: "/api/report/export/excel",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiReportExportExcelCustom = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiReportExportExcelCustomData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiReportExportExcelCustomError,
    ThrowOnError
  >({
    url: "/api/report/export/excel/custom",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiReportByReportIdParameters = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiReportByReportIdParametersData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiReportByReportIdParametersResponse,
    GetApiReportByReportIdParametersError,
    ThrowOnError
  >({
    url: "/api/report/{reportId}/parameters",
    ...options,
  });
};

export const getApiReportByReportIdExcelHeader = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiReportByReportIdExcelHeaderData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiReportByReportIdExcelHeaderResponse,
    GetApiReportByReportIdExcelHeaderError,
    ThrowOnError
  >({
    url: "/api/report/{reportId}/excel-header",
    ...options,
  });
};

export const putApiReportByReportIdExcelHeader = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiReportByReportIdExcelHeaderData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    unknown,
    PutApiReportByReportIdExcelHeaderError,
    ThrowOnError
  >({
    url: "/api/report/{reportId}/excel-header",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiReportByReportIdDebugHeader = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiReportByReportIdDebugHeaderData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    unknown,
    GetApiReportByReportIdDebugHeaderError,
    ThrowOnError
  >({
    url: "/api/report/{reportId}/debug-header",
    ...options,
  });
};

export const postApiReportByReportIdTestHeader = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PostApiReportByReportIdTestHeaderData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).post<
    unknown,
    PostApiReportByReportIdTestHeaderError,
    ThrowOnError
  >({
    url: "/api/report/{reportId}/test-header",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiReportExportExcelConditionalFormattingTest = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    PostApiReportExportExcelConditionalFormattingTestData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiReportExportExcelConditionalFormattingTestError,
    ThrowOnError
  >({
    url: "/api/report/export/excel/conditional-formatting-test",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiReportList = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiReportListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiReportListResponse,
    PostApiReportListError,
    ThrowOnError
  >({
    url: "/api/report/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiReportByReportIdTestPivot = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PostApiReportByReportIdTestPivotData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).post<
    unknown,
    PostApiReportByReportIdTestPivotError,
    ThrowOnError
  >({
    url: "/api/report/{reportId}/test-pivot",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppReportrooms = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppReportroomsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReportroomsResponse,
    GetApiAppReportroomsError,
    ThrowOnError
  >({
    url: "/api/app/reportrooms",
    ...options,
  });
};

export const postApiAppReportroomsReportByDate = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReportroomsReportByDateData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReportroomsReportByDateResponse,
    PostApiAppReportroomsReportByDateError,
    ThrowOnError
  >({
    url: "/api/app/reportrooms/report-by-date",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppReportroomsRoomTypes = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAppReportroomsRoomTypesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReportroomsRoomTypesResponse,
    GetApiAppReportroomsRoomTypesError,
    ThrowOnError
  >({
    url: "/api/app/reportrooms/room-types",
    ...options,
  });
};

export const getApiAppReportRoomsReportRooms = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAppReportRoomsReportRoomsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReportRoomsReportRoomsResponse,
    GetApiAppReportRoomsReportRoomsError,
    ThrowOnError
  >({
    url: "/api/app/report-rooms/report-rooms",
    ...options,
  });
};

export const getApiAppReportRoomsRoomTypesReportByDate = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    GetApiAppReportRoomsRoomTypesReportByDateData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReportRoomsRoomTypesReportByDateResponse,
    GetApiAppReportRoomsRoomTypesReportByDateError,
    ThrowOnError
  >({
    url: "/api/app/report-rooms/room-types-report-by-date",
    ...options,
  });
};

export const postApiAppReservationDetailsList = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationDetailsListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationDetailsListResponse,
    PostApiAppReservationDetailsListError,
    ThrowOnError
  >({
    url: "/api/app/reservation-details/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppReservationDetailsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppReservationDetailsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppReservationDetailsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-details/{id}",
    ...options,
  });
};

export const getApiAppReservationDetailsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppReservationDetailsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppReservationDetailsByIdResponse,
    GetApiAppReservationDetailsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-details/{id}",
    ...options,
  });
};

export const putApiAppReservationDetailsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppReservationDetailsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppReservationDetailsByIdResponse,
    PutApiAppReservationDetailsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-details/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppReservationDetails = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAppReservationDetailsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReservationDetailsResponse,
    GetApiAppReservationDetailsError,
    ThrowOnError
  >({
    url: "/api/app/reservation-details",
    ...options,
  });
};

export const postApiAppReservationDetails = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationDetailsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationDetailsResponse,
    PostApiAppReservationDetailsError,
    ThrowOnError
  >({
    url: "/api/app/reservation-details",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppReservationFoodAndBeveragesList = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    PostApiAppReservationFoodAndBeveragesListData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationFoodAndBeveragesListResponse,
    PostApiAppReservationFoodAndBeveragesListError,
    ThrowOnError
  >({
    url: "/api/app/reservation-food-and-beverages/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppReservationFoodAndBeverages = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAppReservationFoodAndBeveragesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReservationFoodAndBeveragesResponse,
    GetApiAppReservationFoodAndBeveragesError,
    ThrowOnError
  >({
    url: "/api/app/reservation-food-and-beverages",
    ...options,
  });
};

export const postApiAppReservationFoodAndBeverages = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationFoodAndBeveragesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationFoodAndBeveragesResponse,
    PostApiAppReservationFoodAndBeveragesError,
    ThrowOnError
  >({
    url: "/api/app/reservation-food-and-beverages",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppReservationFoodAndBeveragesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    DeleteApiAppReservationFoodAndBeveragesByIdData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppReservationFoodAndBeveragesByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-food-and-beverages/{id}",
    ...options,
  });
};

export const getApiAppReservationFoodAndBeveragesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppReservationFoodAndBeveragesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppReservationFoodAndBeveragesByIdResponse,
    GetApiAppReservationFoodAndBeveragesByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-food-and-beverages/{id}",
    ...options,
  });
};

export const putApiAppReservationFoodAndBeveragesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppReservationFoodAndBeveragesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppReservationFoodAndBeveragesByIdResponse,
    PutApiAppReservationFoodAndBeveragesByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-food-and-beverages/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppReservationFoodAndBeveragesCreateMany = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    PostApiAppReservationFoodAndBeveragesCreateManyData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationFoodAndBeveragesCreateManyResponse,
    PostApiAppReservationFoodAndBeveragesCreateManyError,
    ThrowOnError
  >({
    url: "/api/app/reservation-food-and-beverages/create-many",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const putApiAppReservationFoodAndBeveragesUpdateMany = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    PutApiAppReservationFoodAndBeveragesUpdateManyData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).put<
    PutApiAppReservationFoodAndBeveragesUpdateManyResponse,
    PutApiAppReservationFoodAndBeveragesUpdateManyError,
    ThrowOnError
  >({
    url: "/api/app/reservation-food-and-beverages/update-many",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppReservationRoomsList = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationRoomsListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationRoomsListResponse,
    PostApiAppReservationRoomsListError,
    ThrowOnError
  >({
    url: "/api/app/reservation-rooms/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppReservationRoomsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppReservationRoomsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppReservationRoomsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-rooms/{id}",
    ...options,
  });
};

export const getApiAppReservationRoomsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppReservationRoomsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppReservationRoomsByIdResponse,
    GetApiAppReservationRoomsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-rooms/{id}",
    ...options,
  });
};

export const putApiAppReservationRoomsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppReservationRoomsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppReservationRoomsByIdResponse,
    PutApiAppReservationRoomsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-rooms/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppReservationRooms = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppReservationRoomsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReservationRoomsResponse,
    GetApiAppReservationRoomsError,
    ThrowOnError
  >({
    url: "/api/app/reservation-rooms",
    ...options,
  });
};

export const postApiAppReservationRooms = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationRoomsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationRoomsResponse,
    PostApiAppReservationRoomsError,
    ThrowOnError
  >({
    url: "/api/app/reservation-rooms",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppReservationRoomsCreateMany = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationRoomsCreateManyData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationRoomsCreateManyResponse,
    PostApiAppReservationRoomsCreateManyError,
    ThrowOnError
  >({
    url: "/api/app/reservation-rooms/create-many",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppReservationsList = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationsListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationsListResponse,
    PostApiAppReservationsListError,
    ThrowOnError
  >({
    url: "/api/app/reservations/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppReservations = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppReservationsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReservationsResponse,
    GetApiAppReservationsError,
    ThrowOnError
  >({
    url: "/api/app/reservations",
    ...options,
  });
};

export const postApiAppReservations = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppReservationsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationsResponse,
    PostApiAppReservationsError,
    ThrowOnError
  >({
    url: "/api/app/reservations",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppReservationsCreateWithDetails = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationsCreateWithDetailsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    PostApiAppReservationsCreateWithDetailsError,
    ThrowOnError
  >({
    url: "/api/app/reservations/create-with-details",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const putApiAppReservationsByIdUpdateWithDetails = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    PutApiAppReservationsByIdUpdateWithDetailsData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppReservationsByIdUpdateWithDetailsResponse,
    PutApiAppReservationsByIdUpdateWithDetailsError,
    ThrowOnError
  >({
    url: "/api/app/reservations/{id}/update-with-details",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppReservationsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppReservationsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppReservationsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservations/{id}",
    ...options,
  });
};

export const getApiAppReservationsById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppReservationsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppReservationsByIdResponse,
    GetApiAppReservationsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservations/{id}",
    ...options,
  });
};

export const putApiAppReservationsById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppReservationsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppReservationsByIdResponse,
    PutApiAppReservationsByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservations/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppReservationsByIdWithDetails = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppReservationsByIdWithDetailsData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppReservationsByIdWithDetailsResponse,
    GetApiAppReservationsByIdWithDetailsError,
    ThrowOnError
  >({
    url: "/api/app/reservations/{id}/with-details",
    ...options,
  });
};

export const getApiAppReservationTypes = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppReservationTypesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppReservationTypesResponse,
    GetApiAppReservationTypesError,
    ThrowOnError
  >({
    url: "/api/app/reservation-types",
    ...options,
  });
};

export const postApiAppReservationTypes = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppReservationTypesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppReservationTypesResponse,
    PostApiAppReservationTypesError,
    ThrowOnError
  >({
    url: "/api/app/reservation-types",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppReservationTypesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppReservationTypesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppReservationTypesByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-types/{id}",
    ...options,
  });
};

export const getApiAppReservationTypesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppReservationTypesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppReservationTypesByIdResponse,
    GetApiAppReservationTypesByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-types/{id}",
    ...options,
  });
};

export const putApiAppReservationTypesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppReservationTypesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppReservationTypesByIdResponse,
    PutApiAppReservationTypesByIdError,
    ThrowOnError
  >({
    url: "/api/app/reservation-types/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppRoom = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppRoomData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppRoomResponse,
    GetApiAppRoomError,
    ThrowOnError
  >({
    url: "/api/app/room",
    ...options,
  });
};

export const postApiAppRoom = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppRoomData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppRoomResponse,
    PostApiAppRoomError,
    ThrowOnError
  >({
    url: "/api/app/room",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppRoomById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiAppRoomByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppRoomByIdError,
    ThrowOnError
  >({
    url: "/api/app/room/{id}",
    ...options,
  });
};

export const getApiAppRoomById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppRoomByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppRoomByIdResponse,
    GetApiAppRoomByIdError,
    ThrowOnError
  >({
    url: "/api/app/room/{id}",
    ...options,
  });
};

export const putApiAppRoomById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppRoomByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppRoomByIdResponse,
    PutApiAppRoomByIdError,
    ThrowOnError
  >({
    url: "/api/app/room/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppRoomsList = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppRoomsListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppRoomsListResponse,
    PostApiAppRoomsListError,
    ThrowOnError
  >({
    url: "/api/app/rooms/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppRoomStatus = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppRoomStatusData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppRoomStatusResponse,
    GetApiAppRoomStatusError,
    ThrowOnError
  >({
    url: "/api/app/room-status",
    ...options,
  });
};

export const postApiAppRoomStatus = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppRoomStatusData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppRoomStatusResponse,
    PostApiAppRoomStatusError,
    ThrowOnError
  >({
    url: "/api/app/room-status",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppRoomStatusById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppRoomStatusByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppRoomStatusByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-status/{id}",
    ...options,
  });
};

export const getApiAppRoomStatusById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppRoomStatusByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppRoomStatusByIdResponse,
    GetApiAppRoomStatusByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-status/{id}",
    ...options,
  });
};

export const putApiAppRoomStatusById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppRoomStatusByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppRoomStatusByIdResponse,
    PutApiAppRoomStatusByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-status/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppRoomStatusLogs = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppRoomStatusLogsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppRoomStatusLogsResponse,
    GetApiAppRoomStatusLogsError,
    ThrowOnError
  >({
    url: "/api/app/room-status-logs",
    ...options,
  });
};

export const postApiAppRoomStatusLogs = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppRoomStatusLogsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppRoomStatusLogsResponse,
    PostApiAppRoomStatusLogsError,
    ThrowOnError
  >({
    url: "/api/app/room-status-logs",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppRoomStatusLogsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppRoomStatusLogsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppRoomStatusLogsByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-status-logs/{id}",
    ...options,
  });
};

export const getApiAppRoomStatusLogsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppRoomStatusLogsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppRoomStatusLogsByIdResponse,
    GetApiAppRoomStatusLogsByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-status-logs/{id}",
    ...options,
  });
};

export const putApiAppRoomStatusLogsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppRoomStatusLogsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppRoomStatusLogsByIdResponse,
    PutApiAppRoomStatusLogsByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-status-logs/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppRoomType = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppRoomTypeData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppRoomTypeResponse,
    GetApiAppRoomTypeError,
    ThrowOnError
  >({
    url: "/api/app/room-type",
    ...options,
  });
};

export const postApiAppRoomType = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppRoomTypeData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppRoomTypeResponse,
    PostApiAppRoomTypeError,
    ThrowOnError
  >({
    url: "/api/app/room-type",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppRoomTypeById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiAppRoomTypeByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppRoomTypeByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-type/{id}",
    ...options,
  });
};

export const getApiAppRoomTypeById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppRoomTypeByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppRoomTypeByIdResponse,
    GetApiAppRoomTypeByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-type/{id}",
    ...options,
  });
};

export const putApiAppRoomTypeById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppRoomTypeByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppRoomTypeByIdResponse,
    PutApiAppRoomTypeByIdError,
    ThrowOnError
  >({
    url: "/api/app/room-type/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppServicesList = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppServicesListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppServicesListResponse,
    PostApiAppServicesListError,
    ThrowOnError
  >({
    url: "/api/app/services/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppServices = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppServicesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppServicesResponse,
    GetApiAppServicesError,
    ThrowOnError
  >({
    url: "/api/app/services",
    ...options,
  });
};

export const postApiAppServices = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppServicesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppServicesResponse,
    PostApiAppServicesError,
    ThrowOnError
  >({
    url: "/api/app/services",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppServicesById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiAppServicesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppServicesByIdError,
    ThrowOnError
  >({
    url: "/api/app/services/{id}",
    ...options,
  });
};

export const getApiAppServicesById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppServicesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppServicesByIdResponse,
    GetApiAppServicesByIdError,
    ThrowOnError
  >({
    url: "/api/app/services/{id}",
    ...options,
  });
};

export const putApiAppServicesById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppServicesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppServicesByIdResponse,
    PutApiAppServicesByIdError,
    ThrowOnError
  >({
    url: "/api/app/services/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppServiceTypes = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppServiceTypesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppServiceTypesResponse,
    GetApiAppServiceTypesError,
    ThrowOnError
  >({
    url: "/api/app/service-types",
    ...options,
  });
};

export const postApiAppServiceTypes = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppServiceTypesData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppServiceTypesResponse,
    PostApiAppServiceTypesError,
    ThrowOnError
  >({
    url: "/api/app/service-types",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppServiceTypesById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppServiceTypesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppServiceTypesByIdError,
    ThrowOnError
  >({
    url: "/api/app/service-types/{id}",
    ...options,
  });
};

export const getApiAppServiceTypesById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppServiceTypesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppServiceTypesByIdResponse,
    GetApiAppServiceTypesByIdError,
    ThrowOnError
  >({
    url: "/api/app/service-types/{id}",
    ...options,
  });
};

export const putApiAppServiceTypesById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppServiceTypesByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppServiceTypesByIdResponse,
    PutApiAppServiceTypesByIdError,
    ThrowOnError
  >({
    url: "/api/app/service-types/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiStatusSettings = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiStatusSettingsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiStatusSettingsResponse,
    GetApiStatusSettingsError,
    ThrowOnError
  >({
    url: "/api/status-settings",
    ...options,
  });
};

export const patchApiStatusSettings = <ThrowOnError extends boolean = false>(
  options?: Options<PatchApiStatusSettingsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).patch<
    PatchApiStatusSettingsResponse,
    PatchApiStatusSettingsError,
    ThrowOnError
  >({
    url: "/api/status-settings",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const putApiStatusSettings = <ThrowOnError extends boolean = false>(
  options?: Options<PutApiStatusSettingsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).put<
    PutApiStatusSettingsResponse,
    PutApiStatusSettingsError,
    ThrowOnError
  >({
    url: "/api/status-settings",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppStatusSettings = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppStatusSettingsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppStatusSettingsResponse,
    GetApiAppStatusSettingsError,
    ThrowOnError
  >({
    url: "/api/app/status-settings",
    ...options,
  });
};

export const putApiAppStatusSettings = <ThrowOnError extends boolean = false>(
  options?: Options<PutApiAppStatusSettingsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).put<
    PutApiAppStatusSettingsResponse,
    PutApiAppStatusSettingsError,
    ThrowOnError
  >({
    url: "/api/app/status-settings",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiAppTaxList = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppTaxListData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppTaxListResponse,
    PostApiAppTaxListError,
    ThrowOnError
  >({
    url: "/api/app/tax/list",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppTax = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiAppTaxData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppTaxResponse,
    GetApiAppTaxError,
    ThrowOnError
  >({
    url: "/api/app/tax",
    ...options,
  });
};

export const postApiAppTax = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiAppTaxData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppTaxResponse,
    PostApiAppTaxError,
    ThrowOnError
  >({
    url: "/api/app/tax",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppTaxById = <ThrowOnError extends boolean = false>(
  options: Options<DeleteApiAppTaxByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppTaxByIdError,
    ThrowOnError
  >({
    url: "/api/app/tax/{id}",
    ...options,
  });
};

export const getApiAppTaxById = <ThrowOnError extends boolean = false>(
  options: Options<GetApiAppTaxByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppTaxByIdResponse,
    GetApiAppTaxByIdError,
    ThrowOnError
  >({
    url: "/api/app/tax/{id}",
    ...options,
  });
};

export const putApiAppTaxById = <ThrowOnError extends boolean = false>(
  options: Options<PutApiAppTaxByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppTaxByIdResponse,
    PutApiAppTaxByIdError,
    ThrowOnError
  >({
    url: "/api/app/tax/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiMultiTenancyTenantsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiMultiTenancyTenantsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiMultiTenancyTenantsByIdError,
    ThrowOnError
  >({
    url: "/api/multi-tenancy/tenants/{id}",
    ...options,
  });
};

export const getApiMultiTenancyTenantsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiMultiTenancyTenantsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMultiTenancyTenantsByIdResponse,
    GetApiMultiTenancyTenantsByIdError,
    ThrowOnError
  >({
    url: "/api/multi-tenancy/tenants/{id}",
    ...options,
  });
};

export const putApiMultiTenancyTenantsById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiMultiTenancyTenantsByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiMultiTenancyTenantsByIdResponse,
    PutApiMultiTenancyTenantsByIdError,
    ThrowOnError
  >({
    url: "/api/multi-tenancy/tenants/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiMultiTenancyTenantsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiMultiTenancyTenantsResponse,
    GetApiMultiTenancyTenantsError,
    ThrowOnError
  >({
    url: "/api/multi-tenancy/tenants",
    ...options,
  });
};

export const postApiMultiTenancyTenants = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiMultiTenancyTenantsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiMultiTenancyTenantsResponse,
    PostApiMultiTenancyTenantsError,
    ThrowOnError
  >({
    url: "/api/multi-tenancy/tenants",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiMultiTenancyTenantsByIdDefaultConnectionString = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringError,
    ThrowOnError
  >({
    url: "/api/multi-tenancy/tenants/{id}/default-connection-string",
    ...options,
  });
};

export const getApiMultiTenancyTenantsByIdDefaultConnectionString = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    GetApiMultiTenancyTenantsByIdDefaultConnectionStringData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponse,
    GetApiMultiTenancyTenantsByIdDefaultConnectionStringError,
    ThrowOnError
  >({
    url: "/api/multi-tenancy/tenants/{id}/default-connection-string",
    ...options,
  });
};

export const putApiMultiTenancyTenantsByIdDefaultConnectionString = <
  ThrowOnError extends boolean = false,
>(
  options: Options<
    PutApiMultiTenancyTenantsByIdDefaultConnectionStringData,
    ThrowOnError
  >,
) => {
  return (options.client ?? _heyApiClient).put<
    unknown,
    PutApiMultiTenancyTenantsByIdDefaultConnectionStringError,
    ThrowOnError
  >({
    url: "/api/multi-tenancy/tenants/{id}/default-connection-string",
    ...options,
  });
};

export const getApiTestAuthorizationPublic = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiTestAuthorizationPublicData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/test-authorization/public",
      ...options,
    },
  );
};

export const getApiTestAuthorizationAuthenticated = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiTestAuthorizationAuthenticatedData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/test-authorization/authenticated",
      ...options,
    },
  );
};

export const getApiTestAuthorizationAdminOnly = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiTestAuthorizationAdminOnlyData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/test-authorization/admin-only",
      ...options,
    },
  );
};

export const getApiTestAuthorizationWismaRoom = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiTestAuthorizationWismaRoomData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/test-authorization/wisma-room",
      ...options,
    },
  );
};

export const getApiOptions = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiOptionsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/options",
      ...options,
    },
  );
};

export const getApiCronTickers = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiCronTickersData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/cron-tickers",
      ...options,
    },
  );
};

export const getApiTimeTickers = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiTimeTickersData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/time-tickers",
      ...options,
    },
  );
};

export const getApiTimeTickersGraphDataRange = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiTimeTickersGraphDataRangeData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/time-tickers/:graph-data-range",
      ...options,
    },
  );
};

export const getApiTimeTickersGraphData = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiTimeTickersGraphDataData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/time-tickers/:graph-data",
      ...options,
    },
  );
};

export const getApiCronTickersGraphDataRange = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiCronTickersGraphDataRangeData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/cron-tickers/:graph-data-range",
      ...options,
    },
  );
};

export const getApiCronTickersGraphDataRangeId = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiCronTickersGraphDataRangeIdData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/cron-tickers/:graph-data-range-id",
      ...options,
    },
  );
};

export const getApiCronTickersGraphData = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiCronTickersGraphDataData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/cron-tickers/:graph-data",
      ...options,
    },
  );
};

export const getApiCronTickerOccurrencesCronTickerId = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiCronTickerOccurrencesCronTickerIdData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/cron-ticker-occurrences/:cronTickerId",
      ...options,
    },
  );
};

export const getApiCronTickerOccurrencesCronTickerIdGraphData = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    GetApiCronTickerOccurrencesCronTickerIdGraphDataData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/cron-ticker-occurrences/:cronTickerId/:graph-data",
      ...options,
    },
  );
};

export const postApiTickerCancel = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiTickerCancelData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/ticker/:cancel",
    ...options,
  });
};

export const deleteTimeTickerDelete = <ThrowOnError extends boolean = false>(
  options?: Options<DeleteTimeTickerDeleteData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).delete<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/time-ticker/:delete",
    ...options,
  });
};

export const deleteApiCronTickerDelete = <ThrowOnError extends boolean = false>(
  options?: Options<DeleteApiCronTickerDeleteData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).delete<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/cron-ticker/:delete",
    ...options,
  });
};

export const deleteApiCronTickerOccurrenceDelete = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<DeleteApiCronTickerOccurrenceDeleteData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).delete<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/cron-ticker-occurrence/:delete",
    ...options,
  });
};

export const getApiTickerRequestId = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiTickerRequestIdData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/ticker-request/:id",
      ...options,
    },
  );
};

export const getApiTickerFunctions = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiTickerFunctionsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/ticker-functions",
      ...options,
    },
  );
};

export const putApiTimeTickerUpdate = <ThrowOnError extends boolean = false>(
  options?: Options<PutApiTimeTickerUpdateData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).put<unknown, unknown, ThrowOnError>(
    {
      url: "/api/time-ticker/:update",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
    },
  );
};

export const postApiTimeTickerAdd = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiTimeTickerAddData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/time-ticker/:add",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const postApiCronTickerAdd = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiCronTickerAddData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/cron-ticker/:add",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const putCronTickerUpdate = <ThrowOnError extends boolean = false>(
  options?: Options<PutCronTickerUpdateData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).put<unknown, unknown, ThrowOnError>(
    {
      url: "/cron-ticker/:update",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
    },
  );
};

export const getApiTickerHostNextTicker = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiTickerHostNextTickerData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/ticker-host/:next-ticker",
      ...options,
    },
  );
};

export const postApiTickerHostStop = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiTickerHostStopData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/ticker-host/:stop",
    ...options,
  });
};

export const postApiTickerHostStart = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiTickerHostStartData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/ticker-host/:start",
    ...options,
  });
};

export const postApiTickerHostRestart = <ThrowOnError extends boolean = false>(
  options?: Options<PostApiTickerHostRestartData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    unknown,
    unknown,
    ThrowOnError
  >({
    url: "/api/ticker-host/:restart",
    ...options,
  });
};

export const getApiTickerHostStatus = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiTickerHostStatusData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/ticker-host/:status",
      ...options,
    },
  );
};

export const getApiTickerStatusesGetLastWeek = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiTickerStatusesGetLastWeekData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/ticker/statuses/:get-last-week",
      ...options,
    },
  );
};

export const getApiTickerStatusesGet = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiTickerStatusesGetData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/ticker/statuses/:get",
      ...options,
    },
  );
};

export const getApiTickerMachineJobs = <ThrowOnError extends boolean = false>(
  options?: Options<GetApiTickerMachineJobsData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      url: "/api/ticker/machine/:jobs",
      ...options,
    },
  );
};

export const getApiAppTypeFoodAndBeverage = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAppTypeFoodAndBeverageData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppTypeFoodAndBeverageResponse,
    GetApiAppTypeFoodAndBeverageError,
    ThrowOnError
  >({
    url: "/api/app/type-food-and-beverage",
    ...options,
  });
};

export const postApiAppTypeFoodAndBeverage = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PostApiAppTypeFoodAndBeverageData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).post<
    PostApiAppTypeFoodAndBeverageResponse,
    PostApiAppTypeFoodAndBeverageError,
    ThrowOnError
  >({
    url: "/api/app/type-food-and-beverage",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const deleteApiAppTypeFoodAndBeverageById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<DeleteApiAppTypeFoodAndBeverageByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).delete<
    unknown,
    DeleteApiAppTypeFoodAndBeverageByIdError,
    ThrowOnError
  >({
    url: "/api/app/type-food-and-beverage/{id}",
    ...options,
  });
};

export const getApiAppTypeFoodAndBeverageById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<GetApiAppTypeFoodAndBeverageByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).get<
    GetApiAppTypeFoodAndBeverageByIdResponse,
    GetApiAppTypeFoodAndBeverageByIdError,
    ThrowOnError
  >({
    url: "/api/app/type-food-and-beverage/{id}",
    ...options,
  });
};

export const putApiAppTypeFoodAndBeverageById = <
  ThrowOnError extends boolean = false,
>(
  options: Options<PutApiAppTypeFoodAndBeverageByIdData, ThrowOnError>,
) => {
  return (options.client ?? _heyApiClient).put<
    PutApiAppTypeFoodAndBeverageByIdResponse,
    PutApiAppTypeFoodAndBeverageByIdError,
    ThrowOnError
  >({
    url: "/api/app/type-food-and-beverage/{id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

export const getApiAppUserSynchronizationHealth = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<GetApiAppUserSynchronizationHealthData, ThrowOnError>,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppUserSynchronizationHealthResponse,
    unknown,
    ThrowOnError
  >({
    url: "/api/app/user-synchronization/health",
    ...options,
  });
};

export const getApiAppUserSynchronizationHealthDetailed = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<
    GetApiAppUserSynchronizationHealthDetailedData,
    ThrowOnError
  >,
) => {
  return (options?.client ?? _heyApiClient).get<
    GetApiAppUserSynchronizationHealthDetailedResponse,
    unknown,
    ThrowOnError
  >({
    url: "/api/app/user-synchronization/health/detailed",
    ...options,
  });
};
