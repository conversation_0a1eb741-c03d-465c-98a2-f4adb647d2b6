# Wisma Management System - PT IMIP

## 📌 Description
The **Wisma Management System** is a web-based platform developed using **Next.js** to manage hotel (wisma) reservations and administration at PT IMIP. This application is designed to facilitate users in making reservations, managing rooms, and monitoring wisma availability.

## 🚀 Key Features
- **Reservation Management**: Book rooms with complete information.
- **Room Management**: Add, edit, and delete room data.
- **Availability Monitoring**: Check room status in real-time.
- **User Management**: Admin and regular user access control.
- **Reports & Statistics**: Recap of room usage and reservations.

## 🛠️ Technologies Used
- **Frontend**: [Next.js](https://nextjs.org/) (React Framework)
- **Backend**: API based on .NET ABP.
- **Database**: SQL Server.
- **Authentication**: Using [NextAuth.js](https://next-auth.js.org/).
- **Deployment**: Docker.

## 📦 Installation and Running the Application
1. **Clone the repository**:
   ```sh
   git clone https://github.com/itimip/fowisma-frontend.git
   cd fowisma-frontend
   ```

2. **Install dependencies**:
   ```sh
   npm install
   ```

3. **Configure environment variables**:
   Create a `.env.local` file and add the required variables, example:
   ```env
   DATABASE_URL=your_database_url
   NEXTAUTH_SECRET=your_secret
   NEXTAUTH_URL=http://localhost:3000
   ```

4. **Run the application in development mode**:
   ```sh
   npm run dev
   ```
   Access the application at `http://localhost:3000`

5. **Build for production**:
   ```sh
   npm run build
   npm start
   ```

## 📌 Directory Structure
```
├── public/          # Static assets
├── src/
│   ├── components/  # UI components
│   ├── pages/       # Main pages
│   ├── api/         # API Routes if used
│   ├── hooks/       # Custom hooks
│   ├── styles/      # CSS and Tailwind files
│   ├── utils/       # Utility functions
│   ├── services/    # API service functions
└── .env.local       # Environment configuration
```

## 🌍 Deployment
For deployment, the following options are available:
- **Docker** (If using containerization)

## 👥 Contributors
- **elkodedy** - Fullstack Developer (https://github.com/elkodedy)
- **thisismusni** - Fullstack Developer (https://github.com/thisismusni)

## 📞 Contact & Support
For questions or assistance, please contact the **PT IMIP - IT APPLICATION DEVELOPMENT team** or create an issue in this repository.

---

💡 *Developed with ❤️ by the PT IMIP - IT APPLICATION DEVELOPMENT team*

