export default function GymIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      {...props}
    >
      <g
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit="10"
      >
        <path d="M.35 10.94v1.65c0 .26.21.47.47.47h1.13V7.84H.82a.47.47 0 0 0-.47.47v1M23.18 7.84h-1.13v5.22h1.13c.26 0 .47-.21.47-.47V8.3a.47.47 0 0 0-.47-.47ZM8.34 9.26H4.78M8.34 8.1v1.34a.91.91 0 1 0 1.83 0V8.1a.91.91 0 0 0-1.83 0Z" />
        <path d="M10.17 8.1v1.34a.91.91 0 0 0 1.83 0V8.1a.91.91 0 0 0-1.83 0ZM12.7 10.1V10c0-.3.25-.55.55-.55h.58V8.1a.91.91 0 0 0-1.83 0v1.34c0 .44.31.8.72.9a1.17 1.17 0 0 1-.02-.23ZM15.66 9.45V8.1a.91.91 0 0 0-1.83 0v1.34s0 0 0 0h1.83ZM6.48 6.83l.7.4M8.81 4.5l.41.7M12 3.64v.81M15.19 4.5l-.41.7M17.52 6.83l-.7.4M10.1 18.35v1.54c0 .26.2.47.47.47h3.6c.27 0 .48-.2.48-.47v-3.8c0-.2.08-.37.22-.5l1.16-1.01c.49-.44.77-1.06.77-1.71v-1.23M8.34 11.64V13c0 .58.22 1.13.61 1.55l.98 1.05c.1.12.17.28.17.45v.66M4.08 16.1H2.65a.7.7 0 0 1-.7-.7V5.5c0-.39.31-.7.7-.7h1.43c.39 0 .7.31.7.7v9.9a.7.7 0 0 1-.7.7ZM19.22 7.93v7.47c0 .39.31.7.7.7h1.43a.7.7 0 0 0 .7-.7V5.5a.7.7 0 0 0-.7-.7h-1.43a.7.7 0 0 0-.7.7v.79" />
        <path d="M16.8 12.7v-2.43a.82.82 0 0 0-.82-.82h-2.73c-.3 0-.55.24-.55.54v.12c0 .64.52 1.17 1.17 1.17h1.1l-.92.6c-.48.3-.76.83-.76 1.4v.4M4.78 11.64h9.63M19.22 9.26h-3.56M16.8 11.64h2.42" />
      </g>
    </svg>
  );
}
