"use server";
import { getSession } from "@/lib/actions";
import axios from "axios";

export const deleteRoomMaster = async (
  id: string,
): Promise<{
  success: boolean;
  message: string;
}> => {
  try {
    const session = await getSession();

    await axios({
      url: `${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room/${id}`,
      method: "DELETE",
      headers: {
        Authorization: "Bearer " + session?.access_token,
        "Accept-Language": "en_US",
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    return {
      success: true,
      message: "Room deleted successfully.",
    };
  } catch (error) {
    console.error("Failed to delete room:", error);

    return {
      success: false,
      message: "An unexpected error occurred.",
    };
  }
};
