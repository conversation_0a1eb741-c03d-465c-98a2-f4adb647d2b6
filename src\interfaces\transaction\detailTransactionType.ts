import type {
  FieldErrors,
  FieldValues,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import type { SelectOption } from "rizzui";
import type { Column } from "@/interfaces/table/tableType";

export interface DetailTransactionProps {
  label: string;
  name: string;
  columns: Column[];
  data: Record<string, unknown>[];
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<FieldValues>;
  options: SelectOption[];
  onDelete: (id: number) => void;
  onQtyChange?: (item: number, newQty: number) => Promise<void>;
  onDateChange?: (item: number, newDate: string | Date) => void;
  onClear?: () => void;
  onSave?: () => void;
}
