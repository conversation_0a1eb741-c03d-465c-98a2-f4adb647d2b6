﻿import { useCallback } from "react";

import { useAppConfig } from "./useAppConfig";

/**
 * Represents the various policies that can be granted within the application.
 *
 * The available policies include:
 * - `AbpIdentity.Roles`: Manage roles.
 * - `AbpIdentity.Roles.Create`: Create new roles.
 * - `AbpIdentity.Roles.Update`: Update existing roles.
 * - `AbpIdentity.Roles.Delete`: Delete roles.
 * - `AbpIdentity.Roles.ManagePermissions`: Manage permissions for roles.
 * - `AbpIdentity.Users`: Manage users.
 * - `AbpIdentity.Users.Create`: Create new users.
 * - `AbpIdentity.Users.Update`: Update existing users.
 * - `AbpIdentity.Users.Delete`: Delete users.
 * - `AbpIdentity.Users.ManagePermissions`: Manage permissions for users.
 * - `AbpTenantManagement.Tenants`: Manage tenants.
 * - `AbpTenantManagement.Tenants.Create`: Create new tenants.
 * - `AbpTenantManagement.Tenants.Update`: Update existing tenants.
 * - `AbpTenantManagement.Tenants.Delete`: Delete tenants.
 * - `AbpTenantManagement.Tenants.ManageFeatures`: Manage features for tenants.
 * - `AbpTenantManagement.Tenants.ManageConnectionStrings`: Manage connection strings for tenants.
 * - `FeatureManagement.ManageHostFeatures`: Manage host features.
 * - `SettingManagement.Emailing`: Manage emailing settings.
 * - `SettingManagement.Emailing.Test`: Test emailing settings.
 */
export type Policy =
  | "WismaApp.DiningOptions"
  | "WismaApp.DiningOptions.Create"
  | "WismaApp.DiningOptions.Delete"
  | "WismaApp.DiningOptions.Edit"
  | "WismaApp.DiningOptions.View"
  | "WismaApp.FoodAndBeverage"
  | "WismaApp.FoodAndBeverage.Create"
  | "WismaApp.FoodAndBeverage.Delete"
  | "WismaApp.FoodAndBeverage.Edit"
  | "WismaApp.FoodAndBeverage.View"
  | "WismaApp.FoodAndBeverageType"
  | "WismaApp.FoodAndBeverageType.Create"
  | "WismaApp.FoodAndBeverageType.Delete"
  | "WismaApp.FoodAndBeverageType.Edit"
  | "WismaApp.FoodAndBeverageType.View"
  | "WismaApp.Guest"
  | "WismaApp.Guest.Create"
  | "WismaApp.Guest.Delete"
  | "WismaApp.Guest.Edit"
  | "WismaApp.Guest.View"
  | "WismaApp.MasterCompany"
  | "WismaApp.MasterCompany.Create"
  | "WismaApp.MasterCompany.Delete"
  | "WismaApp.MasterCompany.Edit"
  | "WismaApp.MasterCompany.View"
  | "WismaApp.MasterStatus"
  | "WismaApp.MasterStatus.Create"
  | "WismaApp.MasterStatus.Delete"
  | "WismaApp.MasterStatus.Edit"
  | "WismaApp.MasterStatus.View"
  | "WismaApp.Payment"
  | "WismaApp.Payment.Create"
  | "WismaApp.Payment.Delete"
  | "WismaApp.Payment.Edit"
  | "WismaApp.Payment.View"
  | "WismaApp.PaymentDetails"
  | "WismaApp.PaymentDetails.Create"
  | "WismaApp.PaymentDetails.Delete"
  | "WismaApp.PaymentDetails.Edit"
  | "WismaApp.PaymentDetails.View"
  | "WismaApp.PaymentGuest"
  | "WismaApp.PaymentGuest.Create"
  | "WismaApp.PaymentGuest.Delete"
  | "WismaApp.PaymentGuest.Edit"
  | "WismaApp.PaymentGuest.View"
  | "WismaApp.PaymentMethod"
  | "WismaApp.PaymentMethod.Create"
  | "WismaApp.PaymentMethod.Delete"
  | "WismaApp.PaymentMethod.Edit"
  | "WismaApp.PaymentMethod.View"
  | "WismaApp.Report"
  | "WismaApp.Report.Create"
  | "WismaApp.Report.Delete"
  | "WismaApp.Report.Edit"
  | "WismaApp.Report.View"
  | "WismaApp.Reservation"
  | "WismaApp.Reservation.CheckIn"
  | "WismaApp.Reservation.CheckOut"
  | "WismaApp.Reservation.Create"
  | "WismaApp.Reservation.Delete"
  | "WismaApp.Reservation.Edit"
  | "WismaApp.Reservation.RoomFoodAndBeverage"
  | "WismaApp.Reservation.RoomService"
  | "WismaApp.Reservation.View"
  | "WismaApp.ReservationFoodAndBeverages"
  | "WismaApp.ReservationFoodAndBeverages.Create"
  | "WismaApp.ReservationFoodAndBeverages.Delete"
  | "WismaApp.ReservationFoodAndBeverages.Edit"
  | "WismaApp.ReservationFoodAndBeverages.View"
  | "WismaApp.ReservationRoom"
  | "WismaApp.ReservationRoom.Create"
  | "WismaApp.ReservationRoom.Delete"
  | "WismaApp.ReservationRoom.Edit"
  | "WismaApp.ReservationRoom.View"
  | "WismaApp.ReservationType"
  | "WismaApp.ReservationType.Create"
  | "WismaApp.ReservationType.Delete"
  | "WismaApp.ReservationType.Edit"
  | "WismaApp.ReservationType.View"
  | "WismaApp.Room"
  | "WismaApp.Room.Create"
  | "WismaApp.Room.Delete"
  | "WismaApp.Room.Edit"
  | "WismaApp.Room.View"
  | "WismaApp.RoomStatus"
  | "WismaApp.RoomStatus.Create"
  | "WismaApp.RoomStatus.Delete"
  | "WismaApp.RoomStatus.Edit"
  | "WismaApp.RoomStatus.View"
  | "WismaApp.RoomType"
  | "WismaApp.RoomType.Create"
  | "WismaApp.RoomType.Delete"
  | "WismaApp.RoomType.Edit"
  | "WismaApp.RoomType.View"
  | "WismaApp.Service"
  | "WismaApp.Service.Create"
  | "WismaApp.Service.Delete"
  | "WismaApp.Service.Edit"
  | "WismaApp.Service.View"
  | "WismaApp.ServiceType"
  | "WismaApp.ServiceType.Create"
  | "WismaApp.ServiceType.Delete"
  | "WismaApp.ServiceType.Edit"
  | "WismaApp.ServiceType.View"
  | "WismaApp.Settings.Settings"
  | "WismaApp.Tax"
  | "WismaApp.Tax.Create"
  | "WismaApp.Tax.Delete"
  | "WismaApp.Tax.Edit"
  | "WismaApp.Tax.View";

/**
 * Custom hook to check if a specific policy is granted.
 *
 * This hook uses the application configuration to determine if a given policy is granted.
 *
 * @returns An object with a `can` function that takes a policy key and returns a boolean indicating if the policy is granted.
 *
 * @example
 * const { can } = useGrantedPolicies();
 * const hasPolicy = can('somePolicyKey');
 *
 * @function
 * @name useGrantedPolicies
 */
export const useGrantedPolicies = () => {
  const { data } = useAppConfig();
  // // console.log('grantedPolicies', data?.auth?.grantedPolicies)
  const can = useCallback(
    (key: Policy): boolean => {
      if (data?.auth?.grantedPolicies && !!data.auth.grantedPolicies[key])
        return true;
      return false;
    },
    [data?.auth?.grantedPolicies],
  );
  return { can };
};
