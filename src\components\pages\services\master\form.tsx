import ButtonReset from "@/components/container/button/button-reset";
import React, { useEffect, useState } from "react";
import { Button, Input, Select, type SelectOption } from "rizzui";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { useMasterServiceType } from "@/lib/hooks/services/masterServiceType";
import type { FormProps } from "@/interfaces/form/formType";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  setValue,
  getValues,
  watch,
  onDelete,
  handleReset,
  serviceType,
  setServiceType,
}: FormProps & {
  handleReset: () => void;
  serviceType: SelectOption | undefined;
  setServiceType: (value: SelectOption) => void;
}) {
  const { can } = useGrantedPolicies();
  const [isServiceTypeOpt, setServiceTypeOpt] = useState<SelectOptionType[]>(
    [],
  );
  const { data: masterTypeData } = useMasterServiceType();

  useEffect(() => {
    if (masterTypeData?.items) {
      const mappedTypeOptions = masterTypeData.items.map((item) => ({
        label: item.name ?? "",
        value: item.id ?? "",
      }));
      setServiceTypeOpt(mappedTypeOptions);
    }
  }, [masterTypeData]);
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-5 gap-2">
        <div>
          <Input
            size="sm"
            label="Name"
            disabled={isLoading}
            placeholder="Please fill in Name"
            {...register("name", { required: true })}
            error={errors.name ? "Name is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Service Type"
            options={isServiceTypeOpt}
            value={serviceType}
            onChange={(e: { label: string; value: string }) => {
              setServiceType(e);
              setValue("serviceTypeId", e.value);
            }}
            disabled={isLoading}
            placeholder="Please select Service Type"
            error={
              errors.serviceTypeId ? "Service Type is required" : undefined
            }
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            type="number"
            label="Usage Time"
            disabled={isLoading}
            placeholder="Please fill in Usage Time"
            {...register("usageTime", { required: true })}
            error={errors.usageTime ? "Usage Time is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <CurrencyIDR
            size="sm"
            label="Price"
            name="price"
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Information"
            disabled={isLoading}
            placeholder="Please fill in Information"
            {...register("information", { required: true })}
            error={errors.information ? "Information is required" : undefined}
            className="w-full"
          />
        </div>
      </div>

      <div className="flex justify-end gap-2">
        {can("WismaApp.ReservationRoom.Create") &&
          can("WismaApp.ReservationRoom.Edit") && (
            <Button
              size="sm"
              type="submit"
              disabled={isLoading}
              className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${
                getValues("id") ? "bg-blue-500" : "bg-green-500"
              }`}
            >
              {getValues("id") ? "Update" : "Create"}
            </Button>
          )}
        {getValues("id") && can("WismaApp.ReservationRoom.Delete") && (
          <Button
            size="sm"
            disabled={isLoading}
            className={`rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400`}
            onClick={() => {
              onDelete(String(getValues("id")));
            }}
          >
            Delete
          </Button>
        )}
        <ButtonReset isLoading={isLoading} handleReset={handleReset} />
      </div>
    </form>
  );
}
