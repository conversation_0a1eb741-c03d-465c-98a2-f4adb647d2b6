import React from "react";
import { <PERSON>Funnel, PiFunnelX } from "react-icons/pi";
import { Button } from "rizzui";

export default function ButtonFilter({
  setShowFilters,
  showFilters,
}: {
  setShowFilters: React.Dispatch<React.SetStateAction<boolean>>;
  showFilters: boolean;
}) {
  return (
    <Button
      size="sm"
      variant="outline"
      onClick={() => setShowFilters((prev) => !prev)}
      className="bg-white"
      title="Filters"
    >
      {showFilters ? (
        <PiFunnelX className="inline-block h-4 w-4" />
      ) : (
        <PiFunnel className="inline-block h-4 w-4" />
      )}
    </Button>
  );
}
