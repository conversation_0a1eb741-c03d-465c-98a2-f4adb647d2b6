export default function UserSettingsIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <g clipPath="url(#a)">
        <path
          fill="#FFEED1"
          fillRule="evenodd"
          d="M3.488 5.164a4.184 4.184 0 1 0 8.35-.414s-2.4-2.36-3.456-1.86c-2.037.964-2.239 1.453-4.79 1.343-.068.3-.104.61-.104.93Z"
          clipRule="evenodd"
        />
        <path
          fill="#B3B3B3"
          fillRule="evenodd"
          d="M7.674.98a4.186 4.186 0 0 0-4.081 3.254c2.41 1.143 3.17.363 5.208-.602 1.055-.5 3.037 1.12 3.037 1.12A4.185 4.185 0 0 0 7.674.98Z"
          clipRule="evenodd"
        />
        <path
          fill="#FFE4B5"
          fillRule="evenodd"
          d="M5.664 8.836V10.3l1.805 1.484c.**************.***************.083-.033.205-.103L9.683 10.3V8.836a4.16 4.16 0 0 1-2.01.514 4.16 4.16 0 0 1-2.009-.514Z"
          clipRule="evenodd"
        />
        <path
          fill="#88A0B5"
          fillRule="evenodd"
          d="M11.527 11.99a.346.346 0 0 0 0 .49l.282.283c-.179.3-.317.629-.405.976h-.4a.348.348 0 0 0-.346.346v1.157c0 .19.157.345.346.345h.4c.088.349.226.677.405.978l-.282.283a.346.346 0 0 0 0 .489l.817.817a.345.345 0 0 0 .49 0l.282-.282c.3.179.63.317.976.405v.4c0 .19.156.345.347.345h1.157c.19 0 .345-.156.345-.345v-.4c.348-.088.677-.226.977-.405l.283.282a.346.346 0 0 0 .49 0l.817-.817a.346.346 0 0 0 0-.489l-.282-.283c.178-.3.317-.63.405-.978h.4c.189 0 .344-.155.344-.345v-1.157a.347.347 0 0 0-.345-.346h-.4a3.692 3.692 0 0 0-.404-.976l.282-.283a.346.346 0 0 0 0-.49l-.818-.817a.346.346 0 0 0-.489 0l-.283.282c-.3-.178-.629-.317-.977-.405v-.4a.348.348 0 0 0-.345-.345h-1.157a.348.348 0 0 0-.347.346v.4a3.692 3.692 0 0 0-.976.404l-.283-.282a.346.346 0 0 0-.489 0l-.074.074-.743.744Zm1.542 2.673a1.946 1.946 0 0 1 1.948-1.947 1.948 1.948 0 1 1-1.948 1.947Z"
          clipRule="evenodd"
        />
        <path
          fill="#69D5FF"
          fillRule="evenodd"
          d="M7.878 11.784c-.122.07-.162.105-.205.103-.042.002-.082-.033-.204-.103L5.664 10.3c-.157-.089-.234-.143-.407-.083C2.185 11.267.625 13.149.625 14.89v.628c0 .412.157.694.68.95 1.534.745 4.15 1.117 6.368 1.117 1.225 0 2.57-.113 3.788-.34a.346.346 0 0 1 .066-.399l.282-.283c-.179-.3-.317-.63-.405-.978h-.4a.348.348 0 0 1-.346-.345v-1.157c0-.19.157-.346.346-.346h.4c.088-.347.226-.676.405-.976l-.282-.283a.346.346 0 0 1 0-.49l.743-.743a10.123 10.123 0 0 0-2.18-1.028c-.173-.06-.25-.006-.407.083l-1.805 1.484Z"
          clipRule="evenodd"
        />
        <path
          fill="#D6D6D6"
          fillRule="evenodd"
          d="M14.085 12.952a1.948 1.948 0 1 0 1.865 3.422 1.948 1.948 0 0 0-1.865-3.422Z"
          clipRule="evenodd"
        />
        <path
          fill="#FFE4B5"
          fillRule="evenodd"
          d="M10.458 5.164A4.182 4.182 0 0 1 6.974 9.29a4.16 4.16 0 0 0 2.71-.455 4.184 4.184 0 0 0 2.154-4.086s-.78-.637-1.62-.988a4.184 4.184 0 0 1 .24 1.402Z"
          clipRule="evenodd"
        />
        <path
          fill="#919191"
          fillRule="evenodd"
          d="M6.974 1.038a4.192 4.192 0 0 1 3.243 2.725c.842.351 1.621.988 1.621.988a4.185 4.185 0 0 0-4.864-3.713Z"
          clipRule="evenodd"
        />
        <path
          fill="#F5D296"
          fillRule="evenodd"
          d="M5.664 8.836v1.002a4.16 4.16 0 0 0 2.01.514 4.16 4.16 0 0 0 2.009-.514V8.836a4.16 4.16 0 0 1-2.71.455 4.14 4.14 0 0 1-1.309-.455Z"
          clipRule="evenodd"
        />
        <path
          fill="#4AC1F0"
          fillRule="evenodd"
          d="M7.495 17.585h.179c1.224 0 2.57-.113 3.788-.34a.346.346 0 0 1 .065-.4l.282-.282c-.178-.3-.317-.63-.405-.978h-.4a.348.348 0 0 1-.346-.345v-1.157c0-.19.157-.346.347-.346h.4a3.69 3.69 0 0 1 .404-.976l-.282-.283a.346.346 0 0 1 0-.49l.744-.743a10.124 10.124 0 0 0-2.18-1.028c-.174-.06-.251-.006-.408.083.208 2.138-.62 5.762-2.188 7.285Z"
          clipRule="evenodd"
        />
        <path
          fill="#B3B3B3"
          fillRule="evenodd"
          d="M15.67 14.48c0 .719-.075 1.416-.214 2.08a1.949 1.949 0 0 0 .072-3.779c.092.549.141 1.117.141 1.7Z"
          clipRule="evenodd"
        />
        <path
          fill="#748FA6"
          fillRule="evenodd"
          d="M15.528 12.783a1.95 1.95 0 0 1-.072 3.779 9.223 9.223 0 0 1-.883 2.46h1.022c.19 0 .345-.156.345-.345v-.4c.349-.088.678-.226.978-.405l.283.282a.345.345 0 0 0 .489 0l.817-.817a.346.346 0 0 0 0-.489l-.282-.283c.179-.3.317-.63.405-.978h.4c.19 0 .345-.155.345-.345v-1.157a.347.347 0 0 0-.345-.346h-.4a3.695 3.695 0 0 0-.405-.976l.282-.283a.346.346 0 0 0 0-.49l-.817-.817a.346.346 0 0 0-.489 0l-.283.282c-.3-.178-.63-.317-.978-.405v-.4a.347.347 0 0 0-.345-.345h-.839c.358.756.623 1.59.772 2.478Z"
          clipRule="evenodd"
        />
        <path
          fill="#000"
          d="M19.03 13.426h-.164a3.998 3.998 0 0 0-.253-.61l.114-.115a.652.652 0 0 0 .194-.467.65.65 0 0 0-.193-.465l-.817-.817a.653.653 0 0 0-.466-.193h-.001a.649.649 0 0 0-.463.192l-.117.116a4.005 4.005 0 0 0-.61-.253v-.164a.659.659 0 0 0-.658-.659h-1.158a.66.66 0 0 0-.658.66v.163c-.21.068-.413.152-.61.253l-.115-.115a.653.653 0 0 0-.82-.09 10.631 10.631 0 0 0-2.043-.939.742.742 0 0 0-.197-.041V9.01a4.495 4.495 0 0 0 2.175-3.845A4.502 4.502 0 0 0 7.673.668a4.503 4.503 0 0 0-4.498 4.497c0 1.63.875 3.056 2.176 3.845v.872a.737.737 0 0 0-.195.04C2.168 10.944.313 12.848.313 14.892v.627c0 .571.256.939.856 1.23 1.744.849 4.594 1.15 6.504 1.15 1.22 0 2.509-.114 3.659-.316l.79.79c.**************.466.194a.652.652 0 0 0 .465-.192l.117-.116c.***********.61.253v.164c0 .363.295.658.658.658h1.157a.658.658 0 0 0 .658-.658v-.164c.21-.067.414-.152.611-.253l.115.115a.65.65 0 0 0 .465.193h.001a.655.655 0 0 0 .466-.192l.817-.818a.654.654 0 0 0 0-.93l-.115-.117c.101-.197.186-.401.253-.61h.164a.658.658 0 0 0 .657-.658v-1.157a.659.659 0 0 0-.657-.66ZM7.673 1.293a3.878 3.878 0 0 1 3.702 2.739c-.73-.475-1.892-1.068-2.708-.682-.295.14-.564.275-.815.402-1.402.71-2.14 1.078-3.889.307a3.878 3.878 0 0 1 3.71-2.766ZM3.894 4.327a.119.119 0 0 1-.007.068c-.002.005-.008.007-.011.011l.018-.079Zm-.094.838c0-.163.014-.323.034-.481.72.303 1.307.428 1.83.428.884 0 1.588-.356 2.47-.802a33.6 33.6 0 0 1 .8-.396c.607-.286 1.867.427 2.598.994.006.085.013.17.013.257a3.877 3.877 0 0 1-3.872 3.873A3.877 3.877 0 0 1 3.8 5.165Zm5.57 4.163v.826L7.698 11.53l-.025.015-1.697-1.39v-.826a4.47 4.47 0 0 0 3.394 0Zm1.937 7.298a.648.648 0 0 0-.183.352c0 .004-.003.007-.004.011a21.18 21.18 0 0 1-3.447.285c-2.428 0-4.873-.427-6.231-1.087-.427-.207-.505-.387-.505-.668v-.628c0-1.768 1.694-3.445 4.444-4.387.015.005.062.031.1.053l1.79 1.47a.286.286 0 0 0 .043.03l.079.047c.082.05.17.098.271.097a.558.558 0 0 0 .29-.097l.079-.047a.286.286 0 0 0 .043-.03l1.785-1.467.005-.003.091-.052.032.009c.65.222 1.246.488 1.778.794l-.46.46a.651.651 0 0 0-.194.466v.001a.65.65 0 0 0 .192.464l.116.117c-.1.197-.185.4-.253.61h-.164a.66.66 0 0 0-.659.659v1.157c0 .362.296.657.66.657h.163c.067.21.152.414.253.611l-.114.116Zm7.755-1.384a.034.034 0 0 1-.032.032h-.4a.313.313 0 0 0-.303.236 3.362 3.362 0 0 1-.37.895.312.312 0 0 0 .047.38l.282.283v.047l-.817.818.221.22-.268-.22-.283-.283a.312.312 0 0 0-.38-.047c-.281.167-.582.292-.895.37a.313.313 0 0 0-.236.304v.4a.034.034 0 0 1-.032.032h-1.157c-.018 0-.034-.016-.034-.033v-.4a.312.312 0 0 0-.236-.302 3.388 3.388 0 0 1-.893-.37.311.311 0 0 0-.38.046l-.283.282h-.048l-.817-.817v-.047l.282-.283c.1-.101.12-.258.047-.38a3.373 3.373 0 0 1-.37-.895.312.312 0 0 0-.303-.236h-.4c-.017 0-.033-.016-.033-.032v-1.157c0-.018.016-.034.033-.034h.4a.313.313 0 0 0 .303-.236c.079-.313.204-.613.37-.893a.313.313 0 0 0-.047-.38l-.282-.284v-.047l.817-.817h.002c.01 0 .044.002.045 0l.284.282c.1.1.257.12.38.047.28-.166.58-.29.893-.37a.312.312 0 0 0 .236-.303v-.4c0-.017.016-.034.034-.034h1.157c.016 0 .032.017.032.034v.4c0 .143.097.268.236.303.313.079.614.204.895.37.122.073.279.054.38-.047l.283-.282h.047l.817.817v.047l-.282.284a.312.312 0 0 0-.047.38c.167.28.291.58.37.893.035.139.16.236.303.236h.4c.017 0 .032.016.032.034v1.157Z"
        />
        <path
          fill="#000"
          d="M15.017 12.402a2.263 2.263 0 0 0-2.26 2.26 2.263 2.263 0 0 0 2.26 2.262 2.264 2.264 0 0 0 2.26-2.261 2.263 2.263 0 0 0-2.26-2.26Zm0 3.897a1.637 1.637 0 0 1-1.635-1.636c0-.902.733-1.636 1.635-1.636s1.636.734 1.636 1.636c0 .902-.734 1.636-1.636 1.636Zm-7.308-3.022H7.7a.31.31 0 0 0-.308.313c0 .173.143.312.316.312a.312.312 0 1 0 0-.625ZM7.7 15.234h-.008c-.172 0-.308.14-.308.313s.144.312.316.312a.313.313 0 0 0 0-.625Z"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M0 0h20v20H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}
