export default function SalesIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="512"
      height="512"
      viewBox="0 0 256 256"
      {...props}
    >
      <path fill="#cecece" d="M62.6 32.23h89.63v155.95H62.6z" />
      <path fill="#afafaf" d="M62.6 32.23h14.68v155.95H62.6z" />
      <circle cx="107.42" cy="110.21" r="31.46" fill="#f8af23" />
      <path
        fill="#f8af23"
        d="M152.23 49.27v51.35l-10.11-11.86a11.78 11.78 0 0 0-16.56-1.32 11.78 11.78 0 0 0-1.32 16.56l23 26.98s-6.87 28.96 11.22 45.51h51.14l-4.53-62.22c-.54-5.74-4.09-10.6-7.7-14.62z"
      />
      <path
        fill="#f8d323"
        d="M205.07 114.28c-.55-5.75-4.09-10.61-7.7-14.63l-45.14-50.38v16.37l30.48 34c3.6 4.03 7.15 8.89 7.7 14.63l4.53 62.22h14.66z"
      />
      <path fill="#63bce7" d="M140.54 176.5h79.5v36.9h-79.5z" />
      <path fill="#63e2e7" d="M205.37 176.5h14.67v36.9h-14.67z" />
      <path fill="#cecece" d="M140.54 213.4h79.5v14.93h-79.5z" />
      <path fill="#e2e2e2" d="M205.37 213.4h14.67v14.93h-14.67z" />
      <circle cx="159.38" cy="194.95" r="6.76" fill="#63bce7" />
      <path
        fill="#f46275"
        d="m63.06 83.57 26-26c.41-.4.12-1.1-.45-1.1H78.4a.64.64 0 0 1-.64-.64v-38.5a.64.64 0 0 0-.64-.64h-29a.64.64 0 0 0-.64.65v38.5c0 .35-.29.63-.64.63H36.6c-.58 0-.86.7-.46 1.1l26 26c.26.25.66.25.91 0z"
      />
      <path
        fill="#f43075"
        d="M51.27 56.47H61.5c.36 0 .64-.28.64-.64v-38.5c0-.35.3-.64.65-.64H48.1a.64.64 0 0 0-.64.65v38.5c0 .35-.29.63-.64.63H36.6c-.58 0-.86.7-.46 1.1l26 26c.26.25.66.25.91 0l6.88-6.88-19.12-19.12a.64.64 0 0 1 .45-1.1z"
      />
      <g fill="#3f3679">
        <path d="M27.32 237.8h-18a1.5 1.5 0 1 0 0 3h18a1.5 1.5 0 1 0 0-3zM246.68 237.8h-18a1.5 1.5 0 0 0 0 3h18a1.5 1.5 0 0 0 0-3zM218.48 237.8H37.52a1.5 1.5 0 1 0 0 3h180.96a1.5 1.5 0 0 0 0-3zM248.18 228.33c0-.83-.67-1.5-1.5-1.5h-25.14V176.5c0-.84-.67-1.5-1.5-1.5H211l-4.44-60.87c-.6-6.42-4.63-11.65-8.06-15.48L153.73 48.7V32.23c0-.82-.67-1.5-1.5-1.5H79.25v-13.4c0-1.18-.96-2.14-2.14-2.14h-29c-1.18 0-2.14.96-2.14 2.15v37.63H36.6a2.14 2.14 0 0 0-1.52 3.66l26 26 .02.02v103.53c0 .83.67 1.5 1.5 1.5h76.43v37.15H9.32a1.5 1.5 0 1 0 0 3h237.36c.83 0 1.5-.67 1.5-1.5zm-29.64-16.43h-76.5V178h76.5v33.9zM154.89 175h-1.16v-1.39c.37.47.76.93 1.16 1.38zm41.37-74.35c3.13 3.49 6.79 8.21 7.31 13.73L208 175h-48.94c-16.68-15.82-10.42-43.39-10.35-43.67.1-.46-.01-.95-.32-1.31l-23-26.98a10.17 10.17 0 0 1-2.4-7.46 10.17 10.17 0 0 1 3.55-6.98 10.26 10.26 0 0 1 14.45 1.15l10.1 11.85 19.2 22.52a1.5 1.5 0 0 0 2.12.17c.63-.54.7-1.48.16-2.11l-18.83-22.1V53.19zm-73.16 4.33 12.68 14.88a29.91 29.91 0 0 1-28.36 20.3c-16.52 0-29.96-13.43-29.96-29.95s13.44-29.96 29.96-29.96c6.4 0 12.45 2 17.58 5.73-.14.11-.28.2-.41.32a13.15 13.15 0 0 0-4.6 9.03c-.28 3.54.82 6.96 3.1 9.65zm-84.42-47h8.15c1.18 0 2.14-.97 2.14-2.15V18.2h27.28v37.64c0 1.18.96 2.14 2.14 2.14h8.15L62.61 81.91zm25.43 128.7V84.66l.01-.02 26-26a2.14 2.14 0 0 0-1.5-3.66h-9.37V33.73h71.48v62.82l-7.47-8.76a13.27 13.27 0 0 0-15.48-3.5 32.6 32.6 0 0 0-20.36-7.04 33 33 0 0 0-32.96 32.96 33 33 0 0 0 32.96 32.96 32.9 32.9 0 0 0 30.58-20.7l7.61 8.92a66.3 66.3 0 0 0-1.05 16.23c.5 8.3 2.61 15.64 6.2 21.7-.02.1-.03.2-.03.31V175h-10.2c-.82 0-1.5.67-1.5 1.5v10.2H64.11zm77.93 40.15V214.9h76.5v11.93z" />
        <path d="M110.39 95.37a5.93 5.93 0 0 1 5.93 5.93 1.5 1.5 0 1 0 3 0c0-4.93-4-8.93-8.93-8.93h-1.47V90.9a1.5 1.5 0 1 0-3 0v1.47h-1.47a8.94 8.94 0 0 0 0 17.85h1.47v14.83h-1.47a5.93 5.93 0 0 1-5.93-5.93 1.5 1.5 0 1 0-3 0c0 4.93 4 8.93 8.93 8.93h1.47v1.47a1.5 1.5 0 1 0 3 0v-1.47h1.47c4.92 0 8.93-4 8.93-8.93v-2.97c0-4.92-4-8.93-8.93-8.93h-1.47V95.37zm0 14.85a5.93 5.93 0 0 1 5.93 5.93v2.98a5.93 5.93 0 0 1-5.93 5.92h-1.47v-14.82h1.47zm-4.47-3h-1.47a5.93 5.93 0 0 1 0-11.85h1.47zM159.38 186.69a8.27 8.27 0 0 0 0 16.52 8.27 8.27 0 0 0 0-16.53zm0 13.52a5.27 5.27 0 1 1 0-10.54 5.27 5.27 0 0 1 0 10.54z" />
      </g>
    </svg>
  );
}
