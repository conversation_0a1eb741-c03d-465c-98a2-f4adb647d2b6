export default function DiningIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      {...props}
    >
      <g>
        <path
          fill="currentColor"
          d="M23.25 22.88h-.49l-1.01-2.7V18.8l1.12-4.83a.38.38 0 0 0-.37-.46h-4.12a.37.37 0 0 0-.37.3l-1.06 4.57h-1.2c-.62 0-1.12.51-1.12 1.13v.7l-.67 2.68h-.46v-.75c0-.3-.15-.56-.37-.68v-3.82h2.62a1.13 1.13 0 0 0 0-2.25h-3.37V7.13A33.8 33.8 0 0 1 18 7.5c.04 0 .08 0 .12-.02h.02c1.45-.59 5.01 0 5.05.02a.38.38 0 0 0 .35-.6c-2.75-3.45-9.6-5.02-11.16-5.33V.75a.37.37 0 1 0-.75 0v.82C10.07 1.88 3.2 3.45.46 6.89a.38.38 0 0 0 .35.6c.04 0 3.6-.6 5.05-.02h.01L6 7.5h.08c.02-.01 1.8-.35 5.56-.37v8.25H8.25a1.13 1.13 0 0 0 0 2.24h2.63v3.83a.75.75 0 0 0-.38.68v.75h-.46l-.66-2.68v-.7c0-.62-.5-1.13-1.13-1.13h-1.2L5.99 13.8a.37.37 0 0 0-.36-.29H1.5a.37.37 0 0 0-.37.46l1.12 4.83v1.4l-1 2.68h-.5a.37.37 0 1 0 0 .75h22.5a.37.37 0 1 0 0-.75Zm-5.1-16.2c-1.1-1.75-2.5-3-3.67-3.82 2.34.66 5.74 1.87 7.77 3.75-1.14-.13-2.94-.26-4.1.07ZM1.76 6.6c2.03-1.88 5.43-3.1 7.77-3.75a13.71 13.71 0 0 0-3.68 3.82c-1.15-.33-2.95-.2-4.1-.07ZM12 2.28c.64.3 3.32 1.63 5.23 4.35A44.5 44.5 0 0 0 12 6.38c-2.6 0-4.3.14-5.23.25A13.67 13.67 0 0 1 12 2.28Zm8.51 20.6-.84-2.25h1.44l.85 2.25H20.5Zm-1.65-2.25.85 2.25h-2.73l.56-2.25h1.32Zm-.19-6.38h3.36l-.95 4.13h-3.36l.95-4.13Zm-3.3 5.25c0-.2.17-.38.38-.38H21v.75h-5.63v-.37Zm-.08 1.13h1.48l-.56 2.25h-1.48l.56-2.25ZM7.87 16.5c0-.2.17-.38.38-.38h7.5a.38.38 0 0 1 0 .75h-7.5a.38.38 0 0 1-.38-.37Zm4.5 1.13v3.75h-.75v-3.75h.75Zm-1.12 4.5h1.5v.75h-1.5v-.75Zm-3.46.75-.56-2.25h1.48l.56 2.25H7.79Zm-1.33-2.25.56 2.25H4.29l.84-2.25h1.33Zm2.16-1.13v.38H3v-.75h5.25c.2 0 .38.16.38.37Zm-3.3-5.25.96 4.13H2.92l-.95-4.13h3.36Zm-2.44 6.38h1.45l-.84 2.25H2.04l.84-2.25Z"
        />
      </g>
    </svg>
  );
}
