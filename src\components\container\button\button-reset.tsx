import React from "react";
import { PiArrowClockwise } from "react-icons/pi";
import { Button } from "rizzui";

export default function ButtonReset({
  isLoading,
  handleReset,
}: {
  isLoading: boolean;
  handleReset: () => void;
}) {
  return (
    <Button
      type="button"
      size="sm"
      variant="outline"
      className="bg-white"
      onClick={handleReset}
      isLoading={isLoading}
      title="Reset"
    >
      <PiArrowClockwise className="size-4" />
    </Button>
  );
}
