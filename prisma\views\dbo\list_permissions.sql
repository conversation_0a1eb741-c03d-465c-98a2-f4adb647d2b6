SELECT
  DISTINCT a.menu_name,
  a.parent_id,
  a.icon,
  a.route,
  a.has_child,
  a.has_route,
  a.is_crud,
  a.order_line,
  a.menu_name AS parena_name,
  idx.valuex AS 'index',
  store.valuex AS store,
  edits.valuex AS edits,
  erase.valuex AS erase
FROM
  permissions AS a
  LEFT JOIN permissions AS b ON a.parent_id = b.id
  LEFT JOIN (
    SELECT
      menu_name,
      'Y' AS valuex
    FROM
      permissions AS a1
    WHERE
      RIGHT(a1.name, 5) = 'index'
  ) AS idx ON a.menu_name = idx.menu_name
  LEFT JOIN (
    SELECT
      menu_name,
      'Y' AS valuex
    FROM
      permissions AS a1
    WHERE
      RIGHT(a1.name, 5) = 'store'
  ) AS store ON a.menu_name = store.menu_name
  LEFT JOIN (
    SELECT
      menu_name,
      'Y' AS valuex
    FROM
      permissions AS a1
    WHERE
      RIGHT(a1.name, 5) = 'edits'
  ) AS edits ON a.menu_name = edits.menu_name
  LEFT JOIN (
    SELECT
      menu_name,
      'Y' AS valuex
    FROM
      permissions AS a1
    WHERE
      RIGHT(a1.name, 5) = 'erase'
  ) AS erase ON a.menu_name = erase.menu_name;