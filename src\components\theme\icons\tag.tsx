export default function TagIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="45"
      height="44"
      viewBox="0 0 45 44"
      fill="none"
      {...props}
    >
      <path
        d="M10.6662 38.9054C19.1203 38.9054 24.9258 43.2846 28.9995 43.2846C33.0732 43.2846 41.6287 40.9428 41.6287 22.2025C41.6287 3.46216 30.1191 0.710938 25.6384 0.710938C4.65651 0.710938 -4.30604 38.9054 10.6662 38.9054Z"
        fill="#E2F5FF"
      />
      <path
        d="M12.0443 8.55729C11.0335 8.55729 10.2109 7.73474 10.2109 6.72396C10.2109 5.71318 11.0335 4.89062 12.0443 4.89062C13.055 4.89062 13.8776 5.71318 13.8776 6.72396C13.8776 7.73474 13.055 8.55729 12.0443 8.55729ZM12.0443 6.11285C11.7069 6.11285 11.4332 6.38663 11.4332 6.72396C11.4332 7.06129 11.7069 7.33507 12.0443 7.33507C12.3816 7.33507 12.6554 7.06129 12.6554 6.72396C12.6554 6.38663 12.3816 6.11285 12.0443 6.11285Z"
        fill="#0086CD"
      />
      <path
        d="M31.3654 25.5785C32.0718 24.91 32.513 23.964 32.513 22.9141V14.3585C32.513 13.008 31.4191 11.9141 30.0686 11.9141H21.513C20.4644 11.9141 19.5171 12.3553 18.8486 13.0605L9.34824 23.0668C8.93391 23.5056 8.67969 24.0972 8.67969 24.7474C8.67969 25.4221 8.95347 26.0332 9.39591 26.4756L17.9515 35.0312C18.3939 35.4736 19.005 35.7474 19.6797 35.7474C20.3299 35.7474 20.9215 35.4932 21.359 35.0788L31.3654 25.5785Z"
        fill="white"
      />
      <path
        d="M26.7092 19.8637C27.8905 19.8637 28.8481 18.9061 28.8481 17.7248C28.8481 16.5436 27.8905 15.5859 26.7092 15.5859C25.5279 15.5859 24.5703 16.5436 24.5703 17.7248C24.5703 18.9061 25.5279 19.8637 26.7092 19.8637Z"
        fill="#5EC7FF"
      />
      <path
        d="M20.7015 35.0299L12.1459 26.4744C11.7035 26.0319 11.4297 25.4208 11.4297 24.7462C11.4297 24.096 11.6839 23.5044 12.0982 23.0668L21.5986 13.0605C22.2671 12.3541 23.2131 11.9141 24.263 11.9141H21.513C20.4644 11.9141 19.5171 12.3553 18.8486 13.0605L9.34824 23.0668C8.93391 23.5044 8.67969 24.096 8.67969 24.7462C8.67969 25.4208 8.95347 26.0319 9.39591 26.4744L17.9515 35.0299C18.3939 35.4724 19.005 35.7462 19.6797 35.7462C20.1894 35.7462 20.6599 35.5861 21.051 35.3196C20.9264 35.2328 20.8078 35.1375 20.7015 35.0299Z"
        fill="#D5DBE1"
      />
      <path
        d="M27.3203 17.7248C27.3203 17.0697 27.621 16.4916 28.0842 16.0993C27.7114 15.7839 27.236 15.5859 26.7092 15.5859C25.5285 15.5859 24.5703 16.5442 24.5703 17.7248C24.5703 18.9055 25.5285 19.8637 26.7092 19.8637C27.236 19.8637 27.7114 19.6657 28.0842 19.3504C27.621 18.958 27.3203 18.3799 27.3203 17.7248Z"
        fill="#0086CD"
      />
      <path
        d="M19.6823 36.6667C18.784 36.6667 17.9394 36.3171 17.3051 35.6816L8.74951 27.126C8.11518 26.4929 7.76562 25.6483 7.76562 24.75C7.76562 23.8871 8.09196 23.067 8.68474 22.44L18.1863 12.4324C19.0467 11.5231 20.2616 11 21.5156 11H30.0712C31.9253 11 33.4323 12.507 33.4323 14.3611V22.9167C33.4323 24.1707 32.9104 25.3843 31.9998 26.246L21.9935 35.7463C21.3653 36.3403 20.5452 36.6667 19.6823 36.6667ZM21.5156 12.8333C20.7517 12.8333 20.0416 13.1389 19.5173 13.6938L10.0157 23.7013C9.74807 23.9849 9.59896 24.3577 9.59896 24.75C9.59896 25.1582 9.75785 25.542 10.0463 25.8292L18.6018 34.3848C19.1665 34.9519 20.1504 34.9641 20.7322 34.4141L30.7373 24.915C30.7373 24.915 30.7385 24.915 30.7385 24.9138C31.2934 24.3894 31.599 23.6793 31.599 22.9154V14.3599C31.599 13.5178 30.9133 12.8321 30.0712 12.8321H21.5156V12.8333Z"
        fill="black"
      />
      <path
        d="M26.704 20.7752C25.0198 20.7752 23.6484 19.4038 23.6484 17.7196C23.6484 16.0354 25.0198 14.6641 26.704 14.6641C28.3882 14.6641 29.7595 16.0354 29.7595 17.7196C29.7595 19.4038 28.3882 20.7752 26.704 20.7752ZM26.704 16.4974C26.0305 16.4974 25.4818 17.0462 25.4818 17.7196C25.4818 18.3931 26.0305 18.9418 26.704 18.9418C27.3774 18.9418 27.9262 18.3931 27.9262 17.7196C27.9262 17.0462 27.3774 16.4974 26.704 16.4974Z"
        fill="black"
      />
      <path
        d="M27.9271 17.4193H26.0938V15.1276C26.0938 10.8315 29.5893 7.33594 33.8854 7.33594C35.654 7.33594 37.0938 8.77572 37.0938 10.5443C37.0938 11.0784 37.0412 11.599 36.9373 12.0965L35.1431 11.72C35.2213 11.3473 35.2604 10.9513 35.2604 10.5443C35.2604 9.78649 34.6432 9.16927 33.8854 9.16927C30.6001 9.16927 27.9271 11.8423 27.9271 15.1276V17.4193Z"
        fill="black"
      />
    </svg>
  );
}
