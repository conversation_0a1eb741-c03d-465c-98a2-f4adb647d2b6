export default function ScheduleIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g>
        <g>
          <g>
            <g>
              <path
                d="m54.2 12.2c0-2.2-1.8-3.9-4-3.9h-44.5c-2.2 0-4 1.8-4 3.9v40.6c0 2.2 1.8 3.9 4 3.9h48.6z"
                fill="#fff"
              />
              <g>
                <path d="m18.6 22.9h6.8v6.8h-6.8z" fill="#f3877e" />
                <path d="m30.6 22.9h6.8v6.8h-6.8z" fill="#34cba0" />
                <path d="m42.7 22.9h6.8v6.8h-6.8z" fill="#ffc408" />
                <path d="m6.5 34h6.8v6.8h-6.8z" fill="#d7e8ff" />
                <path d="m18.6 34h6.8v6.8h-6.8z" fill="#ffc408" />
                <path d="m30.6 40.8v-6.8h6.8" fill="#2aabf5" />
                <path d="m6.5 45.2h6.8v6.8h-6.8z" fill="#f3877e" />
                <path d="m18.6 45.2h6.8v6.8h-6.8z" fill="#d7e8ff" />
              </g>
              <ellipse cx="49" cy="49" fill="#d7e8ff" rx="15.2" ry="15.2" />
              <path
                d="m54.2 18.4v-6.3c0-2.2-1.8-3.9-4-3.9h-44.5c-2.2 0-4 1.8-4 3.9v6.3z"
                fill="#2aabf5"
              />
            </g>
            <g
              fill="none"
              stroke="#151f81"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.569"
            >
              <path
                d="m31.5 56.7h-25.8c-2.2 0-4-1.8-4-3.9v-40.6c0-2.2 1.8-3.9 4-3.9h44.6c2.2 0 4 1.8 4 3.9v18.4"
                strokeMiterlimit="10"
              />
              <path
                d="m54.2 18.4v-6.3c0-2.2-1.8-3.9-4-3.9h-44.5c-2.2 0-4 1.8-4 3.9v6.3z"
                strokeMiterlimit="10"
              />
              <path
                d="m11 13.3c-1.6 0-3-1.3-3-3v-5.6c0-1.6 1.3-3 3-3 1.6 0 3 1.3 3 3v3.5"
                strokeMiterlimit="10"
              />
              <path
                d="m22.3 13.3c-1.6 0-3-1.3-3-3v-5.6c0-1.6 1.3-3 3-3 1.6 0 3 1.3 3 3v3.5"
                strokeMiterlimit="10"
              />
              <path
                d="m33.6 13.3c-1.6 0-3-1.3-3-3v-5.6c0-1.6 1.3-3 3-3 1.6 0 3 1.3 3 3v3.5"
                strokeMiterlimit="10"
              />
              <path
                d="m45 13.3c-1.6 0-3-1.3-3-3v-5.6c0-1.6 1.3-3 3-3 1.6 0 3 1.3 3 3v3.5"
                strokeMiterlimit="10"
              />
              <g strokeMiterlimit="10">
                <path d="m18.6 22.9h6.8v6.8h-6.8z" />
                <path d="m30.6 22.9h6.8v6.8h-6.8z" />
                <path d="m42.7 22.9h6.8v6.8h-6.8z" />
                <path d="m6.5 34h6.8v6.8h-6.8z" />
                <path d="m18.6 34h6.8v6.8h-6.8z" />
                <path d="m30.6 40.8v-6.8h6.8" />
                <path d="m6.5 45.2h6.8v6.8h-6.8z" />
                <path d="m18.6 45.2h6.8v6.8h-6.8z" />
              </g>
              <ellipse
                cx="49"
                cy="49"
                rx="15.2"
                ry="15.2"
                strokeMiterlimit="10"
              />
              <ellipse
                cx="49"
                cy="49"
                rx="11.3"
                ry="11.3"
                strokeDasharray="0 2.733"
              />
              <path d="m49 40.8v8.2h4.3" strokeMiterlimit="10" />
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}
