export default function RevenueUpIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="512"
      height="512"
      viewBox="0 0 512 512"
      {...props}
    >
      <g fillRule="evenodd" clipRule="evenodd">
        <circle
          cx="113.25"
          cy="132.09"
          r="105.24"
          fill="#ffd064"
          transform="rotate(-45.01 113.23 132.12)"
        />
        <path
          fill="#ffc338"
          d="M113.25 26.86c58.12 0 105.23 47.11 105.23 105.23s-47.11 105.24-105.23 105.24z"
        />
        <circle cx="113.25" cy="132.09" r="89.24" fill="#ffe177" />
        <path fill="#ffd064" d="M113.25 42.86a89.24 89.24 0 1 1 0 178.47z" />
        <path
          fill="#fe646f"
          d="M85.14 359.93h45.27c5.5 0 10 4.49 10 10v105.22c0 5.5-4.5 10-10 10H85.14c-5.5 0-10-4.5-10-10V369.93c0-5.51 4.5-10 10-10z"
        />
        <path
          fill="#fd4755"
          d="M107.78 359.93h22.63c5.5 0 10 4.49 10 10v105.22c0 5.5-4.5 10-10 10h-22.63z"
        />
        <path
          fill="#ffe177"
          d="M206.34 249.8h45.27c5.51 0 10 4.5 10 10v215.35c0 5.5-4.49 10-10 10h-45.27c-5.5 0-10-4.5-10-10V259.8c0-5.5 4.5-10 10-10z"
        />
        <path
          fill="#ffd064"
          d="M228.97 249.8h22.64c5.5 0 10 4.5 10 10v215.35c0 5.5-4.5 10-10 10h-22.64z"
        />
        <path
          fill="#6cf5c2"
          d="M327.54 294.32h45.27c5.51 0 10 4.49 10 10v170.83c0 5.5-4.49 10-10 10h-45.27c-5.5 0-10-4.5-10-10V304.32c0-5.51 4.5-10 10-10z"
        />
        <path
          fill="#00e499"
          d="M350.18 294.32h22.63c5.51 0 10 4.49 10 10v170.83c0 5.5-4.49 10-10 10h-22.63z"
        />
        <path
          fill="#8ac9fe"
          d="M448.74 164.18H494c5.5 0 10 4.5 10 10v300.97c0 5.5-4.5 10-10 10h-45.27c-5.51 0-10-4.5-10-10V174.18c0-5.5 4.49-10 10-10z"
        />
        <path
          fill="#60b7ff"
          d="M471.37 164.18h22.64c5.5 0 10 4.5 10 10v300.97c0 5.5-4.5 10-10 10h-22.64z"
        />
        <path d="M154.5 155.82c1.21 9.82-2.23 20.01-11.26 26.87-6.22 4.72-14.21 7.5-22 8.58v7.42a8 8 0 0 1-16 0v-7.57a45.57 45.57 0 0 1-17.95-6.86 36.58 36.58 0 0 1-15.56-21.95 7.98 7.98 0 1 1 15.5-3.82 20.78 20.78 0 0 0 8.88 12.46c10.47 6.97 27.38 6.73 37.5-.95 8.94-6.78 5.66-19.28-3-24.51-5.26-3.17-12.13-4.84-18.55-5.9-7.81-1.28-16.26-3.6-23.12-8.08-17.69-11.55-18.22-36.96-2.06-50.09a40.9 40.9 0 0 1 17.7-8.12l.66-.13V65.5a8 8 0 1 1 16 0v7.55c1.07.18 2.14.41 3.2.68 11.37 2.88 21.72 10.36 27.15 23.3a8 8 0 1 1-14.75 6.19c-3.88-9.27-13.44-14.77-23.42-14.78-10.63 0-23.05 6.39-22.47 18.53.58 11.74 14.24 15.32 23.67 16.87 8.09 1.33 16.9 3.54 24.24 7.96a32.9 32.9 0 0 1 15.64 24.02zm-22.1 319.33c0 1.08-.92 2-2 2H85.13c-1.08 0-2-.92-2-2V369.92c0-1.08.92-2 2-2h45.27c1.08 0 2 .92 2 2zm-2-123.23H85.13c-9.93 0-18 8.08-18 18v105.23c0 9.92 8.08 18 18 18h45.27c9.93 0 18-8.08 18-18V369.92c0-9.92-8.07-18-18-18zM496 475.15c0 1.08-.92 2-2 2h-45.27c-1.09 0-2-.92-2-2V174.18c0-1.09.91-2 2-2H494c1.08 0 2 .91 2 2zm-2-318.97h-45.27c-9.93 0-18 8.07-18 18v300.97c0 9.92 8.07 18 18 18H494c9.93 0 18-8.08 18-18V174.18c0-9.93-8.07-18-18-18zM374.8 475.15c0 1.08-.91 2-2 2h-45.27c-1.1 0-2-.92-2-2V304.33c0-1.1.9-2.01 2-2.01h45.27c1.09 0 2 .91 2 2zm-2-188.83h-45.27c-9.93 0-18 8.07-18 18v170.83c0 9.92 8.07 18 18 18h45.27c9.93 0 18-8.08 18-18V304.32c0-9.93-8.07-18-18-18zM253.6 475.15c0 1.08-.91 2-2 2h-45.27c-1.09 0-2-.92-2-2V259.81c0-1.09.91-2.01 2-2.01h45.27c1.09 0 2 .92 2 2zm-2-233.35h-45.27c-9.93 0-18 8.07-18 18v215.35c0 9.92 8.07 18 18 18h45.27c9.93 0 18-8.08 18-18V259.81c0-9.94-8.07-18-18-18zm-138.37-12.47c53.62 0 97.25-43.62 97.25-97.24s-43.63-97.24-97.25-97.24C59.62 34.85 16 78.47 16 132.1s43.62 97.24 97.23 97.24zM493.16 73.66a7.99 7.99 0 1 0 15.94 1.25l2.87-35.14a8 8 0 0 0-8.88-8.57l-35.01 4.14a8 8 0 0 0 1.81 15.87l12.9-1.52-91.34 94.98-95.6-47.16a8.01 8.01 0 0 0-9.27.89l-61.92 53.93a113.51 113.51 0 0 0 1.82-20.24c0-62.44-50.8-113.23-113.25-113.23C50.8 18.86 0 69.66 0 132.09s50.8 113.24 113.23 113.24c44.5 0 83.07-25.79 101.57-63.2l78.28-68.18 96.54 47.62a7.98 7.98 0 0 0 9.44-1.8l95.15-98.93z" />
      </g>
    </svg>
  );
}
