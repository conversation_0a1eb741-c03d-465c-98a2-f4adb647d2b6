import ButtonReset from "@/components/container/button/button-reset";
import React, { useEffect } from "react";
import { Button, Input, Select } from "rizzui";
import type { SelectOption } from "rizzui";
import type { FormProps } from "@/interfaces/form/formType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { UploadFile } from "@/components/theme/ui/file-upload/upload-file";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  setValue,
  getValues,
  onDelete,
  handleReset,
  isDocType,
  setDocType,
  docTypeOptions,
  isDefault,
  setIsDefault,
  isDefaultOpt,
}: FormProps & {
  handleReset: () => void;
  isDocType?: SelectOption;
  setDocType?: (option: SelectOption) => void;
  docTypeOptions?: SelectOption[];
  isDefault?: SelectOption;
  setIsDefault?: (option: SelectOption) => void;
  isDefaultOpt?: SelectOption[];
}) {
  const { can } = useGrantedPolicies();

  useEffect(() => {
    if (isDocType && typeof isDocType.value === "string") {
      setValue("documentType", isDocType.value);
    }
    if (isDefault && typeof isDefault.value === "string") {
      setValue("isDefault", isDefault.value);
    }
  }, [isDocType, isDefault, setValue]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-6 gap-2">
        <div>
          <Input
            size="sm"
            label="Name"
            disabled={isLoading}
            placeholder="Please fill in Name"
            {...register("name", { required: true })}
            error={errors.name ? "Name is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Document Type"
            disabled={isLoading}
            placeholder="Please select Document Type"
            {...register("documentType", { required: true })}
            error={
              errors.documentType ? "Document Type is required" : undefined
            }
            className="w-full"
            options={docTypeOptions ?? []}
            value={isDocType}
            onChange={(e: { label: string; value: string }) => {
              if (setDocType) {
                setDocType(e);
              }
              setValue("documentType", e?.value ?? "");
            }}
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Description"
            disabled={isLoading}
            placeholder="Please fill in Description"
            {...register("description", { required: true })}
            error={errors.description ? "Description is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Is Default"
            disabled={isLoading}
            placeholder="Please select Is Default"
            {...register("isDefault", { required: true })}
            error={errors.isDefault ? "Is Default is required" : undefined}
            className="w-full"
            options={isDefaultOpt ?? []}
            value={isDefault}
            onChange={(e: SelectOption) => {
              setIsDefault?.(e);
              setValue("isDefault", e.value);
            }}
          />
        </div>
        <div>
          <UploadFile
            label="Upload Doc.Template"
            name="docTemplate"
            setValue={setValue}
            getValues={getValues}
            register={register}
            errors={errors}
            required={!getValues("id")}
            accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            size="sm"
            type="file"
          />
        </div>
        <div className="flex items-end justify-start gap-2">
          {can("WismaApp.RoomType.Create") && can("WismaApp.RoomType.Edit") && (
            <Button
              size="sm"
              type="submit"
              disabled={isLoading}
              className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${
                getValues("id") ? "bg-blue-500" : "bg-green-500"
              }`}
            >
              {getValues("id") ? "Update" : "Create"}
            </Button>
          )}
          {getValues("id") && can("WismaApp.RoomType.Delete") && (
            <Button
              size="sm"
              disabled={isLoading}
              className={`rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400`}
              onClick={() => {
                onDelete(String(getValues("id")));
              }}
            >
              Delete
            </Button>
          )}
          <ButtonReset isLoading={isLoading} handleReset={handleReset} />
        </div>
      </div>
    </form>
  );
}
