import React from "react";
import SectionHeader from "@/components/container/header/sectionHeader";
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";
import { PiUserBold } from "react-icons/pi";
import { Input } from "rizzui";
import type { GuestInformationProps } from "@/interfaces/form/guestInformationType";
import type { ReservationDetailsDto } from "@/client";

export default function GuestInformation({
  label,
  name,
  register,
  errors,
  setValue,
  getValues,
  watch,
  options,
  data,
}: GuestInformationProps & { data?: ReservationDetailsDto }) {
  return (
    <div className="rounded-lg border border-gray-300 bg-white p-4">
      <div className="flex flex-col gap-2">
        <SectionHeader title="Guest Information" icon={<PiUserBold />} />
        <div>
          {options && options.length > 0 ? (
            <AutocompleteSelect
              label={label}
              name={name}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
              options={options}
              required={true}
            />
          ) : (
            <Input
              className="w-full"
              label="RFID / ROOM"
              size="sm"
              value="No Data"
              placeholder="No Data"
              disabled={true}
            />
          )}
        </div>
        <Input
          size="sm"
          label="NAME"
          disabled={true}
          value={data?.guest?.fullname ?? ""}
          className="w-full"
        />
        <Input
          size="sm"
          label="NATIONALITY"
          disabled={true}
          value={data?.guest?.nationality ?? ""}
          className="w-full"
        />
        <Input
          size="sm"
          label="COMPANY"
          disabled={true}
          value={data?.guest?.companyName ?? ""}
          className="w-full"
        />
        <Input
          size="sm"
          label="IDENTITY"
          disabled={true}
          value={data?.guest?.identityNumber ?? ""}
          className="w-full"
        />
        <Input
          size="sm"
          label="PHONE NUMBER"
          disabled={true}
          value={data?.guest?.phoneNumber ?? ""}
          className="w-full"
        />
        <Input
          size="sm"
          label="ROOM"
          disabled={true}
          value={data?.room?.roomNumber ?? ""}
          className="w-full"
        />
        <Input
          size="sm"
          label="ROOM TYPE"
          disabled={true}
          value={data?.room?.roomType?.name ?? ""}
          className="w-full"
        />
      </div>
    </div>
  );
}
