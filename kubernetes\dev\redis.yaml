apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: wismafrontend-dev
spec:
  selector:
    matchLabels:
      app: redis
  replicas: 1
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:7-alpine
          ports:
            - containerPort: 6379
          resources:
            limits:
              memory: "256Mi"
              cpu: "200m"
            requests:
              memory: "128Mi"
              cpu: "100m"
          args: ["--requirepass", "$(REDIS_PASSWORD)"]
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: wismafrontend-dev-secrets
                  key: redis-password
                  optional: true
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: wismafrontend-dev
spec:
  selector:
    app: redis
  ports:
    - port: 6379
      targetPort: 6379
  type: ClusterIP
