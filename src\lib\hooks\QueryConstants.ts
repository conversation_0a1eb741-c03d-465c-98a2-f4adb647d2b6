﻿import {
  getApiAppReservationFoodAndBeverages,
  getApiAppServices,
} from "@/client";

/**
 * QueryNames is an object that holds constant values representing the names of various queries
 * used throughout the application. These constants help in maintaining consistency and avoiding
 * hardcoding query names in multiple places.
 */
export const QueryNames = {
  /**
   * Query to get the list of users.
   */
  GetUsers: "GetUsers",
  /**
   * Query to get the application configuration.
   */
  GetAppConfig: "GetAppConfig",
  /**
   * Query to get the list of permissions.
   */
  GetPermissions: "GetPermissions",
  /**
   * Query to get the list of assignable roles.
   */
  GetAssignableRoles: "GetAssignableRoles",
  /**
   * Query to get the profile information.
   */
  GetProfile: "GetProfile",
  /**
   * Query to get the session information.
   */
  GetSession: "GetSession",
  GetGuests: "GetGuests",
  GetPayments: "GetPayments",
  GetroomType: "GetroomType",
  GetReservationWithDetails: "GetReservationWithDetails",
  GetRooms: "GetRooms",
  GetRoomStatus: "GetRoomStatus",
  GetRoomType: "GetRoomType",
  GetReservationsById: "GetReservationsById",
  GetReservationWithDetailsById: "GetReservationWithDetailsById",
  GetServicesType: "GetServicesType",
  GetServices: "GetServices",
  GetFnb: "GetFnb",
  GetFnbType: "GetFnbType",
  GetStatus: "GetStatus",
  getDocumentType: "getDocumentType",
  getDocumentTemplate: "getDocumentTemplate",
  GetCompany: "GetCompany",
  GetGuest: "GetGuest",
  GetDiningOption: "GetDiningOption",
  GetPaymentMethod: "GetPaymentMethod",
  DiningOptions: "DiningOptions",
  getApiMasterStatusServiceByDocTypeByDocType:
    "getApiMasterStatusServiceByDocTypeByDocType",
  getApiAppReservationTypes: "getApiAppReservationTypes",
  getApiMasterCompany: "getApiMasterCompany",
  getApiAppReservationFoodAndBeverages: "getApiAppReservationFoodAndBeverages",
  getApiAppServices: "getApiAppServices",

  putApiAppReservationDetailsById: "putApiAppReservationDetailsById",
  getApiAppReservations: "getApiAppReservations",
  getApiAppReservationDetails: "getApiAppReservationDetails",
  putApiAppReservationFoodAndBeveragesByIdData:
    "putApiAppReservationFoodAndBeveragesByIdData",
  getApiAppReservationRooms: "getApiAppReservationRooms",
  putApiAppReservationRoomsByIdData: "putApiAppReservationRoomsByIdData",
  postApiAppTaxList: "postApiAppTaxList",
  postApiAppReservationDetailsList: "postApiAppReservationDetailsList",
  postApiAppInvoiceDocumentGenerateWismaInvoice:
    "postApiAppInvoiceDocumentGenerateWismaInvoice",
  ListPayments: "ListPayments",
  getApiAppPaymentsById: "getApiAppPaymentsById",
  GetReservations: "GetReservations",
  getApiAppPaymentDetailsById: "getApiAppPaymentDetailsById",
  ListPaymentDetails: "ListPaymentDetails",
  postApiAppReport: "postApiAppReport",
  getApiReportById: "getApiReportById",
  postApiReportList: "postApiReportList",
  GetFnbs: "GetFnbs",
  postApiReportPreview: "postApiReportPreview",
  postApiReportExportExcel: "postApiReportExportExcel",
  postApiAppGuestList: "postApiAppGuestList",
  postApiAppReservationsList: "postApiAppReservationsList",
  putApiAppPaymentDetailsById: "putApiAppPaymentDetailsById",
};
