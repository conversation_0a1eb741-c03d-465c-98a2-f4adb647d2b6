import { type ReportParameters } from "@/interfaces/form/reportParameters";
import {
  type Control,
  type FieldValues,
  type UseFieldArrayRemove,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  type UseFormWatch,
  useFieldArray,
} from "react-hook-form";
import { PiPlus, PiTrash } from "react-icons/pi";
import { Button, Text } from "rizzui";
import { MasterReportCells } from "./cells";

export function MasterReportHeaderRows({
  register,
  setValue,
  getValues,
  index,
  remove,
  control,
  watch,
}: {
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  control: Control<FieldValues>;
  index: number;
  remove: UseFieldArrayRemove;
}) {
  const name = `excelHeaderConfig.headerRows.${index}.cells`

  const { append: appendCells, remove: removeCells } = useFieldArray({
    control: control,
    name: name,
  });

  // useEffect(() => {
  //   // SET NOTES
  //   const notes = getValues(`${name}.notes`,);
  //   if (typeof notes != undefined) setNotes({ label: notes ? "True" : "False", value: notes });

  // }, [
  //   getValues(
  //     `parameters.create.${index}`,
  //   ),
  // ]);

  return (
    // <div className="col-span-full mb-2 grid grid-cols-12 gap-2 rounded-lg border border p-4">
    <div className="col-span-full mb-1 grid grid-cols-12 gap-2 rounded-lg text-xs">
      <>
        {/* HEADER ROWS */}
        <div className="col-span-11 p-4 border-2 rounded bg-gray-50 mt-3">
          <div className="flex gap-2">
            <Text as="b">
              Cells
            </Text>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                appendCells({} as never[]);
              }}
              className="-mt-2"
            >
              Add
              <PiPlus className="ml-2" />
            </Button>
          </div>
          <div className="bt-2">
            {(watch(`excelHeaderConfig.headerRows.${index}.cells`) as ReportParameters[])?.map((e, i) => {
              return (
                <div key={i}>
                  <div className="flex">
                    <MasterReportCells
                      register={register}
                      setValue={setValue as unknown as UseFormSetValue<FieldValues>}
                      getValues={getValues}
                      index={index}
                      index2={i}
                      control={control}
                      remove={removeCells}
                    />
                  </div>
                </div>
              )
            })
            }
          </div>
        </div>
        <div className="">
          <Button
            variant="text"
            size="md"
            className={`hover:text-red ${index == 0 ? "mt-3" : "-mt-1"}`}
            onClick={() => { remove(index); }}
          >
            <PiTrash className="w-4 h-4" />
          </Button>
        </div>
      </>
    </div>
  );
}
