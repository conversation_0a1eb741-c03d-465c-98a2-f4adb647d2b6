// export function formatDate_(isoDate: string, type: "date" | "datetime" = "date"): string {
//   const date = new Date(isoDate);
//   if (isNaN(date.getTime())) {
//     throw new Error("Invalid ISO date string");
//   }

//   const day = String(date.getDate()).padStart(2, "0");
//   const month = String(date.getMonth() + 1).padStart(2, "0");
//   const year = date.getFullYear();
//   const hours = String(date.getHours()).padStart(2, "0");
//   const minutes = String(date.getMinutes()).padStart(2, "0");

//   if (type === "datetime") {
//     return `${day}-${month}-${year} ${hours}:${minutes}`;
//   }

//   return `${day}-${month}-${year}`;
// }

export function formatDate(date: string | Date, format?: string) {
  let formatedDate = "";
  if(!date) return "N/A";
  if (typeof date === "string") {
    switch (format) {
      case "date":
        formatedDate = date.replace("T", " ").slice(0, 10);
        break;
      case "datetime":
        formatedDate = date.replace("T", " ").slice(0, 16);
        break;
      case "datetimesecond":
        formatedDate = date.replace("T", " ").slice(0, 19);
        break;
      default:
        formatedDate = date.replace("T", " ").slice(0, 16);
        break;
    }
  } else if (typeof date === "object") {
    const year = date.getFullYear();
    let month = (date.getMonth() + 1).toString(); // Months are zero-based
    let day = date.getDate().toString();
    let hours = date.getHours().toString();
    let minutes = date.getMinutes().toString();

    // Ensure two digits for month and day
    if (month.length < 2) {
      month = "0" + month;
    }
    if (day.length < 2) {
      day = "0" + day;
    }
    if (hours.length < 2) {
      hours = "0" + hours;
    }
    if (minutes.length < 2) {
      minutes = "0" + minutes;
    }

    switch (format) {
      case "date":
        formatedDate = `${year}-${month}-${day}`;
        break;
      case "datetime":
        formatedDate = `${year}-${month}-${day} ${hours}:${minutes}`;
        break;
      case "datetimesecond":
        formatedDate = `${year}-${month}-${day} ${hours}:${minutes}:${date.getSeconds()}`;
        break;
      default:
        formatedDate = `${year}-${month}-${day} ${hours}:${minutes}`;
        break;
    }
  }
  return formatedDate;
}