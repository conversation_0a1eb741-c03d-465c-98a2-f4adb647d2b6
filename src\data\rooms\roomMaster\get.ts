"use server";
import axios from "axios";
import { getSession } from "@/lib/actions";

export const getDataRooms = async () => {
  try {
    const session = await getSession();

    const res = await axios.get(
      "https://api-wisma-dev.imip.co.id/api/app/room",
      {
        headers: {
          Authorization: "Bearer " + session?.access_token,
          "Accept-Language": "en_US",
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      },
    );

    // return res.data.items;
  } catch (error) {
    console.error("Failed to fetch rooms:", error);
    return [];
  }
};
