"use client";
import LoadingScreenAnimated from '@/components/theme/ui/loading-screen-wisma';
import Header from '@/layouts/lithium/lithium-header';
import { useAppConfig } from '@/lib/hooks/useAppConfig';
export default function LithiumLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data } = useAppConfig();
  if(!data) return <LoadingScreenAnimated />
  return (
    <main className="flex min-h-screen flex-grow">
      <div className="flex w-full flex-col ">
        <Header />
        <div className="flex flex-grow flex-col px-4 md:px-5 lg:px-6 3xl:px-8 3xl:pt-4 4xl:px-10">
        {/* <div className="flex flex-grow flex-col pb-6 lg:pb-8 3xl:pt-4"> */}
          {children}
        </div>
      </div>
      {
        process.env.NEXT_PUBLIC_STACK_ENV === 'development' &&
        <div className="development-banner">
          {process.env.NEXT_PUBLIC_STACK_ENV}
        </div>
      }
    </main>
  );
}
