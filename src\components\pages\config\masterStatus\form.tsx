import ButtonReset from "@/components/container/button/button-reset";
import React, { useEffect } from "react";
import { Button, Input, Select } from "rizzui";
import type { SelectOption } from "rizzui";
import type { FormProps } from "@/interfaces/form/formType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  setValue,
  getValues,
  // watch,
  onDelete,
  handleReset,
  isColor,
  setColor,
}: FormProps & {
  handleReset: () => void;
  isColor: SelectOption | undefined;
  setColor: (option: SelectOption) => void;
}) {
  const { can } = useGrantedPolicies();
  useEffect(() => {
    if (setValue) {
      const values: Record<string, string | number> = {};
      Object.keys(values).forEach((key) => {
        setValue(key, values[key] ?? "");
      });
    }
  }, [setValue]);

  useEffect(() => {
    if (isColor && typeof isColor.value === "string") {
      setValue("color", isColor.value.slice(0, -2));
    }
  }, [isColor, setValue]);

  if (!can("WismaApp.MasterStatus")) return <AccessDeniedLayout />;
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-5 gap-2">
        <div>
          <Input
            size="sm"
            label="Code"
            disabled={isLoading}
            placeholder="Please fill in Code"
            {...register("code", { required: true })}
            error={errors.code ? "Code is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Name"
            disabled={isLoading}
            placeholder="Please fill in Name"
            {...register("name", { required: true })}
            error={errors.name ? "Name is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Document Type"
            disabled={isLoading}
            placeholder="Please fill in Document Type"
            {...register("docType", { required: true })}
            error={errors.docType ? "Document Type is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Color"
            disabled={isLoading}
            value={isColor}
            placeholder="Please select a color"
            {...register("color", { required: true })}
            error={errors.color ? "Color is required" : undefined}
            className="w-full"
            options={[
              { label: "Red", value: "#FF5733" },
              { label: "Green", value: "#33FF57" },
              { label: "Blue", value: "#3357FF" },
              { label: "Yellow", value: "#F1C40F" },
              { label: "Purple", value: "#9B59B6" },
              { label: "Orange", value: "#E67E22" },
              { label: "Gray", value: "#7F8C8D" },
            ]}
            onChange={(e: { label: string; value: string }) => {
              setColor(e);
              setValue("color", e?.value ?? "");
            }}
          />
        </div>
        <div className="flex items-end justify-start gap-2">
          <Button
            size="sm"
            type="submit"
            disabled={isLoading}
            className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${
              getValues("id") ? "bg-blue-500" : "bg-green-500"
            }`}
          >
            {getValues("id") ? "Update" : "Create"}
          </Button>
          {getValues("id") && (
            <Button
              size="sm"
              disabled={isLoading}
              className={`rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400`}
              onClick={() => {
                onDelete(String(getValues("id")));
              }}
            >
              Delete
            </Button>
          )}
          <ButtonReset isLoading={isLoading} handleReset={handleReset} />
        </div>
      </div>
    </form>
  );
}
