'use client';

import <PERSON><PERSON><PERSON>er<PERSON>ust<PERSON> from "@/app/shared/page-header-custom";
import React, { useEffect, useState } from "react";
import { type FieldValue, useForm } from "react-hook-form";
import ReservationDetail from "./_component/reservation-detail";
import BookingRoom from "./_component/booking-room";
import { Button } from "rizzui";
import ExtraBed from "./_component/extra-bed";
import { type Reservation } from "@/interfaces/reservations/reservation";
import swal from "sweetalert";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { CreateUpdateReservationDetailsDto, type CreateUpdateReservationsDto, FilterGroup, postApiAppReservationDetails, postApiAppReservations } from "@/client";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useRouter } from "next/navigation";
import { preventEnter } from "@/lib/helper/prevent-enter";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { useApiAppReservationsById } from "@/lib/hooks/useApiAppReservationsById";
import LoadingScreen from "@/components/theme/ui/loading-screen";
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType";
import { swalError } from "@/lib/helper/swal-error";
import { useMasterRoomOptions } from "@/lib/hooks/useMasterRoom";
import { useMasterRoomTypesOptions } from "@/lib/hooks/useMasterRoomTypes";

export default function FormReservation({
  wiithHeader = true,
  className,
  roomId,
  reservationId
}: {
  wiithHeader?: boolean;
  className?: string;
  page?: string;
  reservationId?: string;
  roomId?: string;
}) {
  const {
    register,
    unregister,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const queryClient = useQueryClient();
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(false)

  // LOAD OPTIONS
  const [filterRooms, setFilterRooms] = useState<FilterGroup>({
    operator: "And",
    conditions: [
      {
        fieldName: "roomStatus.name",
        operator: "Equals",
        value: "READY",
      },
    ],
  });
  const { data: roomOptions } = useMasterRoomOptions(0, 1000, filterRooms);
  const { data: roomTypeOptions } = useMasterRoomTypesOptions(0, 1000);

  // LOAF RESERVATION IF ADD MORE GUESTS
  const { isLoading: isLoadingResv, data: dataSourceResv } = useApiAppReservationsById(reservationId ?? "");
  const { data: statusResvDetOptions } = useMasterStatusByDocTypeOptions("reservationDetails")
  const [isAddReservationDetail, setIsAddReservationDetail] = useState(false); // can add another person to reservation group
  useEffect(() => {
    if (dataSourceResv?.id) {
      Object.entries(dataSourceResv ?? {}).forEach(([key, value]) => {
        setValue(key, value);
      });
      unregister("reservationDetails");
      setValue("reservationDetails.0.guestId", null)
      setValue("reservationDetails.0.guest.fullname", null)
      setValue("reservationDetails.0.guest.nationality", null)
      setValue("reservationDetails.0.guest.phoneNumber", null)
      setValue("reservationDetails.0.guest.companyName", null)
      setValue("reservationDetails.0.guest.city", null)
      setIsAddReservationDetail(true);
    }
  }, [dataSourceResv])

  // const [isLoading, setLoading] = useState(false);
  const canExtraBed = watch([
    "reservationDetails.0.checkInDate",
    "reservationDetails.0.checkOutDate",
    "reservationDetails.0.room.roomTypeId",
    "reservationDetails.0.roomId",
    "reservationDetails.0.price"]
  ).every(value => value !== undefined && value !== "");
  const [extraBed, setExtraBed] = useState(false);

  // DEFINE MUTATION
  const createReservationMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateReservationsDto) =>
      postApiAppReservations({
        body: dataMutation,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Reservation Created Successfully",
        icon: "success",
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetReservations] });
      router.push('/checkin');
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const createReservationDetailMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateReservationDetailsDto) =>
      postApiAppReservationDetails({
        body: dataMutation,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "New Guest Added Successfully",
        icon: "success",
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetReservations] });
      // router.push('/checkin');
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  // POST
  const handlePost = async (submitData: FieldValue<Reservation>) => {
    console.log('submitData', submitData)
    const confirmation = (await swal({
      title: "Accept",
      text: "Add New Reservation?",
      icon: "info",
      buttons: ["Cancel", "Yes"],
    })) as unknown as boolean;
    if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan
    setIsLoading(true);
    // return;
    console.log("POST submit data", submitData);
    const data = JSON.parse(JSON.stringify(submitData)) as CreateUpdateReservationsDto & Reservation;
    delete data.reservationDetails?.[0]?.room;
    // if(data.reservationDetails[0] && !data.reservationDetails?.[0]?.guestId)
    if (data.reservationDetails[0]) {
      data.reservationDetails[0].guestData = data.reservationDetails?.[0]?.guest;
      if (data.reservationDetails[0].guestData) {
        data.reservationDetails[0].guestData.identityNumber = data.bookerIdentityNumber;
        data.reservationDetails[0].guestData.email = "";
        data.reservationDetails[0].guestData.district = "";
      }
    }
    delete data.reservationDetails?.[0]?.guest;

    delete data.reservationDetails?.[1]?.guest;
    // console.log("POST handlePost 0", data);
    const attachment = data.attachment?.match(/^data:(.*);base64,(.*)$/);
    if (attachment?.[1] && attachment?.[2]) {
      data.attachments = [
        {
          contentType: attachment[1],
          base64Content: attachment[2],
          fileName: "reservations-attachments-" + new Date().getTime() + ".pdf",
        }
      ];
    }
    data.attachment = "";
    console.log("POST handlePost", data);
    // return;
    if (isAddReservationDetail) {
      const resvDetail = data.reservationDetails?.[0];
      if (resvDetail) {
        resvDetail.reservationId = data.id;
        resvDetail.rfid = "";
        const statusId = statusResvDetOptions?.find(opt => opt.code == "reserved")?.value as string
        resvDetail.statusId = statusId;
      }
      await createReservationDetailMutation.mutateAsync(resvDetail as unknown as CreateUpdateReservationDetailsDto);
      setIsLoading(false);
    } else {
      // return;
      await createReservationMutation.mutateAsync(data as unknown as CreateUpdateReservationsDto);
      setIsLoading(false);
    }
  }
  // }

  // DEFINE CREATE, UPDATE, DELETE
  // useEffect(() => {
  //   void init();
  // }, [id, page, roomId])

  const { can } = useGrantedPolicies();
  if (!can("WismaApp.Reservation.Create")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-10 mt-4 @container " + className}>
      {(isLoadingResv || isLoading) && <LoadingScreen />}
      {wiithHeader && (
        <>
          <PageHeaderCustom
            // title={"Reservations"}
            breadcrumb={[
              { name: "Reservation", href: "/reservation" },
              { name: "Create" },
            ]}
          >
            {/* <Button
              onClick={handlePost}
              variant="outline"
            >
              Post
            </Button> */}
          </PageHeaderCustom>
        </>
      )}
      {/* <form onSubmit={handleSubmit((e)=> postDataReservations(JSON.parse(JSON.stringify(e))))} className="space-y-4"> */}
      <form onSubmit={handleSubmit(handlePost)} onKeyDown={preventEnter} className="space-y-4">
        <div className="grid grid-cols-3 gap-8">
          <div className="col-span-1">
            <ReservationDetail
              isLoading={isLoading}
              register={register}
              unregister={unregister}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              reservationId={reservationId}
              isAddReservationDetail={isAddReservationDetail}
            />
          </div>
          <div className="col-span-2">
            <BookingRoom
              isLoading={isLoading}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              roomId={roomId}
              roomOptions={roomOptions}
              roomTypeOptions={roomTypeOptions}
            />
            {
              extraBed &&
              <ExtraBed
                isLoading={isLoading}
                register={register}
                unregister={unregister}
                errors={errors}
                setValue={setValue}
                getValues={getValues}
                watch={watch}
                roomOptions={roomOptions}
                roomTypeOptions={roomTypeOptions}
              />
            }
            {/* BUTTON */}
            {
              canExtraBed &&
              <div className="flex justify-end gap-2 pt-5">
                <Button
                  // type="submit"
                  // size="sm"
                  onClick={() => { setExtraBed(!extraBed) }}
                  variant="outline"
                >
                  {extraBed ? "Remove Extra Bed" : "Add Extra Bed"}
                </Button>
              </div>
            }
          </div>
        </div>
      </form>
    </div>
  );
}
