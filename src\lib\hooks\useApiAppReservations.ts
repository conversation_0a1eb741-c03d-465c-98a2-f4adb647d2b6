﻿import type { FilterGroup, SortInfo } from "@/client";
import { getApiAppReservations, postApiAppReservationsList } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";

export const useApiAppReservations = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
  refreshId?: string,
) => {
  return useQuery({
    queryKey: [
      QueryNames.getApiAppReservations,
      pageIndex,
      pageSize,
      filter,
      sorting,
      refreshId,
    ],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppReservations({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};

export const useMasterReservationsList = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
  refreshId?: string,
) => {
  return useQuery({
    queryKey: [
      QueryNames.postApiAppReservationsList,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
      refreshId,
    ],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await postApiAppReservationsList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      return data;
    },
  });
};
