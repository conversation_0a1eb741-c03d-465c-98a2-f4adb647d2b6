export default function CabinIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" {...props}>
      <path
        fill="#ae6834"
        d="M499.75 458.16H12.25a4.75 4.75 0 0 1-4.75-4.75v-28.1a4.75 4.75 0 0 1 4.75-4.76h487.5a4.75 4.75 0 0 1 4.75 4.75v28.11a4.75 4.75 0 0 1-4.75 4.75z"
      />
      <path
        fill="#d88a55"
        d="M478.93 420.78H33.06v-42.94l223.22-353.4 222.65 353.4z"
      />
      <path
        fill="#bf7642"
        d="M256.28 24.43 231.3 64l212.08 336.63a29.53 29.53 0 0 1 4.55 15.74v4.42h31v-42.94z"
      />
      <g fill="#804e2b">
        <path d="m30.5 394.13-11.05-6.98a9.4 9.4 0 0 1-2.93-12.96l226.3-358.26a15.93 15.93 0 0 1 26.95.02l225.7 358.25a9.4 9.4 0 0 1-2.94 12.95l-11.07 6.97a9.4 9.4 0 0 1-12.95-2.94L256.27 54.3 43.47 391.2a9.4 9.4 0 0 1-12.96 2.93zM121.78 503.5H94.62a6.02 6.02 0 0 1-6.03-6.02v-39.32h39.2v39.31c0 3.33-2.69 6.03-6.01 6.03zM220.31 503.5h-27.16a6.02 6.02 0 0 1-6.02-6.02v-39.32h39.2v39.31c0 3.33-2.7 6.03-6.02 6.03zM318.85 503.5h-27.16a6.02 6.02 0 0 1-6.02-6.02v-39.32h39.2v39.31c0 3.33-2.7 6.03-6.02 6.03zM417.38 503.5h-27.16a6.02 6.02 0 0 1-6.02-6.02v-39.32h39.2v39.31c0 3.33-2.7 6.03-6.02 6.03z" />
      </g>
      <path
        fill="#ffdf8e"
        d="M169.66 383.63h-44.34a3.88 3.88 0 0 1-3.88-3.89V335.4a3.88 3.88 0 0 1 3.88-3.88h44.34a3.88 3.88 0 0 1 3.88 3.88v44.34a3.88 3.88 0 0 1-3.88 3.89z"
      />
      <path
        fill="#ffcd75"
        d="M169.66 331.52h-30a3.88 3.88 0 0 1 3.88 3.89v44.34a3.88 3.88 0 0 1-3.88 3.88h30a3.88 3.88 0 0 0 3.88-3.89V335.4a3.88 3.88 0 0 0-3.88-3.88z"
      />
      <path
        fill="#ffdf8e"
        d="M388.34 383.63H344a3.88 3.88 0 0 1-3.88-3.89V335.4a3.88 3.88 0 0 1 3.88-3.88h44.34a3.88 3.88 0 0 1 3.89 3.88v44.34a3.88 3.88 0 0 1-3.89 3.89z"
      />
      <path
        fill="#ffcd75"
        d="M298.6 420.82h-85.22V260.57a4.57 4.57 0 0 1 4.57-4.57h76.09a4.57 4.57 0 0 1 4.57 4.57z"
      />
      <path
        fill="#ffc069"
        d="M294.04 256H268.6v123.27c0 10.24-8.31 18.55-18.56 18.55h-36.67v23h85.23V260.57a4.57 4.57 0 0 0-4.57-4.57z"
      />
      <path
        fill="#ffcd75"
        d="M388.34 331.52h-30a3.88 3.88 0 0 1 3.88 3.89v44.34a3.88 3.88 0 0 1-3.88 3.88h30a3.88 3.88 0 0 0 3.88-3.89V335.4a3.88 3.88 0 0 0-3.88-3.88z"
      />
      <path d="M499.75 413.05h-12.9c-.04-2.28-.72-10.97-.73-13l10.4-6.55a16.91 16.91 0 0 0 5.3-23.3L276.11 11.94A23.32 23.32 0 0 0 256.27 1c-8.08 0-15.49 4.09-19.8 10.92L117.03 201a7.5 7.5 0 0 0 12.68 8.01L249.16 19.93a8.4 8.4 0 0 1 7.13-3.93 8.39 8.39 0 0 1 7.13 3.94l225.7 358.26c.56.88.3 2.05-.59 2.6l-11.06 6.98c-.55.34-1.08.32-1.43.24a1.86 1.86 0 0 1-1.19-.83L262.61 50.29a7.5 7.5 0 0 0-12.69 0L37.13 387.2c-.34.56-.83.77-1.18.84-.35.08-.88.1-1.43-.24l-11.06-6.99c-.54-.34-.75-.83-.83-1.18s-.1-.88.24-1.43l88.46-140.04a7.5 7.5 0 0 0-12.68-8L10.18 370.17c-2.4 3.82-3.19 8.34-2.2 12.74s3.65 8.16 7.46 10.57l10.12 6.38v13.18h-13.3C5.5 413.05 0 418.55 0 425.3v28.11c0 6.75 5.5 12.25 12.25 12.25H81.1v31.81c0 7.46 6.07 13.53 13.53 13.53h27.16c7.45 0 13.52-6.07 13.52-13.53v-31.8h44.33v31.8c0 7.46 6.07 13.53 13.52 13.53h27.16c7.46 0 13.52-6.07 13.52-13.53v-31.8h44.34v31.8c0 7.46 6.06 13.53 13.52 13.53h27.16c7.46 0 13.52-6.07 13.52-13.53v-31.8h44.33v31.8c0 7.46 6.07 13.53 13.53 13.53h27.16c7.45 0 13.52-6.07 13.52-13.53v-31.8h68.84c6.76 0 12.25-5.5 12.25-12.26v-28.1c0-6.76-5.5-12.26-12.25-12.26zm-300.46-254.5 39.66 38.96h-64.27zm64.49-78.27 41.2 65.4-41.2 40.45zm-15 105.85-41.3-40.55 41.3-65.38zm64.37-27.47 24.48 38.85H273.6zM120.3 496H96.1v-30.34h24.2zm98.53 0h-24.2v-30.34h24.2zm98.54 0h-24.2v-30.34h24.2zm98.53 0h-24.2v-30.34h24.2zm81.1-45.34H15v-22.6h355.97a7.5 7.5 0 0 0 0-15H306.1V260.56c0-6.65-5.42-12.07-12.07-12.07h-76.09a12.09 12.09 0 0 0-12.07 12.07v152.48H40.56V402.3a16.76 16.76 0 0 0 9.24-7.1l115.4-182.7h181.88l115.08 182.68a16.76 16.76 0 0 0 9 7.04c.08 1.83.65 9.09.7 10.83h-70.9a7.5 7.5 0 0 0 0 15H497zM291.1 324.83h-70.22v-27.2h70.23zm-70.22 15h70.23v27.2h-70.23zm70.23-57.2h-70.23V263.5h70.23zm-70.23 99.4h70.23v31.02h-70.23z" />
      <path d="M169.66 324.02h-44.34a11.4 11.4 0 0 0-11.38 11.38v44.34a11.4 11.4 0 0 0 11.38 11.39h44.34a11.4 11.4 0 0 0 11.38-11.39V335.4a11.4 11.4 0 0 0-11.38-11.38zm-3.62 52.1h-37.1v-37.1h37.1zM399.73 379.75V335.4a11.4 11.4 0 0 0-11.39-11.39H344a11.4 11.4 0 0 0-11.38 11.39v44.33A11.4 11.4 0 0 0 344 391.13h44.34a11.4 11.4 0 0 0 11.39-11.39zm-15-3.62h-37.1v-37.1h37.1z" />
    </svg>
  );
}
