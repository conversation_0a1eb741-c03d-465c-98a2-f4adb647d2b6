"use client";
import React, { useCallback, useEffect, useState } from "react";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/tableV2";
import ButtonLink from "@/components/container/button/button-link";
import Link from "next/link";
import StatusColor from "@/components/container/color/statusColor";
import { useMasterReservationsList } from "@/lib/hooks/useApiAppReservations";
import { randStr } from "@/lib/helper/generate-random-string";
import { formatDate } from "@/lib/helper/format-date";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { Button, Tooltip } from "rizzui";
import { PiUserPlus } from "react-icons/pi";
import { fetchStatusOptions } from "@/lib/hooks/config/masterStatus";
import type { ReservationsDto, FilterGroup, SortInfo } from "@/client";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";

export default function ListReservation({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  // const [isLoading, setLoading] = useState(false);
  const [data, setData] = useState<Record<string, unknown>[]>([]);
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const { can } = useGrantedPolicies();

  // PAGING
  const [statusOptions, setStatusOptions] = useState<SelectOptionType[]>([]);
  const [sort, setSortConfig] = useState<SortInfo[] | undefined>();
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});
  const [selectFilters, setSelectFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [dateFilters, setDateFilters] = useState<Record<string, string>>({});
  const handleFilterChange = useCallback((filterGroup: FilterGroup) => {
    setFilterGroup(filterGroup);
  }, []);
  const [filterGroup, setFilterGroup] = useState<FilterGroup | undefined>(
    undefined,
  );
  const [pagination, setPagiaton] = useState({
    pageIndex: 0,
    pageSize: 20,
  });
  const { isLoading, data: dataSource } = useMasterReservationsList(
    pagination.pageIndex,
    pagination.pageSize,
    filterGroup,
    "",
    sort,
    refreshId,
  );
  // const isLoading = false;

  // MAIN TABLE IS RESERVATIONS
  const columns = [
    // { dataIndex: "no", title: "NO" },
    {
      dataIndex: "reservationCode",
      title: "RESV. CODE",
      filter: "text" as const,
    },
    {
      dataIndex: "reservationType.name",
      title: "TYPES",
      filter: "text" as const,
    },
    { dataIndex: "bookerName", title: "BOOKER NAME", filter: "text" as const },
    {
      dataIndex: "company.name",
      title: "BOOKER COMPANY",
      filter: "text" as const,
    },
    {
      dataIndex: "bookerPhoneNumber",
      title: "BOOKER PHONE",
      filter: "text" as const,
    },
    // { dataIndex: "reservationDetails.0.guest.fullname", title: "GUEST NAME" },
    // { dataIndex: "reservationDetails.0.guest.phoneNumber", title: "GUEST PHONE" },
    {
      dataIndex: "arrivalDate",
      title: "ARRIVAL DATE",
      filter: "date" as const,
      // render: (value: unknown) => formatDate(String(value), "date"),
    },
    // { dataIndex: "reservationDetails.0.room.roomNumber", title: "ROOMS" },
    { dataIndex: "days", title: "DAYS", filter: "text" as const },
    {
      dataIndex: "status.name",
      title: "STATUS",
      filter: "select" as const,
      render: (_: unknown, record: Record<string, unknown>) => {
        const r = record as ReservationsDto;
        return <StatusColor name={r.status?.name} color={r.status?.color} />;
      },
    },
    { dataIndex: "action", title: "#" },
  ];

  useEffect(() => {
    const fetchStatus = async () => {
      const options = await fetchStatusOptions("reservations");
      setStatusOptions(options);
    };

    void fetchStatus();
  }, []);

  const filterSelectTable = {
    "status.name": statusOptions.map((option) => option.label),
  };

  const init = async () => {
    if (dataSource?.items) {
      const mappedData = dataSource.items.map((item) => ({
        ...item,
        // statusBadge: (
        //   <StatusColor name={item?.status?.name} color={item?.status?.color} />
        // ),
        arrivalDate: formatDate(String(item.arrivalDate), "date"),
        action:
          can("WismaApp.Reservation.Edit") &&
          item?.status?.code === "open" &&
          item?.reservationType?.name === "GROUP (GRP)" ? ( // code for reserved
            <Tooltip size="sm" content={"Add Guest"} placement="top">
              <Link href={"reservation/form/add/" + item.id}>
                <Button
                  size="sm"
                  className="bg-green-500 text-white hover:bg-green-600"
                >
                  {/* Check In */}
                  <PiUserPlus className="h-4 w-4" />
                </Button>
              </Link>
            </Tooltip>
          ) : (
            <></>
          ),
      }));
      setData(mappedData);
    }
  };
  useEffect(() => {
    void init();
  }, [dataSource]);

  const handleDetail = (i: string) => {
    // console.log(i);
  };

  return (
    <div className={"mb-2 @container " + className}>
      {wiithHeader && (
        <>
          <PageHeaderCustom
            // title={"Reservations"}
            breadcrumb={[
              { name: "Reservation", href: "/reservation" },
              { name: "List" },
            ]}
          >
            {can("WismaApp.Reservation.Create") && (
              <div>
                <ButtonLink
                  label="New Reservation"
                  isLoading={false}
                  href="/reservation/form/create"
                  className="mr-2 bg-green-500"
                ></ButtonLink>
                <ButtonLink
                  label="New Group Reservation"
                  isLoading={false}
                  href="/reservation/form_group/create"
                  className="mr-2 bg-green-500"
                ></ButtonLink>
              </div>
            )}
          </PageHeaderCustom>
        </>
      )}
      <div className="flex flex-col gap-4">
        <div className="rounded-lg bg-white p-4 shadow">
          <CustomTable
            columns={columns}
            dataSource={data}
            rowKey="id"
            pageSize={pagination.pageSize}
            isLoading={isLoading}
            totalCount={dataSource?.totalCount ?? 1}
            setPagiaton={setPagiaton}
            searchTerms={searchTerms}
            setSearchTerms={setSearchTerms}
            selectFilters={selectFilters}
            setSelectFilters={setSelectFilters}
            dateFilters={dateFilters}
            setDateFilters={setDateFilters}
            onFilterChange={handleFilterChange}
            filterSelectTable={filterSelectTable}
            setRefreshId={setRefreshId}
            onSortChange={setSortConfig}
            height={"70vh"}
          />
        </div>
      </div>
    </div>
  );
}
