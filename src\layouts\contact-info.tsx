"use client";

import React from "react";

export default function ContactInformation({
  className,
  // configForm,
}: {
  className?: string;
  configForm?: object;
}) {
  // const [value, setValue] = useState("");
  // // console.log("user-detail", configForm);

  return (
    <div className={className}>
      <footer className="w-full">
        <hr className="border-blue-gray-50 my-2" />
        <p
          color="blue-gray"
          className="text-center text-[10px] text-slate-400 2xl:text-sm"
        >
          &copy; 2025 INFORMATION TECHNOLOGY - PT. INDONESIA MOROWALI INDUSTRIAL
          PARK
        </p>
      </footer>
      {/* <div className="mb-5 block">
        <div className="mb-10 ml-20">
          <Text as="strong" className="mb-5">
            Perlu Bantuan ?
          </Text>
        </div>
        <div className="ml-20 flex">
          <div className="flex-auto">
            <div className="flex items-center">
              <PiPhoneCallLight className="mr-3 h-6 w-6 text-blue-400" />
              <Text>Phone Department</Text>
            </div>
            <div className="items-center">
              <Text style={{ marginLeft: "37px", fontSize: "10px" }}>
                Senin - Sabtu, 07:30 - 17:00
              </Text>
            </div>
            <div className="mt-6 items-center">
              <Text as="b" style={{ marginLeft: "37px", fontSize: "12px" }}>
                No Ext. 9000
              </Text>
            </div>
          </div>

          <div className="flex-auto">
            <div className="flex items-center">
              <PiEnvelopeLight className="mr-3 h-6 w-6 text-red-400" />
              <Text>Email</Text>
            </div>
            <div className="items-center">
              <Text style={{ marginLeft: "37px", fontSize: "10px" }}>
                Senin - Sabtu, 07:30 - 17:00
              </Text>
            </div>
            <div className="mt-6 items-center">
              <Text as="b" style={{ marginLeft: "37px", fontSize: "12px" }}>
                <EMAIL>
              </Text>
            </div>
          </div>
          <div className="flex-auto">
            <div className="flex items-center">
              <PiWhatsappLogoLight className="mr-3 h-6 w-6 text-green-400" />
              <Text>WhatsApp - Wirya</Text>
            </div>
            <div className="items-center">
              <Text style={{ marginLeft: "37px", fontSize: "10px" }}>
                24 Jam
              </Text>
            </div>
            <div className="mt-6 items-center">
              <Text as="b" style={{ marginLeft: "37px", fontSize: "12px" }}>
                +62-853-9414-4903
              </Text>
            </div>
          </div>
        </div>
      </div> */}
    </div>
  );
}
