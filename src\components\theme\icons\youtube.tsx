export default function YoutubeIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_9761_38724)">
        <path
          d="M23.5055 6.23876C23.2288 5.21063 22.4182 4.4002 21.3903 4.12335C19.5122 3.60938 11.9998 3.60938 11.9998 3.60938C11.9998 3.60938 4.48753 3.60938 2.60943 4.10376C1.60125 4.38043 0.770871 5.21081 0.4942 6.23876C0 8.11668 0 12.0113 0 12.0113C0 12.0113 0 15.9255 0.4942 17.7839C0.771054 18.8118 1.58148 19.6224 2.60961 19.8993C4.50731 20.4133 12 20.4133 12 20.4133C12 20.4133 19.5122 20.4133 21.3903 19.9189C22.4184 19.6422 23.2288 18.8316 23.5057 17.8037C23.9999 15.9255 23.9999 12.0311 23.9999 12.0311C23.9999 12.0311 24.0197 8.11668 23.5055 6.23876Z"
          fill="#FF0000"
        />
        <path
          d="M9.60938 15.6101L15.8564 12.0121L9.60938 8.41406V15.6101Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_9761_38724">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
