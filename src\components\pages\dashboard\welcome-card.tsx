"use client";
import { useEffect, useState } from "react";
import { Text, Title } from "rizzui";
import HandWaveIcon from "../../theme/icons/hand-wave";
import Image from "next/image";
import HaloImg from "@public/halo.png";

export default function WelcomeCard() {
  const [greeting, setGreeting] = useState("Hy there..");

  useEffect(() => {
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting("Rise and shine! ☀️");
    } else if (hour < 18) {
      setGreeting("Hope you're having a great day! 😊");
    } else if (hour < 21) {
      setGreeting("Good evening! Keep up the good work! 🌆");
    } else {
      setGreeting("Time to unwind! 🌙");
    }
  }, []);
  return (
    <div className="relative col-span-full flex w-full items-center rounded-lg border border-gray-300 bg-white p-4 sm:p-5 lg:p-6">
      <div className="flex-1">
        <Text className="text-sm leading-[1.6] text-gray-700 sm:text-base">
          {"Welcome Back"}
        </Text>
        <Title as="h2" className="mb-1 text-xl sm:mb-2 md:text-2xl">
          {/* {session?.user?.name} */}
          John Doe <HandWaveIcon className="ml-2 inline-flex h-6 w-6" />
          <br />
          {greeting}
        </Title>
        <Text className="text-sm leading-[1.6] text-gray-700 sm:text-base"></Text>
      </div>
      <div className="ml-4 hidden flex-shrink-0 md:inline">
        <Image
          src={HaloImg}
          alt="Halo Image"
          width={0} // Set the width according to your requirement
          height={0} // Set the height according to your requirement
          className="mt-4 h-auto w-[200px] 2xl:h-[250px] 2xl:w-[390px]" // Add margin-top if needed
        />
      </div>
    </div>
  );
}
