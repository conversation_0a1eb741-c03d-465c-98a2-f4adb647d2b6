import React from "react";
import { PiPlus } from "react-icons/pi";
import { Button } from "rizzui";

export default function ButtonCreate({
  label = "New",
  isLoading = false,
  handleCreate,
}: {
  label?: string;
  isLoading?: boolean;
  handleCreate?: () => void;
}) {
  return (
    <Button
      type="button"
      size="sm"
      onClick={() => { if (handleCreate) handleCreate() }}
      isLoading={isLoading}
    >
      {label}
      <PiPlus className="ml-2 size-4" />
    </Button>
  );
}
