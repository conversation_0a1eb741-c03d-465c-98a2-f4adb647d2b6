import { type TForm } from "@/lib/custom-types";
import { useEffect, useState } from "react";
import {
  type Control,
  FieldValues,
  UseFieldArrayRemove,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  useFieldArray,
} from "react-hook-form";
import { PiTrash } from "react-icons/pi";
import { Button, Checkbox, Input, Select, Text, type SelectOption } from "rizzui";

export function MasterReportColumnConfigs({
  register,
  setValue,
  index,
  remove
}: {
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  index: number;
  remove: UseFieldArrayRemove;
}) {
  const name = `excelHeaderConfig.columnConfigs.${index}`

  return (
    <div className="col-span-full mb-1 grid grid-cols-12 gap-2 text-xs">
      <Input
        label={index == 0 && "Column Name"}
        placeholder="Column Name"
        size="sm"
        className={"col-span-3"}
        {...register(`${name}.columnName`,)}
      />
      <Input
        label={index == 0 && "Cell Type"}
        placeholder="Cell Type"
        size="sm"
        className={"col-span-3"}
        {...register(`${name}.cellType`,)}
      />
      <Input
        label={index == 0 && "Number Format"}
        placeholder="Number Format"
        size="sm"
        className={"col-span-3"}
        {...register(`${name}.numberFormat`,)}
      />
      <Input
        label={index == 0 && "Width"}
        placeholder="Width"
        size="sm"
        className={"col-span-2"}
        {...register(`${name}.width`,)}
      />
      <div className="">
        <Button
          variant="text"
          size="md"
          className={`hover:text-red ${index == 0 ? "mt-3" : "-mt-1"}`}
          onClick={() => { remove(index); }}
        >
          <PiTrash className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
