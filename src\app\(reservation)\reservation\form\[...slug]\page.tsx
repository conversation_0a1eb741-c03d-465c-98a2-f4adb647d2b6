import React from "react";
import FormReservation from "@/components/pages/reservation/form";

export default function Page(params: { params: { slug: string[] } }) {
  const page = params.params.slug?.[0] ?? ""; // this return => create, update, detail
  const roomId = params.params.slug?.[1] ?? ""; // this return id
  const reservationId = params.params.slug?.[1] ?? ""; // this return id
  if (page === "create") {
    return (
      <FormReservation
        roomId={roomId}
      />
    );
  }
  else if (page === "add") {
    return (
      <FormReservation
        reservationId={reservationId}
      />
    );
  }
  return (
    <FormReservation />
  );
}
