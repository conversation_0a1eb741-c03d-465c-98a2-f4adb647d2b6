"use client";
import React, { useEffect, useState } from "react";
import Page<PERSON>eader<PERSON>ustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  deleteApiMasterCompanyById,
  postApiMasterCompany,
  putApiMasterCompanyById,
  type CompanyDto,
  type CreateUpdateCompanyDto,
} from "@/client";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";
import { useApiMasterCompany } from "@/lib/hooks/useApiMasterCompany";

export default function MasterCompany({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isDataCompany, setDataCompany] = useState<CompanyDto[]>([]);

  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 1000,
  });

  const { isLoading, data } = useApiMasterCompany(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        alias: item.alias ?? "",
        name: item.name ?? "",
        address: item.address ?? "",
      }));
      setDataCompany(mappedData as CompanyDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "alias", title: "Alias", filter: "text" as const },
    { dataIndex: "name", title: "Name", filter: "text" as const },
    {
      dataIndex: "reservationCodePrefix",
      title: "Code",
      filter: "text" as const,
    },
    { dataIndex: "address", title: "Address", filter: "text" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.getApiMasterCompany],
    });
  };

  const deleteMasterCompanyMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiMasterCompanyById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Company deleted successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Company will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete Company with id:", id);
        await deleteMasterCompanyMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the Company. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const data = isDataCompany.find((e) => e.id === id);
    if (data) {
      setIsEditMode(true);
      Object.entries(data).map(([key, value], _index) => {
        setValue(key, value);
      });
    }
  };

  const createMasterCompanyMutation = useMutation({
    mutationFn: async (data: CreateUpdateCompanyDto) =>
      postApiMasterCompany({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Company created successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateMasterCompanyMutation = useMutation({
    mutationFn: async (data: CompanyDto) => {
      const { id, ...updateData } = data;
      return putApiMasterCompanyById({
        path: { id: id! },
        body: updateData as CreateUpdateCompanyDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Company updated successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      if (isEditMode) {
        // console.log("Updating Company:", formData);
        await updateMasterCompanyMutation.mutateAsync(formData as CompanyDto);
      } else {
        // console.log("Creating Company:", formData);
        await createMasterCompanyMutation.mutateAsync(
          formData as CreateUpdateCompanyDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }
  if (!can("WismaApp.MasterCompany.View")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Config" },
            { name: "Master Company" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
