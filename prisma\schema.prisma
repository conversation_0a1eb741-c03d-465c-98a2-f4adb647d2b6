generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["views"]
  binaryTargets   = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid()) @db.NVarChar(150)
  userId            String  @db.NVarChar(50)
  type              String  @db.NVarChar(150)
  provider          String  @db.NVarChar(50)
  providerAccountId String  @db.NVarChar(50)
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid()) @db.NVarChar(150)
  sessionToken String   @unique
  userId       String   @db.NVarChar(50)
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id               String        @id @default(cuid()) @db.NVarChar(50)
  name             String?       @db.NVarChar(200)
  email            String?       @unique @db.NVarChar(200)
  username         String        @unique @db.NVarChar(50)
  emailVerified    DateTime?
  image            String?
  department       String?       @db.NVarChar(150)
  accounts         Account[]
  sessions         Session[]
}

model VerificationToken {
  identifier String   @db.NVarChar(150)
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}
