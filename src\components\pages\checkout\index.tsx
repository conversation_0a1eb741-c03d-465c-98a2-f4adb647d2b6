"use client";
import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import <PERSON>HeaderCustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/tableV2";
import Link from "next/link";
import PopInvoice from "./_component/pop-invoice";
import AccessDeniedLayout from "@/components/layout/access-denied";
import ModalInvoice from "./_component/modal-preview-invoice";
import StatusColor from "@/components/container/color/statusColor";
import { useApiAppReservationDetailsList } from "@/lib/hooks/useApiAppReservationDetails";
import { Button, Tooltip } from "rizzui";
import { countDays } from "@/lib/helper/count-days";
import { countNights } from "@/lib/helper/count-nights";
import { useMasterPaymentOptions } from "@/lib/hooks/useMasterPayments";
import { PiArrowSquareOutB<PERSON>, <PERSON><PERSON><PERSON> } from "react-icons/pi";
import { randStr } from "@/lib/helper/generate-random-string";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { formatDate } from "@/lib/helper/format-date";
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType";
import { fetchStatusOptions } from "@/lib/hooks/config/masterStatus";
import type { Column } from "@/interfaces/table/tableType";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import type { SelectOption } from "rizzui";
import type {
  FilterCondition,
  FilterGroup,
  PaymentsDto,
  ReservationDetailsDto,
  RoomTypeDto,
  SortInfo,
} from "@/client";
import StreamAttachment from "@/components/layout/preview-stream/stream-attachment";
import ButtonDownloadDocx from "@/components/container/button/button-downloadDocx";
import ViewRegistrationCard from "@/components/layout/preview-stream/registration-card";

export default function CheckoutList({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const { can } = useGrantedPolicies();
  const [checked, setChecked] = useState<Record<string, unknown>[]>([]);
  const [pagination, setPagiaton] = useState({
    pageIndex: 0,
    pageSize: 20,
  });
  const [statusOptions, setStatusOptions] = useState<SelectOptionType[]>([]);
  const [sort, setSortConfig] = useState<SortInfo[] | undefined>();
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const [selectFilters, setSelectFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [dateFilters, setDateFilters] = useState<Record<string, string>>({});
  const handleFilterChange = useCallback((filterGroup: FilterGroup) => {
    setFilterGroup(filterGroup);
  }, []);
  const [filterGroup, setFilterGroup] = useState<FilterGroup | undefined>(
    undefined,
  );
  const defaultFilter: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "status.code",
        operator: "In",
        value: "checkin,checkout",
      },
      ...(filterGroup?.conditions ?? []),
    ],
  };

  const [data, setData] = useState<RoomTypeDto[]>([]);
  const { isLoading, data: dataSource } = useApiAppReservationDetailsList(
    pagination.pageIndex,
    pagination.pageSize,
    defaultFilter,
    "",
    [],
    refreshId,
  );

  useEffect(() => {
    const fetchStatus = async () => {
      const options = await fetchStatusOptions("reservationDetails");
      setStatusOptions(options);
    };

    void fetchStatus();
  }, []);

  const filterSelectTable = {
    "status.name": statusOptions.map((option) => option.label),
  };

  // const invoce
  // const getInvoice = usePostApiInvoice({
  //   paymentId: "2759A76C-8D7E-C8AA-B432-3A1A038CBF09",
  //   templateId: "721EA30F-A891-7DCE-5BBB-3A1A08D943BE",
  //   includePaymentDetails: true,
  //   useAdvancedTable: true,
  //   generatePdf: true,
  //   customFilename: "invoce",
  // })
  const [resvDetailsId, setResvDetailsId] = useState<string>();
  const refreshPaymentId = useMemo(() => randStr(), [resvDetailsId]);
  const filterPayment: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "status.code",
        operator: "NotEquals",
        value: "draft",
      },
      ...((resvDetailsId
        ? [
            {
              fieldName: "paymentDetails.reservationDetails.id",
              operator: "Equals",
              value: resvDetailsId,
            },
          ]
        : []) as FilterCondition[]),
    ],
  };
  const { isLoading: getPaymentsLoading, data: getPayments } =
    useMasterPaymentOptions(1, 100, filterPayment, "", [], refreshPaymentId);
  const { data: paymentStatusOptions } =
    useMasterStatusByDocTypeOptions("paymentStatus");

  const [modalPreview, setModalPreview] = useState(false);
  const [paymentId, setPaymentId] = useState("");
  const [attachmentId, setAttachmentId] = useState("");

  useEffect(() => {
    if (dataSource?.items) {
      const mappedData = dataSource.items.map((item) => ({
        ...item,
        room: {
          roomCode: `${item.room?.roomCode} ${item.isExtraBed ? "(Extra Bed)" : ""}`,
        },
        stays: countDays(
          new Date(item.checkInDate ?? 0),
          new Date(item.checkOutDate ?? 0),
        ),
        nights: countNights(
          new Date(item.checkInDate ?? 0),
          new Date(item.checkOutDate ?? 0),
        ),
        checkInDate: formatDate(item.checkInDate ?? "", "datetime"),
        checkOutDate: formatDate(item.checkOutDate ?? "", "datetime"),
        // statusBadge: (
        //   <StatusColor name={item?.status?.name} color={item?.status?.color} />
        // ),
        // paymentStatusBadge: (
        //   <StatusColor
        //     name={item?.paymentStatus?.name}
        //     color={item?.paymentStatus?.color}
        //   />
        // ),
        action: (
          <Actions
            item={item}
            setResvDetailsId={setResvDetailsId}
            getPaymentsLoading={getPaymentsLoading}
            getPayments={getPayments ?? []}
            setModalPreview={setModalPreview}
            setPaymentId={setPaymentId}
            setAttachmentId={setAttachmentId}
            paymentStatusOptions={paymentStatusOptions ?? []}
          />
        ),
      }));
      setData(mappedData);
    }
  }, [dataSource, getPayments]);

  const columns: Column[] = [
    {
      dataIndex: "reservation.reservationCode",
      title: "Resevation Code",
      filter: "text",
    },
    {
      dataIndex: "marketCode",
      title: "Market Code",
      filter: "text",
    },
    {
      dataIndex: "guest.fullname",
      title: "Guest Name",
      filter: "text",
    },
    {
      dataIndex: "guest.identityNumber",
      title: "Identity",
      filter: "text",
    },
    {
      dataIndex: "guest.companyName",
      title: "Company",
      filter: "text",
    },
    {
      dataIndex: "guest.phoneNumber",
      title: "Contact",
      filter: "text",
    },
    {
      dataIndex: "room.roomCode",
      title: "Room",
      filter: "text",
    },
    // {
    //   dataIndex: "room.roomNumber",
    //   title: "Room",
    //   filter: "text",
    // },
    // {
    //   dataIndex: "room.roomType.name",
    //   title: "Room Type",
    //   filter: "select"
    // },
    {
      dataIndex: "checkInDate",
      title: "Checkin",
      filter: "date",
    },
    {
      dataIndex: "checkOutDate",
      title: "Checkout",
      filter: "date",
    },
    {
      dataIndex: "stays",
      title: "Stays",
      filter: "none",
    },
    {
      dataIndex: "nights",
      title: "Nights",
      filter: "none",
    },
    {
      dataIndex: "status.name",
      title: "Status",
      filter: "select" as const,
      render: (_: unknown, record: Record<string, unknown>) => {
        const r = record as ReservationDetailsDto;
        return <StatusColor name={r.status?.name} color={r.status?.color} />;
      },
    },
    {
      dataIndex: "paymentStatus.name",
      title: "Payment",
      filter: "text",
      render: (_: unknown, record: Record<string, unknown>) => {
        const r = record as ReservationDetailsDto;
        return (
          <StatusColor
            name={r.paymentStatus?.name}
            color={r.paymentStatus?.color}
          />
        );
      },
    },
    {
      dataIndex: "action",
      title: "Action",
      filter: "none",
    },
  ];

  // console.log("dataSource", dataSource);

  const checkedResvIds =
    checked.map((item: ReservationDetailsDto) => item.reservationId) ?? [];
  const checkedGuestIds =
    checked.map((item: ReservationDetailsDto) => item.guestId) ?? [];
  const disabledCheckLists =
    dataSource?.items
      ?.filter(
        (item) =>
          checkedResvIds.length &&
          (!checkedResvIds.includes(item.reservationId) ||
            !checkedGuestIds.includes(item.guestId)),
      )
      ?.map((item) => item.id) ?? [];
  const isAllCheckout = checked.every(
    (item: ReservationDetailsDto) => item.status?.code === "checkout",
  );
  const btnTitle = !isAllCheckout ? "Checkout" : "Create Invoice";
  // const [chekoutPaths, setChekoutPaths] = useState<string>("");
  const chekoutPaths = useMemo(
    () => checked.map((item) => item.id)?.join("_"),
    [checked],
  );

  // const checkOutAll = async () => {
  //   const paths = checked.map((item) => item.id)?.join("_");
  //   setChekoutPaths(paths)
  // };

  if (!can("WismaApp.Reservation")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {/* {isLoading && <LoadingScreen />} */}
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Checkout" },
            { name: "List" },
          ]}
        >
          {can("WismaApp.Reservation.Edit") && checked.length ? (
            <Link href={`/checkout/form_group/${chekoutPaths}`}>
              <Button size="sm" className="bg-blue-500">
                {btnTitle}
              </Button>
            </Link>
          ) : (
            <></>
          )}
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        <div className="rounded-lg bg-white">
          <CustomTable
            columns={columns}
            dataSource={data ?? []}
            pageSize={pagination.pageSize}
            rowKey="id"
            isLoading={isLoading}
            totalCount={dataSource?.totalCount ?? 1}
            setPagiaton={setPagiaton}
            searchTerms={searchTerms}
            setSearchTerms={setSearchTerms}
            selectFilters={selectFilters}
            setSelectFilters={setSelectFilters}
            dateFilters={dateFilters}
            setDateFilters={setDateFilters}
            onFilterChange={handleFilterChange}
            onSortChange={setSortConfig}
            filterSelectTable={filterSelectTable}
            setRefreshId={setRefreshId}
            height="70vh"
            setCheked={setChecked}
            disabledCheckLists={disabledCheckLists}
            // filterPosition="top"
          />
        </div>
      </div>
      <ModalInvoice
        // isLoading={isLoading}
        modalPreview={modalPreview}
        setModalPreview={setModalPreview}
        paymentId={paymentId}
        attachmentId={attachmentId}
      />
    </div>
  );
}

interface ActionsProps {
  item: ReservationDetailsDto;
  setResvDetailsId: (id: string) => void;
  getPaymentsLoading: boolean;
  getPayments: (SelectOption & PaymentsDto)[];
  setModalPreview: React.Dispatch<React.SetStateAction<boolean>>;
  setPaymentId: React.Dispatch<React.SetStateAction<string>>;
  setAttachmentId: React.Dispatch<React.SetStateAction<string>>;
  paymentStatusOptions: SelectOption[];
}
export function Actions({
  item,
  setResvDetailsId,
  getPaymentsLoading,
  getPayments,
  setModalPreview,
  setPaymentId,
  setAttachmentId,
  paymentStatusOptions,
}: ActionsProps) {
  const { can } = useGrantedPolicies();
  return (
    <div className="flex items-center justify-center gap-2">
      {can("WismaApp.Reservation.Edit") &&
        (item?.status?.code === "checkin" ||
          item?.paymentStatus?.code === "pending") && (
          <Tooltip
            size="sm"
            content={
              item?.status?.code === "checkin"
                ? "Check Out"
                : "Process Payments"
            }
            placement="top"
          >
            <Link href={`checkout/form_group/${item.id}`}>
              <Button
                size="sm"
                className="bg-blue-500 text-white hover:bg-blue-700"
              >
                <PiArrowSquareOutBold className="h-4 w-4" />
              </Button>
            </Link>
          </Tooltip>
        )}

      {can("WismaApp.Reservation.View") &&
        item?.status?.code === "checkout" &&
        item?.paymentStatus?.code === "paid" && (
          <Tooltip size="sm" content="Preview Reservation" placement="top">
            {/* <Link href={`checkout/detail/${item.id}`}> */}
            <Link href={`checkout/form_group/${item.id}`}>
              <Button
                size="sm"
                className="bg-gray-400 text-white hover:bg-gray-500"
              >
                <PiEye className="h-4 w-4" />
              </Button>
            </Link>
          </Tooltip>
        )}

      {can("WismaApp.Payment.View") && item?.paymentStatus && (
        <PopInvoice
          item={item}
          getPaymentsLoading={getPaymentsLoading}
          getPayments={getPayments}
          paymentStatusOptions={paymentStatusOptions ?? []}
          setModalPreview={setModalPreview}
          setPaymentId={setPaymentId}
          setAttachmentId={setAttachmentId}
          setResvDetailsId={setResvDetailsId}
        />
      )}
      <ViewRegistrationCard
        itemId={String(item?.id)}
        name={String(item?.guestName)}
      />
    </div>
  );
}
