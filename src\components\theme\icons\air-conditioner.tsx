export default function AirConditionerIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M16.9 6.99c0 .18-.14.33-.32.33H7.42a.33.33 0 1 1 0-.66h9.16c.18 0 .33.15.33.33Zm6.49 6c-.07.92-.77 1.61-1.63 1.61H2.24c-.86 0-1.56-.69-1.63-1.6-.25-3.48-.25-5.95 0-7.54.11-.64.56-1.07 1.13-1.07h20.52c.57 0 1.02.43 1.12 1.07.26 1.6.26 4.06 0 7.54Zm-2.96-.9a.41.41 0 0 0-.41-.4H3.98a.41.41 0 0 0-.4.4v1.86h16.85v-1.86Zm2.3-6.52c-.03-.24-.18-.52-.47-.52H1.74c-.29 0-.44.28-.47.52-.26 1.54-.25 3.95 0 7.38.04.58.45 1 .97 1h.68v-1.86c0-.58.48-1.06 1.06-1.06h16.04c.58 0 1.06.48 1.06 1.06v1.86h.68c.52 0 .93-.42.97-1 .25-3.43.26-5.84 0-7.38ZM12 16.1a.33.33 0 0 0-.33.33v2.85a.33.33 0 0 0 .66 0v-2.85a.33.33 0 0 0-.33-.33Zm-2.82 0a.33.33 0 0 0-.31.34c.04 1-.24 1.96-.78 2.64a.33.33 0 1 0 .52.4c.63-.8.96-1.92.91-3.06a.33.33 0 0 0-.34-.32Zm-2.94 0a.33.33 0 0 0-.34.31c-.05 1-.42 1.94-1.02 2.59a.33.33 0 0 0 .48.44 4.83 4.83 0 0 0 1.2-3 .33.33 0 0 0-.32-.34Zm8.9.34a.33.33 0 0 0-.66-.02 4.72 4.72 0 0 0 .91 ********** 0 1 0 .52-.4 4.06 4.06 0 0 1-.78-2.64Zm2.96-.03a.33.33 0 1 0-.66.04c.06 1.14.5 2.23 1.2 3a.33.33 0 0 0 .*********** 0 0 0 .02-.46 4.17 4.17 0 0 1-1.02-2.59Zm1.86-9.87a.45.45 0 0 0 0 .9.45.45 0 0 0 0-.9Z"
        clipRule="evenodd"
      />
    </svg>
  );
}
