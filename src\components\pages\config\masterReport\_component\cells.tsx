import { type TForm } from "@/lib/custom-types";
import { useEffect, useState } from "react";
import {
  type Control,
  FieldValues,
  UseFieldArrayRemove,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  useFieldArray,
} from "react-hook-form";
import { PiTrash } from "react-icons/pi";
import { Button, Checkbox, Input, Select, Text, type SelectOption } from "rizzui";

export function MasterReportCells({
  register,
  setValue,
  index,
  index2,
  remove,
  control,
}: {
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  index: number;
  index2: number;
  control: Control<FieldValues>;
  remove: UseFieldArrayRemove;
}) {
  const name = `excelHeaderConfig.headerRows.${index}.cells.${index2}`

  // useEffect(() => {
  //   // SET NOTES
  //   const notes = getValues(`${name}.notes`,);
  //   if (typeof notes != undefined) setNotes({ label: notes ? "True" : "False", value: notes });

  // }, [
  //   getValues(
  //     `parameters.create.${index}`,
  //   ),
  // ]);

  return (
    // <div className="col-span-full mb-2 grid grid-cols-12 gap-2 rounded-lg border border p-4">
    <div className="col-span-full mb-1 grid grid-cols-12 gap-2 rounded-lg text-xs">
      <>
        <Input
          label={index2 == 0 && "Text"}
          placeholder="Text"
          size="sm"
          className={"col-span-2"}
          {...register(`${name}.text`,)}
        />
        <Input
          label={index2 == 0 && "Col Span"}
          placeholder="Col Span"
          size="sm"
          className={"col-span-2"}
          {...register(`${name}.colSpan`,)}
        />
        <Input
          label={index2 == 0 && "Bg Color"}
          placeholder="Background Color"
          size="sm"
          className={"col-span-2"}
          {...register(`${name}.style.backgroundColor`,)}
        />
        <Input
          label={index2 == 0 && "Font Color"}
          placeholder="Font Color"
          size="sm"
          className={"col-span-2"}
          {...register(`${name}.style.fontColor`,)}
        />
        <Input
          label={index2 == 0 && "Horizontal Align"}
          placeholder="Horizontal Align"
          size="sm"
          className={"col-span-2"}
          {...register(`${name}.style.horizontalAlignment`,)}
        />
        <div className={"col-span-1"}>
          {index2 == 0 &&
            <Text as="p" className={"mb-1.5 font-medium text-xs"}>
              Bold
            </Text>
          }
          <Checkbox
            size="lg"
            {...register(`${name}.style.bold`)}
          />
        </div>
        <div className="">
          <Button
            variant="text"
            size="md"
            className={`hover:text-red col-span-1 ${index2 == 0 && "mt-3"}`}
            onClick={() => { remove(index2); }}
          >
            <PiTrash className="w-4 h-4" />
          </Button>
        </div>
      </>
    </div>
  );
}
