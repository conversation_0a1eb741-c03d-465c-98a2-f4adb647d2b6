NODE_ENV="development"
NEXT_PUBLIC_STACK_ENV="development"
# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Prisma
# https://www.prisma.io/docs/reference/database-reference/connection-urls#env
DATABASE_URL="sqlserver://**************:1433;database=fowisma_dev;user=fowisma_dev;password=***********;encrypt=false;TrustServerCertificate=true"
PRISMA_CLI_QUERY_ENGINE_TYPE=library

# Next Auth
# You can generate a new secret on the command line with:
# openssl rand -base64 32
# https://next-auth.js.org/configuration/options#secret
# NEXTAUTH_SECRET=""
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="V+3XnKuWIgVPk3mbuSKVVWXAYlNJjGnSf8Bg21M3734="
AUTH_SECRET="HxY/nu2KGl5DqujLkeb3ofzEMjSiYMIJrpjopx8djRg="

# Public enviroment
NEXT_PUBLIC_NEXTURL="http://localhost:3000"
NEXT_PUBLIC_IMIP_AUTH_URL="http://doc-generator.test"
NEXT_PUBLIC_API_SECRET="x0daROpeUGca43YxykTYK6Iw9ffEYuhfZYhoqWFa9DrVZc3g4IjBdWhCDict80v1"
AUTH_PREFIX="wisma-dev-"

NEXT_PUBLIC_API_WISMA_DEV="https://api-wisma-dev.imip.co.id"
NEXT_PUBLIC_HELPDESK_API="http://**************:8112"
# NEXT_PUBLIC_HELPDESK_API="https://helpdesk-api.imip.co.id"

# Next Auth Discord Provider
IMIP_AUTH_CLIENT_ID="9CCCA11E-9B75-4E91-A749-CB3C9CE4C599"
IMIP_AUTH_CLIENT_SECRET="3nGeg8liY2kVku72P6xor0Ed2LQH8OTBTNt3PWx8"
IMIP_AUTH_URL="http://doc-generator.test"
# IMIP_AUTH_URL="http://**************:8100/"

TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=-********** # wisma Log


NEXT_PUBLIC_API_URL=https://api-wisma-dev.imip.co.id
IMIP_AUTH_URL=https://api-identity-dev.imip.co.id
NEXT_PUBLIC_IMIP_AUTH_URL="${IMIP_AUTH_URL}"
NEXT_PUBLIC_CLIENT_ID=WismaClientDev2
NEXT_PUBLIC_APP_URL=https://wisma.imip.co.id
NEXT_PUBLIC_SCOPE='openid profile email'
SESSION_PASSWORD=build-time-session-password-placeholder
NEXT_PUBLIC_CLIENT_SECRET=hYidGYYbYYb9fhcdCssMGAJqoACQih7K

REDIS_HOST="localhost"
REDIS_PASSWORD=""
REDIS_PORT="6379"
