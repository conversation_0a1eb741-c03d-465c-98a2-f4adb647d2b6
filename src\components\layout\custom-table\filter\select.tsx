import type { SelectFilterProps } from "@/interfaces/table/selectFilterType";
import React from "react";
import { PiCaretDown } from "react-icons/pi";

export default function SelectFilter({
  column,
  dataSource,
  tempSelectFilters,
  setTempSelectFilters,
  handleSelectFilterChange,
  searchTerms,
  setSearchTerms,
  showDropdown,
  setShowDropdown,
}: SelectFilterProps) {
  return (
    <div className="relative">
      <span className="absolute inset-y-0 left-0 flex items-center pl-2">
        <PiCaretDown className="text-gray-400" />
      </span>
      <input
        type="text"
        placeholder="Select"
        value={searchTerms[column.dataIndex] ?? ""}
        onClick={() => setShowDropdown(column.dataIndex)}
        onChange={(e) => {
          const searchValue = e.target.value.toLowerCase();
          setSearchTerms((prev) => ({
            ...prev,
            [column.dataIndex]: searchValue,
          }));
        }}
        className="w-full rounded-md border border-gray-300 px-8 py-1 text-xs font-normal"
      />
      {showDropdown === column.dataIndex && (
        <div className="absolute z-50 mt-1 w-full rounded-md border border-gray-300 bg-white shadow-md">
          <div className="custom-scrollbar max-h-40 overflow-y-auto">
            {[
              ...Array.from(
                new Set(
                  dataSource.map((item) => {
                    const value = column.dataIndex
                      .split(".")
                      .reduce<Record<
                        string,
                        unknown
                      > | null>((acc, key) => (acc && typeof acc === "object" && key in acc ? (acc[key] as Record<string, unknown> | null) : null), item as Record<string, unknown> | null);
                    return value ? String(value) : "";
                  }),
                ),
              ),
            ]
              .filter((option) =>
                option
                  .toLowerCase()
                  .includes(searchTerms[column.dataIndex]?.toLowerCase() ?? ""),
              )
              .map((uniqueValue) => {
                const isChecked =
                  tempSelectFilters[column.dataIndex]?.includes(uniqueValue) ??
                  false;
                return (
                  <label
                    key={uniqueValue}
                    title={uniqueValue}
                    className={`flex cursor-pointer items-center gap-2 px-2 py-1 text-sm font-normal hover:bg-gray-50 ${
                      isChecked ? "bg-gray-100 text-blue-500" : ""
                    }`}
                  >
                    <span className="relative h-3 w-3">
                      <input
                        type="checkbox"
                        checked={isChecked}
                        onChange={() => {
                          const currentSelections =
                            tempSelectFilters[column.dataIndex] ?? [];
                          const updatedSelections = isChecked
                            ? currentSelections.filter(
                                (val) => val !== uniqueValue,
                              )
                            : [...currentSelections, uniqueValue];
                          setTempSelectFilters((prev) => ({
                            ...prev,
                            [column.dataIndex]: updatedSelections,
                          }));
                        }}
                        className="peer absolute h-3 w-3 cursor-pointer opacity-0"
                      />
                      <span className="block h-3 w-3 rounded border border-gray-400 peer-checked:border-blue-600 peer-checked:bg-blue-600" />
                      {isChecked && (
                        <svg
                          className="pointer-events-none absolute left-0 top-0 h-3 w-3 text-white"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth={3}
                          viewBox="0 0 24 24"
                        >
                          <path d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </span>
                    <span
                      className="truncate text-xs"
                      style={{ textTransform: "none" }}
                    >
                      {uniqueValue}
                    </span>
                  </label>
                );
              })}
          </div>
          <div className="border-t p-2">
            <div className="flex items-center justify-center gap-2">
              <button
                type="button"
                className="rounded bg-blue-500 px-2 py-1 text-xs font-normal text-white hover:bg-blue-600"
                onClick={() => {
                  handleSelectFilterChange(
                    column.dataIndex,
                    tempSelectFilters[column.dataIndex] ?? [],
                  );
                  setShowDropdown(null);
                }}
              >
                Apply
              </button>
              <button
                type="button"
                className="rounded bg-gray-300 px-2 py-1 text-xs font-normal text-black hover:bg-gray-400 hover:text-white"
                onClick={() => {
                  setTempSelectFilters((prev) => ({
                    ...prev,
                    [column.dataIndex]: [],
                  }));
                }}
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
