import type { SortingProps } from "@/interfaces/table/sortingType";
import React, { useEffect } from "react";

export default function Sorting({
  column,
  sortConfig,
  handleSort,
  filteredData,
  onSortedData,
}: SortingProps) {
  const isActive = sortConfig?.key === column.dataIndex;
  const direction = sortConfig?.direction;

  useEffect(() => {
    const sortedData = [...filteredData].sort((a, b) => {
      if (!sortConfig || sortConfig.key === "action") return 0;

      const { key, direction } = sortConfig;

      const aValue =
        key
          .split(".")
          .reduce<Record<
            string,
            unknown
          > | null>((acc, k) => (acc && typeof acc === "object" && k in acc ? (acc[k] as Record<string, unknown> | null) : null), a as Record<string, unknown> | null) ??
        "";

      const bValue =
        key
          .split(".")
          .reduce<Record<
            string,
            unknown
          > | null>((acc, k) => (acc && typeof acc === "object" && k in acc ? (acc[k] as Record<string, unknown> | null) : null), b as Record<string, unknown> | null) ??
        "";

      if (aValue < bValue) return direction === "asc" ? -1 : 1;
      if (aValue > bValue) return direction === "asc" ? 1 : -1;
      return 0;
    });

    onSortedData(sortedData);
  }, [sortConfig, filteredData, column.dataIndex, onSortedData]);

  return (
    <span
      onClick={() => handleSort(column.dataIndex)}
      title={
        isActive
          ? direction === "asc"
            ? "Sort Ascending"
            : "Sort Descending"
          : "Sort"
      }
      className={`cursor-pointer text-blue-500 ${
        isActive ? "font-bold" : "font-semibold"
      }`}
    >
      {isActive ? (direction === "asc" ? "↑" : "↓") : "↑↓"}
    </span>
  );
}
