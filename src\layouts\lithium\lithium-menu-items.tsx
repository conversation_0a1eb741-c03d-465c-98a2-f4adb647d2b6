// import { routes } from '@/config/routes';
// import { DUMMY_ID } from '@/config/constants';

export type SubMenuItemType = {
  name: string;
  href: string;
};

export type DropdownItemType = {
  name: string;
  icon: string;
  description?: string;
  href?: string;
  subMenuItems?: SubMenuItemType[];
};

export type LithiumMenuItem = {
  name: string;
  type: string;
  dropdownItems?: DropdownItemType[];
  href?: string;
};

export type LithiumMenuItems = {
  name: string;
  type: string;
  dropdownItems?: DropdownItemType[];
  href?: string;
}[];

export const lithiumMenuItems: LithiumMenuItems = [
  {
    name: "Reservation",
    type: "link",
    href: "/",
  },
  {
    name: "Checkin",
    type: "link",
    href: "/",
  },
  {
    name: "Checkout",
    type: "link",
    href: "/",
  },
  {
    name: "Rooms",
    type: "dropdownItems",
    dropdownItems: [
      {
        name: "Rooms A",
        href: "/",
        icon: "DicesIcon",
      },
      {
        name: "Rooms B",
        href: "/",
        icon: "GreenLeafIcon",
      },
      {
        name: "Rooms C",
        href: "/",
        icon: "PieChartCurrencyIcon",
      },
    ],
  },
  {
    name: "Services",
    type: "dropdownItems",
    dropdownItems: [
      {
        name: "Services A",
        href: "/",
        icon: "DicesIcon",
      },
      {
        name: "Services B",
        href: "/",
        icon: "GreenLeafIcon",
      },
      {
        name: "Services C",
        href: "/",
        icon: "PieChartCurrencyIcon",
      },
    ],
  },
  {
    name: "F&B",
    type: "dropdownItems",
    dropdownItems: [
      {
        name: "F&B A",
        href: "/",
        icon: "DicesIcon",
      },
      {
        name: "F&B B",
        href: "/",
        icon: "GreenLeafIcon",
      },
      {
        name: "F&B C",
        href: "/",
        icon: "PieChartCurrencyIcon",
      },
    ],
  },
  {
    name: "Report",
    type: "dropdownItems",
    dropdownItems: [
      {
        name: "Report A",
        href: "/",
        icon: "DicesIcon",
      },
      {
        name: "Report B",
        href: "/",
        icon: "GreenLeafIcon",
      },
      {
        name: "Report C",
        href: "/",
        icon: "PieChartCurrencyIcon",
      },
    ],
  },
];
{
}
