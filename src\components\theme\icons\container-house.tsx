export default function ContainerHouseIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" {...props}>
      <path
        fill="#e8e8e8"
        d="M116.84 440h-64s-10.3-2.24-10.3-5l10.2-18.55 69.1 8.55v10a5 5 0 0 1-5 5z"
      />
      <path
        fill="#dcdbdd"
        d="M55.84 425h66v-18h-85v28a5 5 0 0 0 5 5h11v-12a3 3 0 0 1 3-3z"
      />
      <path
        fill="#e8e8e8"
        d="M470 440h-64c-2.76 0-9.98-6.81-9.98-9.57l-.38-13.98L475 425v10a5 5 0 0 1-5 5z"
      />
      <path
        fill="#dcdbdd"
        d="M409 425h66v-19h-85v29a5 5 0 0 0 5 5h11v-12a3 3 0 0 1 3-3z"
      />
      <path
        fill="#c6805d"
        d="M499.5 122h-474c-2.76 0-9.7 9.47-9.7 12.24l-3.23 266.04a5 5 0 0 0 5 5l480.96 1.38c2.76 0 5.97-10.9 5.97-13.66V127a5 5 0 0 0-5-5z"
      />
      <path
        fill="#ba7052"
        d="M30.5 393a5 5 0 0 1-5-5V122h-13a5 5 0 0 0-5 5v279a5 5 0 0 0 5 5h487a5 5 0 0 0 5-5v-13z"
      />
      <path
        fill="#f9f8f9"
        d="M473 151.5H39a2 2 0 0 0-2 2v226c0 1.1.9 2 2 2h24l4.04-4h78.3l3.66 4h324a2 2 0 0 0 2-2v-226a2 2 0 0 0-2-2zm-135.24.15-.15.08c-.3-.03-.28-.06.15-.08zm-145.7 229.8.06.01c-.08 0-.1 0-.07-.02zm145.99 0 .07.01c-.09 0-.1-.01-.07-.02z"
      />
      <path
        fill="#e8e8e8"
        d="m149 363.5-3.75 9.56-71.74.5V206.4c0-2.76 4.73-11.64 7.49-11.64h63a5 5 0 0 1 5 5V363.5z"
      />
      <path
        fill="#dcdbdd"
        d="M84.5 359.5V194.77h-13a5 5 0 0 0-5 5V381.5h86v-18h-64a4 4 0 0 1-4-4z"
      />
      <path
        fill="#7ad7ee"
        d="M444 292.27h-76a5 5 0 0 1-5-5v-89a5 5 0 0 1 5-5h76a5 5 0 0 1 5 5v89a5 5 0 0 1-5 5z"
      />
      <path
        fill="#4bc3ef"
        d="M384.2 272.33a5 5 0 0 1-5-5v-71h-18a5 5 0 0 0-5 5v89a5 5 0 0 0 5 5h76a5 5 0 0 0 5-5v-18z"
      />
      <path
        fill="#7ad7ee"
        d="M294 292.27h-76a5 5 0 0 1-5-5v-89a5 5 0 0 1 5-5h76a5 5 0 0 1 5 5v89a5 5 0 0 1-5 5z"
      />
      <path
        fill="#4bc3ef"
        d="M237.94 272.33a5 5 0 0 1-5-5v-71h-18a5 5 0 0 0-5 5v89a5 5 0 0 0 5 5h76a5 5 0 0 0 5-5v-18z"
      />
      <path d="M499.5 114.5H257.46L135.31 65.05a7.5 7.5 0 1 0-5.63 13.9l15.32 6.2v29.35H12.5C5.6 114.5 0 120.1 0 127v279c0 6.9 5.6 12.5 12.5 12.5h16.84V435c0 6.9 5.6 12.5 12.5 12.5h75c6.9 0 12.5-5.6 12.5-12.5v-16.5h157.28a7.5 7.5 0 0 0 0-15H15v-274h482v274H321.62a7.5 7.5 0 0 0 0 15h60.88V435c0 6.9 5.6 12.5 12.5 12.5h75c6.9 0 12.5-5.6 12.5-12.5v-16.5h17c6.9 0 12.5-5.6 12.5-12.5V127c0-6.9-5.6-12.5-12.5-12.5zm-385.16 318h-70v-14h70zM160 91.23l57.49 23.27H160zM467.5 432.5h-70v-14h70z" />
      <path d="M77.11 159a7.5 7.5 0 0 0 0-15H37a7.5 7.5 0 0 0-7.5 7.5v230A7.5 7.5 0 0 0 37 389h438a7.5 7.5 0 0 0 7.5-7.5V222.92a7.5 7.5 0 0 0-15 0V374h-131V159h131v28.92a7.5 7.5 0 0 0 15 0V151.5a7.5 7.5 0 0 0-7.5-7.5H112.11a7.5 7.5 0 0 0 0 15h63.39v215H160v-67.57a7.5 7.5 0 0 0-15 0V374H74V202.27h71v69.16a7.5 7.5 0 0 0 15 0v-71.66c0-6.9-5.6-12.5-12.5-12.5h-76c-6.9 0-12.5 5.6-12.5 12.5V374H44.5V159zm113.39 0h131v215h-131z" />
      <path d="M294 187.27h-76c-6.9 0-12.5 5.6-12.5 12.5v89c0 6.89 5.6 12.5 12.5 12.5h76c6.9 0 12.5-5.61 12.5-12.5v-89c0-6.9-5.6-12.5-12.5-12.5zm-2.5 15v25h-71v-25zm-71 84v-44h71v44zM84.61 282.71v17a7.5 7.5 0 0 0 15 0v-17a7.5 7.5 0 0 0-15 0zM364 187.27c-6.9 0-12.5 5.6-12.5 12.5v89c0 6.89 5.6 12.5 12.5 12.5h76c6.9 0 12.5-5.61 12.5-12.5v-89c0-6.9-5.6-12.5-12.5-12.5zm73.5 15v25h-71v-25zm-71 84v-44h71v44z" />
    </svg>
  );
}
