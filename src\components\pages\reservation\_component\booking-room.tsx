import React, { useEffect, useState } from "react";
import { Input, SelectOption, Title } from "rizzui";
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input";
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";
import { DateInput } from "@/components/theme/ui/input-type/dates-input";
import {
  type FieldErrors,
  type FieldValues,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  type UseFormWatch,
} from "react-hook-form";
import { type Reservation } from "@/interfaces/reservations/reservation";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { useMasterRoomOptions } from "@/lib/hooks/useMasterRoom";
import { useMasterRoomTypesOptions } from "@/lib/hooks/useMasterRoomTypes";
import { FilterGroup, RoomDto } from "@/client";
import { countDays } from "@/lib/helper/count-days";

export default function BookingRoom({
  register,
  errors,
  setValue,
  getValues,
  watch,
  roomId,
  roomOptions,
  roomTypeOptions
}: {
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<Reservation>;
  roomId?: string;
  roomOptions? : SelectOption[] & RoomDto[];
  roomTypeOptions? : SelectOption[];
}) {
  // GUEST OPTIONS
  // const { data: roomOptions } = useMasterRoomOptions(0, 1000, filterRooms);
  const [roomOpts, setRoomOpts] = useState<SelectOption[]>([]);

  // UPDATE PRICE IF ROOM OR DAYS CHANGE
  useEffect(() => {
    const selectedRoom = (roomOptions as RoomDto[])?.find((opt) => opt.id == getValues("reservationDetails.0.roomId"),);
    const days = getValues('reservationDetails.0.room.days') ? Number(getValues('reservationDetails.0.room.days')) : 1;
    setValue('reservationDetails.0.price', ((selectedRoom?.price ?? 0) * days))
    console.log('selectedRoom', selectedRoom);
    
    // FOR EXTRA BED
    setValue("reservationDetails.0.room.roomTypeName", selectedRoom?.roomType?.name);
    setValue("reservationDetails.0.room.roomNumber", selectedRoom?.roomNumber);
    // END FOR EXTRA BED
  
  }, [watch("reservationDetails.0.roomId"), watch("reservationDetails.0.room.days")]);
  
  // UPDATE DAYS IF CHECK IN/OUT CHANGES
  useEffect(() => {
      const days = countDays(new Date(getValues('reservationDetails.0.checkInDate') as string), new Date(getValues('reservationDetails.0.checkOutDate') as string));
      setValue("reservationDetails.0.room.days", days);
  }, [watch("reservationDetails.0.checkInDate"), watch("reservationDetails.0.checkOutDate")]);

  // UPDATE ROOMS IF ROOM TYPE CHANGES
  useEffect(() => {
    const temp = roomOptions?.filter((val) => val.roomTypeId == watch("reservationDetails.0.room.roomTypeId"),);
    setRoomOpts(temp ?? []);
    if (!roomId) setValue("reservationDetails.0.roomId", null);
  }, [watch("reservationDetails.0.room.roomTypeId")]);

  useEffect(() => {
    if (roomId) {
      const room = roomOptions?.find(
        (e: RoomDto) => e.id?.toLowerCase() == roomId.toLowerCase(),
      );
      // console.log('roomOptions', roomOptions)
      // console.log('room', room)
      setValue("reservationDetails.0.room.roomTypeId", room?.roomTypeId);
      setValue("reservationDetails.0.roomId", roomId.toLowerCase());
    }
  }, [roomId, roomOptions]);

  return (
    <div className="mt-5 rounded border-2 bg-gray-50 p-4">
      <div className="grid grid-cols-1 gap-2">
        {/* ROOM INFORMATION */}
        <Title as="h6" className="mb-0">
          Booking Room
        </Title>
        <div className="grid grid-cols-12 gap-2">
          <DropdownInput
            className="col-span-2"
            label={"Type"}
            name={"reservationDetails.0.room.roomTypeId"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomTypeOptions}
            required={true}
            size="sm"
          />
          <AutocompleteSelect
            className="col-span-1"
            label={"Room"}
            name={"reservationDetails.0.roomId"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomOpts}
            required={true}
            size="sm"
          />
          <DateInput
            className="col-span-3"
            label={"Check In Date"}
            name={"reservationDetails.0.checkInDate"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            // inline={true}
            maxDate={
              watch("reservationDetails.0.checkOutDate") as Date | undefined
            }
            size="sm"
          />
          <DateInput
            className="col-span-3"
            label={"Check Out Date"}
            name={"reservationDetails.0.checkOutDate"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            // inline={true}
            minDate={
              watch("reservationDetails.0.checkInDate") as Date | undefined
            }
            size="sm"
          />
          <Input
            label="Days"
            size="sm"
            readOnly={true}
            // disabled={isLoading}
            placeholder="Please fill in Days"
            {...register("reservationDetails.0.room.days", { required: true })}
            className="col-span-1"
          />
          <CurrencyIDR
            label={"Rate"}
            name={"reservationDetails.0.price"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            options={roomOptions}
            required={true}
            className="col-span-2"
            size="sm"
          />
        </div>
      </div>
    </div>
  );
}
