export const countNights = (startDate?: Date, endDate?: Date): number => {
  if (!startDate || !endDate) return 0; // Jika salah satu tanggal kosong, kembalikan 0
  const CUTOFF_HOUR = 14; // 2 PM

  function getNightStart(date: Date): Date {
    const cutoff = new Date(date);
    cutoff.setHours(CUTOFF_HOUR, 0, 0, 0);
    if (date < cutoff) {
      cutoff.setDate(cutoff.getDate() - 1);
    }
    return cutoff;
  }

  const msPerDay = 1000 * 60 * 60 * 24;

  const startCutoff = getNightStart(startDate);
  const endCutoff = getNightStart(endDate);

  const diffInMs = endCutoff.getTime() - startCutoff.getTime();
  const nights = Math.floor(diffInMs / msPerDay);

  return nights > 0 ? nights : 0;
};
