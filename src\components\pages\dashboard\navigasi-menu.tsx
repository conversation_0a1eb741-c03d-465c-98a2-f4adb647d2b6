import React, { useEffect, useState } from "react";
import Link from "next/link";
import ModalPreview from "@/components/layout/preview-stream/modal-preview";
import { postApiAppRegistrationCardGenerateDocumentAsAttachment } from "@/client";
import { useApiAttachmentStreamById } from "@/lib/hooks/useApiAppAttachmentById";
import type { navigationMenuType } from "@/interfaces/dashboad/navigationMenuType";

export default function NavigationMenu({
  roomId,
  roomStatus,
  dataReservasi,
  onUpdateRoomStatus,
  reservationInfos,
}: navigationMenuType) {
  const isCheckoutEnable = roomStatus?.code === "OVC"; // OBC IN STAY
  const isNewResvEnable = roomStatus?.code === "VR"; // READY

  const [previewLoading, setPreviewLoading] = useState(false);
  const [blobUrl, setBlobUrl] = useState<string>();
  const [modal, setModal] = useState(false);
  const [attachmentId, setAttachmentId] = useState("");

  // Perbaiki: handleView menerima reservationId
  const handleView = async (reservationId: string) => {
    setPreviewLoading(true);

    const data = {
      documentId: reservationId, // hanya satu id
      templateId: "",
      includeDetails: true,
      useAdvancedTable: true,
      generatePdf: true,
      customFilename: "RegistrationCard",
    };

    console.log("Request Data: ", data);

    const rc = await postApiAppRegistrationCardGenerateDocumentAsAttachment({
      body: data,
    });

    console.log("Registration Card Response: ", rc);

    setAttachmentId(rc.data?.id ?? "");
    setModal(true);
    setPreviewLoading(false);
  };

  const { data } = useApiAttachmentStreamById(attachmentId);
  useEffect(() => {
    if (data) {
      const url = URL.createObjectURL(data as Blob);
      setBlobUrl(url);
      return () => URL.revokeObjectURL(url); // clean up memory
    }
  }, [data]);

  const chekoutUrl = () => {
    const resvDetId = dataReservasi?.find((e) => e.roomId == roomId)?.id;
    if (resvDetId) return `checkout/form_group/${resvDetId}`;
    return "/";
  };

  return (
    <>
      <div className="">
        <ul className="space-y-1 px-2 py-2 text-sm text-gray-700">
          {isNewResvEnable && (
            <li>
              <Link
                href={`reservation/form/create/${roomId}`}
                className="block rounded-md px-3 py-2 transition-colors hover:bg-gray-100"
              >
                NEW RESERVATION
              </Link>
            </li>
          )}
          <li>
            <button
              onClick={() => {
                window.location.href = `/config/masterGuest/info/${roomId}`;
              }}
              className="block w-full rounded-md px-3 py-2 text-left transition-colors hover:bg-gray-100"
            >
              GUEST INFO
            </button>
          </li>
          {!isCheckoutEnable && (
            <li>
              <button
                onClick={onUpdateRoomStatus}
                className="block w-full rounded-md px-3 py-2 text-left transition-colors hover:bg-gray-100"
              >
                UPDATE ROOM STATUS
              </button>
            </li>
          )}
          {isCheckoutEnable && (
            <li>
              <Link
                href={chekoutUrl()}
                className="block rounded-md px-3 py-2 transition-colors hover:bg-gray-100"
              >
                CHECK OUT
              </Link>
            </li>
          )}

          {/* Registration Cards */}
          {Array.isArray(reservationInfos) &&
            reservationInfos.length > 0 &&
            reservationInfos.map((info) => (
              <li key={info.id}>
                <button
                  onClick={() => handleView(info.id)}
                  className="block w-full rounded-md px-3 py-2 text-left transition-colors hover:bg-gray-100"
                >
                  REGISTRATION CARD{" "}
                  {info.guestName && (
                    <span className="ml-1 text-xs font-light text-primary">
                      ({info.guestName})
                    </span>
                  )}
                </button>
              </li>
            ))}
        </ul>
      </div>
      <ModalPreview
        isOpen={modal}
        onClose={() => setModal(false)}
        blobUrl={blobUrl}
        title="Preview Registration Card"
      />
    </>
  );
}
