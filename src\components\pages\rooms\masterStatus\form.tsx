import ButtonReset from "@/components/container/button/button-reset";
import type { FormProps } from "@/interfaces/form/formType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import React, { useEffect } from "react";
import { Button, Input } from "rizzui";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  setValue,
  getValues,
  onDelete,
  handleReset,
}: FormProps & { handleReset: () => void }) {
  const { can } = useGrantedPolicies();
  useEffect(() => {
    if (setValue) {
      const values: Record<string, string | number> = {};
      Object.keys(values).forEach((key) => {
        setValue(key, values[key] ?? "");
      });
    }
  }, [setValue]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-4 gap-2">
        <div>
          <Input
            size="sm"
            label="Code"
            disabled={isLoading}
            placeholder="Please fill in Code"
            {...register("code", { required: true })}
            error={errors.code ? "Code is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Name"
            disabled={isLoading}
            placeholder="Please fill in Name"
            {...register("name", { required: true })}
            error={errors.name ? "Name is required" : undefined}
            className="w-full"
          />
        </div>
        <div className="flex flex-col gap-1">
          <label className="block text-xs font-medium text-gray-700">
            Color
          </label>
          <input
            type="color"
            disabled={isLoading}
            {...register("color", { required: true })}
            className="h-8 w-full rounded-md border border-gray-300 p-0 hover:cursor-pointer hover:border-black focus:border-black focus:ring-1 focus:ring-black"
          />
          {errors.color && (
            <p className="mt-1 text-sm text-red-500">Color is required</p>
          )}
        </div>
        <div className="flex items-end justify-start gap-2">
          {(can("WismaApp.RoomStatus.Create") && can("WismaApp.RoomStatus.Edit")) &&
            <Button
              size="sm"
              type="submit"
              disabled={isLoading}
              className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${getValues("id") ? "bg-blue-500" : "bg-green-500"
                }`}
            >
              {getValues("id") ? "Update" : "Create"}
            </Button>
          }
          {(getValues("id") && can("WismaApp.Room.Delete")) && (
            <Button
              size="sm"
              disabled={isLoading}
              className={`rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400`}
              onClick={() => {
                onDelete(String(getValues("id")));
              }}
            >
              Delete
            </Button>
          )}
          <ButtonReset isLoading={isLoading} handleReset={handleReset} />
        </div>
      </div>
    </form>
  );
}
