import React, { useState, useEffect, useRef } from "react";
import PaginationTable from "./pagination";
import SelectFilter from "./filter/selectV2";
import FreeTextFilter from "./filter/freeText";
import PageSizeSelector from "./pageSizeSelector";
import ButtonReset from "@/components/container/button/button-reset";
import SkeletonTable from "@/components/container/loading/skeleton-table";
import { Checkbox, Empty, Text } from "rizzui";
import { renderCellValue } from "@/lib/client/helper";
import { formatTitle } from "@/lib/helper/format-title";
import { randStr } from "@/lib/helper/generate-random-string";
import type { FilterGroup, SortInfo } from "@/client";
import type { FilterProps } from "@/interfaces/table/filterProps";
import type { CustomTableProps } from "@/interfaces/table/tableTypeV2";
import DateFilter from "./filter/date";

export default function CustomTableV2({
  columns,
  dataSource,
  rowKey,
  pageSize,
  isLoading = false,
  init,
  totalCount,
  setPagiaton,
  searchTerms,
  setSearchTerms,
  selectFilters,
  setSelectFilters,
  dateFilters,
  setDateFilters,
  onFilterChange,
  filterSelectTable,
  onSortChange,
  height = "410px",
  setRefreshId,
  setCheked,
  disabledCheckLists,
  filterPosition = "inside",
}: CustomTableProps & {
  onFilterChange?: (filterGroup: FilterGroup) => void;
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(pageSize);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);
  const direction = sortConfig?.direction;
  const [columnOrder, setColumnOrder] = useState(columns);
  const [draggedColumnIndex, setDraggedColumnIndex] = useState<string | null>(
    null,
  );
  const [showDropdown, setShowDropdown] = useState<string | null>(null);
  const [tempSelectFilters, setTempSelectFilters] = useState<
    Record<string, string[]>
  >({});
  const onFilterChangeRef = useRef(onFilterChange);

  // HANDLE PAGINATION START
  useEffect(() => {
    if (setPagiaton) {
      setPagiaton(() => ({
        pageIndex: currentPage,
        pageSize: itemsPerPage,
      }));
    }
  }, [currentPage, itemsPerPage, setPagiaton]);

  // HANDLE FILTER GROUP START
  type FilterCondition = {
    fieldName: string;
    operator:
      | "Equals"
      | "Contains"
      | "In"
      | "Between"
      | "GreaterThanOrEqual"
      | "LessThanOrEqual"
      | "LessThan";
    value: string;
  };

  useEffect(() => {
    onFilterChangeRef.current = onFilterChange;
  }, [onFilterChange]);

  useEffect(() => {
    const filterGroup = {
      operator: "And" as const,
      conditions: [
        ...Object.entries(searchTerms ?? {})
          .filter(([_, value]) => value && value.trim() !== "")
          .map<FilterCondition>(([key, value]) => ({
            fieldName: key,
            operator: "Contains",
            value,
          })),

        ...Object.entries(selectFilters ?? {})
          .filter(([_, value]) => Array.isArray(value) && value.length > 0)
          .map<FilterCondition>(([key, value]) => {
            if (value.length > 1) {
              return {
                fieldName: key,
                operator: "In",
                value: value.join(","),
              };
            } else {
              return {
                fieldName: key,
                operator: "Contains",
                value: value[0] ?? "",
              };
            }
          }),

        ...Object.entries(dateFilters ?? {})
          .filter(([_, value]) => value && value.trim() !== "")
          .flatMap<FilterCondition>(([key, value]) => {
            const startDate = `${value}T00:00:00`;
            const endDate = `${getNextDate(value)}T00:00:00`;
            return [
              {
                fieldName: key,
                operator: "GreaterThanOrEqual",
                value: startDate,
              },
              {
                fieldName: key,
                operator: "LessThan",
                value: endDate,
              },
            ];
          }),
      ] as FilterCondition[],
    };

    onFilterChangeRef.current?.(filterGroup);
  }, [searchTerms, selectFilters, dateFilters]);
  // HANDLE FILTER GROUP END

  // HANDLE FILTER TEXT START
  const handleTextFilterChange = (dataIndex: string, value: string) => {
    setSearchTerms?.((prev) => ({
      ...prev,
      [dataIndex]: value,
    }));
    setCurrentPage(1);
  };

  // HANDLE FILTER SELECT
  const handleSelectFilterChange = (dataIndex: string, values: string[]) => {
    setSelectFilters?.((prev) => ({
      ...prev,
      [dataIndex]: values,
    }));
    setCurrentPage(1);
  };
  // HANDLE FILTER SELECT END

  // HANDLE DATE FILTER
  const handleDateFilterChange = (dataIndex: string, value: string) => {
    setDateFilters?.((prev) => ({
      ...prev,
      [dataIndex]: value,
    }));
    setCurrentPage(1);
  };

  function getNextDate(dateStr: string): string {
    const date = new Date(dateStr);
    date.setDate(date.getDate() + 1);
    return date.toISOString().split("T")[0] ?? "";
  }
  // HANDLE FILTER DATE END

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        event.target instanceof HTMLElement &&
        !event.target.closest(".relative")
      ) {
        setShowDropdown(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // HANDLE SORTING START
  const handleSort = (key: string) => {
    if (key === "action") return;
    setSortConfig((prev) => {
      const newConfig =
        prev?.key === key
          ? {
              key,
              direction:
                prev.direction === "asc" ? ("desc" as const) : ("asc" as const),
            }
          : { key, direction: "asc" as const };
      return newConfig;
    });
  };

  const sort: SortInfo[] | undefined = React.useMemo(
    () =>
      sortConfig
        ? [
            {
              field: sortConfig.key,
              desc: sortConfig.direction === "desc",
            },
          ]
        : undefined,
    [sortConfig],
  );

  useEffect(() => {
    if (onSortChange) {
      onSortChange(sort);
    }
  }, [sort, onSortChange]);
  // HANDLE SORTING END

  // HANDLE MOVING COLUMN START
  const handleDragStart = (
    e: React.DragEvent<HTMLTableHeaderCellElement>,
    dataIndex: string,
  ) => {
    e.dataTransfer.setData("text/plain", dataIndex);
    setDraggedColumnIndex(dataIndex);
  };

  const handleDrop = (
    e: React.DragEvent<HTMLTableHeaderCellElement>,
    targetDataIndex: string,
  ) => {
    const draggedDataIndex = e.dataTransfer.getData("text/plain");

    const draggedIndex = columnOrder.findIndex(
      (col) => col.dataIndex === draggedDataIndex,
    );
    const targetIndex = columnOrder.findIndex(
      (col) => col.dataIndex === targetDataIndex,
    );

    if (draggedIndex === -1 || targetIndex === -1) return;

    const updatedColumnOrder = [...columnOrder];
    const [draggedColumn] = updatedColumnOrder.splice(draggedIndex, 1);
    if (draggedColumn) {
      updatedColumnOrder.splice(targetIndex, 0, draggedColumn);
    }

    setColumnOrder(updatedColumnOrder);
    setDraggedColumnIndex(null);
  };
  // HANDLE MOVING COLUMN END

  // HANDLE RESET
  const handleReset = () => {
    setSearchTerms?.({});
    setSelectFilters?.({});
    setDateFilters?.({});
    setSortConfig(null);
    setTempSelectFilters({});
    setCurrentPage(1);
    if (init) {
      void init();
    }
    if (setRefreshId) {
      setRefreshId(randStr());
    }
  };

  const totalPages = Math.ceil(totalCount! / itemsPerPage);

  return (
    <div className="custom-table">
      {filterPosition === "top" && (
        <div className="mb-2 flex justify-end gap-2">
          {columnOrder.map((column) => (
            <>
              {column.filter !== "none" && (
                <div key={column.dataIndex} className="w-[150px]">
                  <Text className="text-xs font-semibold">{column.title}</Text>
                  <Filter
                    column={column}
                    searchTerms={searchTerms ?? {}}
                    handleTextFilterChange={handleTextFilterChange}
                    selectFilters={selectFilters ?? {}}
                    setSelectFilters={setSelectFilters!}
                    dateFilters={dateFilters ?? {}}
                    setDateFilters={setDateFilters!}
                    handleDateFilterChange={handleDateFilterChange}
                    tempSelectFilters={tempSelectFilters}
                    setTempSelectFilters={setTempSelectFilters}
                    handleSelectFilterChange={handleSelectFilterChange}
                    filterSelectTable={filterSelectTable}
                    showDropdown={showDropdown}
                    setShowDropdown={setShowDropdown}
                  />
                </div>
              )}
            </>
          ))}
        </div>
      )}
      <div className="rounded-lg border border-gray-300">
        <div
          className={`custom-scrollbar min-h-[400px] overflow-y-auto rounded-lg`}
          style={{ maxHeight: height }}
        >
          <table className="min-w-full border-collapse divide-y divide-gray-200">
            <thead className="bg-slate-50">
              <tr>
                {/* {setCheked && <th></th>} */}
                <th className="sticky top-0 z-10 bg-slate-50 px-2 py-2 text-center text-xs font-semibold uppercase tracking-wider text-gray-500">
                  NO
                </th>
                {columnOrder.map((column) => (
                  <th
                    key={column.dataIndex}
                    className={`sticky top-0 z-10 bg-slate-50 px-2 py-2 text-left text-xs font-semibold uppercase tracking-wider text-gray-500 ${
                      column.dataIndex === "action" ? "" : "cursor-move"
                    } ${
                      draggedColumnIndex === column.dataIndex
                        ? "bg-slate-300"
                        : ""
                    }`}
                    draggable={column.dataIndex !== "action"}
                    onDragStart={(e) => handleDragStart(e, column.dataIndex)}
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={(e) => handleDrop(e, column.dataIndex)}
                  >
                    <div className="flex items-center gap-1">
                      <span
                        onClick={() =>
                          column.dataIndex !== "action" &&
                          handleSort(column.dataIndex)
                        }
                        className={
                          column.dataIndex !== "action"
                            ? "cursor-move"
                            : "cursor-pointer"
                        }
                      >
                        {formatTitle(column.title)}
                      </span>
                      {column.dataIndex !== "action" &&
                        column.filter !== "none" && (
                          <span
                            onClick={() => handleSort(column.dataIndex)}
                            className="cursor-pointer text-blue-500"
                            title={
                              direction
                                ? direction === "asc"
                                  ? "Sort Ascending"
                                  : "Sort Descending"
                                : "Sort"
                            }
                          >
                            {direction
                              ? direction === "asc"
                                ? "↑"
                                : "↓"
                              : "↑↓"}
                          </span>
                        )}
                    </div>

                    {filterPosition === "inside" &&
                      column.filter !== "none" && (
                        <Filter
                          column={column}
                          searchTerms={searchTerms ?? {}}
                          handleTextFilterChange={handleTextFilterChange}
                          selectFilters={selectFilters ?? {}}
                          setSelectFilters={setSelectFilters!}
                          dateFilters={dateFilters ?? {}}
                          setDateFilters={setDateFilters!}
                          handleDateFilterChange={handleDateFilterChange}
                          tempSelectFilters={tempSelectFilters}
                          setTempSelectFilters={setTempSelectFilters}
                          handleSelectFilterChange={handleSelectFilterChange}
                          filterSelectTable={filterSelectTable}
                          showDropdown={showDropdown}
                          setShowDropdown={setShowDropdown}
                        />
                      )}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {isLoading ? (
                <tr>
                  <td colSpan={columns.length + 1}>
                    <SkeletonTable rows={pageSize} columns={columns.length} />
                  </td>
                </tr>
              ) : dataSource.length === 0 ? (
                <tr>
                  <td
                    colSpan={columns.length + 1}
                    className="px-2 py-2 text-center text-sm text-gray-500"
                  >
                    <Empty />
                    <Text className="mt-2 text-center">No Data Available.</Text>
                  </td>
                </tr>
              ) : (
                dataSource.map((item, i) => {
                  return (
                    <tr
                      key={String(item[rowKey])}
                      className="transition-colors hover:bg-gray-50"
                    >
                      <td className="px-2 py-1 text-center text-xs">
                        <div className="flex">
                          {setCheked && (
                            <Checkbox
                              size="sm"
                              inputClassName="h-4 w-4"
                              iconClassName="h-4 w-4"
                              className="mr-2"
                              onChange={(e) => {
                                setCheked((prev) => {
                                  if (e.target.checked) {
                                    return [...prev, item];
                                  } else {
                                    return prev.filter((i) => i !== item);
                                  }
                                });
                              }}
                              disabled={disabledCheckLists?.includes(
                                item.id as string,
                              )}
                            />
                          )}
                          {(currentPage - 1) * itemsPerPage + i + 1}.
                        </div>
                      </td>
                      {columnOrder.map((column) => {
                        const value = column.dataIndex
                          .split(".")
                          .reduce<Record<
                            string,
                            unknown
                          > | null>((acc, key) => (acc && typeof acc === "object" && key in acc ? (acc[key] as Record<string, unknown>) : null), item as Record<string, string | number>);

                        return (
                          <td
                            key={column.dataIndex}
                            className="justify-center whitespace-nowrap px-2 py-1 text-xs"
                          >
                            {column.render
                              ? column.render(value, item)
                              : renderCellValue(value)}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <div className="pt-4 text-xs">
          show {String(itemsPerPage * currentPage - itemsPerPage + 1)}-
          {String(
            itemsPerPage * currentPage > totalCount!
              ? totalCount
              : itemsPerPage * currentPage,
          )}{" "}
          of {String(totalCount)} entries
        </div>
        <div className="mt-4">
          <ButtonReset isLoading={isLoading} handleReset={handleReset} />
        </div>
        <PageSizeSelector
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
        />
        <PaginationTable
          totalPages={totalPages}
          pageNumber={currentPage}
          setPageNumber={setCurrentPage}
        />
      </div>
    </div>
  );
}

function Filter(props: FilterProps) {
  return (
    <>
      {props.column.filter === "date" && (
        <DateFilter
          column={props.column}
          dateFilters={props.dateFilters ?? {}}
          setDateFilters={props.setDateFilters}
          handleDateFilterChange={props.handleDateFilterChange}
        />
      )}
      {props.column.filter === "text" && (
        <FreeTextFilter
          column={props.column}
          searchTerms={props.searchTerms ?? {}}
          handleTextFilterChange={props.handleTextFilterChange}
        />
      )}
      {props.column.filter === "select" && (
        <SelectFilter
          column={props.column}
          dataSource={props.filterSelectTable}
          tempSelectFilters={props.tempSelectFilters}
          setTempSelectFilters={props.setTempSelectFilters}
          handleSelectFilterChange={props.handleSelectFilterChange}
          selectFilters={Object.fromEntries(
            Object.entries(props.selectFilters ?? {}).map(([k, v]) => [
              k,
              Array.isArray(v) ? v.join(",") : v,
            ]),
          )}
          setSelectFilters={props.setSelectFilters}
          showDropdown={props.showDropdown}
          setShowDropdown={props.setShowDropdown}
        />
      )}
    </>
  );
}
