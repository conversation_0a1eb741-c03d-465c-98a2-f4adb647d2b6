﻿import type { FilterGroup, PaymentDetailsDto, SortInfo } from "@/client";
import {
  getApiAppPaymentDetailsById,
  postApiAppPaymentDetailsList,
} from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import type { SelectOption } from "rizzui";

export const useApiAppPaymentDetailsById = (id: string, refreshId?: string) => {
  return useQuery({
    queryKey: [QueryNames.getApiAppPaymentDetailsById, id, refreshId],
    queryFn: async () => {
      const { data } = await getApiAppPaymentDetailsById({
        path: { id }, // Replace with your reservation ID
      });
      return data;
    },
  });
};

export const useMasterPaymentDetails = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [
      QueryNames.ListPaymentDetails,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
    ],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppPaymentDetailsList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      return data;
    },
  });
};

export const useMasterPaymentDetailsOptions = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [
      QueryNames.ListPaymentDetails,
      pageIndex,
      pageSize,
      filter,
      sorting,
    ],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppPaymentDetailsList({
        body: {
          sorting: sorting,
          page: 1,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      // GENERATE OPTIONS
      const options: (SelectOption & PaymentDetailsDto)[] =
        data?.items?.map((val) => ({
          label: `${val.sourceType}`,
          value: val.id ?? "",
          ...val,
        })) ?? [];

      // RETURN OPTIONS
      return options;
    },
    enabled: !!filter?.conditions.length, // 👈 hanya fetch jika id tidak falsy (null, undefined, "")
  });
};
