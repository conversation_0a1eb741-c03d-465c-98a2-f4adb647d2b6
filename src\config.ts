﻿export const siteConfig = {
  name: 'Wisma',
  baseLinks: {
    login: '/auth/login',
  },
}

export type siteConfig = typeof siteConfig

export const clientConfig = {
  url: process.env.NEXT_PUBLIC_IMIP_AUTH_URL,
  audience: process.env.NEXT_PUBLIC_IMIP_AUTH_URL,
  client_id: process.env.NEXT_PUBLIC_CLIENT_ID,
  client_secret: process.env.NEXT_PUBLIC_CLIENT_SECRET,
  scope: process.env.NEXT_PUBLIC_SCOPE,
  redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/auth/openiddict`,
  post_logout_redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}`,
  response_type: 'code',
  grant_type: 'authorization_code',
  post_login_route: `${process.env.NEXT_PUBLIC_APP_URL}`,
  code_challenge_method: 'S256',
}
