version: "3"
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_CLIENTVAR: "clientvar"
    working_dir: /app
    restart: always
    ports:
      - "8200:3000"
    networks:
      - imip-app
    image: wisma-prd_1
    environment:
      NEXTAUTH_URL: "http://wisma.imip.co.id" # Add this line
    extra_hosts:
      # - "auth-dev.imip.co.id:${DOMAIN_HOST:-127.0.0.1}"
      - "auth.imip.co.id:${DOMAIN_HOST:-**********}"
      # - "websocket.imip.co.id:${DOMAIN_HOST:-127.0.0.1}"

# Define a network, which allows containers to communicate
# with each other, by using their container name as a hostname
networks:
  imip-app:
    driver: bridge
