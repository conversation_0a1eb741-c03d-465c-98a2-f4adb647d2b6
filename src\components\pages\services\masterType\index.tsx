"use client";
import React, { useEffect, useState } from "react";
import <PERSON>Header<PERSON>ustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useMasterServiceType } from "@/lib/hooks/services/masterServiceType";
import {
  deleteApiAppServiceTypesById,
  postApiAppServiceTypes,
  putApiAppServiceTypesById,
  type CreateUpdateServiceTypesDto,
  type ServiceTypesDto,
} from "@/client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import type { SelectOption } from "rizzui";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function MasterServicesType({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isServicesType, setDataServiceType] = useState<ServiceTypesDto[]>([]);
  const [isStatus, setStatus] = useState<SelectOption | undefined>();
  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 100,
  });

  const { isLoading, data } = useMasterServiceType(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
        status: item.status?.name ?? "",
      }));
      setDataServiceType(mappedData as ServiceTypesDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "name", title: "Name", filter: "text" as const },
    { dataIndex: "status.name", title: "Status", filter: "select" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
    setStatus(undefined);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetRoomType],
    });
  };

  const deleteServiceTypeMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppServiceTypesById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Service type deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetServicesType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Service type will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete service type with id:", id);
        await deleteServiceTypeMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the service type. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const type = isServicesType.find((type) => type.id === id);
    if (type) {
      setIsEditMode(true);
      Object.entries(type).map(([key, value], _index) => {
        if (key === "status") {
          setStatus({
            label: value as string,
            value: value as string,
          });
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createServiceTypeMutation = useMutation({
    mutationFn: async (data: CreateUpdateServiceTypesDto) =>
      postApiAppServiceTypes({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Service type created successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetServicesType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateServiceTypeMutation = useMutation({
    mutationFn: async (data: ServiceTypesDto) => {
      const { id, ...updateData } = data;
      return putApiAppServiceTypesById({
        path: { id: id! },
        body: updateData as CreateUpdateServiceTypesDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Service type updated successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetServicesType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      if (isEditMode) {
        // console.log("Updating service type:", formData);
        await updateServiceTypeMutation.mutateAsync(
          formData as ServiceTypesDto,
        );
      } else {
        // console.log("Creating service type:", formData);
        await createServiceTypeMutation.mutateAsync(
          formData as CreateUpdateServiceTypesDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  if (!can("WismaApp.ServiceType.View")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Services" },
            { name: "Master Services Type" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              isStatus={isStatus}
              setStatus={setStatus}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
