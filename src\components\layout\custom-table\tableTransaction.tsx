import React, { useEffect, useState } from "react";
import ButtonPlus from "@/components/container/button/buttonPlus";
import ButtonMin from "@/components/container/button/buttonMin";
import ButtonDelete from "@/components/container/button/button-delete";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { renderCellValue } from "@/lib/client/helper";
import { DatetimeInput } from "@/components/theme/ui/input-type/dates-input";
import type {
  FieldErrors,
  FieldValues,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import type { ReservationDetailsDto } from "@/client";
import type { CustomTableProps } from "@/interfaces/table/tableType";

export default function TransactionTable({
  columns,
  dataSource,
  rowKey,
  onDelete,
  onQtyChange,
  onDateChange,
  register,
  errors,
  setValue,
  getValues,
  watch,
}: CustomTableProps & {
  onDelete?: (id: number) => void;
  onQtyChange?: (item: number, newQty: number) => Promise<void>;
  onDateChange?: (item: number, newDate: string | Date) => void;
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<ReservationDetailsDto>;
}) {
  const [editableData, setEditableData] = useState<Record<string, unknown>[]>();

  useEffect(() => {
    setEditableData(Array.isArray(dataSource) ? dataSource : [dataSource]);
    if (Array.isArray(dataSource)) {
      setEditableData(dataSource);

      dataSource.forEach((item, index) => {
        if (item.transactionDate) {
          setValue(`transactionDate.${index}`, item.transactionDate);
        }
      });
    }
  }, [dataSource, setValue]);

  const DateChange = (newDate: Date | null, name: string) => {
    if (typeof onDateChange === "function" && newDate) {
      const index = Number(name.split(".")[1]);

      const format = (n: number) => String(n).padStart(2, "0");
      const localDate =
        `${newDate.getFullYear()}-${format(newDate.getMonth() + 1)}-${format(newDate.getDate())} ` +
        `${format(newDate.getHours())}:${format(newDate.getMinutes())}:${format(newDate.getSeconds())}`;

      onDateChange(index, localDate);
    }
  };

  const totalPrice = (editableData ?? []).reduce((total, item) => {
    const price = typeof item.price === "number" ? item.price : 0;
    return total + price;
  }, 0);

  return (
    <div className="custom-table">
      <div className="overflow-x-auto">
        <div className="inline-block min-w-full align-middle">
          <div className="custom-scrollbar max-h-[550px] overflow-y-auto rounded-lg border border-gray-300 shadow ring-1 ring-black ring-opacity-5">
            <table className="min-w-full border-collapse divide-y divide-gray-200">
              <thead className="bg-slate-50">
                <tr>
                  <th className="sticky top-0 z-10 bg-slate-50 px-6 py-2 text-center text-xs font-semibold uppercase tracking-wider text-gray-500">
                    NO
                  </th>
                  {columns.map((column) => (
                    <th
                      key={column.dataIndex}
                      className="sticky top-0 z-10 bg-slate-50 px-6 py-2 text-left text-xs font-semibold uppercase tracking-wider text-gray-500"
                    >
                      {column.title}
                    </th>
                  ))}
                  <th className="sticky top-0 z-10 w-5 bg-slate-50 px-6 py-2 text-center text-xs font-semibold uppercase tracking-wider text-gray-500">
                    #
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {(editableData ?? []).map((item, i) => (
                  <tr
                    key={String(item[rowKey])}
                    className="table-row hover:bg-gray-50"
                  >
                    <td className="px-6 py-1 text-center">{i + 1}.</td>
                    {columns.map((column) => {
                      const value = item[column.dataIndex];

                      return (
                        <td
                          key={column.dataIndex}
                          className={`whitespace-nowrap px-4 py-1 text-sm hover:bg-gray-100 ${
                            column.dataIndex === "price" ? "text-right" : ""
                          }`}
                        >
                          {column.dataIndex === "date" ? (
                            <div className="flex items-center gap-2">
                              <DatetimeInput
                                className="w-44"
                                name={`transactionDate.${i}`}
                                register={register}
                                setValue={setValue}
                                errors={errors}
                                watch={watch}
                                getValues={getValues}
                                required={true}
                                size="sm"
                                onChange={DateChange}
                              />
                            </div>
                          ) : column.dataIndex === "qty" ? (
                            <div className="flex items-center gap-2">
                              <ButtonMin
                                onClick={() =>
                                  typeof onQtyChange === "function" &&
                                  onQtyChange(i, -1)
                                }
                                disabled={(item.qty as number) <= 1}
                              />
                              <span className="p-2 py-1">
                                {String(item.qty)}
                              </span>
                              <ButtonPlus
                                onClick={() =>
                                  typeof onQtyChange === "function" &&
                                  onQtyChange(i, 1)
                                }
                              />
                            </div>
                          ) : column.dataIndex === "price" ? (
                            typeof value === "number" ? (
                              formatCurrencyIDR(value)
                            ) : (
                              "-"
                            )
                          ) : (
                            renderCellValue(value)
                          )}
                        </td>
                      );
                    })}
                    <td className="w-5 px-6 py-1 text-center">
                      <ButtonDelete
                        onClick={() => onDelete?.(i)}
                        title={"Are you sure?"}
                        text={
                          "This action will delete the selected tansaction. Continue?"
                        }
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="sticky bottom-0 bg-slate-50">
                <tr>
                  <th
                    colSpan={columns.length}
                    className="text-medium px-6 py-1 text-center font-semibold uppercase tracking-wider text-gray-500"
                  >
                    GRAND TOTAL
                  </th>
                  <th className="text-medium px-4 py-1 text-end font-semibold uppercase tracking-wider text-gray-500">
                    {formatCurrencyIDR(totalPrice)}
                  </th>
                  <th className="text-medium px-6 py-1 text-end font-semibold uppercase tracking-wider text-gray-500"></th>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
