"use client";
import React, { useEffect, useState } from "react";
import <PERSON>Header<PERSON>ustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/tableV2";
import { <PERSON><PERSON>, Tooltip, } from "rizzui";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import Link from "next/link";
import { PiArrowSquareOutBold } from "react-icons/pi";
import { useApiReport } from "@/lib/hooks/useApiReport";
import { FilterGroup } from "@/client";
import { randStr } from "@/lib/helper/generate-random-string";

export default function MasterReport({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const { can } = useGrantedPolicies();
  const [dataSource, setDataSource] = useState<Record<string, unknown>[]>([])
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const [pagination] = useState({
    pageIndex: 1,
    pageSize: 10,
  });
const filter: FilterGroup = {
    operator: "And",
    conditions: [],
  }
  const { isLoading, data } = useApiReport(pagination.pageIndex,pagination.pageSize, filter, "", [], refreshId);

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        action: (
          <div className="flex justify-center items-center gap-2">
            {can("WismaApp.Report.Edit") && // code for check in
              <Tooltip size="sm" content={"Update"} placement="top">
                <Link href={'masterReport/form/' + item.id} >
                  <Button
                    size="sm"
                    className="bg-blue-500 text-white hover:bg-blue-700"
                  >
                    <PiArrowSquareOutBold className="w-4 h-4" />
                  </Button>
                </Link >
              </Tooltip>
            }
          </div>
        )
      }));
      setDataSource(mappedData);
    }
  }, [data]);

  const columns = [
    { dataIndex: "name", title: "Name", filter: "text" as const },
    { dataIndex: "description", title: "Description", filter: "text" as const },
    { dataIndex: "query", title: "Query", filter: "text" as const },
    { dataIndex: "isActive", title: "Active", filter: "text" as const },
    { dataIndex: "action", title: "Action" },
  ];


  // if(!can("WismaApp.MasterStatus")) return <AccessDeniedLayout />; 
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Config" },
            { name: "Master Report" },
          ]}
        >
          <Link href={"masterReport/form/create"} >
            <Button size="sm" className="bg-green-500">
              New Report
            </Button>
          </Link>
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {/* {isFormVisible && (
          
        )} */}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={dataSource}
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
          setRefreshId={setRefreshId}
        />
        {/* </div> */}
      </div>
    </div>
  );
}
