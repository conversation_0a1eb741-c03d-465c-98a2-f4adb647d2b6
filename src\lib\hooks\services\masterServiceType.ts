import { getApiAppServiceTypes } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "../QueryConstants";

export const useMasterServiceType = (
  pageIndex?: number,
  pageSize?: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [
      QueryNames.GetServicesType,
      pageIndex,
      pageSize,
      filter,
      sorting,
    ],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex && pageSize) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppServiceTypes({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};
