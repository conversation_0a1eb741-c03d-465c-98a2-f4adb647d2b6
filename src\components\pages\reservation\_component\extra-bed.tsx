import React, { useCallback, useEffect, useState } from "react"
import { Input, type SelectOption, Title } from "rizzui"
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete"
import { DateInput, } from "@/components/theme/ui/input-type/dates-input"
import { type UseFormUnregister, type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form"
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR"
import { useApiAppGuestOptions } from "@/lib/hooks/useApiAppGuest"
import { ReservationsDto, RoomDto } from "@/client"
import { countDays } from "@/lib/helper/count-days"

export default function ExtraBed({
  register,
  unregister,
  errors,
  setValue,
  getValues,
  watch,
  roomOptions,
  roomTypeOptions
}: {
  isLoading: boolean
  register: UseFormRegister<FieldValues>
  unregister: UseFormUnregister<FieldValues>
  setValue: UseFormSetValue<FieldValues>
  getValues: UseFormGetValues<FieldValues>
  watch: UseFormWatch<FieldValues>
  errors: FieldErrors<ReservationsDto>
  roomOptions?: SelectOption[] & RoomDto[];
  roomTypeOptions?: SelectOption[];
}) {
  // const [roomTypeOptions, setRoomTypeOptions] = useState<SelectOption[]>()
  // const [roomOptions, setRoomOptions] = useState<SelectOption[]>()

  // GUEST OPTIONS GUEST
  const { data: getGuestOptions } = useApiAppGuestOptions(0, 1000, "",)
  const [guestOptions, setGuestOptions] = useState<SelectOption[]>([])
  useEffect(() => { setGuestOptions(getGuestOptions ?? []) }, [getGuestOptions])

  const init = useCallback(() => {
    register("reservationDetails.1.roomId", { value: getValues("reservationDetails.0.roomId") as string })
    register("reservationDetails.1.rfid", { value: "" })
  }, [])

  useEffect(() => { init() }, [init])

  // MAKE SURE EXTRA BED ROOM NUMBER IS CHANGE WHEN MAIN ROOM NUMBER CHANGE
  useEffect(() => {
    setValue("reservationDetails.1.roomId", getValues("reservationDetails.0.roomId"))
  }, [watch("reservationDetails.0.roomId")])


  // REGISTER FIELDS
  useEffect(() => {
    setValue("reservationDetails.1.statusId", getValues("reservationDetails.0.statusId"))
    setValue("reservationDetails.1.checkInDate", getValues("reservationDetails.0.checkInDate"))
    setValue("reservationDetails.1.checkOutDate", getValues("reservationDetails.0.checkOutDate"))
    // setValue("reservationDetails.1.checkOutDate", getValues("reservationDetails.0.checkOutDate"))
    // setValue("reservationDetails.1.room.roomTypeId", "056df361-af86-af76-c838-3a19735e8b23") // ID FOR EXTRA BED
    setValue("reservationDetails.1.roomTypeId", getValues("reservationDetails.0.roomTypeId"))
    setValue("reservationDetails.1.rooomId", getValues("reservationDetails.0.rooomId"))
    setValue("reservationDetails.1.criteriaId", getValues("reservationDetails.0.criteriaId"))
    setValue("reservationDetails.1.isExtraBed", true)
    return () => {
      // UNREGISETED FIELDS WHEN COMPONENT UNMOUNTED
      unregister("reservationDetails.1")
    }
  }, [])

  // UPDATE DAYS IF CHECK IN/OUT CHANGES
  const countPrice = () => {
    const days = countDays(new Date(getValues('reservationDetails.1.checkInDate') as string), new Date(getValues('reservationDetails.1.checkOutDate') as string));
    setValue("reservationDetails.1.room.days", days);
    setValue("reservationDetails.1.price", (500000 * days))
    console.log('in', getValues('reservationDetails.1.checkInDate'));
    console.log('out', getValues('reservationDetails.1.checkOutDate'));
    console.log('_____________');
  }
  useEffect(() => {
    void countPrice()
  }, []);

  const identityNumber = watch("reservationDetails.1.guest.identityNumber") as string
  useEffect(() => {
    const selectedOpt = guestOptions.find(opt => opt.value == identityNumber)
    if (selectedOpt?.fullname) {
      setValue("reservationDetails.1.guestId", selectedOpt.id)
      setValue("reservationDetails.1.guest.fullname", selectedOpt.fullname)
      setValue("reservationDetails.1.guest.nationality", selectedOpt.nationality)
      setValue("reservationDetails.1.guest.city", selectedOpt.city)
      setValue("reservationDetails.1.guest.phoneNumber", selectedOpt.phoneNumber)
      setValue("reservationDetails.1.guest.companyName", selectedOpt.companyName)
    } else {
      setValue("reservationDetails.1.guestId", null)
      setValue("reservationDetails.1.guest.fullname", null)
      setValue("reservationDetails.1.guest.nationality", null)
      setValue("reservationDetails.1.guest.city", null)
      setValue("reservationDetails.1.guest.phoneNumber", null)
      setValue("reservationDetails.1.guest.companyName", null)
    }
  }, [identityNumber])

  const onChange = () => {
    const identityNumber = getValues("reservationDetails.1.guest.identityNumber") as string
    const tempOpt: SelectOption = { label: identityNumber, value: 0, id: 0 }
    setGuestOptions(prev => [tempOpt, ...prev.slice(1)])
  }

  return (
    <div className="p-4 border-2 rounded bg-gray-50 mt-5">
      <div className="grid grid-cols-1 gap-2">
        {/* ROOM INFORMATION */}
        <Title as="h6" className="mb-0">
          Extra Bed
        </Title>
        <div className="grid grid-cols-12 gap-2">
          <Input
            label="Type"
            disabled={true}
            value={getValues("reservationDetails.0.room.roomTypeName") as string}
            className="col-span-2"
            size="sm"
          />
          <Input
            label="Room"
            disabled={true}
            value={getValues("reservationDetails.0.room.roomNumber") as string}
            className="w-full"
            size="sm"
          />
          <DateInput
            className="col-span-3"
            label={"Check In Date"}
            name={"reservationDetails.1.checkInDate"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            // inline={true}
            minDate={watch("reservationDetails.0.checkInDate") as Date | undefined}
            maxDate={watch("reservationDetails.1.checkOutDate") as Date | undefined}
            size="sm"
            onChange={()=> void countPrice()}
          />
          <DateInput
            className="col-span-3"
            label={"Check Out Date"}
            name={"reservationDetails.1.checkOutDate"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            required={true}
            // inline={true}
            minDate={watch("reservationDetails.1.checkInDate") as Date | undefined}
            maxDate={watch("reservationDetails.0.checkOutDate") as Date | undefined}
            size="sm"
            onChange={()=> void countPrice()}
          />
          <Input
            label="Days"
            size="sm"
            readOnly={true}
            // disabled={isLoading}
            placeholder="Please fill in Days"
            {...register("reservationDetails.1.room.days", { required: true })}
            className="col-span-1"
          />
          <CurrencyIDR
            className="col-span-2"
            label="Rate"
            name="reservationDetails.1.price"
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            size="sm"
          />
        </div>
        {/* GUEST INFORMATION */}
        <Title as="h6" className="mb-0 pt-5">
          Guest Information
        </Title>
        <div className="grid grid-cols-3 gap-2">
          <AutocompleteSelect
            label={"Guest Identity Number"}
            name={"reservationDetails.1.guest.identityNumber"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            onChange={onChange}
            options={guestOptions}
            required={true}
            size="sm"
          />
          <Input
            label="Guest Name"
            // disabled={isLoading}
            placeholder="Please fill in Guest Name"
            {...register("reservationDetails.1.guest.fullname", { required: true })}
            error={errors.reservationDetails?.[1]?.guest?.fullname ? "Guest Name is required" : undefined}
            className="w-full"
            size="sm"
          />
          <Input
            label="Guest Company"
            // disabled={isLoading}
            placeholder="Please fill in Guest Company"
            {...register("reservationDetails.1.guest.companyName", { required: true })}
            error={errors.reservationDetails?.[1]?.guest?.companyName ? "Guest Company is required" : undefined}
            className="w-full"
            size="sm"
          />
          {/* <Input
            label="Guest Identity Number"
            // disabled={isLoading}
            placeholder="Please fill in Guest Identity Number"
            {...register("reservationDetails.1.guest.identityNumber", { required: true })}
            error={errors.reservationDetails?.[1]?.guest?.identityNumber ? "Guest Identity Number is required" : undefined}
            className="w-full"
          /> */}
          <Input
            label="Guest Nationality"
            // disabled={isLoading}
            placeholder="Please fill in Guest Nationality"
            {...register("reservationDetails.1.guest.nationality", { required: true })}
            error={errors.reservationDetails?.[1]?.guest?.nationality ? "Guest Nationality is required" : undefined}
            className="w-full"
            size="sm"
          />
          <Input
            label="Guest City"
            size="sm"
            // disabled={isLoading}
            placeholder="Please fill in Guest City"
            {...register("reservationDetails.1.guest.city", { required: true })}
            error={errors.reservationDetails?.[1]?.guest?.city ? "Guest City is required" : undefined}
            className="w-full"
          />
          <Input
            label="Guest Phone Number"
            // disabled={isLoading}
            placeholder="Please fill in Guest Phone Number"
            {...register("reservationDetails.1.guest.phoneNumber", { required: true })}
            error={errors.reservationDetails?.[1]?.guest?.phoneNumber ? "Guest Phone Number is required" : undefined}
            className="w-full"
            size="sm"
          />
        </div>
      </div>
    </div>
  )
}
