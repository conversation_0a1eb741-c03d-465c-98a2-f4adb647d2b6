export default function ChickenIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" {...props}>
      <path
        fill="#D87E35"
        d="M15.45 9.057c.517 1.586 2.912 1.132 2.912 2.657 0 1.393-4.298 2.257-5.694 2.33l-5.822.107c-3.729 0-6.431-1.017-6.431-4.073 0-3.056 2.757-6.365 6.699-6.365 3.723 0 7.386 2.425 8.336 5.344Z"
      />
      <path
        fill="#C67030"
        d="M16.925 10.335a.665.665 0 0 0-.73.144c-1.003 1.021-4.22 1.64-5.392 1.7l-5.821.108c-1.703 0-3.192-.212-4.307-.734.76 1.918 3.122 2.598 6.171 2.598l5.822-.108c1.396-.072 5.694-.936 5.694-2.329 0-.829-.708-1.073-1.437-1.379Z"
      />
      <path
        fill="#E2E2E2"
        d="M17.187 3.895c-.206.28-.166.619-.097.853a.44.44 0 0 1-.163.482l-2.585 1.873a.701.701 0 1 0 .83 1.13l2.561-1.906a.44.44 0 0 1 .508-.011c.203.136.514.276.843.163.431-.148.612-.626.431-1.043a.824.824 0 0 0-.76-.505.196.196 0 0 1-.16-.082.196.196 0 0 1-.03-.177.824.824 0 0 0-.254-.876c-.344-.298-.854-.268-1.124.099Z"
      />
      <path
        fill="#E88C38"
        d="M7.69 12.856c1.073 1.462 3.197 1.726 4.744.59.728-.535 1.17-1.263 1.448-2.129.37-1.15.91-2.077 1.811-2.905a.589.589 0 0 0 .098-.78l-.69-.94a.589.589 0 0 0-.774-.141c-1.06.612-2.107.85-3.314.858-.91.006-1.737.21-2.465.744-1.547 1.136-1.931 3.242-.858 4.703Z"
      />
      <path
        fill="#D38231"
        d="m15.79 7.631-.69-.94c-.055-.075-.16-.113-.28-.13a.961.961 0 0 0-.848.33c-.723.825-1.421 1.907-1.71 2.806-.277.866-.72 1.595-1.447 2.129-1.061.779-2.393.9-3.471.427.09.21.205.412.346.603 1.073 1.462 3.197 1.726 4.744.59.728-.535 1.17-1.263 1.448-2.129.37-1.15.91-2.077 1.811-2.905a.589.589 0 0 0 .098-.78Z"
      />
      <path
        fill="#E2E2E2"
        d="M19.44 13.92H.56a.56.56 0 1 0 0 1.12h1.094c.14 0 .265.084.318.213l.192.469c.179.438.605.725 1.08.725h13.513c.474 0 .9-.287 1.08-.725l.191-.47a.343.343 0 0 1 .318-.213h1.094a.56.56 0 1 0 0-1.12Z"
      />
      <path
        fill="#D8D6D4"
        d="M19.44 13.92H17.7a1.12 1.12 0 0 1-1.12 1.12H1.665a.343.343 0 0 1 .307.213l.16.39h13.441c-.015.03-.03.06-.043.092l-.136.335a.606.606 0 0 1-.561.377h1.924c.474 0 .9-.287 1.08-.725l.191-.47a.343.343 0 0 1 .318-.213h1.094a.56.56 0 1 0 0-1.12Z"
      />
    </svg>
  );
}
