import React from "react";
import { PiMinusCircle } from "react-icons/pi";

interface ButtonMinProps {
  onClick: () => void;
  disabled?: boolean;
  type?: "button" | "submit" | "reset" | undefined;
}

export default function ButtonMin({ onClick, disabled, type }: ButtonMinProps) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      type={type ?? "button"}
      className={`${disabled ? "cursor-not-allowed" : "hover:scale-125 hover:text-red-700"
        }`}
    >
      <PiMinusCircle
        className={`h-5 w-5 transition-transform duration-200 ${disabled ? "text-gray-400" : "text-red-500"
          }`}
      />
    </button>
  );
}
