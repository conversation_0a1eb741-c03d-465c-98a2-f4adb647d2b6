import React, { useEffect, useRef, useState } from "react";
import { HotTable } from "@handsontable/react-wrapper";
// import "handsontable/dist/handsontable.full.min.css";
import 'handsontable/styles/handsontable.css';
import 'handsontable/styles/ht-theme-main.css';
import { type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import { type Reservation } from "@/interfaces/reservations/reservation";
import type Handsontable from "handsontable";
import { Button, Modal, type SelectOption, Text } from "rizzui";
import { type ReservationDetail } from "@/interfaces/reservations/reservationDetail";
import { countNights } from "@/lib/helper/count-nights";
import { type ColumnSettings } from "node_modules/handsontable/settings";
import { DateInput } from "./input-type/dates-input";
import { createRoot } from "react-dom/client";
import { registerAllCellTypes } from "handsontable/cellTypes";
import { useMasterRoomTypesOptions } from "@/lib/hooks/useMasterRoomTypes";
import { useMasterRoomOptions } from "@/lib/hooks/useMasterRoom";
import { useApiAppGuestOptions } from "@/lib/hooks/useApiAppGuest";
import { FilterGroup, ReservationDetailsDto, RoomDto, RoomStatusDto } from "@/client";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { formatDate } from "@/lib/helper/format-date";

interface HotTableWithInstance extends React.ComponentRef<typeof HotTable> {
  hotInstance: Handsontable.Core;
}

// ON TESTING JADI MASIH TERHAMBUR
export function Handsontables({
  isLoading = false,
  register,
  errors,
  setValue,
  getValues,
  watch,
}: {
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<Reservation>;
}) {
  // OPTIONS
  const filterRooms: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "roomStatus.name",
        operator: "Equals",
        value: "READY"
      }
    ]
  }
  const { data: guestOptions } = useApiAppGuestOptions(0, 1000, "",)
  const { data: roomOptions } = useMasterRoomOptions(0, 1000, filterRooms);
  const { data: roomTypeOptions } = useMasterRoomTypesOptions(0, 1000, "",);

  const hotRef = useRef<HotTableWithInstance>(null);
  registerAllCellTypes();

  // Struktur kolom untuk pemetaan data
  const columns: ColumnSettings[] = [
    {
      type: "text", width: 35, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const container = document.createElement("div");
        td.appendChild(container);
        createRoot(container).render(
          <center>{String(row + 1)}</center>
        );
      },
    },
    {
      data: "guestIdentityNumber",
      type: "dropdown",
      width: 160,
      source: guestOptions?.map(opt => opt.label) ?? [],
    },
    {
      data: "guestFullname", type: "text", width: 100, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
        const identityNumber = rowData.guestIdentityNumber;
        const fullName = guestOptions?.find(opt => opt.label === identityNumber)?.fullname as string ?? "";
        td.textContent = fullName;
      }
    },
    {
      data: "guestCompanyName", type: "text", width: 100, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
        const identityNumber = rowData.guestIdentityNumber;
        const companyName = guestOptions?.find(opt => opt.label === identityNumber)?.companyName as string ?? "";
        td.textContent = companyName;
      }
    },
    {
      data: "guestPhoneNumber", type: "text", width: 100, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
        const identityNumber = rowData.guestIdentityNumber;
        const phoneNumber = guestOptions?.find(opt => opt.label === identityNumber)?.phoneNumber as string ?? "";
        td.textContent = phoneNumber;
      }
    },
    {
      data: "checkInDate",
      type: "date",
      width: 100,
      dateFormat: 'YYYY-MM-DD',
      correctFormat: true,
    },
    {
      data: "checkOutDate",
      type: "date",
      width: 100,
      dateFormat: 'YYYY-MM-DD',
      correctFormat: true,
    },
    {
      data: "roomTypeName",
      type: "select",
      width: 140,
      selectOptions: roomTypeOptions?.map(opt => opt.label) ?? [],
    },
    {
      data: "roomName",
      type: "select",
      width: 100,
      selectOptions: roomOptions?.map(opt => opt.label) ?? [],
    },
    {
      data: "roomStatus", type: "text", width: 100, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
        const roomName = rowData?.roomName;
        const roomStatus = roomOptions?.find(opt => opt.label === roomName)?.roomStatus as RoomStatusDto ?? {} as RoomStatusDto;
        td.textContent = roomStatus?.name ?? "N/A";
      }
    },
    {
      data: "price", type: "text", width: 100, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const rowData = instance.getSourceDataAtRow(row) as Record<string, string>;
        const nights = rowData?.nights ?? 1;
        const roomName = rowData?.roomName;
        const roomPrice = roomOptions?.find(opt => opt.label === roomName)?.price as number ?? 0;
        td.textContent = formatCurrencyIDR(Number(roomPrice) * Number(nights)) ?? "N/A";
      }
    },
    {
      data: "nights", type: "text", width: 120, readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        td.textContent = String(instance.getDataAtCell(row, instance.propToCol("nights")) || "1");
      }
    },
    {
      data: "arrivalDate",
      type: "text",
      width: 100,
      readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        td.textContent = formatDate(watch('arrivalDate') as Date, "date");
      }
    },
    {
      width: 30,
      readOnly: true,
      renderer: (instance: Handsontable.Core, td: HTMLElement, row: number) => {
        td.innerHTML = "";
        const container = document.createElement("div");
        td.appendChild(container);
        createRoot(container).render(
          <button style={{ cursor: "pointer" }} onClick={() => { instance.alter("remove_row", row); }}>
            ❌
          </button>
        );
      },
    }
  ];

  const [data, setData] = useState<ReservationDetail[]>([{} as ReservationDetail]);

  // Menambahkan baris baru dengan default values
  const addRow = () => {
    setData((prevData) => {
      // // console.log("prevData", prevData);
      if (prevData)
        // Mengambil data dari getValues dan menambahkan baris baru
        return [...prevData, {}] as ReservationDetail[]
      return [{} as ReservationDetail];
    });
  };

  // Mengambil nilai dalam format objek
  const getValuess = async () => {
    let form = {} as ReservationDetailsDto[];
    if (hotRef.current) {
      const hot = hotRef.current.hotInstance; // ✅ Property 'hotInstance' does not exist on type 'HotTable'.ts(2339)
      const tableData = hot.getData() as [];
      // // console.log("tableData:", tableData);
      // Konversi array of arrays ke array of objects
      const formattedData = tableData.map((row) =>
        Object.fromEntries(columns.map((col, index) => [col.data, row[index] ?? ""])) as Record<string, string> // Unsafe return of an `any` typed value.eslint@typescript-eslint/no-unsafe-return
      );
      // // console.log("Formatted Data:", formattedData);
      // SET FORM VALUE
      form = formattedData.map((value, key) => {
        const room = roomOptions?.find(opt => opt.label === value.roomName);
        return {
          checkInDate: `${value.checkInDate}T00:00:00.000Z`,
          checkOutDate: `${value.checkOutDate}T00:00:00.000Z`,
          price: Number(room?.price ?? 0) * Number(value?.nights ?? "1"),
          guestId: guestOptions?.find(opt => opt.label === value.guestIdentityNumber)?.id as string ?? "",
          roomId: room?.id as string ?? "",
          statusId: getValues('reservationDetailsStatus') as string ?? "",
          rfid: "",
        }
      })
    }
    // // console.log("form:", form);
    return form;
  };

  useEffect(() => {
    let tempDatas = getValues("reservationDetails") as ReservationDetail[];
    tempDatas = tempDatas?.map((e) => ({
      ...e,
    }));
    setData(tempDatas);
    // // console.log('reservationDetails', getValues("reservationDetails"))
  }, [watch])

  const handleAfterChange = async (changes: Handsontable.CellChange[] | null, source: Handsontable.ChangeSource) => {
    if (source === "edit") {
      if (changes) {
        if (hotRef.current) {
          const hot = hotRef.current.hotInstance; // ✅ Property 'hotInstance' does not exist on type 'HotTable'.ts(2339)
          const tableData = hot.getData() as [];
          for (const [row, prop, oldVal, newVal] of changes) {
            // set nights value
            if (prop === 'checkInDate' || prop === 'checkOutDate') {
              const rowData = hot.getSourceDataAtRow(row) as ReservationDetailsDto;
              const dateIn = new Date(rowData?.checkInDate ?? "");
              const dateOut = new Date(rowData?.checkOutDate ?? "");
              const nights = Math.max(1, countNights(dateIn, dateOut));
              hot.setDataAtCell(row, hot.propToCol("nights"), nights, "nights-computed");
            }
          }
        }
      }
      const reservationDetails = await getValuess();
      // // console.log('reservationDetails', reservationDetails);
      setValue("reservationDetails", reservationDetails)
    }
    const allData = getValues();
    // // console.log('allData', allData);
  };

  return (
    <div>
      <h5>Guest Informations</h5>
      <div className="overflow-scrollable">
        <HotTable
          ref={hotRef}
          colHeaders={["No", "Identity", "Name", "Company", "Contact", "Check In", "Check Out", "Room Type", "Room Number", "Room Status", "Rate", "Nights", "Arr. Date", "#"]}
          data={data}
          columns={columns}
          autoWrapRow={true}
          autoWrapCol={true}
          height="350"
          fixedColumnsLeft={5}
          licenseKey="non-commercial-and-evaluation"
          copyPaste={true}
          afterChange={handleAfterChange}
        />
      </div>
      <div className="mt-2 flex gap-2">
        <Button onClick={addRow} size="sm">Add Row</Button>
        <Button onClick={getValuess} size="sm">Get Values</Button>
      </div>
    </div>
  );
}



// const ModalDateInput = ({
//   label,
//   name,
//   register,
//   setValue,
//   getValues,
//   watch,
//   openModal,
//   maxDate,
//   minDate,
// }: {
//   label: string;
//   name: string;
//   register: UseFormRegister<FieldValues>;
//   setValue: UseFormSetValue<FieldValues>;
//   getValues: UseFormGetValues<FieldValues>;
//   watch?: UseFormWatch<FieldValues>;
//   openModal: boolean;
//   maxDate?: Date;
//   minDate?: Date;
// }) => {
//   const [modalState, setModalState] = useState(false);

//   return (
//     <>
//       <Text onClick={() => setModalState(openModal)} className="text-sm h-[1.2rem]">
//         {getValues(name) && formatDate(getValues(name) as Date, "date")}
//       </Text>
//       <Modal
//         isOpen={modalState}
//         onClose={() => setModalState(false)}
//         size={"sm"}
//       >
//         <div className="m-auto p-2 text-center">
//           <Text className="font-mendium text-md">
//             {label}
//           </Text>
//           <div className="flex justify-center">
//             <DateInput
//               label=""
//               name={name}
//               register={register}
//               setValue={setValue}
//               watch={watch}
//               getValues={getValues}
//               required={true}
//               size="sm"
//               errors={{}}
//               maxDate={maxDate}
//               minDate={minDate}
//               popperPlacement="right"
//               inline={true}
//             />
//           </div>
//         </div>
//       </Modal>
//     </>
//   );
// }