import React from "react";
import { Input } from "rizzui";
import { PiMagnifyingGlass } from "react-icons/pi";

interface SearchProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

export default function Search({ searchTerm, setSearchTerm }: SearchProps) {
  return (
    <Input
      type="search"
      placeholder="Search..."
      // size="sm"
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      clearable
      suffix={<PiMagnifyingGlass className="h-4 w-4 text-gray-600" />}
    />
  );
}
