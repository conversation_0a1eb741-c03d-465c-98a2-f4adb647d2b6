import { clientConfig } from '@/config'
import { getSession } from '@/lib/actions'
import { getClientConfig } from '@/lib/session-utils'
import * as client from 'openid-client'
/**
 * Handles the GET request for the login route.
 *
 * This function initiates the PKCE (Proof Key for Code Exchange) flow for OAuth2 authentication.
 * It generates a code verifier and code challenge, constructs the authorization URL with the necessary parameters,
 * and redirects the user to the authorization endpoint.
 *
 * @returns {Promise<Response>} A promise that resolves to a Response object that redirects the user to the authorization URL.
 */
export async function GET() {
  try {
    const session = await getSession()

    // Generate new PKCE code verifier and challenge
    const code_verifier = client.randomPKCECodeVerifier()
    // console.log('Generated PKCE code_verifier with length:', code_verifier.length)

    const code_challenge = await client.calculatePKCECodeChallenge(code_verifier)
    // console.log('Generated PKCE code_challenge with length:', code_challenge.length)

    // Get OpenID client configuration
    const openIdClientConfig = await getClientConfig()
    const metadata = openIdClientConfig.serverMetadata()

    // console.log('OpenID Client Config endpoints:', {
    //   token_endpoint: metadata.token_endpoint ?? 'undefined',
    //   authorization_endpoint: metadata.authorization_endpoint ?? 'undefined',
    // })

    // Validate we have all required endpoints
    if (!metadata.authorization_endpoint) {
      throw new Error('Missing authorization endpoint in OpenID configuration')
    }

    let tenantId = session.tenantId

    if (
      !tenantId ||
      tenantId === 'default' ||
      (typeof tenantId === 'object' && Object.keys(tenantId).length === 0)
    ) {
      tenantId = ''
    }

    // // console.log('Login route: tenantId:', tenantId)

    // Prepare authorization parameters
    const parameters: Record<string, string> = {
      client_id: clientConfig.client_id!,
      response_type: 'code',  // Explicitly set response_type
      redirect_uri: clientConfig.redirect_uri,
      scope: clientConfig.scope!,
      code_challenge,
      code_challenge_method: clientConfig.code_challenge_method,
      __tenant: tenantId,
    }

    // Generate state for CSRF protection
    const state = client.randomState()
    parameters.state = state

    // console.log('Auth request parameters:', {
    //   redirect_uri: parameters.redirect_uri,
    //   code_challenge_method: parameters.code_challenge_method,
    //   code_challenge_length: code_challenge.length,
    //   state_length: state.length,
    // })

    // Build authorization URL manually to avoid request objects
    const authEndpoint = metadata.authorization_endpoint
    if (!authEndpoint) {
      throw new Error('Missing authorization endpoint in OpenID configuration')
    }
    const url = new URL(authEndpoint)

    // Add all parameters to the URL
    Object.entries(parameters).forEach(([key, value]) => {
      if (value) {
        url.searchParams.append(key, value)
      }
    })

    // console.log('Authorization URL:', url.href)

    // Save code_verifier and state in session
    session.code_verifier = code_verifier
    session.state = state

    // Ensure session is saved before redirecting
    await session.save()

    // console.log('Session saved with:', {
    //   code_verifier_length: code_verifier.length,
    //   state_length: state.length,
    // })

    return Response.redirect(url.href)
  } catch (error) {
    console.error('Error in login route:', error)
    return new Response(
      JSON.stringify({
        error: 'Login initialization failed',
        message: 'There was an error starting the login process. Please try again.',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}
