"use client";
import React, { useEffect, useState } from "react";
import { Button, Checkbox, Input, Text, Textarea } from "rizzui";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { MasterReportParameters } from "./_component/parameters";
import { type FieldValues, useFieldArray, useForm, type UseFormSetValue } from "react-hook-form";
import { type ReportParameters } from "@/interfaces/form/reportParameters";
import { PiPlus } from "react-icons/pi";
import { MasterReportExcelHeader } from "./_component/excel-header";
import { MasterReportHeaderRows } from "./_component/header-rows";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import Link from "next/link";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { CreateUpdateReportDto, postApiReport, putApiReportById, ReportParameterDto } from "@/client";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useRouter } from "next/navigation";
import { useApiReportById } from "@/lib/hooks/useApiReport";
import swal from "sweetalert"
import { MasterReportColumnConfigs } from "./_component/column-configs";
import { randStr } from "@/lib/helper/generate-random-string";
import { swalError } from "@/lib/helper/swal-error";

export default function FormReport({ id }: { id: string }) {
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const router = useRouter();

  // --INITIATE USE FORM--
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    control,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const { append, remove } = useFieldArray({
    control: control,
    name: `parameters`,
  });
  const { append: appendRowHeaders, remove: removeRowHeaders } = useFieldArray({
    control: control,
    name: `excelHeaderConfig.headerRows`,
  });
  const { append: appendColumnConfigs, remove: removeColumnConfigs } = useFieldArray({
    control: control,
    name: `excelHeaderConfig.columnConfigs`,
  });

  const [refreshId, setRefreshId] = useState<string>(randStr());

  // --GET REPORT--
  const { data: dataReport } = useApiReportById(id, refreshId)
  useEffect(() => {
    if (dataReport?.id) {
      Object.entries(dataReport ?? {}).forEach(([key, value]) => {
        setValue(key, value);
      });
      const parameters = (() => { try { return JSON.parse(dataReport.parameters ?? "[]") as ReportParameterDto[] } catch { return [] } })();
      setValue('parameters', parameters)
      setValue('excelHeaderConfig', JSON.parse(dataReport.excelHeaderConfig ?? "[]"))
    }
  }, [dataReport])
  // console.log('dataReport', dataReport);
  // console.log('getValues', dataReport);

  // --- POST/ PUT ---
  const updateReportMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateReportDto) => {
      const { id } = dataMutation;
      return putApiReportById({
        path: { id: id! },
        body: dataMutation,
      })
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Report Updated Successfully",
        icon: "success",
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.postApiAppReport] });
      router.push('/config/masterReport');
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const createReportMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateReportDto) =>
      postApiReport({
        body: dataMutation,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Report Created Successfully",
        icon: "success",
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.postApiAppReport] });
      router.push('/config/masterReport');
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  async function onSubmit(formData: Record<string, object>) {
    // console.log('formData', formData)
    const data = {
      ...formData,
      parameters: JSON.stringify(formData.parameters),
      excelHeaderConfig: JSON.stringify(formData.excelHeaderConfig),
    }
    // console.log('data', data)
    // return;
    if (formData.id) await updateReportMutation.mutateAsync(data as CreateUpdateReportDto);
    else await createReportMutation.mutateAsync(data as CreateUpdateReportDto);
  }

  useEffect(() => {
    if (setValue) {
      const values: Record<string, string | number> = {};
      Object.keys(values).forEach((key) => {
        setValue(key, values[key] ?? "");
      });
    }
  }, [setValue]);

  if (!can("WismaApp.MasterStatus")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container px-10"}>
      <PageHeaderCustom
        breadcrumb={[
          { name: "Home", href: "/dashboard" },
          { name: "Config" },
          { name: "Master Report", href: "/config/masterReport" },
          { name: dataReport?.name ?? "New Report", href: "/config/masterReport" },
        ]}
      >
      </PageHeaderCustom>
      <div className="rounded-lg bg-white p-4">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="">
              <div className="grid grid-cols-12 gap-2">
                <div className="col-span-6">
                  <Input
                    size="sm"
                    label="Name"
                    placeholder="Please fill in Name"
                    {...register("name", { required: true })}
                    error={errors.name ? "Name is required" : undefined}
                    className="w-full"
                  />
                </div>
                <div className="col-span-5">
                  <Input
                    size="sm"
                    label="Query Type"
                    placeholder="Please fill in Query Type"
                    {...register("queryType", { required: true })}
                    error={errors.queryType ? "Query Type is required" : undefined}
                    className="w-full"
                  />
                </div>
                <div className="col-span-1">
                  <Text as="p" className={"mb-1.5 font-medium text-xs"}>
                    Active
                  </Text>
                  <Checkbox
                    size="lg"
                    {...register(`isActive`)}
                  />
                </div>
                <div className="col-span-12">
                  <Textarea
                    size="sm"
                    label="Query"
                    placeholder="Please fill in Query"
                    {...register("query", { required: true })}
                    error={errors.query ? "Query is required" : undefined}
                    className="w-full"
                  />
                </div>
                <div className="col-span-12">
                  <Textarea
                    size="sm"
                    label="Description"
                    placeholder="Please fill in Description"
                    {...register("description", { required: true })}
                    error={errors.description ? "Description is required" : undefined}
                    className="w-full"
                  />
                </div>

                {/* PARAMETERS */}
                <div className="col-span-12 p-4 border-2 rounded bg-gray-50">
                  <div className="flex gap-2">
                    <Text as="b">
                      Paremeters
                    </Text>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        append({} as never[]);
                      }}
                      className="-mt-2"
                    >
                      Add
                      <PiPlus className="ml-2" />
                    </Button>
                  </div>
                  <div className="">
                    {(watch('parameters') as ReportParameters[])?.map((e, i) => {
                      return (
                        <div key={i}>
                          <MasterReportParameters
                            register={register}
                            setValue={setValue as unknown as UseFormSetValue<FieldValues>}
                            getValues={getValues}
                            index={i}
                            remove={remove}
                          />
                        </div>
                      )
                    })
                    }
                  </div>
                </div>

                {/* BUTTONS */}
                <div className="col-span-12 flex items-end justify-end gap-2 py-4">
                  <Button
                    size="sm"
                    type="submit"
                    className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${getValues("id") ? "bg-blue-500" : "bg-green-500"}`}
                  >
                    {getValues("id") ? "Update" : "Create"}
                  </Button>
                  <Link href={"/config/masterReport"}>
                    <Button
                      type="button"
                      size="sm"
                      // disabled={isLoading}
                      variant="outline"
                      className="rounded-lg px-4 py-2 disabled:bg-gray-400"
                    >
                      Quit
                    </Button>
                  </Link>
                  {/* <Button
                    size="sm"
                    type="button"
                    className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-green-500`}
                    onClick={() => { console.log('getValues', getValues()) }}
                  >
                    Get Values
                  </Button> */}
                </div>
              </div>
            </div>
            <div className="col-span-2">
              <div className="">
                {/* HEADER STYLE */}
                <div className="col-span-4 p-4 border-2 rounded bg-gray-50">
                  <MasterReportExcelHeader
                    register={register}
                    setValue={setValue as unknown as UseFormSetValue<FieldValues>}
                    getValues={getValues}
                  />

                  {/* HEADER ROWS */}
                  <div className="p-4 mt-3">
                    <div className="flex gap-2">
                      <Text as="b">
                        Header Rows
                      </Text>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          appendRowHeaders({} as never[]);
                        }}
                        className="-mt-2"
                      >
                        Add
                        <PiPlus className="ml-2" />
                      </Button>
                    </div>
                    <div className="">
                      {(watch('excelHeaderConfig.headerRows') as ReportParameters[])?.map((e, i) => {
                        return (
                          <div key={i}>
                            <div className="flex">
                              <MasterReportHeaderRows
                                register={register}
                                setValue={setValue as unknown as UseFormSetValue<FieldValues>}
                                getValues={getValues}
                                index={i}
                                control={control}
                                watch={watch}
                                remove={removeRowHeaders}
                              />
                            </div>
                          </div>
                        )
                      })
                      }
                    </div>
                  </div>

                  {/* COLUMN CONFIGS */}
                  <div className="p-4 mt-3">
                    <div className="flex gap-2">
                      <Text as="b">
                        Column Configs
                      </Text>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          appendColumnConfigs({} as never[]);
                        }}
                        className="-mt-2"
                      >
                        Add
                        <PiPlus className="ml-2" />
                      </Button>
                    </div>
                    <div className="">
                      {(watch('excelHeaderConfig.columnConfigs') as ReportParameters[])?.map((e, i) => {
                        return (
                          <div key={i}>
                            <div className="flex">
                              <MasterReportColumnConfigs
                                register={register}
                                setValue={setValue as unknown as UseFormSetValue<FieldValues>}
                                getValues={getValues}
                                index={i}
                                remove={remove}
                              />
                            </div>
                          </div>
                        )
                      })
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
