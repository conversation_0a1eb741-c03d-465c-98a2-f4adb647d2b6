'use client';

import <PERSON><PERSON><PERSON>er<PERSON>ustom from "@/app/shared/page-header-custom";
import React, { useEffect, useRef } from "react";
import { type FieldValue, useForm } from "react-hook-form";
import { <PERSON><PERSON> } from "rizzui";
import { type Reservation } from "@/interfaces/reservations/reservation";
import ReservationGroupDetail from "./_component/reservation-group-detail";
import { type CreateUpdateReservationsDto, postApiAppReservations, ReservationsDto, } from "@/client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { preventEnter } from "@/lib/helper/prevent-enter";
import { ReservationGroupHotTable, ReservationGroupHotTableRef } from "./_component/reservation-group-hot-table";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function FormReservationGroup({
  wiithHeader = true,
  className,
  page,
  id,
}: {
  wiithHeader?: boolean;
  className?: string;
  page?: string;
  id?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const isLoading = false;
  const queryClient = useQueryClient();
  const router = useRouter();
  const htTableRef = useRef<ReservationGroupHotTableRef>(null);

  // DEFINE CREATE, UPDATE, DELETE
  useEffect(() => {
    switch (page) {
      case "create":
        break;
      case "update":
        // RETURN IF ID NOT DEFINED
        if (!id) return;
        // GET DATA
        // const data = getDataReservation(id);
        // REGISTER DATA
        // if (data) {
        //   (Object.keys(data) as Array<keyof Reservation>).forEach(key => {
        //     setValue(key, data[key]);
        //   });
        // }
        break;
      case "detail":
        break;
      default:
        // redirect to not found
        break;
    }
  }, [id, page])

  // POST
  const handlePost = async (submitData: FieldValue<ReservationsDto>) => {
    const valid = await htTableRef.current?.getValuess();
    const datas = {
      ...(submitData as ReservationsDto),
      reservationDetails: valid
    } as ReservationsDto;
    // console.log("POST submit data", datas);
    // submitData.reservationDetails = valid;
    if(!valid) return;

    const confirmation = (await swal({
      title: "Accept",
      text: "Add New Reservation?",
      icon: "info",
      buttons: ["Cancel", "Yes"],
    })) as unknown as boolean;
    if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan
    const data = JSON.parse(JSON.stringify(datas)) as CreateUpdateReservationsDto & Reservation & { reservationDetailsStatus?: string };
    delete data?.reservationDetailsStatus;
    // delete data.attachment;
    const attachment = data.attachment?.match(/^data:(.*);base64,(.*)$/);
    if (attachment?.[1] && attachment?.[2]) {
      data.attachments = [
        {
          contentType: attachment[1],
          base64Content: attachment[2],
          fileName: "reservations-attachments-" + new Date().getTime() + ".pdf",
        }
      ];
    }
    data.attachment = ""
    // console.log("POST handlePost", data);
    await createReservationWithDetailMutation.mutateAsync(data as unknown as CreateUpdateReservationsDto);
  }

  const createReservationWithDetailMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateReservationsDto) =>
      postApiAppReservations({
        body: dataMutation,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Reservation Created Successfully",
        icon: "success",
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetReservationWithDetails] });
      router.push('/checkin');
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const { can } = useGrantedPolicies();
  if(!can("WismaApp.Reservation.Create")) return <AccessDeniedLayout />; 
  return (
    <div className={"mb-10 mt-4 @container " + className}>
      {wiithHeader && (
        <>
          <PageHeaderCustom
            // title={"Reservations"}
            breadcrumb={[
              { name: "Reservation", href: "/reservation" },
              { name: "Create" },
            ]}
          >
          </PageHeaderCustom>
        </>
      )}
      <form onSubmit={handleSubmit(handlePost)} onKeyDown={preventEnter} className="space-y-4">
        <div className="grid grid-cols-1 gap-8">
          <div className="">
            <ReservationGroupDetail
              isLoading={isLoading}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch} />
          </div>
          <div className="">
            <ReservationGroupHotTable
              ref={htTableRef}
              isLoading={isLoading}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch} />
          </div>
          <div className="">
            {/* BUTTON */}
            <div className="flex justify-end gap-2">
              <div className="-mt-[70px]">
                <Button
                  type="submit"
                  // size="sm"
                  disabled={isLoading}
                  className="rounded-lg bg-green-500 px-4 py-2 text-white disabled:bg-gray-400 mr-2"
                >
                  Accept
                </Button>
                <Link href={"/reservation"}>
                  <Button
                    // type="submit"
                    // size="sm"
                    disabled={isLoading}
                    variant="outline"
                    className="rounded-lg px-4 py-2 disabled:bg-gray-400"
                  >
                    Quit
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
