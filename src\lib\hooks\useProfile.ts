﻿import { type ProfileDtoReadable, getApiAccountMyProfile } from '@/client'
import { type UseQueryResult, useQuery } from '@tanstack/react-query'
import { QueryNames } from './QueryConstants'

/**
 * Custom hook to fetch the user's profile data.
 *
 * This hook uses the `useQuery` hook from `react-query` to fetch the profile data
 * asynchronously. The query key used is `QueryNames.GetProfile` and the query function
 * is `getApiAccountMyProfile`.
 *
 * @returns {UseQueryResult<ProfileDtoReadable, unknown>} The result of the query, which includes
 * the profile data and query status.
 */
export const useProfile = (): UseQueryResult<ProfileDtoReadable | undefined, unknown> => {
  return useQuery({
    queryKey: [QueryNames.GetProfile],
    queryFn: async () => {
      const { data } = await getApiAccountMyProfile()
      return data
    },
  })
}
