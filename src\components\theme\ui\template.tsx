/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import cherryIco from "@public/Cherry.png";
import approvalEngineIco from "@public/Approval Engine.png";
import Image from "next/image";

/* eslint-disable @typescript-eslint/no-explicit-any */
export function Chip(item: any) {
  const color: Record<string, string> = {
    Approved: "bg-[#2B7F75]",
    "Waiting Approval": "bg-[#ffd66b]",
    Rejected: "bg-[#c50000]",
    Cancelled: "bg-gray-500",
    Draft: "bg-gray-300",
    "Waiting Verification": "bg-[#f7b787]",
    // Add other statuses as needed
  };

  const className = color[item.item.status as keyof typeof color];

  return (
    <>
      <div className="relative inline-flex">
        <span className={"inline-block whitespace-nowrap rounded-2xl px-2.5 font-medium text-white " + className}>
          {item.item.status}
        </span>
        {/* <Badge
          size="sm"
          variant="flat"
          className="absolute right-0 top-0 -translate-y-1/3 translate-x-1 p-0"
        >
        <Image src={item.item.approvalTo == 'Cherry' ? cherryIco : approvalEngineIco} alt="avatar" width={12} height={12} className="rounded-full" />
        </Badge> */}
        {process.env.NEXT_PUBLIC_STACK_ENV !== 'production' &&
          <div className="p-1">
            <Image src={item.item.approvalTo == 'Cherry' ? cherryIco : approvalEngineIco} alt="avatar" width={12} height={12} className="rounded-full" />
          </div>
        }
      </div>
    </>
  );
}
