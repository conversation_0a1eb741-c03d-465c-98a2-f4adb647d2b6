import { type ReservationDetail } from "./reservationDetail";

export interface Reservation {
    id: string;
    reservationCode: string;
    bookerIdentityNumber: string;
    bookerName: string;
    bookerEmail: string;
    bookerPhoneNumber: string;
    paymentMethod?: string;
    companyName: string;
    arrivalDate: string;
    days: number;
    status: "Confirmed" | "Pending" | "Checked-in" | "Checked-out" | "Cancelled";
    groupCode: string;
    diningOptions: string;
    attachment?: string;
    reservationDetails: ReservationDetail[];
    action?: string;
}

export interface ReservationResponse {
    totalCount: number,
    items: Reservation[]
}

export interface ReservationErrorResponse {
    response?: {
        data: {
            error: {
                details: string;
            }
        }
    },
    status?: boolean,
    error?: string
}