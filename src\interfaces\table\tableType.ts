export interface Column {
  dataIndex: string;
  title: string;
  filter?: "text" | "select" | "date" | "none";
  render?: (value: unknown, record: Record<string, unknown>) => React.ReactNode;
}

export interface CustomTableProps {
  columns: Column[];
  dataSource: Record<string, unknown>[];
  // dataSource: string[] | undefined;
  rowKey: string;
  pageSize: number;
  isLoading?: boolean;
  init?: () => Promise<void>;
}
