"use client";
import React, { useEffect, useState } from "react";
import PageHeader<PERSON>ustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import {
  addAlphaComponentColor,
  contrastingColor,
  stripAlphaFromHex,
} from "@/lib/helper/format-color";
import ButtonForm from "@/components/container/button/button-form";
import { useMasterStatus } from "@/lib/hooks/rooms/masterStatus/useMasterStatus";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import {
  deleteApiAppRoomStatusById,
  postApiAppRoomStatus,
  putApiAppRoomStatusById,
} from "@/client";
import type { CreateUpdateRoomStatusDto, RoomStatusDto } from "@/client";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function RoomsMasterStatus({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();

  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isRoomStatus, setRoomStatus] = useState<RoomStatusDto[]>([]);
  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 1000,
  });

  const { isLoading, data } = useMasterStatus(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
        color: item.color ?? "",
        code: item.code ?? "",
      }));
      setRoomStatus(mappedData as RoomStatusDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "code", title: "Code", filter: "text" as const },
    { dataIndex: "name", title: "Name", filter: "text" as const },
    { dataIndex: "color", title: "Color", filter: "text" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetRoomStatus],
    });
  };

  const deleteRoomStatusMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppRoomStatusById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room status deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetRoomStatus],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Room status will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete room status with id:", id);
        await deleteRoomStatusMutation.mutateAsync(id);

        handleReset();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the room status. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const status = isRoomStatus.find((status) => status.id === id);
    if (status) {
      setIsEditMode(true);
      Object.entries(status).forEach(([key, value]) => {
        if (key === "color") {
          setValue(key, stripAlphaFromHex(value ?? ""));
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createRoomStatusMutation = useMutation({
    mutationFn: async (data: CreateUpdateRoomStatusDto) =>
      postApiAppRoomStatus({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room status created successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetRoomStatus],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateRoomStatusMutation = useMutation({
    mutationFn: async (data: RoomStatusDto) => {
      const { id, ...updateData } = data;

      return putApiAppRoomStatusById({
        path: { id: id! },
        body: updateData as CreateUpdateRoomStatusDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room status updated successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetRoomStatus],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    const formattedData: {
      id: string;
      code: string;
      name: string;
      color: string;
    } = {
      id: formData.id as string,
      code: formData.code as string,
      name: formData.name as string,
      color: addAlphaComponentColor(formData.color as string),
    };

    try {
      if (isEditMode) {
        // console.log("Updating room:", formattedData);
        await updateRoomStatusMutation.mutateAsync(
          formattedData as RoomStatusDto,
        );
      } else {
        // console.log("Creating room:", formattedData);
        await createRoomStatusMutation.mutateAsync(
          formattedData as CreateUpdateRoomStatusDto,
        );
      }
      handleAction();
    } catch (error) {
      console.error("Error in onSubmit:", error);
      await swal({
        title: "Error",
        text: "An unexpected error occurred. Please try again later.",
        icon: "error",
      });
    } finally {
      setIsEditMode(false);
      Object.keys(getValues()).forEach((key) => {
        setValue(key, "");
      });
    }
  }

  if (!can("WismaApp.RoomStatus.View")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Rooms" },
            { name: "Room Status" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  color: (
                    <span
                      style={{
                        backgroundColor: e.color ?? "#000000",
                        color: contrastingColor(e.color ?? "#000000", -50),
                        padding: "4px 8px",
                        borderRadius: "4px",
                        display: "inline-block",
                      }}
                    >
                      {e.color}
                    </span>
                  ),
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
