import { useApiAttachmentStreamById } from "@/lib/hooks/useApiAppAttachmentById";
import React, { useEffect, useState } from "react";
import { PiFileDocBold } from "react-icons/pi";
import { Button } from "rizzui";

export default function ButtonDownloadDocx({
  itemId,
  name,
}: {
  itemId: string;
  name?: string;
}) {
  const [blobUrl, setBlobUrl] = useState<string>();
  const { data } = useApiAttachmentStreamById(itemId);

  useEffect(() => {
    if (data) setBlobUrl(URL.createObjectURL(data as Blob));
  }, [data]);

  const handleDownload = () => {
    if (!blobUrl) return;

    const link = document.createElement("a");
    link.href = blobUrl;
    link.download = name ?? "Document Template";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Button
      className="h-[1.5rem] rounded-lg hover:bg-blue-100"
      variant="text"
      onClick={handleDownload}
      title="Download"
    >
      <PiFileDocBold
        size={20}
        className="text-blue-500"
        title="Download"
        aria-label="Download"
      />
    </Button>
  );
}
