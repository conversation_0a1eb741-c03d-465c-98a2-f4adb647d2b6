import React from "react";
import { DatePicker } from "@/components/theme/ui/datepicker";
import type { DateFilterProps } from "@/interfaces/table/dateFilterType";

export default function DateFilter({
  column,
  dateFilters,
  handleDateFilterChange,
}: DateFilterProps) {
  const dateValue = dateFilters[column.dataIndex];
  const selectedDate = dateValue ? new Date(`${dateValue}T12:00:00`) : null;

  console.log("dateValue", dateValue);
  console.log("selectedDate", selectedDate);

  function formatDate(date: Date | null) {
    if (!date) return null;

    const yyyy = date.getFullYear();
    const mm = (date.getMonth() + 1).toString().padStart(2, "0");
    const dd = date.getDate().toString().padStart(2, "0");

    return `${yyyy}-${mm}-${dd}`;
  }

  return (
    // <div className="relative bg-red-200">
    <DatePicker
      selected={selectedDate}
      onChange={(date: Date | null) =>
        handleDateFilterChange(column.dataIndex, formatDate(date) ?? "")
      }
      dateFormat="yyyy/MM/dd"
      placeholderText="Select Date"
      className="date-picker-event-calendar w-full"
      inputProps={{
        size: "sm",
      }}
    />
    // </div>
  );
}
