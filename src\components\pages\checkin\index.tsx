"use client";
import React, { useState, useEffect, useCallback } from "react";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/tableV2";
import {
  putApiAppReservationDetailsById,
  type CreateUpdateReservationDetailsDto,
  type FilterGroup,
  type ReservationDetailsDto,
  type RoomTypeDto,
  type SortInfo,
} from "@/client";
import { useApiAppReservationDetailsList } from "@/lib/hooks/useApiAppReservationDetails";
import { type Column } from "@/interfaces/table/tableType";
import { But<PERSON>, Tooltip } from "rizzui";
import Link from "next/link";
import { countDays } from "@/lib/helper/count-days";
import { countNights } from "@/lib/helper/count-nights";
import StatusColor from "@/components/container/color/statusColor";
import { PiArrowSquareInBold } from "react-icons/pi";
import { randStr } from "@/lib/helper/generate-random-string";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType";
import { formatDate } from "@/lib/helper/format-date";
import LoadingScreen from "@/components/theme/ui/loading-screen";
import swal from "sweetalert";
import { swalError } from "@/lib/helper/swal-error";
import { fetchStatusOptions } from "@/lib/hooks/config/masterStatus";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";

export default function CheckinList({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const { can } = useGrantedPolicies();
  const queryClient = useQueryClient();
  const [pagination, setPagiaton] = useState({
    pageIndex: 0,
    pageSize: 20,
  });
  const [statusOptions, setStatusOptions] = useState<SelectOptionType[]>([]);
  const [sort, setSortConfig] = useState<SortInfo[] | undefined>();
  const [checked, setChecked] = useState<Record<string, unknown>[]>([]);
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const [postLoading, setPostLoading] = useState<boolean>(false);
  const [selectFilters, setSelectFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [dateFilters, setDateFilters] = useState<Record<string, string>>({});
  const handleFilterChange = useCallback((filterGroup: FilterGroup) => {
    setFilterGroup(filterGroup);
  }, []);
  const [filterGroup, setFilterGroup] = useState<FilterGroup | undefined>(
    undefined,
  );
  const defaultFilter: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "status.code",
        operator: "In",
        value: "reserved,checkin",
      },
      ...(filterGroup?.conditions ?? []),
    ],
  };

  const [data, setData] = useState<RoomTypeDto[]>([]);
  const { isLoading, data: dataSource } = useApiAppReservationDetailsList(
    pagination.pageIndex,
    pagination.pageSize,
    defaultFilter,
    "",
    sort,
    refreshId,
  );
  const { isLoading: isLoadingResvDetOptions, data: statusResvDetOptions } =
    useMasterStatusByDocTypeOptions("reservationDetails");

  // const { data: masterStatusData } = useMasterStatus();
  useEffect(() => {
    const fetchStatus = async () => {
      const options = await fetchStatusOptions("reservationDetails");
      setStatusOptions(options);
    };

    void fetchStatus();
  }, []);

  const filterSelectTable = {
    "status.name": statusOptions.map((option) => option.label),
  };

  useEffect(() => {
    if (dataSource?.items) {
      const mappedData = dataSource.items.map((item) => ({
        ...item,
        room: {
          roomCode: `${item.room?.roomCode} ${item.isExtraBed ? "(Extra Bed)" : ""}`,
        },
        checkInDate: formatDate(item.checkInDate ?? "", "datetime"),
        checkOutDate: formatDate(item.checkOutDate ?? "", "datetime"),
        stays: countDays(
          new Date(item.checkInDate ?? 0),
          new Date(item.checkOutDate ?? 0),
        ),
        nights: countNights(
          new Date(item.checkInDate ?? 0),
          new Date(item.checkOutDate ?? 0),
        ),
        statusBadge: (
          <StatusColor name={item?.status?.name} color={item?.status?.color} />
        ),
        action:
          can("WismaApp.Reservation.Edit") &&
          item?.status?.code === "reserved" ? ( // code for reserved
            <Tooltip size="sm" content={"Check In"} placement="top">
              <Link href={"checkin/form/" + item.id}>
                <Button
                  // variant="outline"
                  size="sm"
                  className="bg-green-500 text-white hover:bg-green-600"
                >
                  {/* Check In */}
                  <PiArrowSquareInBold className="h-4 w-4" />
                </Button>
              </Link>
            </Tooltip>
          ) : (
            <></>
          ),
      }));
      setData(mappedData);
    }
  }, [dataSource]);

  const columns: Column[] = [
    {
      dataIndex: "reservation.reservationCode",
      title: "Resevation Code",
      filter: "text",
    },
    {
      dataIndex: "marketCode",
      title: "Market Code",
      filter: "text",
    },
    {
      dataIndex: "guest.fullname",
      title: "Guest Name",
      filter: "text",
    },
    {
      dataIndex: "guest.identityNumber",
      title: "Identity",
      filter: "text",
    },
    {
      dataIndex: "guest.companyName",
      title: "Company",
      filter: "text",
    },
    {
      dataIndex: "guest.phoneNumber",
      title: "Contact",
      filter: "text",
    },
    {
      dataIndex: "room.roomCode",
      title: "Room",
      filter: "text",
    },
    // {
    //   dataIndex: "room.roomNumber",
    //   title: "Room",
    //   filter: "text",
    // },
    // {
    //   dataIndex: "room.roomType.name",
    //   title: "Room Type",
    //   filter: "select"
    // },
    {
      dataIndex: "checkInDate",
      title: "Checkin",
      filter: "date",
    },
    {
      dataIndex: "checkOutDate",
      title: "Checkout",
      filter: "date",
    },
    {
      dataIndex: "stays",
      title: "Stays",
      filter: "none",
    },
    {
      dataIndex: "nights",
      title: "Nights",
      filter: "none",
    },
    {
      dataIndex: "status.name",
      title: "Status",
      filter: "select" as const,
      render: (_: unknown, record: Record<string, unknown>) => {
        const r = record as ReservationDetailsDto;
        return <StatusColor name={r.status?.name} color={r.status?.color} />;
      },
    },
    {
      dataIndex: "action",
      title: "Action",
      filter: "none",
    },
  ];

  // CHECK IN ALL ACTIONS
  // DEFINE MUTATIONS
  const updateReservationDetailsMutation = useMutation({
    mutationFn: async (dataMutation: ReservationDetailsDto) => {
      const { id, ...updateData } = dataMutation;

      return putApiAppReservationDetailsById({
        path: { id: id! },
        body: updateData as CreateUpdateReservationDetailsDto,
      });
    },
    onSuccess: async () => {
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationDetailsById],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  // DEFINCE ACTIONS
  const handlePost = async (resvDet: ReservationDetailsDto) => {
    // console.log("resvDet", resvDet);
    const statusId = statusResvDetOptions?.find(
      (opt) => opt.code == "checkin",
    )?.value; // code fore Check In
    const data: ReservationDetailsDto = {
      id: resvDet?.id,
      reservationId: resvDet?.reservationId,
      roomId: resvDet?.roomId,
      statusId: statusId as string,
      guestId: resvDet?.guestId,
      // "checkInDate": resvDet?.checkInDate,
      checkInDate: formatDate(new Date(), "datetime"),
      checkOutDate: resvDet?.checkOutDate,
      rfid: resvDet?.rfid,
      price: resvDet?.price,
    };
    const update = await updateReservationDetailsMutation.mutateAsync(data);
  };
  const checkInAll = async () => {
    const confirmation = (await swal({
      title: "Checkin All",
      text: "Checkin All Checked Reservation?",
      icon: "info",
      buttons: ["Cancel", "Yes"],
    })) as unknown as boolean;
    if (!confirmation) return;

    setPostLoading(true);

    for (const item of checked) {
      const data = item as ReservationDetailsDto;
      if (data.status?.code === "reserved") {
        await handlePost(data); // tunggu sampai selesai sebelum lanjut
      }
    }

    setPostLoading(false);
    setChecked([]);
    setRefreshId(randStr());

    await swal({
      title: "Success",
      text: "Check In Successfully",
      icon: "success",
      timer: 1000,
      buttons: false as unknown as (string | boolean)[],
    });
  };

  // PERMISSION
  if (!can("WismaApp.Reservation")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {postLoading && <LoadingScreen />}
      {wiithHeader && (
        <PageHeaderCustom
          // title="Checkin List"
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Checkin" },
            { name: "List" },
          ]}
          className="h-[32px]"
        >
          {can("WismaApp.Reservation.Edit") && checked.length ? (
            <Button onClick={checkInAll} size="sm" className="bg-green-500">
              Checkin All
            </Button>
          ) : (
            <></>
          )}
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        <div className="rounded-lg bg-white">
          <CustomTable
            columns={columns}
            dataSource={data ?? []}
            pageSize={pagination.pageSize}
            rowKey="id"
            isLoading={isLoading}
            totalCount={dataSource?.totalCount ?? 1}
            setPagiaton={setPagiaton}
            searchTerms={searchTerms}
            setSearchTerms={setSearchTerms}
            selectFilters={selectFilters}
            setSelectFilters={setSelectFilters}
            dateFilters={dateFilters}
            setDateFilters={setDateFilters}
            onFilterChange={handleFilterChange}
            onSortChange={setSortConfig}
            filterSelectTable={filterSelectTable}
            height="70vh"
            setRefreshId={setRefreshId}
            setCheked={setChecked}
          />
        </div>
      </div>
    </div>
  );
}
