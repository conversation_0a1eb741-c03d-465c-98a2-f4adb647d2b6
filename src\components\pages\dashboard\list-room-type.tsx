import React, { useState } from "react";
import { useMasterType } from "@/lib/hooks/rooms/masterType/useMasterType";

export default function ListRoomType({
  setSelectedType,
}: {
  setSelectedType: (type: string | null) => void;
}) {
  const [activeType, setActiveType] = useState<string | null>(null);
  const { data: masterTypeData } = useMasterType();
  // // console.log("masterTypeData:", masterTypeData);

  return (
    <div className="flex gap-2">
      {masterTypeData?.items?.map((item) => {
        const isActive: boolean = activeType === item.alias;

        return (
          <div
            key={item.id}
            className={`flex cursor-pointer items-center justify-center rounded-lg border px-2 py-1 shadow-sm xl:w-10 2xl:w-16 2xl:p-3 ${
              isActive ? "border-gray-400" : "border-gray-300"
            }`}
            style={{
              backgroundColor: isActive ? "#f0f0f0" : "white",
            }}
            onClick={() => {
              const newType: string | null = isActive
                ? null
                : (item.alias ?? null);
              setActiveType(newType);
              setSelectedType(newType);
            }}
            title={item.name!}
          >
            <span className="text-[5px] text-gray-500 sm:text-[7px] md:text-[9px] xl:text-[8px] 2xl:text-xs">
              {item.alias}
            </span>
          </div>
        );
      })}
      <button
        className={`flex cursor-pointer items-center justify-center rounded-lg border px-2 py-1 shadow-sm xl:w-10 2xl:w-16 2xl:p-3 ${
          activeType === null ? "border-gray-400" : "border-gray-300"
        }`}
        onClick={() => {
          setActiveType(null);
          setSelectedType(null);
        }}
        title="ALL TYPE"
      >
        <span className="text-[5px] text-gray-500 sm:text-[7px] md:text-[9px] xl:text-[8px] 2xl:text-xs">
          ALL
        </span>
      </button>
    </div>
  );
}
