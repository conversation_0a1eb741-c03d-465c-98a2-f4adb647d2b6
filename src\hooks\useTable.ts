import { isString } from "lodash";
import { useState, useEffect, useMemo } from "react";

type GenericObject<T> = {
  id: string;
  [key: string]: T | string;
};
export function useTable<T, U extends GenericObject<T>>(
  initialData: U[],
  countPerPage = 10,
  initialFilterState?: Partial<U>
) {
  const [data, setData] = useState(initialData);

  /*
   * Dummy loading state.
   */
  const [isLoading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(false);
  }, []);

  /*
   * Handle row selection
   */
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const handleRowSelect = (recordKey: string) => {
    const selectedKeys = [...selectedRowKeys];
    if (selectedKeys.includes(recordKey)) {
      setSelectedRowKeys(selectedKeys.filter((key) => key !== recordKey));
    } else {
      setSelectedRowKeys([...selectedKeys, recordKey]);
    }
  };
  const handleSelectAll = () => {
    if (selectedRowKeys.length === data.length) {
      setSelectedRowKeys([]);
    } else {
      // Ensure that every record is defined and every id is a string
      const ids = data.filter((record): record is U => record !== undefined && typeof record.id === 'string').map(record => record.id);
      setSelectedRowKeys(ids);
    }
  };

  /*
   * Handle sorting
   */
  const [sortConfig, setSortConfig] = useState({
    key: "",
    direction: "",
  });

  function sortData<T>(data: T[], sortKey: keyof T, sortDirection: 'asc' | 'desc'): T[] {
    return [...data].sort((a, b) => {
      const aValue = a[sortKey];
      const bValue = b[sortKey];

      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      } else if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  const sortedData = useMemo(() => {
    const newData = data;
    if (!sortConfig.key || !sortConfig.direction) {
      return newData;
    }
    // Provide a default direction if sortConfig.direction is null
    const direction = (sortConfig.direction || 'asc') as 'asc' | 'desc';
    return sortData(newData, sortConfig.key, direction);
  }, [sortConfig, data]);

  function handleSort(key: string) {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  }

  /*
   * Handle pagination
   */
  const [currentPage, setCurrentPage] = useState(1);
  function paginatedData(data: U[] = sortedData) {
    const start = (currentPage - 1) * countPerPage;
    const end = start + countPerPage;

    if (data.length > start) return data.slice(start, end);
    return data;
  }

  function handlePaginate(pageNumber: number) {
    setCurrentPage(pageNumber);
  }

  /*
   * Handle delete
   */
  function handleDelete(id: string | string[]) {
    const updatedData = Array.isArray(id)
      ? data.filter((item) => !id.includes(item.id))
      : data.filter((item) => item.id !== id);

    setData(updatedData);
  }

  /*
   * Handle Filters and searching
   */
  const [searchTerm, setSearchTerm] = useState('');
  type FilterValue = string | [number | Date, number | Date];

  const [filters, setFilters] = useState<Record<string, FilterValue>>(
    (initialFilterState as Record<string, FilterValue>) ?? {}
  );

  function updateFilter(columnId: string, filterValue: FilterValue) {
    if (Array.isArray(filterValue) && filterValue.length !== 2) {
      throw new Error('filterValue data must be an array of length 2');
    }

    setFilters((prevFilters) => ({
      ...prevFilters,
      [columnId]: filterValue,
    }));
  }

  function applyFilters() {
    const searchTermLower = searchTerm.toLowerCase();

    return (
      sortedData
        .filter((item) => {
          const isMatchingItem = Object.entries(filters).some(
            ([columnId, filterValue]) => {
              if (Array.isArray(filterValue) && typeof filterValue[1] === 'object') {
                const itemValue = item[columnId];
                if (itemValue !== undefined && (typeof itemValue === 'string' || typeof itemValue === 'number')) { // Ensure the value is not undefined and is a string or number
                  const dateValue = new Date(itemValue);
                  return (
                    dateValue >= filterValue[0] && dateValue <= filterValue[1]
                  );
                }
                return false; // Return false if itemValue is undefined or not a string/number
              }
              if (Array.isArray(filterValue) && typeof filterValue[1] === 'string') {
                const itemPrice = item[columnId];
                if (itemPrice !== undefined && !isNaN(Number(itemPrice))) { // Ensure the value is not undefined and is a number
                  const price = Math.ceil(Number(itemPrice));
                  return (
                    price >= Number(filterValue[0]) &&
                    price <= Number(filterValue[1])
                  );
                }
                return false; // Return false if itemPrice is undefined or not a number
              }
              if (isString(filterValue) && !Array.isArray(filterValue)) {
                const itemValue = item[columnId]?.toString().toLowerCase();
                if (itemValue !== undefined) { // Check if itemValue is not undefined
                  return itemValue === filterValue.toString().toLowerCase();
                }
                return false; // Return false if itemValue is undefined
              }
            }
          );
          return isMatchingItem;
        })
        // global search after running filters
        .filter((item) => Object.values(item).some((value) => typeof value === 'object'
          ? value &&
          Object.values(value).some(
            (nestedItem) => nestedItem &&
              String(nestedItem).toLowerCase().includes(searchTermLower)
          )
          : value && String(value).toLowerCase().includes(searchTermLower)
        )
        )
    );
  }

  /*
   * Handle searching
   */
  function handleSearch(searchValue: string) {
    setSearchTerm(searchValue);
  }

  function searchedData() {
    if (!searchTerm) return sortedData;

    const searchTermLower = searchTerm.toLowerCase();

    return sortedData.filter((item) => Object.values(item).some((value) => typeof value === 'object'
      ? value &&
      Object.values(value).some(
        (nestedItem) => nestedItem &&
          String(nestedItem).toLowerCase().includes(searchTermLower)
      )
      : value && String(value).toLowerCase().includes(searchTermLower)
    )
    );
  }

  /*
   * Reset search and filters
   */
  function handleReset() {
    setData(() => initialData);
    handleSearch('');

    if (initialFilterState) {
      const newFilters: Record<string, FilterValue> = {};
      Object.keys(initialFilterState).forEach(key => {
        const value = initialFilterState[key];
        if (typeof value === 'string' || Array.isArray(value)) {
          newFilters[key] = value as FilterValue;
        }
      });
      setFilters(newFilters);
    }
  }

  /*
   * Set isFiltered and final filtered data
   */
  const isFiltered = applyFilters().length > 0;
  function calculateTotalItems() {
    if (isFiltered) {
      return applyFilters().length;
    }
    if (searchTerm) {
      return searchedData().length;
    }
    return sortedData.length;
  }
  const filteredAndSearchedData = isFiltered ? applyFilters() : searchedData();
  const tableData = paginatedData(filteredAndSearchedData);

  /*
   * Go to first page when data is filtered and searched
   */
  useEffect(() => {
    handlePaginate(1);
  }, [isFiltered, searchTerm]);

  // useTable returns
  return {
    isLoading,
    isFiltered,
    tableData,
    // pagination
    currentPage,
    handlePaginate,
    totalItems: calculateTotalItems(),
    // sorting
    sortConfig,
    handleSort,
    // row selection
    selectedRowKeys,
    setSelectedRowKeys,
    handleRowSelect,
    handleSelectAll,
    // searching
    searchTerm,
    handleSearch,
    // filters
    filters,
    updateFilter,
    applyFilters,
    handleDelete,
    handleReset,
  };
}
