﻿import type { FilterGroup, InvoiceGenerationDto, SortInfo } from "@/client";
import { postApiAppInvoiceDocumentGenerateWismaInvoice, postApiAppRoomsList } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import type { SelectOption } from "rizzui";

export const usePostApiInvoice = (datas: InvoiceGenerationDto) => {
  return useQuery({
    queryKey: [QueryNames.postApiAppInvoiceDocumentGenerateWismaInvoice, datas.paymentId],
    queryFn: async () => {
      const { data } = await postApiAppInvoiceDocumentGenerateWismaInvoice({
        body: { ...datas, },
      });

      return data;
    },
  });
};
