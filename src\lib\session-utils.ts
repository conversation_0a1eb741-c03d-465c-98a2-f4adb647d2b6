﻿import { getApiMultiTenancyTenants } from '@/client'
import { clientConfig } from '@/config'
import * as client from 'openid-client'
// import type { Configuration } from 'openid-client'
import { getSession } from './actions'

interface OpenIDMetadata {
  issuer?: string
  token_endpoint?: string
  authorization_endpoint?: string
  userinfo_endpoint?: string
  [key: string]: unknown
}

/**
 * Interface representing session data.
 * This interface defines the structure of the session data object,
 * including login status, tokens, user information, and tenant ID.
 */
export interface SessionData {
  isLoggedIn: boolean
  access_token?: string
  code_verifier?: string
  state?: string
  userInfo?: {
    sub: string
    name: string
    email: string
    email_verified: boolean
  }
  tenantId?: string
}

/**
 * Default session data.
 * This object provides the default values for a new session, indicating the user is not logged in.
 * It initializes all optional fields to undefined.
 */
export const defaultSession: SessionData = {
  isLoggedIn: false,
  access_token: undefined,
  code_verifier: undefined,
  state: undefined,
  userInfo: undefined,
  tenantId: undefined,
}

/**
 * Retrieves the client configuration.
 * This function fetches the client configuration needed for authentication.
 * It uses the OpenID client library to discover the configuration based on the provided URL and client ID.
 *
 * @returns {Promise<Configuration>} The client configuration.
 */
export async function getClientConfig() {
  try {
    if (!clientConfig.url || !clientConfig.client_id) {
      console.error('Missing required configuration: URL or client_id')
      throw new Error('Missing required configuration')
    }

    // console.log('Discovering OpenID configuration from:', clientConfig.url)

    // Use the discovery function with all three parameters
    // Using type assertion to tell TypeScript we're confident about the return type
    const discoveryResult = await client.discovery(
      new URL(clientConfig.url),
      clientConfig.client_id,
      clientConfig.client_secret
    );
    const config = discoveryResult

    // Validate that we got a proper configuration
    const metadata: OpenIDMetadata = config.serverMetadata()

    // console.log('OpenID configuration metadata received:', {
    //   issuer: metadata.issuer ?? 'undefined',
    //   token_endpoint: metadata.token_endpoint ?? 'undefined',
    //   authorization_endpoint: metadata.authorization_endpoint ?? 'undefined',
    //   userinfo_endpoint: metadata.userinfo_endpoint ?? 'undefined',
    // })

    if (!metadata.token_endpoint || !metadata.authorization_endpoint) {
      console.error('Invalid OpenID configuration: missing endpoints', metadata)

      // Try to force reload the configuration by making direct calls
      try {
        const discoveryUrl = new URL(clientConfig.url)
        const wellKnownUrl = new URL('.well-known/openid-configuration', discoveryUrl)

        // console.log('Attempting direct discovery from:', wellKnownUrl.toString())

        const response = await fetch(wellKnownUrl.toString())
        if (response.ok) {
          const directMetadata = (await response.json()) as OpenIDMetadata
          // console.log('Direct discovery successful:', directMetadata)

          // Manually set the metadata
          Object.assign(metadata, directMetadata)

          if (metadata.token_endpoint && metadata.authorization_endpoint) {
            // console.log('Successfully loaded endpoints from direct discovery')
            return config
          }
        } else {
          console.error('Direct discovery failed:', response.status, response.statusText)
        }
      } catch (discoveryError) {
        console.error('Error in direct discovery attempt:', discoveryError)
      }

      throw new Error('Invalid OpenID configuration: missing required endpoints')
    }

    // console.log('Client authentication configured:', {
    //   client_id: clientConfig.client_id,
    //   auth_method: 'client_secret_basic',
    //   has_secret: !!clientConfig.client_secret,
    // })

    return config
  } catch (error) {
    console.error('Error discovering OpenID configuration:', error)
    throw error
  }
}

/**
 * Sets the tenant ID in the session based on the provided host.
 * This function updates the session with the tenant ID associated with the given host.
 * If the session already has a tenant ID, it does nothing. Otherwise, it fetches the tenant ID and saves it in the session.
 *
 * @param {string} host - The host to get the tenant ID for.
 * @returns {Promise<void>}
 */
export async function setTenantWithHost(host: string) {
  const session = await getSession()
  if (session.tenantId) {
    return
  }
  const { data } = await getApiMultiTenancyTenants({
    query: { Filter: `host eq '${host}'` },
  })
  session.tenantId = data?.items?.[0]?.id ?? undefined
  await session.save()
}

