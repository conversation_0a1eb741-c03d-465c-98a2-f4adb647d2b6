﻿import type { FilterGroup, PaymentsDto, SortInfo } from "@/client";
import { getApiAppPaymentsById, postApiAppPaymentsList } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import type { SelectOption } from "rizzui";

export const useApiAppPaymentsById = (id: string, refreshId?: string) => {
  return useQuery({
    queryKey: [QueryNames.getApiAppPaymentsById, id, refreshId],
    queryFn: async () => {
      const { data } = await getApiAppPaymentsById({
        path: { id }, // Replace with your reservation ID
      });
      return data;
    },
  });
};

export const useMasterPayments = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [
      QueryNames.ListPayments,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
    ],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppPaymentsList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      return data;
    },
    enabled: filter?.conditions.length !== 0, // 👈 Query hanya jalan jika id tidak kosong
  });
};

export const useMasterPaymentOptions = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
  refreshId?: string,
) => {
  return useQuery({
    queryKey: [
      QueryNames.GetPayments,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
      refreshId,
    ],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppPaymentsList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      // GENERATE OPTIONS
      const options: (SelectOption & PaymentsDto)[] =
        data?.items?.map((val) => ({
          label: `${val.paymentCode}`,
          value: val.id ?? "",
          ...val,
        })) ?? [];

      // RETURN OPTIONS
      return options;
    },
    enabled: filter?.conditions.length !== 0, // 👈 Query hanya jalan jika id tidak kosong
  });
};
