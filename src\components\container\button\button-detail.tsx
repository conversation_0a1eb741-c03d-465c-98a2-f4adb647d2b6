import React from "react";
import { PiEyeBold } from "react-icons/pi";
import { Button } from "rizzui";

export default function ButtonDetail({
  itemId,
  handleDetail,
}: {
  itemId: string;
  handleDetail: (itemId: string) => void;
}) {
  return (
    <Button
      className="h-[1.5rem] rounded-lg hover:bg-yellow-100"
      variant="text"
      onClick={() => void handleDetail(itemId)}
      title="Detail"
    >
      <PiEyeBold
        size={20}
        className="text-yellow-500"
        title="Detail"
        aria-label="Detail"
      />
    </Button>
  );
}
