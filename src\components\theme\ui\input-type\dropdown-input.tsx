import { Select, type SelectOption } from "rizzu<PERSON>";
import { useEffect, useState } from "react";
import { type InputProps } from "@/interfaces/form/inputType";
import { get } from "lodash";

export default function DropdownInput(props: InputProps) {
  const [value, setValue] = useState<SelectOption>();

  props.register(props.name, {
    value: value,
    required: props.required,
  });

  const onChange = (e: SelectOption) => {
    setValue(e);
    props.setValue(props.name, e.value);
    if(props.onChange){
      void props.onChange(e.value)
    }
  };

  useEffect(() => {
    props.options?.find(
      (option: SelectOption) =>
        props.getValues(props.name) == option.value,
    );
  }, []);
  const optionsKey = props.options?.map(opt => opt.value).join(',');
  useEffect(() => {
    const defaultVal = props.options?.find((e) => e.value == props.getValues(props.name));
    // // console.log(`${props.name} OPT`, props.options);
    // // console.log(`${props.name} VAL`, defaultVal);
    setValue(defaultVal);
  }, [props.watch(props.name), optionsKey])

  return (
    <div className={props.className ?? ""}>
      <Select
        label={props.label}
        name={props.name}
        placeholder={props.label}
        value={value}
        options={props.options ?? []}
        onChange={onChange}
        disabled={props.readOnly}
        error={
          get(props.errors, props.name) &&
          props.label + " is required"
        }
        selectClassName="bg-white"
        dropdownClassName={"dropdown-select"}
        size={props.size ?? "md"}
      />
    </div>
  );
}
