export default function CargoPallet({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="512"
      height="512"
      viewBox="0 0 512 512"
      {...props}
    >
      <path fill="#e8834d" d="M8 439.95h62.36v33.84H8z" />
      <path fill="#d66e41" d="M8 439.95h62.36v18H8z" />
      <path fill="#c4603d" d="M256 208.87h216.82v200.87H256z" />
      <path fill="#b7583b" d="M256 391.74h216.82v18H256z" />
      <path fill="#d66e41" d="M39.18 208.87H256v200.87H39.18z" />
      <path fill="#c4603d" d="M39.18 391.74H256v18H39.18z" />
      <path fill="#e8834d" d="M147.59 8H364.4v200.87H147.6z" />
      <path fill="#d66e41" d="M147.59 190.87H364.4v18H147.6z" />
      <g fill="#ffe9c8">
        <path d="m178.68 277.22-31.1-10.97-31.08 10.97v-68.35h62.18zM287.09 76.35l-31.1-10.97-31.08 10.97V8h62.18zM395.5 277.22l-31.1-10.97-31.08 10.97v-68.35h62.18z" />
      </g>
      <path
        fill="#ffdca7"
        d="M116.5 259.22v18l31.09-10.97 31.09 10.97v-18l-29.43-10.38a5 5 0 0 0-3.33 0zM224.91 58.35v18L256 65.38l31.1 10.97v-18l-29.43-10.38a5 5 0 0 0-3.33 0zM333.32 259.22v18l31.09-10.97 31.09 10.97v-18l-29.43-10.38a5 5 0 0 0-3.33 0z"
      />
      <path fill="#e8834d" d="M224.82 439.95h62.35v33.84h-62.35z" />
      <path fill="#d66e41" d="M224.82 439.95h62.35v18h-62.35z" />
      <path fill="#e8834d" d="M441.64 439.95H504v33.84h-62.36z" />
      <path fill="#d66e41" d="M441.64 439.95H504v18h-62.36z" />
      <path
        fill="#7b7179"
        d="M8.13 409.74h495.73v30.2H8.13zM8.13 473.79h495.73V504H8.13z"
      />
      <path d="M79.86 345.01h33.85a7.52 7.52 0 1 0 0-15.03H79.86a7.52 7.52 0 1 0 0 15.03zM147.32 363.74H79.86a7.52 7.52 0 1 0 0 15.03h67.46a7.52 7.52 0 1 0 0-15.03zM188.54 143.75h33.85a7.52 7.52 0 1 0 0-15.03h-33.85a7.52 7.52 0 1 0 0 15.03zM188.54 177.5H256a7.52 7.52 0 1 0 0-15.02h-67.46a7.52 7.52 0 1 0 0 15.03zM297.22 345.01h33.85a7.52 7.52 0 1 0 0-15.03h-33.85a7.52 7.52 0 1 0 0 15.03zM364.68 363.74h-67.46a7.52 7.52 0 1 0 0 15.03h67.46a7.52 7.52 0 1 0 0-15.03z" />
      <path d="M512 410.04a7.52 7.52 0 0 0-7.52-7.52h-23.6V208.78a7.52 7.52 0 0 0-7.52-7.52H372.2V7.51A7.52 7.52 0 0 0 364.68 0H147.32a7.52 7.52 0 0 0-7.52 7.51v71.5a7.52 7.52 0 0 0 15.04 0V15.02h62.48V76c.11 4.52 4.54 8.94 10.02 7.08L256 72.98l28.67 10.1c4.19 1.58 10.04-1.62 10.02-7.08V15.03h62.47v186.23H154.84v-92.2a7.52 7.52 0 0 0-15.04 0v92.2H38.64a7.52 7.52 0 0 0-7.52 7.52v193.74H7.52A7.52 7.52 0 0 0 0 410.04v94.45A7.52 7.52 0 0 0 7.52 512h124.77a7.52 7.52 0 1 0 0-15.03H15.04v-15.24h481.92v15.24h-334.6a7.52 7.52 0 1 0 0 15.03h342.12a7.52 7.52 0 0 0 7.52-7.51v-94.45zM279.65 65.38l-21.15-7.46a7.53 7.53 0 0 0-5 0l-21.14 7.46V15.03h47.29zm61.39 150.91h47.29v50.35l-21.15-7.45a7.53 7.53 0 0 0-5 0l-21.14 7.45zm-217.36 0h47.29v50.35l-21.15-7.45a7.53 7.53 0 0 0-5 0l-21.14 7.45zm-77.52 0h62.48v60.97c.12 4.52 4.54 8.95 10.02 7.09l28.66-10.1 28.67 10.1c4.19 1.57 10.04-1.62 10.02-7.09V216.3h62.47v186.24H46.16zM15.04 417.55H380.8a7.52 7.52 0 1 0 0-15.03H263.52V216.3H326v60.97c-.1 4.48 4.87 8.93 10.02 7.09l28.66-10.1 28.67 10.1c3.94 1.63 10.15-1.62 10.02-7.09V216.3h62.48v186.24h-54.97a7.52 7.52 0 1 0 0 15.02h86.08v15.25H15.04zm202.19 30.27v18.88H77.42v-18.88zm15.03 0h47.48v18.88h-47.48zm62.51 0H434.6v18.88H294.77zM14.91 466.7v-18.88h47.47v18.88zm434.72 0v-18.88h47.47v18.88z" />
    </svg>
  );
}
