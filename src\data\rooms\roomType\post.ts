"use server";
import { getSession } from "@/lib/actions";
import type {
  RoomType,
  RoomTypePostResponse,
} from "@/interfaces/rooms/roomType";
import axios from "axios";

export const postRoomType = async ({
  name,
  status,
  alias,
}: {
  name: string;
  status: number;
  alias: string;
}): Promise<RoomTypePostResponse> => {
  try {
    const session = await getSession();

    const res = await axios<RoomType>({
      url: `${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room-type`,
      method: "POST",
      headers: {
        Authorization: "Bearer " + session?.access_token,
        "Accept-Language": "en_US",
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: {
        name: name,
        status: status,
        alias: alias,
      },
    });

    return {
      success: true,
      message: "Room type created successfully.",
      data: res.data,
    };
  } catch (error) {
    console.error("Failed to post room type:", error);

    return {
      success: false,
      message: "An unexpected error occurred.",
      data: null,
      error: {
        code: "ERROR_CODE",
        message: error instanceof Error ? error.message : "Unknown error",
        details: "An error occurred while posting the room type.",
      },
    };
  }
};
