"use server";

import { type ReservationErrorResponse, type Reservation, ReservationResponse } from "@/interfaces/reservations/reservation";
import axios from "axios";
import { type FieldValue } from "react-hook-form";


export async function postDataReservations(payload: Reservation) {
  try {
    const res = await axios.post(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/reservations/reservation-with-details`, payload);
    return { success: true, data: res.data as ReservationResponse };
  } catch (error) {
    const err = error as ReservationErrorResponse;
    console.error("Server error:", err?.response?.data);
    return {
      success: false,
      error: err?.response?.data?.error?.details ?? "Unknown server error",
    };
  }
}

// export const postDataReservations = async (data: FieldValue<Reservation>) => {
//   // console.log("POST postDataReservations", data);
//   await axios.post<Reservation>(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/reservations`, data)
//     .then((response) => {
//       // console.log('RESPONSE POST postDataReservation', response.data);
//       return response.data;
//     })
//     .catch((error: ReservationErrorResponse) => {
//       // console.error("Error fetching reservations:", error.response?.data.error.details);
//       console.error("Error fetching reservations:", error.response?.data.error.details);
//       return {
//         success: false,
//         error: error?.response?.data?.error?.details ?? "Something went wrong",
//       };
//     });
// }
