"use client";
import React, { useState, useEffect, useCallback } from "react";
import Form from "./form";
import <PERSON>HeaderCustom from "@/app/shared/page-header-custom";
import ButtonDetail from "@/components/container/button/button-detail";
import StatusColor from "@/components/container/color/statusColor";
import ButtonForm from "@/components/container/button/button-form";
import CustomTableV2 from "@/components/layout/custom-table/tableV2";
import { useForm } from "react-hook-form";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { useMasterRooms } from "@/lib/hooks/useMasterRoom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useMasterType } from "@/lib/hooks/rooms/masterType/useMasterType";
import { useMasterStatus } from "@/lib/hooks/rooms/masterStatus/useMasterStatus";
import {
  deleteApiAppRoomById,
  postApi<PERSON><PERSON>Room,
  putApiAppRoomById,
} from "@/client";
import type {
  CreateUpdateRoomDto,
  FilterGroup,
  RoomDto,
  RoomTypeDto,
  SortInfo,
} from "@/client";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function RoomsMaster({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [dataRooms, setDataRooms] = useState<RoomTypeDto[]>([]);
  const [isRoomStatus, setRoomStatus] = useState<SelectOptionType>();
  const [isRoomType, setRoomType] = useState<SelectOptionType>();

  const [sort, setSortConfig] = useState<SortInfo[] | undefined>();
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});
  const [selectFilters, setSelectFilters] = useState<Record<string, string[]>>(
    {},
  );
  const handleFilterChange = useCallback((filterGroup: FilterGroup) => {
    setFilterGroup(filterGroup);
  }, []);
  const [filterGroup, setFilterGroup] = useState<FilterGroup | undefined>(
    undefined,
  );
  const [pagination, setPagiaton] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const { isLoading, data } = useMasterRooms(
    pagination.pageIndex,
    pagination.pageSize,
    filterGroup,
    "",
    sort,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        roomNumber: item.roomNumber ?? "",
        roomCode: item.roomCode ?? "",
        roomTypeName: item.roomType?.name ?? "",
        size: item.size ?? "",
        price: item.price ?? 0,
        roomStatusName: item.roomStatus?.name ?? "",
        information: item.information ?? "",
      }));

      setDataRooms(mappedData);
    }
  }, [data]);

  const columns = [
    { dataIndex: "roomNumber", title: "Room Number", filter: "text" as const },
    { dataIndex: "roomCode", title: "Room Code", filter: "text" as const },
    {
      dataIndex: "roomType.name",
      title: "Room Type",
      filter: "select" as const,
    },
    { dataIndex: "size", title: "Size", filter: "text" as const },
    { dataIndex: "price", title: "Price", filter: "text" as const },
    {
      dataIndex: "roomStatus.name",
      title: "Room Status",
      filter: "select" as const,
      render: (_: unknown, record: RoomDto) => (
        <StatusColor
          color={record.roomStatus?.color ?? ""}
          name={record.roomStatus?.name ?? "N/A"}
        />
      ),
    },
    { dataIndex: "information", title: "Information", filter: "text" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const { data: masterStatusData } = useMasterStatus();
  const { data: masterTypeData } = useMasterType();

  const roomStatusOptions: SelectOptionType[] = masterStatusData?.items
    ? masterStatusData.items.map((item) => ({
        label: item.name ?? "",
        value: item.id ?? "",
      }))
    : [];

  const roomTypeOptions: SelectOptionType[] = masterTypeData?.items
    ? masterTypeData.items.map((item) => ({
        label: item.name ?? "",
        value: item.id ?? "",
      }))
    : [];

  const roomTypeLabels = roomTypeOptions.map((option) => option.label);
  const roomStatusLabels = roomStatusOptions.map((option) => option.label);

  const filterSelectTable = {
    "roomType.name": roomTypeLabels,
    "roomStatus.name": roomStatusLabels,
  };

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setRoomStatus(undefined);
    setRoomType(undefined);
    setIsEditMode(false);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetRooms],
    });
  };

  const deleteRoomMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppRoomById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetRooms],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Room will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        await deleteRoomMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the room. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const room = dataRooms.find((room) => room.id === id) as
      | RoomDto
      | undefined;

    if (room) {
      setIsEditMode(true);
      Object.entries(room).forEach(([key, value]) => {
        if (key === "roomTypeId") {
          setRoomType({
            label: room.roomType?.name ?? "",
            value: String(value),
          });
        } else if (key === "roomStatusId") {
          setRoomStatus({
            label: room.roomStatus?.name ?? "",
            value: String(value),
          });
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createRoomMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateRoomDto) =>
      postApiAppRoom({
        body: dataMutation,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room Created Successfully",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRooms] });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateRoomMutation = useMutation({
    mutationFn: async (dataMutation: RoomDto) => {
      const { id, ...updateData } = dataMutation;

      return putApiAppRoomById({
        path: { id: id! },
        body: updateData as CreateUpdateRoomDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room Updated Successfully",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRooms] });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: RoomDto) {
    try {
      if (isEditMode) {
        formData.roomStatusId = !formData.roomStatusId
          ? formData.roomStatus?.id
          : formData.roomStatusId;
        formData.roomTypeId = !formData.roomTypeId
          ? formData.roomType?.id
          : formData.roomTypeId;

        await updateRoomMutation.mutateAsync(formData);
      } else {
        const { ...dataMutation } = formData;
        await createRoomMutation.mutateAsync(
          dataMutation as CreateUpdateRoomDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  }

  if (!can("WismaApp.Room.View")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Rooms" },
            { name: "Master Room" },
          ]}
        >
          {can("WismaApp.Room.Create") && can("WismaApp.Room.Edit") && (
            <ButtonForm
              isLoading={isLoading}
              isFormVisible={isFormVisible}
              setIsFormVisible={(visible) => {
                if (visible) {
                  handleReset();
                }
                setIsFormVisible(visible);
              }}
            />
          )}
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              isRoomStatus={isRoomStatus}
              setRoomStatus={(option) =>
                setRoomStatus({
                  label: option.label,
                  value: String(option.value),
                })
              }
              isRoomType={isRoomType}
              setRoomType={(option) =>
                setRoomType({
                  label: option.label,
                  value: String(option.value),
                })
              }
              roomStatusOptions={roomStatusOptions}
              roomTypeOptions={roomTypeOptions}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTableV2
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  price: formatCurrencyIDR(e.price ?? 0),
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          rowKey="id"
          pageSize={pagination.pageSize}
          isLoading={isLoading}
          totalCount={data?.totalCount ?? 1}
          setPagiaton={setPagiaton}
          searchTerms={searchTerms}
          setSearchTerms={setSearchTerms}
          selectFilters={selectFilters}
          setSelectFilters={setSelectFilters}
          onFilterChange={handleFilterChange}
          filterSelectTable={filterSelectTable}
          onSortChange={setSortConfig}
        />
        {/* </div> */}
      </div>
    </div>
  );
}
