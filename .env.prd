NODE_ENV="production"
NEXT_PUBLIC_STACK_ENV="production"
# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Prisma
# https://www.prisma.io/docs/reference/database-reference/connection-urls#env
DATABASE_URL="sqlserver://**************:1433;database=fowisma_dev;user=fowisma_dev;password=***********;encrypt=false;TrustServerCertificate=true"
PRISMA_CLI_QUERY_ENGINE_TYPE=library

# Next Auth
# You can generate a new secret on the command line with:
# openssl rand -base64 32
# https://next-auth.js.org/configuration/options#secret
# NEXTAUTH_SECRET=""
NEXTAUTH_URL="https://wisma.imip.co.id"
NEXTAUTH_SECRET="V+3XnKuWIgVPk3mbuSKVVWXAYlNJjGnSf8Bg21M3734="

AUTH_URL="https://wisma.imip.co.id"
AUTH_SECRET="HxY/nu2KGl5DqujLkeb3ofzEMjSiYMIJrpjopx8djRg="
NEXT_PUBLIC_API_SECRET="x0daROpeUGca43YxykTYK6Iw9ffEYuhfZYhoqWFa9DrVZc3g4IjBdWhCDict80v1"
AUTH_PREFIX="wisma-prd-"

# Public enviroment
NEXT_PUBLIC_NEXTURL="https://wisma.imip.co.id"
NEXT_PUBLIC_IMIP_AUTH_URL="https://auth.imip.co.id"

# Next Auth Discord Provider
IMIP_AUTH_CLIENT_ID="9BEBE618-45DC-4A59-A935-02707D5CB00A"
IMIP_AUTH_CLIENT_SECRET="Ki0NFPZ3QuFZ9RqUbtVFiFT09mGvBo6fVS7tdwYo"
IMIP_AUTH_URL="https://auth.imip.co.id"

TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=-********** # WISMA Log