import { type PagedResultDtoOfGuestDto } from '@/client';
import { type SelectOption } from "rizzui";

export const getOptionGuests = (data: PagedResultDtoOfGuestDto | undefined) => {
  // const data = await getDataGuests();
  const options: SelectOption[] = data?.items?.map(val => ({
    label: `${val.identityNumber} - ${val.fullname}`,
    value: val.identityNumber ?? `${val.fullname}`,
    id: val.id ?? 0,
    ...val
  })) ?? [];
  return [
    {label: "Select Guest", value: 0, disabled: true},
    ...options
  ];
}
