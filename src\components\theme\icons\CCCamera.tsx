export default function CCCameraIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        d="M3.00289 21.1372H1.49654C1.30877 21.1372 1.15771 20.9861 1.15771 20.7984V11.1123C1.15771 10.9245 1.30877 10.7734 1.49654 10.7734H3.00289C3.49983 10.7734 3.9036 11.1772 3.9036 11.6741V20.2365C3.9036 20.7334 3.49983 21.1372 3.00289 21.1372ZM1.83536 20.4596H3.00289C3.12571 20.4596 3.22595 20.3593 3.22595 20.2365V11.6741C3.22595 11.5513 3.12571 11.4511 3.00289 11.4511H1.83536V20.4596Z"
        fill="currentColor"
      />
      <path
        d="M5.63454 18.9746H3.5649C3.37713 18.9746 3.22607 18.8236 3.22607 18.6358V13.2753C3.22607 13.0876 3.37713 12.9365 3.5649 12.9365H5.63313C5.8209 12.9365 5.97196 13.0876 5.97196 13.2753V18.6358C5.97337 18.8236 5.82231 18.9746 5.63454 18.9746ZM3.90372 18.297H5.29431V13.6142H3.90372V18.297Z"
        fill="currentColor"
      />
      <path
        d="M8.79235 17.3278H5.63423C5.44647 17.3278 5.29541 17.1768 5.29541 16.989V14.9193C5.29541 14.7316 5.44647 14.5805 5.63423 14.5805H7.93682C8.07659 14.5805 8.20929 14.5255 8.30812 14.4266L10.2182 12.5165C10.3509 12.3838 10.5655 12.3838 10.6968 12.5165C10.8281 12.6492 10.8295 12.8638 10.6968 12.9951L8.7867 14.9052C8.55941 15.1325 8.25729 15.2582 7.93682 15.2582H5.97306V16.6502H8.79235C8.93212 16.6502 9.06482 16.5951 9.16365 16.4963L12.2681 13.3932C12.4008 13.2605 12.6154 13.2605 12.7467 13.3932C12.8794 13.5259 12.8794 13.7405 12.7467 13.8718L9.64223 16.9763C9.41635 17.2036 9.11423 17.3278 8.79235 17.3278Z"
        fill="currentColor"
      />
      <path
        d="M19.8594 13.2877C19.4796 13.2877 19.0985 13.2101 18.7385 13.0562L7.7733 8.37477C7.48247 8.25054 7.258 8.02183 7.14083 7.72818C7.02365 7.43454 7.02647 7.11406 7.15071 6.82324L7.79589 5.31124C8.05142 4.71124 8.74741 4.43312 9.34741 4.68865L22.3681 10.2482C22.6829 10.3823 22.8284 10.7479 22.6942 11.0614L22.4853 11.5512C22.186 12.2529 21.6312 12.795 20.9225 13.0802C20.5794 13.2185 20.2194 13.2877 19.8594 13.2877ZM8.88436 5.27171C8.68812 5.27171 8.50177 5.38606 8.41989 5.57806L7.77471 7.09006C7.72247 7.2143 7.71965 7.35124 7.77047 7.47548C7.8213 7.59971 7.91589 7.69854 8.04012 7.75077L19.0039 12.4322C19.5389 12.6609 20.1305 12.6665 20.6712 12.4505C21.2119 12.2345 21.634 11.8195 21.8627 11.2844L22.0491 10.8482L9.082 5.31265C9.01706 5.28442 8.95071 5.27171 8.88436 5.27171Z"
        fill="currentColor"
      />
      <path
        d="M19.1973 11.6955C19.5404 11.6955 19.8185 11.4174 19.8185 11.0743C19.8185 10.7312 19.5404 10.4531 19.1973 10.4531C18.8543 10.4531 18.5762 10.7312 18.5762 11.0743C18.5762 11.4174 18.8543 11.6955 19.1973 11.6955Z"
        fill="currentColor"
      />
      <path
        d="M15.8925 15.3428C15.7372 15.3428 15.5805 15.3117 15.4294 15.2482L7.35128 11.7993C6.75128 11.5437 6.47316 10.8477 6.72869 10.2477L7.69999 7.97479C7.73528 7.8915 7.80163 7.82656 7.88493 7.79408C7.96822 7.7602 8.0614 7.76161 8.14469 7.79691L17.7743 11.908C17.9466 11.9814 18.027 12.1804 17.9522 12.3527L16.9809 14.6256C16.7889 15.0746 16.3513 15.3428 15.8925 15.3428ZM15.6962 14.6256C15.9518 14.7343 16.2482 14.6157 16.3583 14.3602L17.1955 12.3979L8.18987 8.5522L7.35269 10.5146C7.24399 10.7701 7.36257 11.0666 7.6181 11.1767L15.6962 14.6256Z"
        fill="currentColor"
      />
      <path
        d="M17.7179 14.711C17.4116 14.711 17.1066 14.6489 16.8172 14.5246C16.7636 14.502 16.7113 14.4766 16.6605 14.4512C16.4939 14.3651 16.429 14.1604 16.5151 13.9938C16.6012 13.8272 16.8059 13.7623 16.9725 13.8484C17.0078 13.8667 17.0459 13.8851 17.084 13.9006C17.4822 14.07 17.9212 14.0757 18.3222 13.9147C18.7231 13.7538 19.0379 13.446 19.2073 13.0479C19.2229 13.0098 19.2384 12.9717 19.2511 12.9336C19.3118 12.7571 19.5038 12.6625 19.6817 12.7218C19.8582 12.7825 19.9528 12.9745 19.8935 13.1524C19.8751 13.206 19.8539 13.2611 19.8313 13.3147C19.5899 13.878 19.1438 14.3157 18.5749 14.5444C18.2968 14.6559 18.0073 14.711 17.7179 14.711Z"
        fill="currentColor"
      />
      <path
        d="M19.8198 8.25582C19.6856 8.25582 19.5586 8.17676 19.5063 8.04406C19.3031 7.54429 18.9176 7.15465 18.4221 6.94571C17.9252 6.73676 17.3774 6.73253 16.8776 6.93582C16.704 7.00641 16.5063 6.92312 16.4358 6.74947C16.3652 6.57582 16.4485 6.37818 16.6221 6.30759C17.2885 6.03653 18.0212 6.04077 18.6847 6.32029C19.3482 6.59982 19.8621 7.12218 20.1346 7.78853C20.2052 7.96218 20.1219 8.15982 19.9482 8.23041C19.9059 8.24735 19.8621 8.25582 19.8198 8.25582Z"
        fill="currentColor"
      />
      <path
        d="M21.1626 7.44554C21.0285 7.44554 20.9015 7.36648 20.8492 7.23378C20.1589 5.53825 18.2191 4.71942 16.5222 5.40836C16.3485 5.47895 16.1509 5.39566 16.0803 5.22201C16.0097 5.04836 16.093 4.85072 16.2666 4.78013C17.2563 4.37778 18.3419 4.38483 19.3259 4.79989C20.3099 5.21495 21.0737 5.9886 21.4761 6.97825C21.5466 7.15189 21.4633 7.34954 21.2897 7.42013C21.2473 7.43707 21.205 7.44554 21.1626 7.44554Z"
        fill="currentColor"
      />
      <path
        d="M22.5036 6.63506C22.3695 6.63506 22.2424 6.556 22.1902 6.42329C21.7243 5.27976 20.842 4.38471 19.7041 3.90471C18.5662 3.42471 17.3097 3.41624 16.1662 3.88212C15.9926 3.95271 15.7949 3.86941 15.7243 3.69576C15.6537 3.52212 15.737 3.32447 15.9107 3.25388C16.5728 2.98424 17.2688 2.85294 17.9789 2.86282C18.6636 2.87271 19.3328 3.01247 19.9681 3.28071C20.602 3.54753 21.1695 3.92871 21.6552 4.41294C22.1577 4.91412 22.5488 5.50424 22.8184 6.16776C22.889 6.34141 22.8057 6.53906 22.6321 6.60965C22.5897 6.62659 22.546 6.63506 22.5036 6.63506Z"
        fill="currentColor"
      />
    </svg>
  );
}
