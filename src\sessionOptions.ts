﻿import { type SessionOptions } from 'iron-session'
import crypto from 'crypto'

// Generate a secure password if not provided in environment variables
const getSecurePassword = () => {
  if (process.env.SESSION_PASSWORD) return process.env.SESSION_PASSWORD;

  // In development, generate a random password but warn about it
  if (process.env.NODE_ENV !== 'production') {
    console.warn(
      '⚠️ Using auto-generated session password. Set SESSION_PASSWORD in .env for production.'
    );
    return crypto.randomBytes(32).toString('hex');
  }

  // In production, error out if no password is set
  throw new Error(
    'Session password not set. Please set SESSION_PASSWORD in environment variables.'
  );
};

export const sessionOptions: SessionOptions = {
  password: getSecurePassword(),
  cookieName: 'abp-react-session-id',
  cookieOptions: {
    // secure only works in `https` environments
    // if your localhost is not on `https`, then use: `secure: process.env.NODE_ENV === "production"`
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60 * 24 * 7, // 1 week in seconds
  },
  ttl: 60 * 60 * 24 * 7, // 1 week in seconds
}
