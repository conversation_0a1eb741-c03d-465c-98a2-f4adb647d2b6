export default function CarParkingIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M20.06 3.82H18.9c-.18 0-.29.11-.29.24v3.78c0 .16.2.24.38.24.19 0 .38-.08.38-.24V6.5h.65c.77 0 1.37-.36 1.37-1.32v-.04c0-.95-.58-1.3-1.32-1.3Zm.57 1.4c0 .46-.24.68-.62.68h-.65V4.49h.65c.38 0 .62.21.62.67v.07ZM13.74 5.3a5.82 5.82 0 0 0-8.29 0 .34.34 0 1 0 .49.49 5.14 5.14 0 0 1 7.31 0 .34.34 0 0 0 .49 0 .34.34 0 0 0 0-.49Z"
      />
      <path
        fill="currentColor"
        d="M12.33 6.71a3.87 3.87 0 0 0-5.47 0 .34.34 0 1 0 .49.49 3.18 3.18 0 0 1 4.5 0 .34.34 0 0 0 .48 0 .34.34 0 0 0 0-.49ZM10.94 8.1a1.9 1.9 0 0 0-2.69 0 .34.34 0 1 0 .49.49 1.22 1.22 0 0 1 1.72 0 .34.34 0 0 0 .48 0 .34.34 0 0 0 0-.49ZM16.82 14.76a5.6 5.6 0 0 1-.43-.26l-2.26-1.53a4.12 4.12 0 0 0-2.32-.71H7.66c-1.1 0-2.14.43-2.92 1.2-.4.4-.85.74-1.35 1a.9.9 0 0 0 .41 1.68H5.2a.34.34 0 0 0 0-.69H3.8a.2.2 0 0 1-.1-.38c.56-.3 1.08-.67 1.53-1.12.65-.65 1.51-1 2.43-1h.93v2.5H7.46a.34.34 0 1 0 0 .69h9.37c.28 0 .51-.23.51-.51a1 1 0 0 0-.52-.87Zm-7.54.7v-2.52h2.53c.7 0 1.36.2 1.94.6L16 15.06l.49.3c.04.02.07.06.1.1H9.28ZM10.12 16.66H9.07a.34.34 0 0 0 0 .68h1.05a.34.34 0 0 0 0-.68Z"
      />
      <path
        fill="currentColor"
        d="m23.7 16.37-.1-.18a2.81 2.81 0 0 0-1.86-1.3l-.88-.18v-4.47h2.28c.47 0 .85-.38.85-.85V2.73a.85.85 0 0 0-.85-.86h-6.66a.85.85 0 0 0-.85.86v2.52a.34.34 0 1 0 .68 0V2.73c0-.1.08-.17.17-.17h6.66c.1 0 .17.07.17.17v6.66c0 .09-.08.16-.17.16h-6.66a.17.17 0 0 1-.17-.16V7.48a.34.34 0 1 0-.68 0v1.9c0 .48.38.86.85.86h2.6v4.12l-.46-.1a4.82 4.82 0 0 1-1.64-.77l-2.26-1.53a5.17 5.17 0 0 0-2.9-.88H7.65c-1.38 0-2.68.53-3.66 1.5a4.5 4.5 0 0 1-2.25 1.22l-.5.1A1.6 1.6 0 0 0 0 15.47v3.3c0 .4.23.76.6.92l1.36.6a2.26 2.26 0 0 0 2.22 1.85 2.26 2.26 0 0 0 2.18-1.66h11.36a2.25 2.25 0 0 0 2.18 1.66 2.25 2.25 0 0 0 2.18-1.66h.65c.7 0 1.27-.56 1.27-1.26v-1.78c0-.37-.1-.74-.3-1.06Zm-3.94-6.13h.41v4.34l-.41-.09v-4.25ZM5.3 20.98a1.57 1.57 0 0 1-2.24 0 1.59 1.59 0 0 1-.32-1.78v-.01a1.54 1.54 0 0 1 .13-.23l.03-.03.03-.04.02-.03.03-.04.02-.02.06-.06.01-.01.05-.05h.01l.05-.05a1.59 1.59 0 0 1 2 0l.05.04.01.01a1.66 1.66 0 0 1 .12.12l.03.03a1.73 1.73 0 0 1 .12.16c.4.62.33 1.45-.2 1.99Zm15.72 0a1.57 1.57 0 0 1-2.24 0 1.59 1.59 0 0 1-.2-1.99 1.54 1.54 0 0 1 .12-.16l.02-.03.06-.06a1.57 1.57 0 0 1 2.3.06l.03.03.03.04.03.04.01.02.04.05V19c.4.62.34 1.45-.2 1.99Zm2.3-1.77c0 .32-.27.58-.59.58h-.56c-.01-.5-.19-.97-.5-1.36h-.01a2.63 2.63 0 0 0-.06-.07l-.02-.02a2.48 2.48 0 0 0-.06-.07l-.01-.02a2.25 2.25 0 0 0-3.21 0l-.01.02-.07.07-.02.01-.06.08c-.32.4-.5.86-.5 1.36H6.44c-.01-.5-.19-.97-.5-1.36a2.38 2.38 0 0 0-.07-.08l-.01-.01a2.3 2.3 0 0 0-1.69-.75 2.26 2.26 0 0 0-1.68.74c0 .01 0 .02-.02.03a2.24 2.24 0 0 0-.12.14l-.01.02-.12.18a2.29 2.29 0 0 0-.3.83l-1.06-.47a.32.32 0 0 1-.19-.3v-3.3c0-.43.3-.8.72-.88l.5-.1c.98-.21 1.87-.7 2.59-1.4a4.47 4.47 0 0 1 3.17-1.32h4.15c.9 0 1.78.27 2.52.77l2.26 1.53c.46.3 1.15.73 1.9.88l3.12.62c.58.12 1.1.47 1.4.99l.1.18c.14.2.2.45.2.7v1.78Z"
      />
      <path
        fill="currentColor"
        d="M4.92 19.12a1.03 1.03 0 0 0-1.47 0 1.03 1.03 0 0 0 0 1.47 1.03 1.03 0 0 0 1.47 0c.4-.4.4-1.06 0-1.47Zm-.49.99a.35.35 0 0 1-.5 0 .35.35 0 0 1 0-.5.35.35 0 0 1 .5 0c.14.14.14.36 0 .5ZM20.64 19.12a1.03 1.03 0 0 0-1.47 0 1.04 1.04 0 1 0 1.47 0Zm-.49.99a.35.35 0 0 1-.5 0 .35.35 0 1 1 .5 0Z"
      />
    </svg>
  );
}
