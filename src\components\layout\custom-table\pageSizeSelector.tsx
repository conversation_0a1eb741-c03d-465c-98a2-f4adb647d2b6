import type { PageSizeSelectorProps } from "@/interfaces/table/pageSizeSelector";
import React from "react";
import { PiCaretDownBold } from "react-icons/pi";
import { Select } from "rizzui";

const paginationLimitOptions = [5, 10, 15, 20, 25, 50].map((v, idx) => ({
  id: idx,
  label: String(v),
  value: v,
}));

const PageSizeSelector: React.FC<PageSizeSelectorProps> = ({
  itemsPerPage,
  setItemsPerPage,
}) => {
  return (
    <Select
      options={paginationLimitOptions}
      onChange={setItemsPerPage}
      size="sm"
      variant="flat"
      value={itemsPerPage}
      getOptionValue={({ value }) => value}
      suffix={<PiCaretDownBold />}
      dropdownClassName="!p-1.5 border w-12 border-gray-100 !z-10 shadow-lg dropdownClassName"
      className="ms-1 mt-4 w-auto bg-white [&_button]:font-medium"
      optionClassName="px-1"
    />
  );
};

export default PageSizeSelector;
