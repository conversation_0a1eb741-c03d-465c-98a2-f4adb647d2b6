export default function TrendingIcon({
  // strokeWidth,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="10" cy="10" r="10" fill="#D7E3FE" fillOpacity="0.7" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.3075 4.79743C11.3807 4.83818 11.4381 4.90227 11.4707 4.97948C11.5032 5.05669 11.5089 5.14258 11.487 5.22343L10.491 8.87493H14.125C14.1981 8.87493 14.2695 8.89629 14.3306 8.93638C14.3918 8.97646 14.4398 9.03353 14.4689 9.10057C14.498 9.1676 14.5069 9.24168 14.4945 9.31369C14.4821 9.38571 14.4489 9.45253 14.399 9.50593L9.14898 15.1309C9.09179 15.1923 9.01567 15.2328 8.93279 15.2458C8.84991 15.2588 8.76505 15.2437 8.69178 15.2028C8.61852 15.1619 8.56109 15.0976 8.52867 15.0202C8.49626 14.9428 8.49073 14.8568 8.51298 14.7759L9.50898 11.1249H5.87498C5.8019 11.1249 5.73041 11.1036 5.6693 11.0635C5.6082 11.0234 5.56014 10.9663 5.53103 10.8993C5.50192 10.8323 5.49303 10.7582 5.50546 10.6862C5.51789 10.6141 5.55109 10.5473 5.60098 10.4939L10.851 4.86893C10.9082 4.80775 10.9842 4.76748 11.0669 4.75452C11.1496 4.74156 11.2343 4.75667 11.3075 4.79743Z"
        fill="#3872FA"
      />
    </svg>
  );
}
