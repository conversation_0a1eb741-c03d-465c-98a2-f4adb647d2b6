"use client";

import <PERSON><PERSON>eader<PERSON>ustom from "@/app/shared/page-header-custom";
import React, { useEffect, useState } from "react";
import ReservationDetail from "./_component/reservation-detail";
import BookingRoom from "./_component/booking-room";
import swal from "sweetalert";
import CheckoutFnB from "./_component/fnb";
import Payments from "./_component/payments";
import CheckoutServices from "./_component/services";
import LoadingScreen from "@/components/theme/ui/loading-screen";
import { useApiAppReservationDetailsById } from "@/lib/hooks/useApiAppReservationDetailsById";
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { But<PERSON>, Title } from "rizzui";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { formatDate } from "@/lib/helper/format-date";
import { useRouter } from "next/navigation";
import { preventEnter } from "@/lib/helper/prevent-enter";
import { randStr } from "@/lib/helper/generate-random-string";
import { FieldValues, useForm } from "react-hook-form";
import {
  postApiAppPayments,
  postApiAppReservationDetails,
  postApiInvoiceGenerateWisma,
  putApiAppReservationDetailsById,
  type CreateUpdatePaymentDetailsDto,
  type CreateUpdatePaymentGuestsDto,
  type CreateUpdatePaymentsDto,
  type CreateUpdateReservationDetailsDto,
  type PaymentSourceType,
  type ReservationDetailsDto,
  type ReservationFoodAndBeveragesDto,
  type ReservationRoomsDto,
  type ReservationsDto,
} from "@/client";
import Link from "next/link";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function FormCheckout({
  wiithHeader = true,
  className,
  id,
}: {
  wiithHeader?: boolean;
  className?: string;
  id: string;
}) {
  const {
    register,
    unregister,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  // DEFINE HOOKS
  const queryClient = useQueryClient();
  const router = useRouter();

  // DEFINE VARIABLES
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const [postLoading, setPostLoading] = useState<boolean>(false);

  // GET DATA
  const { isLoading: isLoadingDetails, data: dataSourceDet } = useApiAppReservationDetailsById(id, refreshId);
  const { isLoading: isLoadingResvDetOptions, data: statusResvDetOptions } = useMasterStatusByDocTypeOptions("reservationDetails");
  const { data: statusPaymentOptions } = useMasterStatusByDocTypeOptions("paymentDetails");
  const isLoading = isLoadingDetails || isLoadingResvDetOptions;

  // FNB & SERVICES
  const fnbs = watch("reservationDetails.0.reservationFoodAndBeverages",) as (ReservationFoodAndBeveragesDto & { selected: boolean })[];
  const selectedFnbs = fnbs?.filter((fnb) => fnb.selected === true);

  const services = watch("reservationDetails.0.reservationRooms",) as (ReservationRoomsDto & { selected: boolean })[];
  const selectedServices = services?.filter((serv) => serv.selected === true);

  // EACH PRICES
  const paymentStatus = watch("reservationDetails.0.paymentStatus.code",) as string;
  const roomPrice = ["pending", "paid"].includes(paymentStatus) ? 0 : ((watch("reservationDetails.0.price") as number) ?? 0);
  const fnbsPrice = (watch("payments.fnbTotalPrice") as number) ?? 0;
  const resvRoomPrice = (watch("payments.serviceTotalPrice") as number) ?? 0;
  const paidTax = (watch("payments.paidTax") as number) ?? 0;

  // TOTAL PRICE
  const totalPrice = roomPrice + fnbsPrice + resvRoomPrice;
  const grantTotal = totalPrice + paidTax;

  // DEFINE MUTATIONS
  const updateReservationDetailsMutation = useMutation({
    mutationFn: async (dataMutation: ReservationDetailsDto) => {
      const { id, ...updateData } = dataMutation;
      return putApiAppReservationDetailsById({
        path: { id: id! },
        body: updateData as CreateUpdateReservationDetailsDto,
      });
    },
    onSuccess: async () => {
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationDetailsById],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const createPaymentsMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdatePaymentsDto) => {
      // const { id, ...updateData } = dataMutation;
      return postApiAppPayments({
        body: dataMutation,
      });
    },
    onSuccess: async () => {
      // router.push('/checkout'); // Replace with your target URL
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationDetailsById],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const createReservationDetailMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateReservationDetailsDto) =>
      postApiAppReservationDetails({
        body: dataMutation,
      }),
    onSuccess: async () => {
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetReservations] });
      // router.push('/checkin');
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  // DEFINE FUNCTIONS submitData: FieldValues, command: string
  const handlePost = async (submitData: FieldValues, command: string) => {
    // -- GET RESERVATION DETAILS DATA --
    const resv = JSON.parse(JSON.stringify(submitData)) as ReservationsDto;
    const resvDet = resv.reservationDetails?.[0];
    const statusId = statusResvDetOptions?.find((opt) => opt.code == "checkout",)?.value; // code fore Check Out

    // SET CHECK OUT DATE IF STATUS IS CHECK IN
    if (dataSourceDet?.status?.code === "checkin" && resvDet) {
      resvDet.checkOutDate = formatDate(new Date(), "datetime")
    }
    const data: ReservationDetailsDto = {
      id: resvDet?.id,
      reservationId: resvDet?.reservationId,
      roomId: resvDet?.roomId,
      statusId: statusId as string,
      guestId: resvDet?.guestId,
      checkInDate: resvDet?.checkInDate,
      checkOutDate: resvDet?.checkOutDate,
      rfid: resvDet?.rfid,
      price: resvDet?.price,
      // "paymentStatusId": null,
    };

    // -- GET PAYMENTS DATA --
    const paymentMethod = resv.paymentMethod?.name;
    const statusPaymentCode = paymentMethod == "Cash" ? "paid" : "pending"; // JIKA CASH MAKA STATUS LANGSUNG PAID, JIKA TIDAK MAKA PENDING
    const statudPaymentId = statusPaymentOptions?.find(
      (opt) => opt.code == statusPaymentCode,
    )?.value as string; // GET STATUSID BY PAYMENT STATUS

    const paymentRoom: CreateUpdatePaymentDetailsDto[] = [
      ...(resvDet?.status?.code === "checkin"
        ? [
          {
            reservationDetailsId: resvDet?.id ?? "",
            sourceType: 1 as PaymentSourceType, // [ReservationRoom = 1, ReservationRoomFoodAndBeverage = 2, ReservationRoomService = 3]
            sourceId: resvDet?.id ?? "",
            amount: Number(resvDet?.price) ?? 0,
            qty: getValues("reservationDetails.0.days") as number,
            unitPrice: resvDet?.room?.price ?? 0,
          },
        ]
        : []), // IF STATUS CHECKOUT THEN ADD ROOM PAYMENT
    ];
    const paymentFnb: CreateUpdatePaymentDetailsDto[] = selectedFnbs?.map(
      (fnb) => {
        return {
          reservationDetailsId: resvDet?.id ?? "",
          sourceType: 2, // [ReservationRoom = 1, ReservationRoomFoodAndBeverage = 2, ReservationRoomService = 3]
          sourceId: fnb.id ?? "",
          amount: Number(fnb.totalPrice) ?? 0,
          qty: Number(fnb.quantity),
          unitPrice: Number(fnb.foodAndBeverage?.price) ?? 0,
        };
      },
    );
    const paymentServices: CreateUpdatePaymentDetailsDto[] =
      selectedServices?.map((serv) => {
        return {
          reservationDetailsId: resvDet?.id ?? "",
          sourceType: 3, // [ReservationRoom = 1, ReservationRoomFoodAndBeverage = 2, ReservationRoomService = 3]
          sourceId: serv.id ?? "",
          amount: Number(serv.totalPrice) ?? 0,
          qty: Number(serv.quantity),
          unitPrice: Number(serv.services?.price) ?? 0,
        };
      });
    const paymentGuest: CreateUpdatePaymentGuestsDto[] = [
      {
        guestId: resvDet?.guestId ?? "",
        amountPaid: 0,
      },
    ];
    const paymentDetails: CreateUpdatePaymentDetailsDto[] = [
      ...paymentRoom,
      ...paymentFnb,
      ...paymentServices,
    ];
    const payments: CreateUpdatePaymentsDto = {
      totalAmount: totalPrice,
      paidAmount: Number(getValues("payments.paidAmount")),
      vatRate: Number(getValues("payments.taxRate")),
      vatAmount: Number(getValues("payments.paidTax")),
      grantTotal: grantTotal,
      paymentCode: "-", // GENERATED FROM BACKEND
      transactionDate: formatDate(new Date()),
      reservationsId: String(getValues("id")),
      paymentMethodId: String(getValues("payments.paymentMethodId")),
      statusId: statudPaymentId,
      taxId: getValues("payments.taxId")
        ? String(getValues("payments.taxId"))
        : null,
      paymentDetails: paymentDetails,
      paymentGuests: paymentGuest,
    };
    // console.log('payments', payments)
    // setPostLoading(false);
    // return;

    // CREATE PAYMENTS
    // -- UPDATE RESERVATION DETAILS --
    // console.log(command);
    // setPostLoading(false);
    // return;

    if (command === "changeRoom") {
      const oldResvDet = resv.reservationDetails?.[0];
      const newResvDet = resv.reservationDetails?.[1];
      // UPDATE RESERVATION DETAILS
      const confirmation = (await swal({
        title: "Change Room?",
        // text: "Change Room Will Checkout Current Room, \nAnd Checkin To New Room! \n\nAre You Sure?",
        content: {
          element: "div",
          attributes: {
            innerHTML: `
              <div style="text-align: center;">
                Change Room Will Checkout Current Room (${oldResvDet?.room?.roomNumber}),<br/>
                And Checkin To New Room (${newResvDet?.room?.roomNumber})!<br/><br/>
                <b>Are You Sure?</b>
              </div>
            `
          }
        },
        icon: "info",
        buttons: ["Cancel", "Yes"],
      })) as unknown as boolean;
      if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

      setPostLoading(true);

      // DATA FOR NEW CHANGED ROOMS
      const newData: CreateUpdateReservationDetailsDto = {
        reservationId: newResvDet?.reservationId,
        roomId: newResvDet?.roomId,
        statusId: statusResvDetOptions?.find(opt => opt.code == "checkin")?.value as string,
        guestId: newResvDet?.guestId,
        checkInDate: newResvDet?.checkInDate,
        checkOutDate: newResvDet?.checkOutDate,
        rfid: newResvDet?.rfid,
        price: newResvDet?.price,
      };
      // CHECK OUT CURRENT ROOM
      data.checkOutDate = newResvDet?.checkInDate;
      await updateReservationDetailsMutation.mutateAsync(data);
      // CHECK IN NEW ROOM
      await createReservationDetailMutation.mutateAsync(newData);
      // return;
      await swal({
        title: "Success",
        text: "Change Room Successfully",
        icon: "success",
      });
      router.push("/checkout"); // Redirect to checkout page after successful payment
    }
    else if (command === "preview-payments") {

    }
    else if (command === "checkout") {
      // UPDATE RESERVATION DETAILS
      const confirmation = (await swal({
        title: "Checkout",
        text: "Checkout This Reservation?",
        icon: "info",
        buttons: ["Cancel", "Yes"],
      })) as unknown as boolean;
      if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

      await updateReservationDetailsMutation.mutateAsync(data);
      const res = await createPaymentsMutation.mutateAsync(payments);
      if (res?.data?.id) {
        await postApiInvoiceGenerateWisma({
          body: {
            paymentId: res?.data?.id,
            // "templateId": "721EA30F-A891-7DCE-5BBB-3A1A08D943BE",
            includePaymentDetails: true,
            useAdvancedTable: true,
            generatePdf: true,
            // "customFilename": "invoice-" + res?.data?.paymentCode,
          },
        });
        await swal({
          title: "Success",
          text: "Checkout Successfully",
          icon: "success",
        });
        router.push("/checkout"); // Redirect to checkout page after successful payment
      }
    }
    setPostLoading(false);
  };
  
  // DEFINE EFFECTS
  // set setValue from data source
  useEffect(() => {
    // SET RESERVATIONS
    Object.entries(dataSourceDet?.reservation ?? {}).forEach(([key, value]) => {
      setValue(key, value);
    });
    unregister("reservationDetails");
    // SET RESERVATIONS DETAILS
    Object.entries(dataSourceDet ?? {}).forEach(([key, value]) => {
      setValue(`reservationDetails.0.${key}`, value);
    });

    // SET CHECK OUT DATE IF STATUS IS CHECK IN
    if (dataSourceDet?.status?.code === "checkin") {
      setValue('reservationDetails.0.checkOutDate', new Date())
    }

    // SET FNB AND SERVICES TO UNPAID ONLY IF STATUS IS CHECK OUT
    const fnbs = watch("reservationDetails.0.reservationFoodAndBeverages",) as (ReservationFoodAndBeveragesDto & { selected: boolean })[];
    const unpaidFnbs = fnbs?.filter((fnb) => fnb.paymentStatus?.code !== "paid",);
    setValue("reservationDetails.0.reservationFoodAndBeverages", unpaidFnbs);

    const services = watch("reservationDetails.0.reservationRooms",) as (ReservationRoomsDto & { selected: boolean })[];
    const unpaidServices = services?.filter((serv) => serv.paymentStatus?.code !== "paid",);
    setValue("reservationDetails.0.reservationRooms", unpaidServices);
  }, [dataSourceDet]);

  useEffect(() => {
    setValue("payments.totalPrice", totalPrice);
    setValue("payments.grantTotal", grantTotal);
  }, [totalPrice, grantTotal]);

  // REDIRECT TO DETAIL IF PAYMENTS IS PAID
  // useEffect(() => {
  //   const paymentStatus = watch("reservationDetails.0.paymentStatus.code",) as string;
  //   if (paymentStatus === "paid") {
  //     const resvId = getValues("reservationDetails.0.id") as string;
  //     // router.push(`/detail/${resvId}`);
  //   }
  // }, [watch("reservationDetails.0.paymentStatus.code")]);

  // --PERMISSION--
  const { can } = useGrantedPolicies();
  if (!(can("WismaApp.Reservation.Edit") && can("WismaApp.Payment.Create"))) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 @container " + className}>
      {(isLoading || postLoading) && <LoadingScreen />}
      {wiithHeader && (
        <>
          <PageHeaderCustom
            breadcrumb={[
              { name: "Reservation", href: "/reservation" },
              { name: "Checkout", href: "/checkout" },
              { name: getValues("reservationCode") as string, href: "" },
            ]}
          >
            {/* <Button
              type="button"
              size="sm"
              variant="outline"
              className="rounded-lg px-4 py-2"
              onClick={() => {
                console.log('getValues', getValues());
              }}
            >
              Get Values
            </Button> */}
          </PageHeaderCustom>
        </>
      )}
      <form
        onSubmit={handleSubmit((e) => handlePost(e, ""))}
        onKeyDown={preventEnter}
        className="space-y-4"
      >
        <div className="grid grid-cols-3 gap-8">
          <div className="col-span-1">
            <ReservationDetail
              isLoading={isLoadingResvDetOptions}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
            />
          </div>
          <div className="col-span-2">
            <BookingRoom
              isLoading={false}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              handleSubmit={handleSubmit}
              handlePost={handlePost}
            />
            <CheckoutFnB
              register={register}
              unregister={unregister}
              setValue={setValue}
              fnbs={fnbs}
            />
            <CheckoutServices
              register={register}
              unregister={unregister}
              setValue={setValue}
              services={services}
            />
          </div>
        </div>
        {/* BUTTON */}
        <div className="grid grid-cols-3 gap-8">
          <div className="col-span-1"></div>
          <div className="col-span-2">
            <Payments
              isLoading={false}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              selectedFnbs={selectedFnbs}
              selectedServices={selectedServices}
            />
            <div className="mt-3 rounded border-2 bg-gray-50 px-4 py-2">
              <div className="flex justify-between">
                <div className="">
                  <Title as="h6" className="mb-0 p-1 text-gray-600">
                    {formatCurrencyIDR(grantTotal)}
                  </Title>
                </div>
                <div className="flex gap-2">
                  <Button
                    // type="submit"
                    size="sm"
                    isLoading={isLoadingResvDetOptions}
                    className="rounded-lg bg-blue-500 px-4 py-2 text-white"
                    onClick={async () => { await handleSubmit((e) => handlePost(getValues(), "checkout"))(); }}
                  >
                    {getValues("reservationDetails.0.status.code") ==
                      "checkout" ? (
                      <>Process Payment</>
                    ) : (
                      <>Check Out</>
                    )}
                  </Button>
                  <Link href={"/checkout"}>
                    <Button
                      type="button"
                      size="sm"
                      disabled={isLoading}
                      variant="outline"
                      className="rounded-lg px-4 py-2 disabled:bg-gray-400"
                    >
                      Quit
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
