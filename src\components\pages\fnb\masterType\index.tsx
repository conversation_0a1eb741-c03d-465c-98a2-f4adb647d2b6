"use client";
import React, { useEffect, useState } from "react";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useMasterFnbType } from "@/lib/hooks/fnb/masterFnbType";
import {
  deleteApiAppTypeFoodAndBeverageById,
  postApiAppTypeFoodAndBeverage,
  putApiAppTypeFoodAndBeverageById,
} from "@/client";
import type {
  CreateUpdateTypeFoodAndBeverageDto,
  TypeFoodAndBeverageDto,
} from "@/client";
import type { SelectOption } from "rizzui";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function MasterFnBType({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isFnbType, setDataFnBType] = useState<TypeFoodAndBeverageDto[]>([]);
  const [isStatus, setStatus] = useState<SelectOption | undefined>();
  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 100,
  });

  const { isLoading, data } = useMasterFnbType(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
        status: item.status?.name ?? "",
      }));
      setDataFnBType(mappedData as TypeFoodAndBeverageDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "name", title: "Name", filter: "text" as const },
    { dataIndex: "status.name", title: "Status", filter: "select" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
    setStatus(undefined);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetRoomType],
    });
  };

  const deleteFnbTypeMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppTypeFoodAndBeverageById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "F&B type deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetFnbType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "F&B type will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete f&b type with id:", id);
        await deleteFnbTypeMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the f&b type. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const data = isFnbType.find((e) => e.id === id);
    if (data) {
      setIsEditMode(true);
      Object.entries(data).map(([key, value], _index) => {
        if (key === "status") {
          setStatus({
            label: value as string,
            value: value as string,
          });
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createFnbTypeMutation = useMutation({
    mutationFn: async (data: CreateUpdateTypeFoodAndBeverageDto) =>
      postApiAppTypeFoodAndBeverage({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "F&B type created successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetFnbType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateFnbTypeMutation = useMutation({
    mutationFn: async (data: TypeFoodAndBeverageDto) => {
      const { id, ...updateData } = data;
      return putApiAppTypeFoodAndBeverageById({
        path: { id: id! },
        body: updateData as CreateUpdateTypeFoodAndBeverageDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "F&B type updated successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetFnbType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      if (isEditMode) {
        // console.log("Updating f&b type:", formData);
        await updateFnbTypeMutation.mutateAsync(
          formData as TypeFoodAndBeverageDto,
        );
      } else {
        // console.log("Creating f&b type:", formData);
        await createFnbTypeMutation.mutateAsync(
          formData as CreateUpdateTypeFoodAndBeverageDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  if (!can("WismaApp.FoodAndBeverageType.View")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "F&B" },
            { name: "F&B Type" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              isStatus={isStatus}
              setStatus={setStatus}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
