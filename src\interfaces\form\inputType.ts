import {
  type FieldErrors,
  type FieldValues,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  type UseFormWatch,
} from "react-hook-form";
import { type SelectOption } from "rizzui";

export interface InputProps {
  size?: "sm" | "md" | "lg";
  label: string;
  name: string;
  disabled?: boolean;
  className?: string;
  inputClassName?: string;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  errors: FieldErrors<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  required?: boolean;
  readOnly?: boolean;
  isLoading?: boolean;
  options?: SelectOption[];
  onChange?: (e?: string | number) => void;
  optionsHeight?: string;
}
