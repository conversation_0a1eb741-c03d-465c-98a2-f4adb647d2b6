import { type PagedResultDtoOfRoomDto } from '@/client';
import { type SelectOption } from "rizzui";

export const getOptionRooms = (data: PagedResultDtoOfRoomDto | undefined) => {
  // const data = await getDataRooms();
  const options: SelectOption[] = data?.items?.map(element => ({
    label: element.roomNumber ?? "",
    value: element.id ?? "",
    price: element.price ?? "",
  })) ?? [];
  return options;
}
