"use client"
import React, { useState } from "react";
import { AdvancedRadio, Button, Input, RadioGroup, Text } from "rizzui";
import { FieldValues, useForm } from "react-hook-form";
import { DateInput } from "@/components/theme/ui/input-type/dates-input";
import { PiCheckCircleFill } from "react-icons/pi";
import { useReportRoomDaily, useReportRoomMonthly, useReportRoomRental } from "@/lib/hooks/useReportRoom";
import { ReportRoomHotTable } from "../_component/reservation-group-hot-table";
import { formatDate } from "@/lib/helper/format-date";
import { Settings } from "handsontable/plugins/nestedHeaders";

export default function ReportRentalRoom() {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const [reportType, setReportType] = useState("rental");
  const reportTypeOptions = [
    {
      value: 'rental',
      title: 'Rental Room',
      description: '',
    },
  ]

  const report = useReportRoomRental("", "");

  const columns = [
    { title: "Date", data: "Date", type: "text", width: 100, },
    { title: "Room No.", data: "RoomNo.", type: "text", width: 100, },
    { title: "Company", data: "Company", type: "text", width: 100, },
    { title: "Guest Name", data: "GuestName", type: "text", width: 100, },
    { title: "Guest ID CARD No. \n (Passport No. for foreigner)\n(KTP NO. for local)", data: "DocumentName", type: "text", width: 100, },
    { title: "ADDRESS ON GUEST GUEST ID CARD", data: "GuestID", type: "text", width: 100, },
    { title: "Nationality", data: "Nationality", type: "text", width: 100 },
    { title: "ID Address", data: "IDAddress", type: "text", width: 100, },
    { title: "Date In", data: "DateIn", type: "text", width: 100, },
    { title: "Date Out", data: "DateOut", type: "text", width: 100, },
    { title: "Length Stay", data: "LengthStay", type: "text", width: 100, },
    { title: "Room Price", data: "RoomPrice", type: "text", width: 100, },
    { title: "Total (with VAT)", data: "TotalwithVAT", type: "text", width: 100, },
    { title: "Cash", data: "PaymentCash", type: "text", width: 100, },
    { title: "Bank", data: "PaymentBank", type: "text", width: 100, },
    { title: "Invoice No", data: "InvoiceNo", type: "text", width: 100, },
    { title: "Remark", data: "Remark", type: "text", width: 100, },
  ]
  const nestedHeaders: Settings =
    [
      [
        'Date',
        'Room No.',
        'Company',
        'Guest Name',
        'Name Of Document',
        'Guest ID Card No. \n (Passport No. for foreigner)\n(KTP NO. for local)',
        { label: 'Address On \n Guest ID Card', colspan: 2 },
        // { label: 'Guest Info', colspan: 2 },
        'Date In',
        'Date Out',
        'Length Stay',
        'Room Price',
        'Total (with VAT)',
        { label: 'Payment', colspan: 2 },
        'Invoice No',
        'Remark'
      ],
      [
        '',
        '',
        '',
        '',
        '',
        '',
        'Nationality',
        'ID Address',
        '',
        '',
        '',
        '',
        '',
        'Cash',
        'Bank',
        '',
        '',
        '',
      ],
    ]

  const reportTitle = `Rental Report at ${formatDate(String(watch('dateStart')), 'date')} to ${formatDate(String(watch('dateEnd')), 'date')}`;


  async function onSubmit(submitData: FieldValues) {
    try {
      console.log('submitData', submitData)
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  }
  return (<div className={"mb-2 @container"}>
    {/* <PageHeaderCustom
      // title={"Reservations"}
      breadcrumb={[
        { name: "Report" },
        { name: "Room" },
      ]}
    >
    </PageHeaderCustom> */}
    <div className="flex justify-center">
      {/* FORM FILTER */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="flex gap-4">
          <div className="">
            <Text as="p" className={"mb-1.5 font-medium"}>
              Select Report type
            </Text>
            <RadioGroup
              value={reportType}
              setValue={setReportType}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // console.log('e', e.target.value)
                setValue('reportType', e.target.value)
              }}
              className="grid grid-cols-1 sm:grid-cols-2 max-w-md mx-auto gap-4 mb-3"
            >
              {reportTypeOptions.map((item) => (
                <AdvancedRadio
                  key={item.value}
                  name="payment"
                  value={item.value}
                  inputClassName="[&:checked~span_.icon]:block"
                  readOnly={true}
                >
                  <span className="flex justify-between">
                    <Text as="b">{item.title}</Text>
                    <PiCheckCircleFill className="icon hidden h-5 w-5 text-secondary" />
                  </span>
                  <Text>{item.description}</Text>
                </AdvancedRadio>
              ))}
            </RadioGroup>
          </div>
          <div className="flex gap-4">
            <DateInput
              label={"Select Report Date"}
              name={"dateStart"}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
            // required={true}
            // size="sm"
            // inline={true}
            />
            <DateInput
              label={"Select Report Date"}
              name={"dateEnd"}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
            // className="w-full"
            // required={true}
            // size="sm"
            // inline={true}
            />
            <div className="pt-6">
              <Button
                // size="sm"
                type="submit"
                // disabled={isLoading}
                className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-green-500`}
              >
                Generate Report
              </Button>
            </div>
            <div className="pt-6">
              <Button
                // size="sm"
                type="button"
                // disabled={isLoading}
                className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-blue-500`}
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = '/reportRentalRoomSample.xlsx';
                  link.download = 'reportRentalRoomSample.xlsx'; // Optional: customize file name
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                Download Report
              </Button>
            </div>
          </div>
        </div>
        {/* {JSON.stringify(errors)} */}
      </form>
    </div>

    <div className="w-full">
      <div>
        {/* HOT TABLE */}
        <ReportRoomHotTable
          isLoading={false}
          nestedHeaders={nestedHeaders}
          title={reportTitle}
          data={report}
          columns={columns}
          withNumber={true}
        />
      </div>
    </div>
  </div>
  );
}
