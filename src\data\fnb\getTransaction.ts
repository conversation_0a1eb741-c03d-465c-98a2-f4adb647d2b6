import type { FnbTransaction } from "@/interfaces/fnb/fnbTransaction";

export const getFnbTransaction: FnbTransaction[] = [
  {
    id: "1",
    name: "<PERSON><PERSON> <PERSON><PERSON>",
    type: "Food",
    qty: 2,
    unitPrice: 50000,
    price: 100000,
  },
  {
    id: "2",
    name: "<PERSON><PERSON>",
    type: "Food",
    qty: 2,
    unitPrice: 35000,
    price: 70000,
  },
  {
    id: "3",
    name: "Air Mineral",
    type: "Drink",
    qty: 2,
    unitPrice: 40000,
    price: 80000,
  },
  {
    id: "4",
    name: "<PERSON><PERSON>",
    type: "Drink",
    qty: 2,
    unitPrice: 75000,
    price: 150000,
  },
];
