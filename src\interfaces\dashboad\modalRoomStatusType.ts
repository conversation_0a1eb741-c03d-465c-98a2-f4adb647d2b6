import type { SelectOption } from "rizzui";

export interface ModalUpdateRoomStatusProps {
  roomId?: string;
  roomNumber?: string | null;
  roomCode?: string | null;
  roomType?: string | null;
  roomStatus?: SelectOption | undefined;
  roomSize?: string | null;
  roomInformation?: string | null;
  roomTypeId?: string | null;
  roomStatusId?: string | null;
  roomPrice?: number | null;
  isModalOpen: boolean;
  setModalOpen: () => void;
}
