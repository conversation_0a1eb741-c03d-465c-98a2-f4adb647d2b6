"use server";
import { type RoomResponse } from "@/interfaces/rooms/roomMaster";
import axios from "axios";


export const getDataRooms = () => {
  return axios.get<RoomResponse>(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room`)
    .then((response) => {
      // console.log('GET getDataRooms', response.data);
      return response.data;
    })
    .catch((error) => {
      console.error("Error fetching reservations:", error);
      throw error;
    });
}
