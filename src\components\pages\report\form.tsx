"use client"
import React, { useEffect, useState } from "react";
import { AdvancedRadio, Button, RadioGroup, Text } from "rizzui";
import { type FieldValues, useForm } from "react-hook-form";
import { DateInput } from "@/components/theme/ui/input-type/dates-input";
import { PiCheckCircleFill } from "react-icons/pi";
import { ReportRoomHotTable } from "./_component/reservation-group-hot-table";
import { useApiReport } from "@/lib/hooks/useApiReport";
import { postApiReportExportExcel, postApiReportPreview, type ReportParameterDto, type ReportPreviewDto, type ReportExecutionDto, ReportDto, type FilterGroup } from "@/client";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import { YearInput } from "@/components/theme/ui/input-type/year-input";
import { MonthInput } from "@/components/theme/ui/input-type/month-input";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { type ColumnSettings } from "node_modules/handsontable/settings";
import swal from "sweetalert"
import { type Settings } from "handsontable/plugins/nestedHeaders";
import { swalError } from "@/lib/helper/swal-error";
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input";

interface ReportParameterAdditional {
  label: string;
}

export default function ReportPreview({ id }: { id: string }) {
  const queryClient = useQueryClient();
  const {
    register,
    unregister,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const filter: FilterGroup = {
    operator: "And",
    conditions: [
      {
        fieldName: "isActive",
        operator: "In",
        value: true,
      },
    ],
  }
  const { data: dataReport } = useApiReport(1, 20, filter);
  const [columns, setColumns] = useState<ColumnSettings[]>([]);
  const [nestedHeaders, setNestedHeaders] = useState<Settings | undefined>([]);
  const [report, setReport] = useState<object[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [reportType, setReportType] = useState("");
  const reportTypeOptions = dataReport?.items?.map(e => {
    return {
      value: e.id ?? "",
      title: e.name ?? "",
      label: e.name ?? ""
      // description: '',
    }
  });
  const selectedReport = dataReport?.items?.find(e => e.id == reportType);
  // const parameters = JSON.parse(selectedReport?.parameters as unknown as string ?? "[]") as (ReportParameterDto & ReportParameterAdditional)[];
  const parameters = (() => { try { return JSON.parse(selectedReport?.parameters as unknown as string ?? "[]") as (ReportParameterDto & ReportParameterAdditional)[] } catch { return [] } })();

  const reportTitle = selectedReport?.name ?? ""

  useEffect(() => {
    unregister('parameters')
    setReport([]);
  }, [reportType]);

  // --- POST/ PUT ---
  const GetReportPreviewMutation = useMutation({
    mutationFn: async (dataMutation: ReportExecutionDto) => {
      setIsLoading(true)
      return postApiReportPreview({
        body: dataMutation,
      })
    },
    onSuccess: async (data) => {
      void queryClient.invalidateQueries({ queryKey: [QueryNames.postApiReportPreview] });

      const nestedHeaders = data.data?.excelHeaderConfig?.headerRows?.map((row) => {
        const cells = row.cells?.map((cell) => {
          return {
            label: cell.text,
            width: 120,
            ...(cell.colSpan && cell.colSpan > 1 ? { colspan: cell.colSpan } : {}),
            // colSpan: (cell.colSpan && cell.colSpan > Number(1)) ? cell.colSpan : undefined,
          };
        }) ?? [];
        return cells;
      }) as ColumnSettings[][] as Settings;
      setNestedHeaders(nestedHeaders ?? undefined);
      console.log('nestedHeaders', nestedHeaders)

      const column = data?.data?.excelHeaderConfig?.columnConfigs?.map((e) => {
        return {
          title: e.columnName,
          data: e.columnName,
          type: "text",
          width: Number(e.width) * 10,
        } as ColumnSettings;
      }) ?? [];

      console.log('column', column)
      // if(nestedHeaders.length) setNestedHeaders(column);
      setColumns(column);
      setReport(data.data?.data ?? []);

      if (column.length == 0) {
        const tempColumn = data.data?.columns?.map((e) => {
          return {
            title: e,
            data: e,
            type: "text",
            width: 150,
          };
        })
        setColumns(tempColumn ?? []);
        setNestedHeaders(undefined);
        console.log('tempColumn', tempColumn)
      }
      setIsLoading(false)
      void queryClient.invalidateQueries({ queryKey: [QueryNames.postApiAppReport] });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const GetReportDownloadMutation = useMutation({
    mutationFn: async (dataMutation: ReportExecutionDto) => {
      return postApiReportExportExcel({
        body: dataMutation,
      })
    },
    onSuccess: async (data) => {
      void queryClient.invalidateQueries({ queryKey: [QueryNames.postApiAppReport] });

      // Buat object URL dari Blob
      const blob = (data as unknown as { data: Blob }).data; // Jika `data` adalah Blob langsung
      const url = window.URL.createObjectURL(blob);

      // FILE NAME
      const filtername = parameters?.map(e => String(getValues(`parameters.${e.name}`))).join('-');
      const filename = `${selectedReport?.name} | ${filtername}.xlsx`;

      // GENERATE DOWNLOAD LINK
      const link = document.createElement('a');
      link.href = url;
      link.download = filename; // Nama file
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Hapus object URL setelah digunakan
      window.URL.revokeObjectURL(url);
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  async function onSubmit(submitData: FieldValues, command: string) {
    try {
      if (command == "preview") {
        setColumns([]);
        setReport([]);
        await GetReportPreviewMutation.mutateAsync(submitData as ReportExecutionDto);
      }
      if (command == "download") {
        await GetReportDownloadMutation.mutateAsync(submitData as ReportExecutionDto);
      }
    } catch (error) {
      // console.error("Error submitting form:", error);
    }
  }
  return (
    <div className={"pl-10"}>
      <PageHeaderCustom
        // title={"Reports"}
        breadcrumb={[
          { name: "Report" },
          { name: "All Report" },
        ]}
      >
      </PageHeaderCustom>
      <div className="mt-6">
        {/* FORM FILTER */}
        <form onSubmit={handleSubmit((e) => onSubmit(e, ""))} className="space-y-4">
          <div className="flex justify-between w-full">
            <div className="flex gap-2">
              <DropdownInput
                className="col-span-2 w-[250px]"
                label={"Report type"}
                name={"reportId"}
                register={register}
                setValue={setValue}
                errors={errors}
                watch={watch}
                getValues={getValues}
                options={reportTypeOptions}
                onChange={(e) => setReportType(String(e))}
                required={true}
              // size="sm"
              />
              {parameters?.length > 0 &&
                parameters.map(e => (
                  <div key={e.name} className="">
                    {e.type == 'date' &&
                      <DateInput
                        label={"Date"}
                        name={`parameters.${e.name}`}
                        register={register}
                        setValue={setValue}
                        errors={errors}
                        watch={watch}
                        getValues={getValues}
                        required={e.required}
                        className="w-[150px]"
                      />
                    }
                    {e.type == 'month' &&
                      <MonthInput
                        label={e.label ?? "Month"}
                        name={`parameters.${e.name}`}
                        register={register}
                        setValue={setValue}
                        errors={errors}
                        watch={watch}
                        getValues={getValues}
                        required={e.required}
                        className="w-[150px]"
                      />
                    }
                    {e.type == 'year' &&
                      <YearInput
                        label={e.label ?? "Year"}
                        name={`parameters.${e.name}`}
                        register={register}
                        unregister={unregister}
                        setValue={setValue}
                        errors={errors}
                        watch={watch}
                        getValues={getValues}
                        required={e.required}
                        className="w-[150px]"
                      />
                    }
                  </div>
                ))}
            </div>
            <div className="grid grid-cols-2 gap-2 pt-6">
              <Button
                // size="sm"
                type="button"
                onClick={async () => { await handleSubmit((e) => onSubmit(e, "preview"))(); }}
                className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-green-500 w-full`}
              >
                Preview
              </Button>
              <Button
                // size="sm"
                type="button"
                className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-blue-500 w-full`}
                onClick={async () => { await handleSubmit((e) => onSubmit(e, "download"))(); }}
              >
                Download
              </Button>
            </div>
          </div>
          <div className="col-span-12">
            <ReportRoomHotTable
              isLoading={isLoading}
              title={reportTitle}
              data={report as Record<string, string | number | null>[]}
              columns={columns}
              nestedHeaders={nestedHeaders}
            />
          </div>


          {/* OLD */}
          <div className="grid grid-cols-6 gap-4">
            {/* <div className="col-span-1">
              <div className="grid grid-cols-2 gap-2">
                <Button
                  // size="sm"
                  type="button"
                  onClick={async () => { await handleSubmit((e) => onSubmit(e, "preview"))(); }}
                  className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-green-500 w-full`}
                >
                  Preview
                </Button>
                <Button
                  // size="sm"
                  type="button"
                  className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-blue-500 w-full`}
                  onClick={async () => { await handleSubmit((e) => onSubmit(e, "download"))(); }}
                >
                  Download
                </Button>
              </div>
              <div className="mt-4">
                <div className="mt-4 mb-2">
                  <Text as="b">
                    Select Report type
                  </Text>
                </div>
                <RadioGroup
                  value={reportType}
                  setValue={setReportType}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setValue('reportId', e.target.value)
                  }}
                  className="grid grid-cols-1 max-w-md mx-auto gap-2 mb-3"
                >
                  {reportTypeOptions?.map((item) => (
                    <AdvancedRadio
                      key={item.value}
                      name="payment"
                      value={item.value}
                      inputClassName="[&:checked~span_.icon]:block"
                    >
                      <span className="flex justify-between">
                        <Text as="b">{item.title}</Text>
                        <PiCheckCircleFill className="icon hidden h-5 w-5 text-secondary" />
                      </span>
                    </AdvancedRadio>
                  ))}
                </RadioGroup>
                {parameters?.length > 0 && (
                  <div className="mt-4">
                    <div className="mt-4 mb-2">
                      <Text as="b">
                        Filter
                      </Text>
                    </div>
                    {parameters.map(e => (
                      <div key={e.name} className="my-1">
                        {e.type == 'date' &&
                          <DateInput
                            label={"Date"}
                            name={`parameters.${e.name}`}
                            register={register}
                            setValue={setValue}
                            errors={errors}
                            watch={watch}
                            getValues={getValues}
                            required={e.required}
                          />
                        }
                        {e.type == 'month' &&
                          <MonthInput
                            label={e.label ?? "Month"}
                            name={`parameters.${e.name}`}
                            register={register}
                            setValue={setValue}
                            errors={errors}
                            watch={watch}
                            getValues={getValues}
                            required={e.required}
                          />
                        }
                        {e.type == 'year' &&
                          <YearInput
                            label={e.label ?? "Year"}
                            name={`parameters.${e.name}`}
                            register={register}
                            unregister={unregister}
                            setValue={setValue}
                            errors={errors}
                            watch={watch}
                            getValues={getValues}
                            required={e.required}
                          />
                        }
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div> */}
            <div className="col-span-5">
              {/* HOT TABLE */}
              {/* <ReportRoomHotTable
                isLoading={isLoading}
                title={reportTitle}
                data={report as Record<string, string | number | null>[]}
                columns={columns}
                nestedHeaders={nestedHeaders}
              /> */}
            </div>
          </div>
          {/* {JSON.stringify(errors)} */}
        </form>
      </div>
    </div>
  );
}
