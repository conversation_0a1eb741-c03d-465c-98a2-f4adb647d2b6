"use client";
import React, { useEffect, useState } from "react";
import PageHeaderCustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  deleteApiMasterPaymentMethodById,
  postApiMasterPaymentMethod,
  putApiMasterPaymentMethodById,
  type PaymentMethodDto,
} from "@/client";
import type { CreateUpdatePaymentMethodDto } from "@/client";
import { useApiMasterPaymentMethod } from "@/lib/hooks/config/masterPaymentMethod";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function MasterPaymentMethod({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isDataPaymentMethod, setDataPaymentMethod] = useState<
    PaymentMethodDto[]
  >([]);

  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 1000,
  });

  const { isLoading, data } = useApiMasterPaymentMethod(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
        information: item.information ?? "",
      }));
      setDataPaymentMethod(mappedData as PaymentMethodDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "name", title: "Name", filter: "text" as const },
    {
      dataIndex: "information",
      title: "Information",
      filter: "text" as const,
    },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetPaymentMethod],
    });
  };

  const deleteMasterPaymentMethodMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiMasterPaymentMethodById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Payment Method deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetPaymentMethod],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Payment Method will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete Payment Method with id:", id);
        await deleteMasterPaymentMethodMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the Payment Method. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const data = isDataPaymentMethod.find((e) => e.id === id);
    if (data) {
      setIsEditMode(true);
      Object.entries(data).map(([key, value], _index) => {
        setValue(key, value);
      });
    }
  };

  const createMasterPaymentMethodMutation = useMutation({
    mutationFn: async (data: CreateUpdatePaymentMethodDto) =>
      postApiMasterPaymentMethod({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Payment Method created successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetPaymentMethod],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateMasterPaymentMethodMutation = useMutation({
    mutationFn: async (data: PaymentMethodDto) => {
      const { id, ...updateData } = data;
      return putApiMasterPaymentMethodById({
        path: { id: id! },
        body: updateData as CreateUpdatePaymentMethodDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Payment Method updated successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetPaymentMethod],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      if (isEditMode) {
        // console.log("Updating Payment Method:", formData);
        await updateMasterPaymentMethodMutation.mutateAsync(
          formData as PaymentMethodDto,
        );
      } else {
        // console.log("Creating Payment Method:", formData);
        await createMasterPaymentMethodMutation.mutateAsync(
          formData as CreateUpdatePaymentMethodDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  if (!can("WismaApp.PaymentMethod")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Config" },
            { name: "Payment Method" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
