import { type SessionType } from "@/interfaces/sessionType";
import { type SetStateAction } from "react";

// type JSONValue = string | number | boolean | JSONObject | JSONArray;
export type CustomType = {
  id: string;
  name: string;
  username: string;
  email: string;
  workLocation: string;
  externalUser: boolean;
  value: string | string[];
  dateValue: string | Date;
  sub: string;
  form: TForm;
  formOptions: TForm;
  type: string;
  notes: TForm;
  note: string;
  label: string;
  options: string | string[] | { label: string; value: string }[];
  formDtId: string;
};
export interface CustomFile extends File {
  preview?: string;
  path?: string;
  id?: string;
  dtId?: string;
  url?: string;
  base64?: string;
}

export type CustomUser = {
  id: string;
  sub: string;
  name: string;
  username: string;
  email: string;
  department: string;
  workLocation: string;
  externalUser: boolean;
  user: CustomUser;
};

export type CustomSession = SessionType & { user: CustomUser } & {
  access_token: string;
} & {
  role: string[];
} & {
  hasDivision: string[];
};

export type TForm = Record<
  string,
  | string
  | number
  | boolean
  | never[]
  | string[]
  | object
  | object[]
  | Date
  | BodyInit
  | SetStateAction<string | number>
  | null
>;

export type CustomActivityLog = {
  id: string;
  type: string;
  trigger: string;
  activity: string;
  message: string;
  properties: string;
  userId: string;
  userIpAddress: string;
  userAgent: string;
  createdAt: Date;
  updatedAt: Date;
  User: CustomUser;
};

export type CustomError = {
  response: {
    data: { message: string };
  };
  message?: string;
  data?: TForm;
  status: string;
};

export type CustomBreadcrumb = { name: string; href?: string | undefined };