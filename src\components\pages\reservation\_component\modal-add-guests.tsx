'use client';

import React, { useEffect, useState } from "react";
import { type FieldValue, useForm } from "react-hook-form";
import { ActionIcon, Button, Input, Modal, Title } from "rizzui";
import { type Reservation } from "@/interfaces/reservations/reservation";
import swal from "sweetalert";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { preventEnter } from "@/lib/helper/prevent-enter";
import { PiX } from "react-icons/pi";

export default function ModalAddGuest({
  modal,
  setModal,
}: {
  modal: boolean;
  setModal: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const [isLoading, setIsLoading] = useState(false)

  // POST
  const handlePost = async (submitData: FieldValue<Reservation>) => {
    console.log('submitData', submitData)
    const confirmation = (await swal({
      title: "Accept",
      text: "Add New Reservation?",
      icon: "info",
      buttons: ["Cancel", "Yes"],
    })) as unknown as boolean;
    if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan
    setIsLoading(true);
    // return;
  }
  return (
    <Modal
      isOpen={modal}
      size={"xl"}
      onClose={() => setModal(false)}
    >
      <div className="m-auto px-7 pb-8 pt-6">
        <div className="mb-3 flex items-center justify-between">
          <Title as="h3">Change Rooms To</Title>
          <ActionIcon
            size="sm"
            variant="text"
            onClick={() => setModal(false)}
          >
            <PiX className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>
        <form onSubmit={handleSubmit(handlePost)} onKeyDown={preventEnter} className="space-y-4">
          <div className="grid grid-cols-3 gap-2">
            <Input
              label="Identify Number"
              size="sm"
              disabled={isLoading}
              placeholder="Please fill in Identify Number"
              {...register("identityNumber", { required: true })}
              error={errors.fullname ? "Identify Number is required" : undefined}
              className="w-full"
            />
            <Input
              label="Guest Name"
              size="sm"
              disabled={isLoading}
              placeholder="Please fill in Guest Name"
              {...register("fullname", { required: true })}
              error={errors.fullname ? "Guest Name is required" : undefined}
              className="w-full"
            />
            <Input
              label="Guest Company"
              size="sm"
              disabled={isLoading}
              placeholder="Please fill in Guest Company"
              {...register("companyName", { required: true })}
              error={errors.companyName ? "Guest Company is required" : undefined}
              className="w-full"
            />
            <Input
              label="Guest Nationality"
              size="sm"
              disabled={isLoading}
              placeholder="Please fill in Guest Nationality"
              {...register("nationality", { required: true })}
              error={errors.nationality ? "Guest Nationality is required" : undefined}
              className="w-full"
            />
            <Input
              label="Guest City"
              size="sm"
              disabled={isLoading}
              placeholder="Please fill in Guest City"
              {...register("city", { required: true })}
              error={errors.city ? "Guest City is required" : undefined}
              className="w-full"
            />
            <Input
              label="Guest Phone Number"
              size="sm"
              disabled={isLoading}
              placeholder="Please fill in Guest Phone Number"
              {...register("phoneNumber", { required: true })}
              error={errors.phoneNumber ? "Guest Phone Number is required" : undefined}
              className="w-full"
            />
            <div className="flex justify-end gap-2">
            <Button
              type="submit"
              size="sm"
              className="rounded-lg bg-blue-500 px-4 py-2 text-white"
            >
              Accept
            </Button>
            <Button
              type="button"
              size="sm"
              variant="outline"
              onClick={() => setModal(false)}
              className="rounded-lg px-4 py-2 disabled:bg-gray-400"
            >
              Quit
            </Button>
          </div>
          </div>
        </form>
      </div>
    </Modal>
  );
}
