"use client";

import <PERSON><PERSON><PERSON>er<PERSON>ustom from "@/app/shared/page-header-custom";
import React, { useEffect, useState } from "react";
import ReservationDetail from "./_component/reservation-detail";
import BookingRoom from "./_component-group/booking-room-group";
import CheckoutFnB from "./_component-group/fnb-group";
import Payments from "./_component-group/payments-group";
import CheckoutServices from "./_component-group/service-group";
import LoadingScreen from "@/components/theme/ui/loading-screen";
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { Button, Title } from "rizzui";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { useRouter } from "next/navigation";
import { preventEnter } from "@/lib/helper/prevent-enter";
import { randStr } from "@/lib/helper/generate-random-string";
import { type FieldValues, useForm } from "react-hook-form";
import {
  type FilterCondition,
  type FilterGroup,
  postApiAppPayments,
  postApiAppReservationDetails,
  putApiAppReservationDetailsById,
  type CreateUpdatePaymentsDto,
  type CreateUpdateReservationDetailsDto,
  type ReservationDetailsDto,
  type ReservationFoodAndBeveragesDto,
  type ReservationsDto,
  type CreateUpdatePaymentDetailsDto,
  type PaymentSourceType,
  type ReservationRoomsDto,
  type CreateUpdatePaymentGuestsDto,
  postApiInvoiceGenerateWisma,
} from "@/client";
import Link from "next/link";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";
import { useApiAppReservationDetailsList } from "@/lib/hooks/useApiAppReservationDetails";
import { formatDate } from "@/lib/helper/format-date";
import ModalInvoice from "./_component/modal-preview-invoice";
import PopInvoice from "./_component/pop-invoice";
import { useMasterPaymentOptions } from "@/lib/hooks/useMasterPayments";

export default function FormGroupCheckout({
  wiithHeader = true,
  className,
  id = "",
}: {
  wiithHeader?: boolean;
  className?: string;
  id: string;
}) {
  const {
    register,
    unregister,
    setValue,
    getValues,
    handleSubmit,
    watch,
    setError,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  // DEFINE HOOKS
  const queryClient = useQueryClient();
  const router = useRouter();

  // DEFINE VARIABLES
  const ids = id.split("_");
  const isGroup = ids.length > 1;
  const [refreshId, setRefreshId] = useState<string>(randStr());
  const [modalPreview, setModalPreview] = useState<boolean>(false);
  const [paymentId, setPaymentId] = useState<string>("");
  const [attachmentId, setAttachmentId] = useState<string>("");
  const [postLoading, setPostLoading] = useState<boolean>(false);
  const [previewLoading, setPreviewLoading] = useState<boolean>(false);
  const [tempSubmitData, setTempSubmitData] = useState<FieldValues>();
  const headerCode = `${String(getValues('reservationCode'))} (${String(getValues('reservationDetails.0.marketCode'))})`;

  // GET DATA
  const defaultFilter: FilterGroup = {
    operator: "Or",
    conditions: [
      // {
      //   fieldName: "id",
      //   operator: "In",
      //   value: id.split("_"),
      //   // value: id.replace("_", ","),
      // },
      // {
      //   fieldName: "status.code",
      //   operator: "In",
      //   value: "checkin,checkout",
      // },
      ...ids.map((id) => ({
        fieldName: "id",
        operator: "Equals",
        value: id,
      } as FilterCondition)),
    ],
  }
  const { isLoading: isLoadingDetails, data: dataSourceDets } = useApiAppReservationDetailsList(1, 100, defaultFilter, "", [], refreshId);
  const { isLoading: isLoadingResvDetOptions, data: statusResvDetOptions } = useMasterStatusByDocTypeOptions("reservationDetails");
  const { data: paymentDetailsOptions } = useMasterStatusByDocTypeOptions("paymentDetails");
  const { data: paymentStatusOptions } = useMasterStatusByDocTypeOptions("paymentStatus");
  const isLoading = isLoadingDetails || isLoadingResvDetOptions;

  const dataSourceDet = dataSourceDets?.items;
  const resvDet = (watch("reservationDetails") ?? []) as ReservationDetailsDto[]
  const isAllCheckout = resvDet.every((item) => item.status?.code === "checkout");
  const btnTitle = !isAllCheckout ? "Check Out" : "Create Invoice"

  // GET PAYMENTS
  const filterPayment: FilterGroup = {
    operator: "Or",
    conditions: [
      ...ids.map((id) => ({
        fieldName: "paymentDetails.reservationDetails.id",
        operator: "Equals",
        value: id,
      } as FilterCondition)),
    ],
  };
  const { isLoading: getPaymentsLoading, data: getPayments } = useMasterPaymentOptions(1, 100, filterPayment);

  // FNB & SERVICES
  const fnbs = (resvDet?.flatMap((item) => item.reservationFoodAndBeverages ?? []) ?? []) as (ReservationFoodAndBeveragesDto & { selected: boolean })[];
  const selectedFnbs = fnbs?.filter((fnb) => fnb.selected === true);

  const services = (resvDet?.flatMap((item) => item.reservationRooms ?? []) ?? []) as (ReservationRoomsDto & { selected: boolean })[];
  const selectedServices = services?.filter((serv) => serv.selected === true);

  // EACH PRICES
  const roomPrice = (watch("payments.roomTotalPrice") as number) ?? 0;
  const fnbsPrice = (watch("payments.fnbTotalPrice") as number) ?? 0;
  const resvRoomPrice = (watch("payments.serviceTotalPrice") as number) ?? 0;
  const paidTax = (watch("payments.paidTax") as number) ?? 0;

  // TOTAL PRICE
  const totalPrice = roomPrice + fnbsPrice + resvRoomPrice;
  const grantTotal = totalPrice + paidTax;

  // DEFINE MUTATIONS
  const updateReservationDetailsMutation = useMutation({
    mutationFn: async (dataMutation: ReservationDetailsDto) => {
      const { id, ...updateData } = dataMutation;
      return putApiAppReservationDetailsById({
        path: { id: id! },
        body: updateData as CreateUpdateReservationDetailsDto,
      });
    },
    onSuccess: async () => {
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationDetailsById],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const createPaymentsMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdatePaymentsDto) => {
      // const { id, ...updateData } = dataMutation;
      return postApiAppPayments({
        body: dataMutation,
      });
    },
    onSuccess: async () => {
      // router.push('/checkout'); // Replace with your target URL
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationDetailsById],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const createReservationDetailMutation = useMutation({
    mutationFn: async (dataMutation: CreateUpdateReservationDetailsDto) =>
      postApiAppReservationDetails({
        body: dataMutation,
      }),
    onSuccess: async () => {
      void queryClient.invalidateQueries({ queryKey: [QueryNames.GetReservations] });
      // router.push('/checkin');
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  // DEFINE FUNCTIONS submitData: FieldValues, command: string
  const handlePost = async (submitData: FieldValues, command: string) => {
    // return;
    //   GET RESERVATION DETAILS DATA --
    const resv = JSON.parse(JSON.stringify(submitData)) as ReservationsDto;
    const resvDet = resv.reservationDetails as (ReservationDetailsDto & { selected: boolean })[];

    //   GET PAYMENTS DATA --
    const paymentMethod = watch('payments.paymentMethodName') as string;
    const statusPaymentCode = paymentMethod == "Cash" ? "paid" : "pending"; // JIKA CASH MAKA STATUS LANGSUNG PAID, JIKA TIDAK MAKA PENDING
    const statudPaymentId = paymentDetailsOptions?.find((opt) => opt.code == statusPaymentCode)?.value as string; // GET STATUSID BY PAYMENT STATUS

    const paymentRoom: (CreateUpdatePaymentDetailsDto)[] = [];
    resvDet?.map((row, i) => {
      if (row.selected) {
        paymentRoom.push({
          reservationDetailsId: row?.id ?? "",
          sourceType: 1 as PaymentSourceType, // [ReservationRoom = 1, ReservationRoomFoodAndBeverage = 2, ReservationRoomService = 3]
          sourceId: row?.id ?? "",
          amount: Number(row?.price) ?? 0,
          qty: getValues(`reservationDetails.${i}.days`) as number,
          unitPrice: row?.room?.price ?? 0,
        });
      }
    });

    const paymentFnb: CreateUpdatePaymentDetailsDto[] = selectedFnbs?.map(
      (fnb) => {
        return {
          reservationDetailsId: fnb?.reservationDetailsId ?? "",
          sourceType: 2, // [ReservationRoom = 1, ReservationRoomFoodAndBeverage = 2, ReservationRoomService = 3]
          sourceId: fnb.id ?? "",
          amount: Number(fnb.totalPrice) ?? 0,
          qty: Number(fnb.quantity),
          unitPrice: Number(fnb.foodAndBeverage?.price) ?? 0,
        };
      },
    );
    const paymentServices: CreateUpdatePaymentDetailsDto[] =
      selectedServices?.map((serv) => {
        return {
          reservationDetailsId: serv?.reservationDetailsId ?? "",
          sourceType: 3, // [ReservationRoom = 1, ReservationRoomFoodAndBeverage = 2, ReservationRoomService = 3]
          sourceId: serv.id ?? "",
          amount: Number(serv.totalPrice) ?? 0,
          qty: Number(serv.quantity),
          unitPrice: Number(serv.services?.price) ?? 0,
        };
      });
    const paymentGuest: CreateUpdatePaymentGuestsDto[] = [
      {
        guestId: resvDet?.[0]?.guest?.id ?? "",
        amountPaid: 0,
      },
    ];
    const paymentDetails: CreateUpdatePaymentDetailsDto[] = [
      ...paymentRoom,
      ...paymentFnb,
      ...paymentServices,
    ];
    const payments: CreateUpdatePaymentsDto = {
      totalAmount: totalPrice,
      paidAmount: Number(getValues("payments.paidAmount")),
      vatRate: Number(getValues("payments.taxRate")),
      vatAmount: Number(getValues("payments.paidTax")),
      grantTotal: grantTotal,
      paymentCode: "-", // GENERATED FROM BACKEND
      transactionDate: formatDate(new Date()),
      reservationsId: String(getValues("id")),
      paymentMethodId: String(getValues("payments.paymentMethodId")),
      statusId: statudPaymentId,
      taxId: getValues("payments.taxId") ? String(getValues("payments.taxId")) : null,
      paymentDetails: paymentDetails,
      paymentGuests: paymentGuest,
    };
    //   // console.log('payments', payments)
    //   // setPostLoading(false);
    //   // return;

    if (command === "changeRoom") {
      await changeRoom(resv)
    }
    else if (command === "preview-payments") {
      await previewPayments(payments)
    }
    else if (command === "checkout") {
      await checkout(resvDet, payments);
    }
    setPostLoading(false);
  };

  const changeRoom = async (resv: ReservationsDto) => {
    // UPDATE RESERVATION DETAILS
    const confirmation = (await swal({
      title: "Change Room?",
      // text: "Change Room Will Checkout Current Room, \nAnd Checkin To New Room! \n\nAre You Sure?",
      content: {
        element: "div",
        attributes: {
          innerHTML: `
                  <div style="text-align: center;">
                    Change Room Will Checkout Current Room,<br/>
                    And Checkin To New Room!<br/><br/>
                    <b>Are You Sure?</b>
                  </div>
                `
        }
      },
      icon: "info",
      buttons: ["Cancel", "Yes"],
    })) as unknown as boolean;
    if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

    setPostLoading(true);

    // CHECK OUT CURRENT ROOM
    const oldResvDet = resv?.reservationDetails?.[0];
    const oldData: ReservationDetailsDto = {
      id: oldResvDet?.id,
      reservationId: oldResvDet?.reservationId,
      roomId: oldResvDet?.roomId,
      statusId: statusResvDetOptions?.find((opt) => opt.code == "checkout",)?.value as string, // code fore Check Out,
      guestId: oldResvDet?.guestId,
      checkInDate: oldResvDet?.checkInDate,
      checkOutDate: formatDate(new Date(), "datetime"),
      rfid: oldResvDet?.rfid,
      price: oldResvDet?.price,
      // "paymentStatusId": null,
    };
    await updateReservationDetailsMutation.mutateAsync(oldData);

    // DATA FOR NEW CHANGED ROOMS
    const newResvDet = (resv as ReservationsDto & { changeRoom: ReservationDetailsDto }).changeRoom;
    const newData: CreateUpdateReservationDetailsDto = {
      reservationId: newResvDet?.reservationId,
      roomId: newResvDet?.roomId,
      statusId: statusResvDetOptions?.find(opt => opt.code == "checkin")?.value as string,
      guestId: newResvDet?.guestId,
      // checkInDate: newResvDet?.checkInDate,
      checkInDate: formatDate(new Date(), "datetime"),
      checkOutDate: newResvDet?.checkOutDate,
      rfid: newResvDet?.rfid,
      price: newResvDet?.price,
    };
    // CHECK IN NEW ROOM
    await createReservationDetailMutation.mutateAsync(newData);
    await swal({
      title: "Success",
      text: "Change Room Successfully",
      icon: "success",
    });
    router.push("/checkout"); // Redirect to checkout page after successful payment
  }

  const previewPayments = async (payments: CreateUpdatePaymentsDto) => {
    setPreviewLoading(true);
    const statudPaymentId = paymentDetailsOptions?.find((opt) => opt.code == 'draft',)?.value as string; // GET STATUSID BY PAYMENT STATUS
    payments.statusId = statudPaymentId;
    const isPaymentSuccess = await createPayments(payments);
    setPreviewLoading(false);
    if (isPaymentSuccess) setModalPreview(true);
  }

  const checkout = async (resvDet: (ReservationDetailsDto & { selected: boolean })[], payments: CreateUpdatePaymentsDto) => {
    // SET REQUIRED FORM payments.paymentMethodId
    setError("payments.paymentMethodId", { type: "required", message: "Required for submit" });

    // CONFIRMATIONS
    const confirmation = (await swal({
      title: "Checkout",
      text: "Checkout This Reservation?",
      icon: "info",
      buttons: ["Cancel", "Yes"],
    })) as unknown as boolean;
    if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

    setPostLoading(true);
    // -- UPDATE RESERVATION DETAILS --
    // SET CHECK OUT DATE IF STATUS IS CHECK IN
    resvDet?.map((row, i) => {
      if (row?.status?.code === "checkin" && row.selected) {
        row.checkOutDate = formatDate(new Date(), "datetime")
        const statusId = statusResvDetOptions?.find((opt) => opt.code == "checkout",)?.value; // code fore Check Out
        const data: ReservationDetailsDto = {
          id: row?.id,
          reservationId: row?.reservationId,
          roomId: row?.roomId,
          statusId: statusId as string,
          guestId: row?.guestId,
          checkInDate: row?.checkInDate,
          checkOutDate: row?.checkOutDate,
          rfid: row?.rfid,
          price: row?.price,
          // "paymentStatusId": null,
        };
        try { void updateReservationDetailsMutation.mutateAsync(data); }
        catch { return };
      }
    })
    //   CREATE PAYMENTS
    const isPaymentSuccess = await createPayments(payments);
    await swal({
      title: "Success",
      text: "Checkout Successfully",
      icon: "success",
    });
    if (isPaymentSuccess) router.push("/checkout"); // Redirect to checkout page after successful payment

  }
  const createPayments = async (payments: CreateUpdatePaymentsDto) => {
    try {
      const res = await createPaymentsMutation.mutateAsync(payments);
      const body = {
        paymentId: res?.data?.id ?? "",
        // "templateId": "721EA30F-A891-7DCE-5BBB-3A1A08D943BE",
        includePaymentDetails: true,
        useAdvancedTable: true,
        generatePdf: true,
        // "customFilename": "invoice-" + res?.data?.paymentCode,
      };
      await postApiInvoiceGenerateWisma({ body: body })
        .then((res) => {
          const data = res?.data as { fileId: string };
          if (data?.fileId) setAttachmentId(data.fileId);
        })
      return true;
    } catch (error) {
      await swalError(error);
      return false;
    }
  }


  // DEFINE EFFECTS
  // set setValue from data source
  useEffect(() => {
    // SET RESERVATIONS
    Object.entries(dataSourceDet?.[0]?.reservation ?? {}).forEach(([key, value]) => {
      setValue(key, value);
    });
    unregister("reservationDetails");
    // SET RESERVATIONS DETAILS
    Object.entries(dataSourceDet ?? {}).forEach(([_key, details]) => {
      Object.entries(details ?? {}).forEach(([key, value]) => {
        setValue(`reservationDetails.${_key}.${key}`, value);
      });
      // SET CHECK OUT DATE IF STATUS IS CHECK IN
      if (details?.status?.code === "checkin") {
        setValue(`reservationDetails.${_key}.checkOutDate`, new Date())
      }
    });

    // SET FNB AND SERVICES TO UNPAID ONLY IF STATUS IS CHECK OUT
    // const fnbs = watch("reservationDetails.x.reservationFoodAndBeverages",) as (ReservationFoodAndBeveragesDto & { selected: boolean })[];
    // const unpaidFnbs = fnbs?.filter((fnb) => fnb.paymentStatus?.code !== "paid",);
    // setValue("reservationDetails.x.reservationFoodAndBeverages", unpaidFnbs);

    // const services = watch("reservationDetails.x.reservationRooms",) as (ReservationRoomsDto & { selected: boolean })[];
    // const unpaidServices = services?.filter((serv) => serv.paymentStatus?.code !== "paid",);
    // setValue("reservationDetails.x.reservationRooms", unpaidServices);
  }, [dataSourceDet]);

  useEffect(() => {
    setValue("payments.totalPrice", totalPrice);
    setValue("payments.grantTotal", grantTotal);
  }, [totalPrice, grantTotal]);

  // REDIRECT TO DETAIL IF PAYMENTS IS PAID
  // useEffect(() => {
  //   const paymentStatus = watch("reservationDetails.x.paymentStatus.code",) as string;
  //   if (paymentStatus === "paid") {
  //     const resvId = getValues("reservationDetails.x.id") as string;
  //     router.push(`/detail/${resvId}`);
  //   }
  // }, [watch("reservationDetails.x.paymentStatus.code")]);

  // --PERMISSION--
  const { can } = useGrantedPolicies();
  if (!(can("WismaApp.Reservation.Edit") && can("WismaApp.Payment.Create"))) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 @container " + className}>
      {(isLoading || postLoading) && <LoadingScreen />}
      {wiithHeader && (
        <>
          <PageHeaderCustom
            breadcrumb={[
              { name: "Reservation", href: "/reservation" },
              // { name: "Checkout", href: "/checkout" },
              // { name: btnTitle, href: "/checkout" },
              { name: "Detail", href: "/checkout" },
              { name: headerCode, href: "" },
            ]}
          >
            {/* <Button
              type="button"
              size="sm"
              variant="outline"
              className="rounded-lg px-4 py-2"
              onClick={() => {
                console.log('getValues', getValues());
              }}
            >
              Get Values
            </Button> */}
            <PopInvoice
              item={dataSourceDet?.[0] ?? ({} as ReservationDetailsDto)}
              getPaymentsLoading={getPaymentsLoading}
              getPayments={getPayments ?? []}
              paymentStatusOptions={paymentStatusOptions ?? []}
              setModalPreview={setModalPreview}
              setPaymentId={setPaymentId}
              setAttachmentId={setAttachmentId}
              label="Invoice List"
            // setResvDetailsId={ids[0] ?? ""}
            />
            <ModalInvoice
              // isLoading={isLoading}
              modalPreview={modalPreview}
              setModalPreview={setModalPreview}
              paymentId={paymentId}
              attachmentId={attachmentId}
            />
          </PageHeaderCustom>
        </>
      )}
      <form
        onSubmit={handleSubmit((e) => handlePost(e, ""))}
        onKeyDown={preventEnter}
        className="space-y-4"
      >
        <div className="grid grid-cols-3 gap-8">
          <div className="col-span-1">
            <ReservationDetail
              isLoading={isLoadingResvDetOptions}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
            />
          </div>
          <div className="col-span-2">
            <BookingRoom
              isLoading={false}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              handleSubmit={handleSubmit}
              handlePost={handlePost}
            />
            <CheckoutFnB
              register={register}
              unregister={unregister}
              setValue={setValue}
              watch={watch}
            // fnbs={fnbs}
            />
            <CheckoutServices
              register={register}
              unregister={unregister}
              setValue={setValue}
              watch={watch}
            // services={services}
            />
          </div>
        </div>
        {/* BUTTON */}
        <div className="grid grid-cols-3 gap-8">
          <div className="col-span-1"></div>
          <div className="col-span-2">
            <Payments
              isLoading={false}
              register={register}
              errors={errors}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              selectedFnbs={selectedFnbs}
              selectedServices={selectedServices}
            />
            <div className="mt-3 rounded border-2 bg-gray-50 px-4 py-2">
              <div className="flex justify-between">
                <div className="">
                  <Title as="h6" className="mb-0 p-1 text-gray-600">
                    {formatCurrencyIDR(grantTotal)}
                  </Title>
                </div>
                <div className="flex gap-2">
                  <Button
                    // type="submit"
                    size="sm"
                    isLoading={isLoading}
                    className="rounded-lg bg-blue-500 px-4 py-2 text-white"
                    onClick={async () => { await handleSubmit((e) => handlePost(getValues(), "checkout"))(); }}
                  >
                    {btnTitle}
                  </Button>
                  <Button
                    type="button"
                    size="sm"
                    isLoading={previewLoading}
                    // className="rounded-lg bg-blue-500 px-4 py-2 text-white"
                    variant="outline"
                    onClick={async () => { await handleSubmit((e) => handlePost(getValues(), "preview-payments"))(); }}
                  >
                    Preview Invoice
                  </Button>
                  <Link href={"/checkout"}>
                    <Button
                      type="button"
                      size="sm"
                      disabled={isLoading}
                      variant="outline"
                      className="rounded-lg px-4 py-2 disabled:bg-gray-400"
                    >
                      Quit
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
