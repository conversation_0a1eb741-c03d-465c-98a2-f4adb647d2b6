"use client";
import Header from "@/layouts/hydrogen/header";
import Sidebar from "@/layouts/hydrogen/sidebar";
import { useEffect, useState } from "react";

export default function HydrogenLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Load initial sidebar state from localStorage, defaulting to false
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(true);

  // Toggle the sidebar and save the new state to localStorage
  const toggleSidebar = () => {
    const newSidebarState = !isSidebarOpen;
    setIsSidebarOpen(newSidebarState);
    localStorage.setItem("isSidebarOpen", JSON.stringify(newSidebarState));
  };

  // Sync with localStorage when the component mounts
  useEffect(() => {
    // Only access localStorage on the client
    const storedState = localStorage.getItem("isSidebarOpen");
    setIsSidebarOpen(storedState ? storedState === "true" : true);

    const savedState = localStorage.getItem("isSidebarOpen");
    if (savedState !== null) {
      // console.log("isSidebarOpen", JSON.parse(savedState));
      setIsSidebarOpen(JSON.parse(savedState) === true);
    }
  }, []);
  return (
    <main className="flex min-h-screen flex-grow">
      <Sidebar
        className={`fixed z-10 hidden transition-all xl:block _dark:bg-gray-50 ${isSidebarOpen ? "xl:block" : "xl:hidden"}`}
      />
      <div
        className={`flex w-full flex-col ${isSidebarOpen ? "xl:ms-[270px] xl:w-[calc(100%-270px)] 2xl:ms-72 2xl:w-[calc(100%-288px)]" : "w-full"}`}
      >
        {/* {isSidebarOpen ? "block" : "hidden"} */}
        <Header toggleSidebar={toggleSidebar} isSidebarOpen={isSidebarOpen} />
        <div className="flex flex-grow flex-col bg-gray-100/60 px-4 pb-6 pt-2 md:px-5 lg:px-6 lg:pb-8 3xl:px-8 3xl:pt-4 4xl:px-10 4xl:pb-9">
          {children}
        </div>
      </div>
      {process.env.NEXT_PUBLIC_STACK_ENV !== 'production' &&
        <div className="development-banner">
          {process.env.NEXT_PUBLIC_STACK_ENV}
        </div>
      }
    </main>
  );
}
