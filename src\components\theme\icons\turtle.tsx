export default function TurtleIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 510.53 510.53"
      {...props}
    >
      <path
        fill="#f7d8a4"
        d="m345.33 361.56-3.95 2.37a21.32 21.32 0 0 0 10.97 39.61h44.67s-.38-73.99 0-73.99h-26.54l-9.06 22.35zM187.68 370.32a33.22 33.22 0 0 1-33.22 33.22h-39.03v-73.99h72.25v40.77z"
      />
      <path
        fill="#f6e2c4"
        d="m111.78 347.38 6.82-89h284.3l46.64 18.24 10.16 2.99a44.82 44.82 0 0 0 18.93 1.37l4.95-.7c14.62-2.07 24.75 14.2 16.45 26.4a49.76 49.76 0 0 1-34.14 21.3l-19.55 2.78-49.32 19.01L367.4 358a651 651 0 0 1-205.22 2.46l-9.69-1.43c-8.45-.96-40.7-11.64-40.7-11.64z"
      />
      <path
        fill="#f6e2c4"
        d="m94.49 324.4-6.3.63a82.94 82.94 0 0 1-66.35-23.26l-2.47-2.42a39.44 39.44 0 0 1 39.21-65.87l13.14 4.05 80.76 27.83-7.32 38.33-2.46 13.34z"
      />
      <path
        fill="#e5bda7"
        d="m429.01 228.35 17.33 28.07-104.07 51.97s-167.47-.7-169.1 0c-1.64.7-104.19-48.5-104.19-48.5L86.45 234l49.95-79a103.1 103.1 0 0 1 87.15-48h71.93a103.1 103.1 0 0 1 87.15 48z"
      />
      <g fill="#f8deb6">
        <path d="M152.48 370.32a33.22 33.22 0 0 1-33.22 33.22H80.23a17.83 17.83 0 0 1-8.21-33.66l7.32-3.8v-9.93c0-12.32 5.57-24 15.15-31.75l19.76-16 38.23-25.5zM412.2 291.72l12.98 11.77a80.84 80.84 0 0 1 29.03 62.06v16.78c0 11.71-9.5 21.21-21.21 21.21h-44.67a21.32 21.32 0 0 1-10.97-39.6l3.43-2.07a3.85 3.85 0 0 0 1.86-3.64l-3.75-42.24-3.5-37.4z" />
      </g>
      <path
        fill="#dfae91"
        d="M224.3 164.3c43.2-27.16 90.92-38.67 134.42-35.64A103.1 103.1 0 0 0 295.48 107h-71.93a103.1 103.1 0 0 0-87.15 48l-49.95 79-17.47 25.9s30.78 14.76 58.88 28c13.39-47.08 46.95-92.47 96.44-123.6z"
      />
      <path
        fill="#dfae91"
        d="M330.17 324.88H179.64c-27.41 0-53.94-8.06-76.71-23.31L69.8 279.4a24.5 24.5 0 1 1 27.26-40.72l33.11 22.17a88.6 88.6 0 0 0 49.46 15.03h150.53a88.6 88.6 0 0 0 49.45-15.03l42.35-28.35a24.5 24.5 0 1 1 27.26 40.72l-42.35 28.35a137.44 137.44 0 0 1-76.7 23.3z"
      />
      <path
        fill="#d79c7a"
        d="M136.23 264.55a89.4 89.4 0 0 1-6.05-3.7l-33.11-22.17a24.5 24.5 0 0 0-27.26 40.72l33.11 22.17a138.5 138.5 0 0 0 19.8 11 180.44 180.44 0 0 1 13.5-48.02z"
      />
      <path d="M34.94 252.4a7.5 7.5 0 0 0-7.5 7.5v3.96a7.5 7.5 0 0 0 15 0v-3.96a7.5 7.5 0 0 0-7.5-7.5z" />
      <path d="M506.77 284.16a24.22 24.22 0 0 0-24.25-11.3l-4.94.7c-5.31.75-10.61.37-15.76-1.15l-.71-.2a31.88 31.88 0 0 0 5.88-13.13c1.66-8.39-.04-16.92-4.8-24.02s-12-11.93-20.38-13.59a32.16 32.16 0 0 0-8.6-.52l-44.23-69.96a110.05 110.05 0 0 0-93.5-51.5h-71.93c-38.15 0-73.1 19.25-93.49 51.5l-48.1 76.08a32.35 32.35 0 0 0-10.83 2.43L60.8 226.3a46.76 46.76 0 0 0-55.87 24 46.82 46.82 0 0 0 9.2 54.4l2.48 2.42a90.14 90.14 0 0 0 61.18 25.77 48.26 48.26 0 0 0-5.94 23.26v5.36l-3.28 1.7a25.24 25.24 0 0 0-13.66 22.49 25.36 25.36 0 0 0 25.33 25.33h74.23c21.9 0 39.8-17.38 40.68-39.06a657 657 0 0 0 130.56-.66 28.65 28.65 0 0 0-2.17 10.9 28.86 28.86 0 0 0 28.82 28.82H433a28.74 28.74 0 0 0 28.71-28.7v-16.8c0-9.9-1.65-19.58-4.78-28.7l10.02-1.43a57.38 57.38 0 0 0 39.28-24.5 24.22 24.22 0 0 0 .54-26.75zm-134.26-69 28.34-17.31 17.68 27.96c-.25.15-.5.3-.73.46l-42.35 28.35c-.97.65-1.95 1.27-2.95 1.87v-41.33zM376.3 159l16.54 26.16-27.83 17-45.25-27.63v-56.95A95.08 95.08 0 0 1 376.3 159zm-80.81-44.52c3.12 0 6.21.15 9.27.44v59.6l-10.23 6.25a7.5 7.5 0 0 0 7.82 12.8l9.91-6.05 45.24 27.63v48.49a81.23 81.23 0 0 1-27.33 4.73h-63.15v-53.22l8.04-4.91a7.5 7.5 0 0 0-7.82-12.8l-7.72 4.71-45.24-27.62v-59.6c3.06-.3 6.15-.45 9.27-.45zm-152.75 44.52a95.08 95.08 0 0 1 56.54-41.42v56.95l-45.25 27.62-27.83-17zm-8.38 95.61-33.12-22.17a32.43 32.43 0 0 0-3.61-2.09l20.55-32.51 28.35 17.3v46.19a81.64 81.64 0 0 1-12.17-6.72zM24.62 293.99a31.86 31.86 0 0 1-6.27-37.02 31.82 31.82 0 0 1 38.02-16.33l.74.23-.26.37c-4.76 7.1-6.46 15.63-4.8 24.02s6.49 15.62 13.59 20.37l33.11 22.17c.8.53 1.6 1.03 2.4 1.54l-9.64 7.82-4.07.4a75.25 75.25 0 0 1-60.35-21.15zm55.6 102.05a10.34 10.34 0 0 1-4.75-19.5l7.33-3.8a7.5 7.5 0 0 0 4.04-6.66v-9.93c0-10.11 4.51-19.56 12.37-25.93l15.96-12.93a144.55 144.55 0 0 0 29.81 10.9v42.13a25.75 25.75 0 0 1-25.72 25.72H80.23zm74.24 0h-3.66a40.52 40.52 0 0 0 9.18-25.72v-2.61l1.1.16a661 661 0 0 0 19.1 2.5 25.75 25.75 0 0 1-25.72 25.67zm85.95-36.34v-7.8a7.5 7.5 0 0 0-15 0v7.23c-12.25-.6-24.5-1.56-36.72-2.87v-4.36a7.5 7.5 0 0 0-15 0v2.57a648 648 0 0 1-10.42-1.44l-3.29-.48v-21.52c6.48.88 13.04 1.35 19.66 1.35h116.64a7.5 7.5 0 0 0 0-15H179.64c-25.92 0-51-7.62-72.54-22.04l-33.12-22.17c-3.77-2.53-6.33-6.37-7.22-10.83s.03-8.98 2.55-12.75c2.53-3.78 6.37-6.34 10.83-7.22s8.98.02 12.76 2.55L126 267.08a96.07 96.07 0 0 0 53.63 16.3h11.16a7.5 7.5 0 0 0 0-15h-11.16c-6.14 0-12.2-.7-18.1-2.03v-51.2l45.24-27.62 45.24 27.63v53.22h-29.26a7.5 7.5 0 0 0 0 15h107.41c19.16 0 37.7-5.64 53.63-16.3l42.34-28.34a16.88 16.88 0 0 1 12.76-2.55c4.46.88 8.3 3.44 10.83 7.22 2.52 3.77 3.43 8.3 2.55 12.76s-3.45 8.3-7.22 10.82l-42.35 28.35a129.97 129.97 0 0 1-72.54 22.04h-1.92a7.5 7.5 0 0 0 0 15h1.92c14.4 0 28.55-2.11 42.1-6.21l2.05 23.02-8.2 1.4a644.8 644.8 0 0 1-22.27 3.43v-2.12a7.5 7.5 0 0 0-15 0v4a643.26 643.26 0 0 1-36.72 3.06v-7.06a7.5 7.5 0 0 0-15 0v7.7a644 644 0 0 1-36.72.1zm98.12 22.52a13.9 13.9 0 0 1 6.7-11.86l3-1.8c5.43-.76 10.85-1.6 16.26-2.49a28.65 28.65 0 0 0-1.44 29.97h-10.7c-7.62 0-13.82-6.2-13.82-13.82zm108.18.1c0 7.57-6.15 13.72-13.71 13.72h-44.67a13.84 13.84 0 0 1-7.12-25.68l3.44-2.06a11.42 11.42 0 0 0 5.47-10.74l-3.26-36.69c8.4-3.56 16.5-7.92 24.2-13.07l4.2-2.82 5.12 4.27a73.28 73.28 0 0 1 26.33 56.3zm47.12-79.85a42.35 42.35 0 0 1-29 18.08l-14.25 2.04a88.36 88.36 0 0 0-20.59-24.85l-1.74-1.45 18.82-12.6c.12.04.23.09.36.12l10.15 3a51.97 51.97 0 0 0 22.1 1.6l4.95-.7a9.28 9.28 0 0 1 9.4 4.39 9.28 9.28 0 0 1-.2 10.37z" />
    </svg>
  );
}
