import {
  type CreateUpdateReservationRoomsDto,
  deleteApiAppReservationRoomsById,
  putApiAppReservationRoomsById,
  type ReservationRoomsDto,
} from "@/client";
import ButtonDelete from "@/components/container/button/button-delete";
import ButtonMin from "@/components/container/button/buttonMin";
import ButtonPlus from "@/components/container/button/buttonPlus";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { swalError } from "@/lib/helper/swal-error";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import {
  type UseFormUnregister,
  type FieldErrors,
  type FieldValues,
  type UseFormGetValues,
  type UseFormRegister,
  type UseFormSetValue,
  type UseFormWatch,
} from "react-hook-form";
import { PiTrash } from "react-icons/pi";
import { Checkbox, Text } from "rizzui";

export default function CheckoutServices({
  register,
  unregister,
  setValue,
  services,
  readonly = false,
}: {
  register: UseFormRegister<FieldValues>;
  unregister: UseFormUnregister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  services: (ReservationRoomsDto & { selected: boolean })[];
  readonly?: boolean;
}) {
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  // const serv = watch("reservationDetails.0.reservationRooms") as reservationRooms[];
  // const services = serv?.filter((serv) => serv.paymentStatus?.code !== "paid");
  const totalPrice = services?.reduce((total, item) => {
    const price = item.selected ? Number(item.totalPrice) : 0;
    return total + price;
  }, 0);
  const columns = [
    { dataIndex: "serviceName", title: "Name" },
    { dataIndex: "serviceTypeName", title: "Type" },
    { dataIndex: "quantity", title: "QTY" },
    { dataIndex: "totalPrice", title: "Price" },
  ];

  // DEFINE MUTATIONS
  const updateApiAppReservationRoomsById = useMutation({
    mutationFn: async (data: CreateUpdateReservationRoomsDto) => {
      const { id, ...updateData } = data;
      return putApiAppReservationRoomsById({
        path: { id: id! },
        body: data,
      });
    },
    onSuccess: async (res) => {
      console.log("success", res);
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationRoomsByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });
  const removeApiAppReservationRoomsById = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppReservationRoomsById({
        path: { id: id },
      });
    },
    onSuccess: async (res) => {
      console.log("success remove", res);
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.putApiAppReservationRoomsByIdData],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  // FUNCTIONS
  const updateServQty = async (key: number, add: number) => {
    const data = services[key] as CreateUpdateReservationRoomsDto & {
      selected?: boolean;
    };
    const qty = Number(data?.quantity) + add;
    const pricePerItem = Number(data?.totalPrice) / Number(data?.quantity);
    const totalPrice = pricePerItem * qty;
    if (data) {
      data.quantity = qty;
      data.totalPrice = totalPrice;
      setValue(`reservationDetails.0.reservationRooms.${key}`, data);
      delete data.selected;
      await updateApiAppReservationRoomsById.mutateAsync(data);
    }
  };
  const removeServ = async (key: number) => {
    // const confirmation = (await swal({
    //   title: "Remove",
    //   text: "Remove Transaction List?",
    //   icon: "info",
    //   buttons: ["Cancel", "Yes"],
    // })) as unknown as boolean;
    // if (!confirmation) return; // User tekan "Cancel", jangan lanjutkan

    const data = services[key];
    if (data?.id) {
      unregister(`reservationDetails.0.reservationRooms.${key}`);
      await removeApiAppReservationRoomsById.mutateAsync(data.id);
    }
  };

  // EFFECTS
  useEffect(() => {
    setValue("payments.serviceTotalPrice", totalPrice);
  }, [totalPrice]);

  const hasSetDefaultSelected = useRef(false);
  useEffect(() => {
    if (services?.length && !hasSetDefaultSelected.current) {
      console.log("am here 2");
      services.forEach((_, key) => {
        setValue(`reservationDetails.0.reservationRooms.${key}.selected`, true);
      });
      hasSetDefaultSelected.current = true; // Jangan jalankan lagi
    }
  }, [services, setValue]);

  return (
    <div className="mt-3 rounded border-2 bg-gray-50 p-4 py-3">
      <div className="grid grid-cols-1 gap-0">
        <Text as="p" className="mb-0 text-sm font-bold text-gray-800">
          Room Service Transactions
        </Text>
        <>
          {/* Header Table */}
          <table className="min-w-full table-auto border-collapse">
            <thead className="bg-slate-50">
              <tr>
                {!readonly &&
                  <th className="sticky top-0 bg-slate-50 px-2 py-1 text-center text-xs font-semibold tracking-wider text-gray-500 w-[4%]">
                    <Checkbox
                      size="sm"
                      inputClassName="h-4 w-4"
                      iconClassName="h-4 w-4"
                      onChange={(e) => {
                        services.forEach((_, key) => {
                          setValue(`reservationDetails.0.reservationRooms.${key}.selected`, e.target.checked);
                        });
                      }}
                    />
                  </th>
                }
                <th className="sticky top-0 bg-slate-50 px-2 py-1 text-center text-xs font-semibold tracking-wider text-gray-500 w-[6%]">
                  No
                </th>
                {columns.map((column, key) => (
                  <th
                    key={key}
                    className="sticky top-0 w-[22%] bg-slate-50 px-1 py-1 text-left text-xs font-semibold tracking-wider text-gray-500"
                  >
                    {column.title}
                  </th>
                ))}
                <th className="sticky top-0 w-5 w-[4%] bg-slate-50 px-2 py-1 text-left text-xs tracking-wider text-gray-500">
                  {/* # */}
                </th>
              </tr>
            </thead>
          </table>

          {/* Scrollable Body Table */}
          <div className="h-[120px] max-h-[120px] overflow-y-auto">
            <table className="min-w-full table-auto border-collapse">
              <tbody className="divide-y divide-gray-200">
                {services?.length === 0 && (
                  <tr>
                    <td className="p-6 text-center text-xs tracking-wider text-gray-500">
                      {services?.length > 0
                        ? "All F&B transactions have been paid."
                        : "No F&B transaction found."}
                    </td>
                  </tr>
                )}
                {services?.map((row, key) => (
                  <tr className="hover:bg-gray-50" key={key}>
                    {!readonly &&
                      <td className="px-2 text-center text-xs tracking-wider text-gray-500 w-[4%]">
                        <Checkbox
                          size="sm"
                          inputClassName="h-4 w-4"
                          iconClassName="h-4 w-4"
                          {...register(`reservationDetails.0.reservationRooms.${key}.selected`)}
                        />
                      </td>
                    }
                    <td className="px-2 text-center text-xs px-2 py-1 tracking-wider text-gray-500 w-[6%]">
                      {key + 1}.
                    </td>
                    <td className="w-[22%] px-1 text-xs tracking-wider text-gray-500">
                      {row.serviceName}
                    </td>
                    <td className="w-[22%] px-1 text-xs tracking-wider text-gray-500">
                      {row.serviceTypeName}
                    </td>
                    <td className="px-1 text-xs tracking-wider text-gray-500 w-[22%]">
                      <div className="flex">
                        {(!readonly && can("WismaApp.ReservationRoom.Edit")) &&
                          <ButtonMin
                            type="button"
                            onClick={async () => await updateServQty(key, -1)}
                            disabled={(Number(row?.quantity) ?? 1) <= 1}
                          />
                        }
                        <div className="p-2 py-1">{row.quantity}</div>
                        {(!readonly && can("WismaApp.ReservationRoom.Edit")) &&
                          <ButtonPlus
                            type="button"
                            onClick={async () => await updateServQty(key, 1)}
                          />
                        }
                      </div>
                    </td>
                    <td className="w-[22%] px-1 text-xs tracking-wider text-gray-500">
                      {formatCurrencyIDR(row?.totalPrice ?? 0)}
                    </td>
                    <td className="w-[4%]">
                      {(!readonly && can("WismaApp.ReservationRoom.Delete")) &&
                        // <button
                        //   type="button"
                        //   onClick={async () => await removeServ(key)}
                        //   className="rounded-md px-2 py-1 text-xs text-white"
                        // >
                        //   <PiTrash className="h-4 w-4 text-red-500 transition-transform duration-200 hover:scale-125 hover:text-red-700" />
                        // </button>
                        <ButtonDelete
                          onClick={async () => await removeServ(key)}
                          title={"Are you sure?"}
                          text={
                            "This action will delete the selected tansaction. Continue?"
                          }
                        />
                      }
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Footer Table */}
          <table className="min-w-full table-auto border-collapse">
            <tfoot className="bg-slate-50">
              <tr>
                <th className="text-medium px-1 py-1 text-start tracking-wider text-gray-500">
                  TOTAL
                </th>
                <th className="text-medium w-[29%] px-1 py-1 text-start tracking-wider text-gray-500">
                  {formatCurrencyIDR(totalPrice)}
                </th>
              </tr>
            </tfoot>
          </table>
        </>
      </div>
    </div>
  );
}
