# Deployment Guide for IMIP Wisma Application

This guide provides step-by-step instructions for deploying the Next.js application to Kubernetes using GitLab CI/CD.

## Environment URLs

### Development Environment
- Frontend: https://wisma-dev.imip.co.id
- API: https://api-wisma-dev.imip.co.id

### Production Environment
- Frontend: https://wisma.imip.co.id
- API: https://api-wisma.imip.co.id

## Prerequisites

1. A Kubernetes cluster
2. kubectl configured to access your cluster
3. GitLab account with a project containing this codebase
4. GitLab Runner installed on your Kubernetes cluster

## Deployment Steps

### 1. Set Up Kubernetes Namespaces and Secrets

Run the `setup-secrets.sh` script to create the necessary namespaces and secrets:

```bash
chmod +x setup-secrets.sh
./setup-secrets.sh
```

This script will:
- Create the `wismafrontend-dev` and `wismafrontend-prod` namespaces
- Generate secure random passwords for session encryption and Redis
- Create Kubernetes secrets with these passwords
- Display the generated passwords (save these for reference)

### 2. Configure GitLab CI/CD Variables

In your GitLab project:
1. Go to Settings > CI/CD > Variables
2. Add the following variables:
   - `CI_REGISTRY`: Your Docker registry URL (e.g., registry.gitlab.com)
   - `CI_REGISTRY_USER`: Username for Docker registry
   - `CI_REGISTRY_PASSWORD`: Password for Docker registry
   - `CI_REGISTRY_IMAGE`: Full path to your Docker image (e.g., registry.gitlab.com/wayantisna74/wismafrontend)
   - `NEXT_PUBLIC_CLIENT_SECRET`: Your client secret for authentication

### 3. Configure External Nginx (if applicable)

If you're using an external Nginx server to proxy requests to your Kubernetes cluster, configure it as follows:

#### Development Environment

```nginx
server {
    listen 80;
    server_name wisma-dev.imip.co.id;

    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name wisma-dev.imip.co.id;

    # SSL configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    location / {
        proxy_pass http://kubernetes-ingress-ip-or-hostname;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### Production Environment

```nginx
server {
    listen 80;
    server_name wisma.imip.co.id;

    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name wisma.imip.co.id;

    # SSL configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    location / {
        proxy_pass http://kubernetes-ingress-ip-or-hostname;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 4. Deploy the Application

The deployment will be triggered automatically when you push to:
- `dev` branch: Deploys to the development environment (https://wisma-dev.imip.co.id)
- `main` branch: Deploys to the production environment (https://wisma.imip.co.id) (manual approval required)

## Deployment Architecture

The deployment consists of:

1. **Next.js Application**:
   - Containerized using Docker
   - Deployed as a Kubernetes Deployment
   - Exposed via a Service and Ingress

2. **Redis**:
   - Used for session storage
   - Deployed as a separate Deployment and Service
   - Only accessible within the Kubernetes cluster

## Troubleshooting

### Check Pod Status
```bash
kubectl get pods -n wismafrontend-dev
kubectl get pods -n wismafrontend-prod
```

### View Pod Logs
```bash
kubectl logs -n wismafrontend-dev deployment/wismafrontend
kubectl logs -n wismafrontend-prod deployment/wismafrontend
```

### Check Ingress Configuration
```bash
kubectl get ingress -n wismafrontend-dev
kubectl get ingress -n wismafrontend-prod
```

### Restart Deployment
```bash
kubectl rollout restart deployment/wismafrontend -n wismafrontend-dev
kubectl rollout restart deployment/wismafrontend -n wismafrontend-prod
```

## Scaling the Application

To scale the application, update the `replicas` field in the Kubernetes deployment file:

```yaml
spec:
  replicas: 3  # Change this number to scale up or down
```

Then apply the changes:
```bash
kubectl apply -f kubernetes/dev/deployment.yaml
# or
kubectl apply -f kubernetes/prod/deployment.yaml
```

## Monitoring

Monitor your application using:

1. **Kubernetes Dashboard**:
   ```bash
   kubectl -n kubernetes-dashboard create token admin-user
   kubectl proxy
   ```
   Then access: http://localhost:8001/api/v1/namespaces/kubernetes-dashboard/services/https:kubernetes-dashboard:/proxy/

2. **Application Logs**:
   ```bash
   kubectl logs -f -n wismafrontend-dev deployment/wismafrontend
   ```

3. **Redis Monitoring**:
   ```bash
   kubectl port-forward -n wismafrontend-dev service/redis 6379:6379
   ```
   Then connect using Redis CLI or RedisInsight.
