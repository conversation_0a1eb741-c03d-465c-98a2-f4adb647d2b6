import React from "react";
import { But<PERSON> } from "rizzui";

export default function ButtonForm({
  isLoading,
  isFormVisible,
  setIsFormVisible,
}: {
  isLoading: boolean;
  isFormVisible: boolean;
  setIsFormVisible: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  return (
    <Button
      size="sm"
      type="button"
      onClick={() => setIsFormVisible((prev) => !prev)}
      disabled={isLoading}
      className="rounded-lg bg-green-500 px-4 py-2 text-white disabled:bg-gray-400"
    >
      {isFormVisible ? "Hide Form" : "Show Form"}
    </Button>
  );
}
