WITH rolehaspermission AS (
  SELECT
    B.menu_name,
    RIGHT(B.name, 5) AS name
  FROM
    role_has_permissions AS A
    LEFT JOIN permissions AS B ON A.permission_id = B.id
)
SELECT
  DISTINCT a.menu_name,
  a.role_id,
  a.parent_id,
  a.icon,
  a.route,
  a.has_child,
  a.has_route,
  a.is_crud,
  a.order_line,
  a.menu_name AS parena_name,
  CASE
    WHEN idx.valuex IS NULL THEN 'N'
    ELSE 'Y'
  END AS 'index',
  CASE
    WHEN store.valuex IS NULL THEN 'N'
    ELSE 'Y'
  END AS 'store',
  CASE
    WHEN edits.valuex IS NULL THEN 'N'
    ELSE 'Y'
  END AS 'edits',
  CASE
    WHEN erase.valuex IS NULL THEN 'N'
    ELSE 'Y'
  END AS 'erase'
FROM
  (
    SELECT
      A.role_id,
      B.id,
      B.parent_id,
      B.icon,
      B.route,
      B.has_child,
      B.has_route,
      B.is_crud,
      B.order_line,
      B.menu_name
    FROM
      role_has_permissions AS A
      LEFT JOIN permissions AS B ON A.permission_id = B.id
  ) AS a
  LEFT JOIN permissions AS b ON a.parent_id = b.id
  LEFT JOIN (
    SELECT
      menu_name,
      'Y' AS valuex
    FROM
      rolehaspermission AS a1
    WHERE
      RIGHT(a1.name, 5) = 'index'
  ) AS idx ON a.menu_name = idx.menu_name
  LEFT JOIN (
    SELECT
      menu_name,
      'Y' AS valuex
    FROM
      rolehaspermission AS a1
    WHERE
      RIGHT(a1.name, 5) = 'store'
  ) AS store ON a.menu_name = store.menu_name
  LEFT JOIN (
    SELECT
      menu_name,
      'Y' AS valuex
    FROM
      rolehaspermission AS a1
    WHERE
      RIGHT(a1.name, 5) = 'edits'
  ) AS edits ON a.menu_name = edits.menu_name
  LEFT JOIN (
    SELECT
      menu_name,
      'Y' AS valuex
    FROM
      rolehaspermission AS a1
    WHERE
      RIGHT(a1.name, 5) = 'erase'
  ) AS erase ON a.menu_name = erase.menu_name;