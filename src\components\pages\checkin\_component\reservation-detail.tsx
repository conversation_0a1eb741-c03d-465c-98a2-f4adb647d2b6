import { DateInput } from "@/components/theme/ui/input-type/dates-input"
import { UploadFile } from "@/components/theme/ui/file-upload/upload-file"
import React, { useCallback, useEffect, useState } from "react"
import { type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form"
import { Button, Input, type SelectOption, Title } from "rizzui"
import { type Reservation } from "@/interfaces/reservations/reservation"
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete"
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input"
import { useApiMasterPaymentMethodOptions } from "@/lib/hooks/useApiMasterPaymentMethod"
import { useApiMasterDiningOptionsOptions } from "@/lib/hooks/useApiMasterDiningOptions"
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType"
import { useApiAppReservationTypesOptions } from "@/lib/hooks/useMasterReservationTypes"
import { useApiMasterCompanyOptions } from "@/lib/hooks/useApiMasterCompany"
import { useApiAppGuestOptions } from "@/lib/hooks/useApiAppGuest"
import StreamAttachment from "@/components/layout/preview-stream/stream-attachment"
import Link from "next/link"

export default function ReservationDetail({
  isLoading = false,
  register,
  errors,
  setValue,
  getValues,
  watch,
}: {
  isLoading: boolean
  register: UseFormRegister<FieldValues>
  setValue: UseFormSetValue<FieldValues>
  getValues: UseFormGetValues<FieldValues>
  watch: UseFormWatch<FieldValues>
  errors: FieldErrors<Reservation>
}) {

  // OPTIONS
  const { data: paymentMethodOptions } = useApiMasterPaymentMethodOptions(0, 1000, "",);
  const { data: diningOptions } = useApiMasterDiningOptionsOptions(0, 1000, "",);
  const { data: statusResvOptions } = useMasterStatusByDocTypeOptions("reservations")
  const { data: statusResvDetOptions } = useMasterStatusByDocTypeOptions("reservationDetails")
  const { data: guestCriteriaOptions } = useMasterStatusByDocTypeOptions("reservationDetailsGuestCriteria")
  const { data: reservationTypesOptions } = useApiAppReservationTypesOptions(0, 1000, "")
  const { data: companyOptions } = useApiMasterCompanyOptions(0, 1000, "")
  const { data: dataGuestOptions } = useApiAppGuestOptions(0, 1000, "",)
  const [guestOptions, setGuestOptions] = useState<SelectOption[]>([])
  useEffect(() => { setGuestOptions(dataGuestOptions ?? []) }, [dataGuestOptions])

  // INIT
  const init = useCallback(() => {
    register("reservationDetails.0.rfid", { value: "" })
  }, [])

  useEffect(() => { init() }, [init])
  // INIT STATUS ID
  useEffect(() => {
    // const statusId = statusResvOptions?.find(opt => opt.code == "open")?.value // code fore Open
    // setValue("statusId", statusId ?? 0)
  }, [statusResvOptions])

  // INIT RESERVATION TYPE ID
  useEffect(() => {
    // const reservationTypesId = reservationTypesOptions?.find(opt => opt.name == "INDIVIDU (BSG)")?.value // code fore Individu
    // setValue("reservationTypeId", reservationTypesId ?? 0)
  }, [reservationTypesOptions])

  // INIT STATUS RESERVATION ID
  useEffect(() => {
    // const statusId = statusResvDetOptions?.find(opt => opt.code == "reserved")?.value // code fore Reserved
    // setValue("reservationDetails.0.statusId", statusId ?? 0)
  }, [statusResvDetOptions])


  // UPDATE GUEST WHEN IDENTITY NUMBER CHANGED
  const identityNumber = watch("reservationDetails.0.guest.identityNumber") as string
  useEffect(() => {
    const selectedOpt = guestOptions?.find(opt => opt.value == identityNumber)
    if (selectedOpt?.fullname) {
      setValue("reservationDetails.0.guestId", selectedOpt.id)
      setValue("reservationDetails.0.guest", selectedOpt)
    } else {
      setValue("reservationDetails.0.guestId", null)
      setValue("reservationDetails.0.guest.fullname", null)
      setValue("reservationDetails.0.guest.nationality", null)
      setValue("reservationDetails.0.guest.phoneNumber", null)
      setValue("reservationDetails.0.guest.companyName", null)
    }
  }, [identityNumber])

  const onChange = () => {
    const identityNumber = getValues("reservationDetails.0.guest.identityNumber") as string
    const tempOpt: SelectOption = { label: identityNumber, value: 0, id: 0 }
    setGuestOptions(prev => [tempOpt, ...prev.slice(1)])
  }
  return (
    <div className="">
      <div className="grid grid-cols-2 gap-2">
        {/* RESERVATION DETAIL */}
        <Input
          label="Booker Identity Number"
          size="sm"
          disabled={true}
          placeholder="Please fill in Booker Identity Number"
          {...register("bookerIdentityNumber")}
          error={errors.bookerIdentityNumber ? "Booker Identity Number is required" : undefined}
          className="w-full"
          readOnly={true}
        />
        <Input
          label="Booker Name"
          size="sm"
          disabled={true}
          placeholder="Please fill in Booker Name"
          {...register("bookerName")}
          error={errors.bookerName ? "Booker Name is required" : undefined}
          className="w-full"
          readOnly={true}
        />
        <AutocompleteSelect
          label={"Company Name"}
          name={"companyId"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          onChange={onChange}
          options={companyOptions}
          required={true}
          readOnly={true}
          size="sm"
        />
        <Input
          label="Email"
          size="sm"
          disabled={true}
          placeholder="Please fill in Email"
          {...register("bookerEmail")}
          error={errors.bookerEmail ? "Email is required" : undefined}
          className="w-full"
          readOnly={true}
        />
        <Input
          label="Phone Number"
          size="sm"
          disabled={true}
          placeholder="Please fill in Phone Number"
          {...register("bookerPhoneNumber")}
          error={errors.bookerPhoneNumber ? "Phone Number is required" : undefined}
          className="w-full"
          readOnly={true}
        />
        <DateInput
          label={"Arrival Date"}
          name={"arrivalDate"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          required={true}
          size="sm"
          readOnly={true}
        />
        <DropdownInput
          label={"Payment Method"}
          name={"paymentMethodId"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={paymentMethodOptions}
          size="sm"
          readOnly={true}
        />
        <Input
          label="Days"
          size="sm"
          type="number"
          disabled={true}
          placeholder="Please fill in days"
          {...register("days")}
          error={errors.days ? "days is required" : undefined}
          className="w-full"
          readOnly={true}
        />
        {/* <Input
          label="Group Code"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Group Code"
          {...register("groupCode", { required: true })}
          error={errors.groupCode ? "Group Code is required" : undefined}
          className="w-full"
        /> */}
        <DropdownInput
          label={"Criteria Options"}
          name={"reservationDetails.0.criteriaId"}
          size="sm"
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={guestCriteriaOptions}
          // required={true}
          readOnly={true}
        />
        <DropdownInput
          label={"Dining Options"}
          name={"diningOptions.id"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={diningOptions}
          size="sm"
          readOnly={true}
        />
        {/* GUEST INFORMATION */}
        <div className="grid grid-cols-3 col-span-2 gap-2">
          <Title as="h6" className="mb-0 col-span-3 pt-5">
            Guest Information
          </Title>
          <AutocompleteSelect
            label={"Guest Identity Number"}
            name={"reservationDetails.0.guest.identityNumber"}
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
            onChange={onChange}
            options={guestOptions}
            required={true}
            size="sm"
            readOnly={true}
          />
          <Input
            label="Guest Name"
            size="sm"
            disabled={true}
            placeholder="Please fill in Guest Name"
            {...register("reservationDetails.0.guest.fullname")}
            error={errors.reservationDetails?.[0]?.guest?.fullname ? "Guest Name is required" : undefined}
            className="w-full"
            readOnly={true}
          />
          {/* <Input
            label="Guest Identity Number"
            // size="sm"
            disabled={true}
            placeholder="Please fill in Guest Identity Number"
            {...register("reservationDetails.0.guest.identityNumber")}
            error={errors.reservationDetails?.[0]?.guest?.identityNumber ? "Guest Identity Number is required" : undefined}
            className="w-full"
          /> */}
          <Input
            label="Guest Nationality"
            size="sm"
            disabled={true}
            placeholder="Please fill in Guest Nationality"
            {...register("reservationDetails.0.guest.nationality")}
            error={errors.reservationDetails?.[0]?.guest?.nationality ? "Guest Nationality is required" : undefined}
            className="w-full"
            readOnly={true}
          />
          <Input
            label="Guest Phone Number"
            size="sm"
            disabled={true}
            placeholder="Please fill in Guest Phone Number"
            {...register("reservationDetails.0.guest.phoneNumber")}
            error={errors.reservationDetails?.[0]?.guest?.phoneNumber ? "Guest Phone Number is required" : undefined}
            className="w-full"
            readOnly={true}
          />
          <Input
            label="Guest Company"
            size="sm"
            disabled={true}
            placeholder="Please fill in Guest Company"
            {...register("reservationDetails.0.guest.companyName")}
            error={errors.reservationDetails?.[0]?.guest?.companyName ? "Guest Company is required" : undefined}
            className="w-full cursor-not-allowed"
            prefixClassName="cursor-not-allowed"
            helperClassName="cursor-not-allowed"
            inputClassName="cursor-not-allowed"
            readOnly={true}
          />
          {/* <div></div> */}
          <div className="col-span-2 flex items-center">
            {/* <UploadFile
              label={"Document Reservation"}
              name={"attachment"}
              setValue={setValue}
              getValues={getValues}
              register={register}
              errors={errors}
              // required={true}
              size="sm"
              readOnly={true}
              accept="application/pdf"
            /> */}
            {(watch("attachments.0.id")) &&
              <StreamAttachment size="xs" label="Preview Document" url={String(watch("attachments.0.id"))} />
            }
          </div>
          {/* BUTTON */}
          <div className="flex justify-end gap-2 pt-5">
            <Button
              type="submit"
              size="sm"
              isLoading={isLoading}
              className="rounded-lg bg-green-500 px-4 py-2 text-white"
            >
              Check In
            </Button>
            <Link href={"/checkin"}>
              <Button
                type="button"
                size="sm"
                disabled={isLoading}
                variant="outline"
                className="rounded-lg px-4 py-2 disabled:bg-gray-400"
              >
                Quit
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}