/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
await import("./src/env.js");

/** @type {import("next").NextConfig} */
const config = {
  // reactStrictMode: true,
  output: "standalone",
  // fastRefresh: true,
  // concurrentFeatures: true,
  swcMinify: true,
  productionBrowserSourceMaps: false, // Disable source maps in development
  optimizeFonts: false, // Disable font optimization
  // minify: false, // Disable minification
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'cn'],
  },
};

export default config;
