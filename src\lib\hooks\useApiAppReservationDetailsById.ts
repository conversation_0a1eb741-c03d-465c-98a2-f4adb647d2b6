﻿import { getApiAppReservationDetailsById } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";

export const useApiAppReservationDetailsById = (
  id: string,
  refreshId?: string
) => {
  return useQuery({
    queryKey: [QueryNames.GetReservationWithDetailsById, id, refreshId],
    queryFn: async () => {
      const { data } = await getApiAppReservationDetailsById({
        path: { id } // Replace with your reservation ID
      });
      return data;
    },
  });
};