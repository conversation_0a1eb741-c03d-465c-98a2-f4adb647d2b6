import { getApiMasterStatusServiceByDocTypeByDocType } from "@/client";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";

export const fetchStatusOptions = async (
  docType: string,
): Promise<SelectOptionType[]> => {
  try {
    const response = await getApiMasterStatusServiceByDocTypeByDocType({
      path: { docType },
    });

    if (response?.data) {
      return response.data.map((item) => ({
        label: item.name ?? "",
        value: item.id ?? "",
      }));
    }

    return [];
  } catch (error) {
    console.error("Error fetching masterStatusData:", error);
    return [];
  }
};
