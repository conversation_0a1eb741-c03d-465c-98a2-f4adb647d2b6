export default function UserInfoIcon({
  // strokeWidth,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        d="M6.948 4.558A4.07 4.07 0 0 1 9.807 5.74a4.02 4.02 0 0 1 1.183 2.847 4.07 4.07 0 0 1-1.183 2.859 4.059 4.059 0 0 1-2.86 1.172 4.01 4.01 0 0 1-2.846-1.172 4.07 4.07 0 0 1-1.184-2.859 4.02 4.02 0 0 1 1.184-2.847 4.02 4.02 0 0 1 2.847-1.183Zm0 12.396c.187 0 .351.152.351.34s-.164.34-.351.34a.34.34 0 0 1 0-.68Zm0-1.172a.35.35 0 0 1 .351.34.35.35 0 0 1-.351.34.34.34 0 0 1 0-.68Zm8.846-14.704a.885.885 0 0 1 0 1.77.89.89 0 0 1-.89-.891c0-.48.398-.88.89-.88Zm0 .539a.35.35 0 0 0-.352.34c0 .199.164.351.352.351.187 0 .34-.152.34-.351a.34.34 0 0 0-.34-.34Zm0 1.5c.41 0 .738.328.738.738V6.28c0 .41-.34.75-.738.75-.41 0-.75-.34-.75-.75V3.855c0-.41.34-.738.75-.738Zm0 .539c-.117 0-.2.093-.2.199V6.28c0 .105.083.2.2.2.105 0 .187-.095.187-.2V3.855c0-.106-.082-.2-.187-.2Zm2.402-2.098A3.374 3.374 0 0 0 15.77.551a3.428 3.428 0 0 0-3.42 3.116c-.071.762.105 1.523.538 2.191.082.117.152.211.07.363l-.503 1.043 1.09-.492a.301.301 0 0 1 .28.035 3.398 3.398 0 0 0 2.25.598 3.389 3.389 0 0 0 2.12-.996 3.416 3.416 0 0 0 0-4.85Zm.386-.386a3.967 3.967 0 0 1 0 5.624 3.904 3.904 0 0 1-2.46 1.148 3.975 3.975 0 0 1-2.496-.61l-1.628.739c-.223.117-.48-.13-.363-.364l.773-1.605a3.914 3.914 0 0 1-.598-2.484A3.975 3.975 0 0 1 15.77 0c1.02 0 2.04.387 2.812 1.172ZM9.49 13.263a4.57 4.57 0 0 1 2.965 1.476c.75.82 1.206 1.91 1.206 3.105v1.875c0 .152-.128.281-.269.281H.516a.275.275 0 0 1-.27-.281v-1.875c0-1.195.457-2.285 1.207-3.105A4.662 4.662 0 0 1 4.1 13.3c.164-.024.422-.106.55.034.317.281.668.492 1.067.645.386.14.796.222 1.23.222.445 0 .855-.082 1.242-.222.363-.14.691-.34.984-.574.105-.082.164-.176.316-.141Zm2.555 1.851a4.028 4.028 0 0 0-2.508-1.3 3.94 3.94 0 0 1-1.16.668 3.921 3.921 0 0 1-2.859 0 3.881 3.881 0 0 1-1.148-.668 4.087 4.087 0 0 0-2.519 1.3 4.023 4.023 0 0 0-1.054 2.73v1.605H13.11v-1.605a3.978 3.978 0 0 0-1.066-2.73ZM9.42 6.128a3.469 3.469 0 0 0-2.472-1.02c-.96 0-1.828.387-2.46 1.02a3.462 3.462 0 0 0-1.02 2.46c0 .96.387 1.84 1.02 2.472.632.621 1.5 1.02 2.46 1.02.972 0 1.84-.399 2.472-1.02.633-.632 1.02-1.511 1.02-2.472s-.387-1.828-1.02-2.46Z"
      />
      <path
        fill="#F69E07"
        fillRule="evenodd"
        d="M4.487 6.129a3.462 3.462 0 0 0-1.02 2.46c0 .96.387 1.84 1.02 2.472.633.621 1.5 1.02 2.46 1.02.973 0 1.84-.399 2.473-1.02.632-.632 1.02-1.511 1.02-2.472s-.388-1.828-1.02-2.46a3.469 3.469 0 0 0-2.472-1.02c-.961 0-1.828.387-2.46 1.02Z"
        clipRule="evenodd"
      />
      <path
        fill="#FF6F0C"
        fillRule="evenodd"
        d="M7.3 16.12a.35.35 0 0 1-.352.34.34.34 0 0 1 0-.68.35.35 0 0 1 .352.34Zm-2.93-2.308a4.087 4.087 0 0 0-2.519 1.301 4.023 4.023 0 0 0-1.054 2.73v1.605H13.11v-1.605a3.978 3.978 0 0 0-1.066-2.73 4.028 4.028 0 0 0-2.508-1.3 3.94 3.94 0 0 1-1.16.667 3.921 3.921 0 0 1-2.859 0 3.881 3.881 0 0 1-1.148-.668Zm2.578 3.14c.187 0 .352.153.352.34s-.165.34-.352.34a.34.34 0 0 1 0-.68Z"
        clipRule="evenodd"
      />
      <path
        fill="#FFCF00"
        fillRule="evenodd"
        d="M12.349 3.667c-.07.762.105 1.524.539 *************.152.211.07.364l-.504 1.042 1.09-.492a.301.301 0 0 1 .281.035 3.398 3.398 0 0 0 2.25.598 3.389 3.389 0 0 0 2.12-.996 3.416 3.416 0 0 0 0-4.85A3.374 3.374 0 0 0 15.77.55a3.428 3.428 0 0 0-3.421 3.116Zm4.323-1.71c0 .492-.398.89-.878.89a.89.89 0 0 1-.89-.89.885.885 0 0 1 1.77 0Zm-1.628 1.898c0-.41.34-.738.75-.738s.738.328.738.738V6.28c0 .41-.34.75-.738.75-.41 0-.75-.34-.75-.75V3.855Z"
        clipRule="evenodd"
      />
      <path
        fill="#FF2C00"
        fillRule="evenodd"
        d="M15.595 3.855v2.426c0 .***************.199.105 0 .187-.094.187-.2V3.856c0-.105-.082-.199-.187-.199-.117 0-.2.094-.2.2Zm-.153-1.898c0 .2.164.351.352.351.187 0 .34-.152.34-.351a.34.34 0 0 0-.34-.34.35.35 0 0 0-.352.34Z"
        clipRule="evenodd"
      />
    </svg>
  );
}
