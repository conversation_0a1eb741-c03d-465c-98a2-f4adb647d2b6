﻿import { getApiAppRoomStatus, getApiAppRoomType } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import { type SelectOption } from "rizzui";

export const useMasterRoomStatus = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetRoomStatus, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex && pageSize) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppRoomStatus({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};

export const useMasterRoomStatusOptions = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetroomType, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppRoomStatus({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      // GENERATE OPTIONS
      const options: SelectOption[] = data?.items?.map(val => ({
        label: val.name ?? "",
        value: val.id ?? "",
        ...val
      })) ?? [];

      // RETURN OPTIONS
      return options;
    },
  });
};
