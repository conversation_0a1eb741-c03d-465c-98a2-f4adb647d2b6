"use server";
import { type ReservationResponse } from "@/interfaces/reservations/reservation";
import axios from "axios";

export const getDataReservation = (id: string) => {
  return axios.get<ReservationResponse>(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/reservations/${id}`)
    .then((response) => {
      // console.log('GET getDataReservation', response.data);
      return response.data;
    })
    .catch((error) => {
      console.error("Error fetching reservations:", error);
      throw error;
    });
}

// import { getDataReservations } from "./getDatas";

// export const getDataReservation = (id: string) => {
//   const datas = getDataReservations;
//   const data = datas.find((e) => e.id == id);
//   return data
// }