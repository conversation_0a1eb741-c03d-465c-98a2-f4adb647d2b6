export default function ShipIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#FEE45A"
        d="M7.972 12.813H2.966a.31.31 0 0 1-.31-.31V9.06a.31.31 0 0 1 .31-.31h5.006a.31.31 0 0 1 .31.31v3.443a.31.31 0 0 1-.31.31Z"
      />
      <path
        fill="#FED402"
        d="M7.972 8.75H6.8a.31.31 0 0 1 .31.31v3.443a.31.31 0 0 1-.31.31h1.172a.31.31 0 0 0 .31-.31V9.06a.31.31 0 0 0-.31-.31Z"
      />
      <path
        fill="#F2FBFF"
        d="M1.506 12.938 2.12 16.4a.955.955 0 0 0 .941.788h12.603c.496 0 2.212-1.551 2.829-4.25.014-.065-.04-.126-.106-.126H1.611a.107.107 0 0 0-.105.126Z"
      />
      <path
        fill="#DFF6FD"
        d="M18.388 12.813h-1.172c.067 0 .*************-.617 2.699-2.333 4.25-2.83 4.25h1.173c.496 0 2.212-1.551 2.829-4.25.014-.065-.04-.126-.106-.126Z"
      />
      <path
        fill="#90D8F9"
        d="M19.707 16.276a.214.214 0 0 0-.233-.215c-.986.093-1.064.771-2.194.771-1.213 0-1.213-.781-2.427-.781-1.213 0-1.213.781-2.427.781s-1.213-.781-2.426-.781c-1.214 0-1.214.781-2.427.781s-1.213-.781-2.427-.781-1.213.781-2.426.781c-1.13 0-1.208-.678-2.194-.77a.214.214 0 0 0-.233.213v1.498c0 1.068.866 1.934 1.934 1.934h15.546a1.934 1.934 0 0 0 1.934-1.934v-1.498Z"
      />
      <path
        fill="#75CEF9"
        d="M19.474 16.064c-.433.04-.69.194-.939.353v1.358a1.934 1.934 0 0 1-1.933 1.934h1.171a1.934 1.934 0 0 0 1.934-1.934v-1.497a.214.214 0 0 0-.233-.214Z"
      />
      <path
        fill="#FEE45A"
        d="M15.029 12.813h-5.006a.31.31 0 0 1-.31-.31V9.06a.31.31 0 0 1 .31-.31h5.006a.31.31 0 0 1 .31.31v3.443a.31.31 0 0 1-.31.31Z"
      />
      <path
        fill="#FED402"
        d="M15.029 8.75h-1.172a.31.31 0 0 1 .31.31v3.443a.31.31 0 0 1-.31.31h1.172a.31.31 0 0 0 .31-.31V9.06a.31.31 0 0 0-.31-.31Z"
      />
      <path
        fill="#F4FBFF"
        d="M7.994 2.948C7.94 5.122 6.511 6.307 5.827 6.75a.65.65 0 0 1-.713-.002c-.696-.46-2.172-1.697-2.172-3.928A2.527 2.527 0 0 1 5.488.293c1.428.01 2.542 1.228 2.506 2.655Z"
      />
      <path
        fill="#DD636E"
        d="M7.994 2.948C7.94 5.122 6.511 6.307 5.827 6.75a.65.65 0 0 1-.713-.002c-.696-.46-2.172-1.697-2.172-3.928A2.527 2.527 0 0 1 5.488.293c1.428.01 2.542 1.228 2.506 2.655Z"
      />
      <path
        fill="#DA4A54"
        d="M5.489.293a2.535 2.535 0 0 0-.607.07c1.147.275 1.976 1.351 1.944 2.585C6.777 4.9 5.621 6.054 4.89 6.59c.08.06.156.113.224.158a.65.65 0 0 0 .713.002c.684-.443 2.113-1.627 2.168-3.802C8.03 1.521 6.917.304 5.489.293Z"
      />
      <path
        fill="#F2FBFF"
        d="M5.47 3.975a1.153 1.153 0 1 0 0-2.307 1.153 1.153 0 0 0 0 2.307Z"
      />
      <path
        className="stroke-black"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={0.6}
        d="M16.611 16.559c.679-.69 1.496-1.93 1.883-3.621.014-.065-.04-.125-.106-.125H1.611a.107.107 0 0 0-.105.125L2.12 16.4c.02.11.057.212.11.304M4.02 10v1.767M5.469 10v1.767M6.919 10v1.767"
      />
      <path
        className="stroke-black"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={0.6}
        d="M16.08 19.707h1.693a1.934 1.934 0 0 0 1.934-1.934v-1.497a.214.214 0 0 0-.233-.215c-.986.093-1.064.771-2.194.771-1.213 0-1.213-.781-2.427-.781-1.213 0-1.213.781-2.427.781s-1.213-.781-2.426-.781c-1.214 0-1.214.781-2.427.781s-1.213-.781-2.427-.781-1.213.781-2.426.781c-1.13 0-1.208-.678-2.194-.77a.214.214 0 0 0-.233.214v1.497c0 1.068.866 1.934 1.934 1.934h12.447m-6.702-6.895H2.966a.31.31 0 0 1-.31-.31V9.06a.31.31 0 0 1 .31-.31h5.006a.31.31 0 0 1 .31.31v3.443a.31.31 0 0 1-.31.31ZM11.077 10v1.767M12.526 10v1.767M13.976 10v1.767m1.053 1.046h-5.006a.31.31 0 0 1-.31-.31V9.06a.31.31 0 0 1 .31-.31h5.006a.31.31 0 0 1 .31.31v3.443a.31.31 0 0 1-.31.31ZM1.927 14.219H17.97M3.955.797c.427-.32.958-.508 1.533-.504 1.428.01 2.542 1.228 2.506 2.655C7.94 5.123 6.511 6.307 5.827 6.75a.65.65 0 0 1-.713-.002c-.696-.46-2.172-1.697-2.172-3.928 0-.331.064-.647.18-.937"
      />
      <path
        className="stroke-black"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={0.6}
        d="M5.47 3.975a1.153 1.153 0 1 0 0-2.307 1.153 1.153 0 0 0 0 2.307Z"
      />
    </svg>
  );
}
