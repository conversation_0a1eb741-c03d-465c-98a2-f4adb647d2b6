"use server";
import { getSession } from "@/lib/actions";
import type { RoomStatusResponse } from "@/interfaces/rooms/roomStatus";
import axios from "axios";

export const getRoomStatus = async () => {
  try {
    const session = await getSession();

    const res = await axios.get<RoomStatusResponse>(
      `${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room-statuses`,
      {
        headers: {
          Authorization: "Bearer " + session?.access_token,
          "Accept-Language": "en_US",
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      },
    );

    return res.data.items;
  } catch (error) {
    console.error("Failed to fetch rooms:", error);
    return [];
  }
};
