export default function TagIcon2({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="45"
      height="44"
      viewBox="0 0 45 44"
      fill="none"
      {...props}
    >
      <path
        d="M34.361 5.09016C25.9069 5.09016 20.1013 0.710938 16.0277 0.710938C11.954 0.710938 3.39844 3.05272 3.39844 21.793C3.39844 40.5334 14.9081 43.2834 19.3888 43.2834C40.3707 43.2846 49.3332 5.09016 34.361 5.09016Z"
        fill="#EBE2FF"
      />
      <path
        d="M13.4349 11.0026C12.4241 11.0026 11.6016 10.18 11.6016 9.16927C11.6016 8.15849 12.4241 7.33594 13.4349 7.33594C14.4457 7.33594 15.2682 8.15849 15.2682 9.16927C15.2682 10.18 14.4457 11.0026 13.4349 11.0026ZM13.4349 8.55816C13.0988 8.55816 12.8238 8.83194 12.8238 9.16927C12.8238 9.5066 13.0988 9.78038 13.4349 9.78038C13.771 9.78038 14.046 9.5066 14.046 9.16927C14.046 8.83194 13.771 8.55816 13.4349 8.55816Z"
        fill="#5200FF"
      />
      <path
        d="M33.2334 23.8125L30.5689 33.9569C30.41 34.5314 30.0311 35.0569 29.4811 35.3992C28.8945 35.7536 28.2345 35.8269 27.6234 35.6803L22.2578 34.3725L33.2334 23.8125Z"
        fill="#AD86FF"
      />
      <path
        d="M35.2039 21.9144C35.9103 21.2459 36.3516 20.2999 36.3516 19.25V10.6944C36.3516 9.34389 35.2577 8.25 33.9071 8.25H25.3516C24.3029 8.25 23.3557 8.69122 22.6871 9.39644L9.52012 23.0707C9.10578 23.5082 8.85156 24.0998 8.85156 24.75C8.85156 25.4247 9.12534 26.0358 9.56778 26.4782L18.1233 35.0338C18.5658 35.4762 19.1769 35.75 19.8516 35.75C20.5018 35.75 21.0933 35.4958 21.5309 35.0814L35.2039 21.9144Z"
        fill="white"
      />
      <path
        d="M31.1606 14.9696C32.0044 14.9696 32.6884 14.2856 32.6884 13.4418C32.6884 12.5981 32.0044 11.9141 31.1606 11.9141C30.3168 11.9141 29.6328 12.5981 29.6328 13.4418C29.6328 14.2856 30.3168 14.9696 31.1606 14.9696Z"
        fill="#AD86FF"
      />
      <path
        d="M20.8733 35.0338L12.3178 26.4782C11.8753 26.0358 11.6016 25.4247 11.6016 24.75C11.6016 24.0998 11.8558 23.5082 12.2701 23.0707L25.4371 9.39644C26.1057 8.69 27.0517 8.25 28.1016 8.25H25.3516C24.3029 8.25 23.3557 8.69122 22.6871 9.39644L9.52012 23.0707C9.10578 23.5082 8.85156 24.0998 8.85156 24.75C8.85156 25.4247 9.12534 26.0358 9.56778 26.4782L18.1233 35.0338C18.5658 35.4762 19.1769 35.75 19.8516 35.75C20.3612 35.75 20.8318 35.5899 21.2229 35.3234C21.0982 35.2367 20.9797 35.1413 20.8733 35.0338Z"
        fill="#D5DBE1"
      />
      <path
        d="M25.0078 34.3725L32.3033 27.3533L33.2334 23.8125L22.2578 34.3725L27.6234 35.6803C28.2039 35.8196 28.8285 35.7524 29.3919 35.4407L25.0078 34.3725Z"
        fill="#5200FF"
      />
      <path
        d="M32.3828 13.4418C32.3828 13.2096 32.4427 12.9957 32.5356 12.799C32.2911 12.2795 31.7717 11.9141 31.1606 11.9141C30.3173 11.9141 29.6328 12.5985 29.6328 13.4418C29.6328 14.2852 30.3173 14.9696 31.1606 14.9696C31.7717 14.9696 32.2911 14.6042 32.5356 14.0847C32.4427 13.888 32.3828 13.6741 32.3828 13.4418Z"
        fill="#5200FF"
      />
      <path
        d="M28.2162 36.6669C27.9473 36.6669 27.6772 36.6351 27.4095 36.5703L22.0391 35.2613L22.473 33.4806L27.8397 34.7883C28.1245 34.8568 28.5608 34.8837 29.0057 34.6136C29.337 34.4082 29.5802 34.0856 29.6841 33.7116L32.3461 23.5781L34.1195 24.0438L31.4538 34.1882C31.2241 35.0193 30.6961 35.7209 29.9627 36.1768C29.4274 36.5007 28.8273 36.6669 28.2162 36.6669Z"
        fill="black"
      />
      <path
        d="M19.8542 36.6693C18.9558 36.6693 18.1101 36.3197 17.4769 35.6842L8.92261 27.1286C8.28706 26.4955 7.9375 25.6509 7.9375 24.7526C7.9375 23.8897 8.26383 23.0696 8.85661 22.4426L22.0297 8.76349C22.8853 7.85905 24.1002 7.33594 25.3542 7.33594H33.9097C35.7638 7.33594 37.2708 8.84294 37.2708 10.697V19.2526C37.2708 20.5054 36.7489 21.7178 35.8396 22.5795L22.1703 35.744C21.5372 36.3429 20.7158 36.6693 19.8542 36.6693ZM25.3542 9.16927C24.5903 9.16927 23.8814 9.47483 23.3558 10.0297L10.1827 23.7088C9.91872 23.9875 9.77083 24.3603 9.77083 24.7526C9.77083 25.1608 9.92972 25.5446 10.2182 25.8318L18.7737 34.3874C19.3396 34.9533 20.3235 34.9679 20.9028 34.4167L34.5709 21.257C34.5734 21.2546 34.5758 21.2534 34.5771 21.2509C35.1319 20.7266 35.4375 20.0165 35.4375 19.2526V10.697C35.4375 9.85494 34.7518 9.16927 33.9097 9.16927H25.3542Z"
        fill="black"
      />
      <path
        d="M31.1554 15.8889C29.8073 15.8889 28.7109 14.7926 28.7109 13.4444C28.7109 12.0963 29.8073 11 31.1554 11C32.5035 11 33.5998 12.0963 33.5998 13.4444C33.5998 14.7926 32.5035 15.8889 31.1554 15.8889ZM31.1554 12.8333C30.8193 12.8333 30.5443 13.1071 30.5443 13.4444C30.5443 13.7818 30.8193 14.0556 31.1554 14.0556C31.4915 14.0556 31.7665 13.7818 31.7665 13.4444C31.7665 13.1071 31.4915 12.8333 31.1554 12.8333Z"
        fill="black"
      />
    </svg>
  );
}
