"use client";
import React, { useEffect, useState } from "react";
import PageHeader<PERSON>ustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonDetail from "@/components/container/button/button-detail";
import ButtonForm from "@/components/container/button/button-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useMasterStatus } from "@/lib/hooks/config/masterStatus";
import {
  deleteApiMasterStatusById,
  postApiMasterStatus,
  putApiMasterStatusById,
  type CreateUpdateMasterStatusDto,
  type MasterStatusDto,
} from "@/client";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import type { SelectOption } from "rizzui";
import {
  addAlphaComponentColor,
  contrastingColor,
} from "@/lib/helper/format-color";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { swalError } from "@/lib/helper/swal-error";

export default function MasterStatus({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isDataStatus, setDataStatus] = useState<MasterStatusDto[]>([]);
  const [isColor, setColor] = useState<SelectOption | undefined>();

  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 1000,
  });

  const { isLoading, data } = useMasterStatus(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        code: item.code ?? "",
        name: item.name ?? "",
        docType: item.docType ?? "",
        color: item.color ?? "",
      }));
      setDataStatus(mappedData as MasterStatusDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "code", title: "Code", filter: "select" as const },
    { dataIndex: "name", title: "Name", filter: "select" as const },
    {
      dataIndex: "docType",
      title: "Document Type",
      filter: "select" as const,
    },
    {
      dataIndex: "color",
      title: "Color",
      filter: "select" as const,
      render: (_: unknown, record: MasterStatusDto) => (
        <span
          style={{
            backgroundColor: record.color ?? "#000000",
            color: contrastingColor(record.color ?? "#000000", -50),
            padding: "4px 8px",
            borderRadius: "4px",
            display: "inline-block",
          }}
        >
          {record.color}
        </span>
      ),
    },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });

    setIsEditMode(false);
    setColor(undefined);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetStatus],
    });
  };

  const deleteMasterStatusMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiMasterStatusById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Status deleted successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Status will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete Status with id:", id);
        await deleteMasterStatusMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the Status. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const data = isDataStatus.find((e) => e.id === id);
    if (data) {
      setIsEditMode(true);
      Object.entries(data).map(([key, value], _index) => {
        if (key === "color") {
          const colorOption = [
            { label: "Red", value: "#FF573342" },
            { label: "Green", value: "#33FF5742" },
            { label: "Blue", value: "#3357FF42" },
            { label: "Yellow", value: "#F1C40F42" },
            { label: "Purple", value: "#9B59B642" },
            { label: "Orange", value: "#E67E2242" },
            { label: "Gray", value: "#7F8C8D42" },
          ].find((option) => option.value === value);

          if (colorOption) {
            setColor(colorOption);
          }
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createMasterStatusMutation = useMutation({
    mutationFn: async (data: CreateUpdateMasterStatusDto) =>
      postApiMasterStatus({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Status created successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateMasterStatusMutation = useMutation({
    mutationFn: async (data: MasterStatusDto) => {
      const { id, ...updateData } = data;
      return putApiMasterStatusById({
        path: { id: id! },
        body: updateData as CreateUpdateMasterStatusDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Status updated successfully.",
        icon: "success",
        timer: 1200,
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      formData.color = addAlphaComponentColor(formData.color as string);
      if (isEditMode) {
        // console.log("Updating Status:", formData);
        await updateMasterStatusMutation.mutateAsync(
          formData as MasterStatusDto,
        );
      } else {
        // console.log("Creating Status:", formData);
        await createMasterStatusMutation.mutateAsync(
          formData as CreateUpdateMasterStatusDto,
        );
      }

      handleAction();
    } catch (error) {
      console.error("Error submitting form:", error);
      await swal({
        title: "Error",
        text: "Failed to submit the form. Please try again later.",
        icon: "error",
      });
    }
  }

  if (!can("WismaApp.MasterStatus")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Config" },
            { name: "Master Status" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              isColor={isColor}
              setColor={setColor}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
