export default function SwimmingPoolIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      {...props}
    >
      <g fill="currentColor">
        <path d="M23.85 15.33a.35.35 0 0 0-.5.1 2.6 2.6 0 0 1-2.03 1.05c-1.3 0-2.01-1.01-2.04-1.06-.13-.2-.46-.2-.58 0-.01.02-.73 1.06-2.04 1.06-1.1 0-1.78-.72-1.98-.97v-5.46h8.97a.35.35 0 1 0 0-.7h-2.83V6.77a4.25 4.25 0 0 0-4.18-4.27 4.18 4.18 0 0 0-3.91 2.55A4.23 4.23 0 0 0 8.87 2.5a4.18 4.18 0 0 0-4.21 4.22v2.61H.38a.35.35 0 1 0 0 .7h4.28v1.4a.35.35 0 1 0 .7 0V6.73c0-.95.37-1.83 1.04-2.5a3.48 3.48 0 0 1 2.5-1.01 3.55 3.55 0 0 1 3.48 3.56v2.56h-.89V6.73A2.63 2.63 0 0 0 8.87 4.1c-.68 0-1.32.26-1.81.75-.51.5-.8 1.2-.8 1.9v9.46a2.75 2.75 0 0 1-.9-.72v-2.65a.35.35 0 0 0-.7 0v2.66c-.2.25-.88.97-1.98.97-1.3 0-2-1.01-2.04-1.06a.35.35 0 0 0-.58.4c.04.05.94 1.37 2.62 1.37 1.15 0 1.94-.62 2.33-1.02.4.4 1.18 1.02 2.33 1.02 1.15 0 1.94-.62 2.33-1.02.4.4 1.18 1.02 2.33 1.02 1.15 0 1.94-.62 2.33-1.02.4.4 1.18 1.02 2.33 1.02 1.15 0 1.93-.62 2.33-1.02.4.4 1.18 1.02 2.33 1.02a3.32 3.32 0 0 0 2.62-1.37.35.35 0 0 0-.1-.49ZM6.95 12.5h5.43v.98H6.96v-.98Zm5.43-.7H6.96v-1.75h5.42v1.74ZM7.55 5.35c.37-.36.85-.56 1.36-.55 1.04.02 1.88.88 1.88 1.92v2.61H6.96V6.77c0-.53.21-1.04.59-1.4Zm2.41 10.06c-.13-.2-.45-.2-.58 0a2.6 2.6 0 0 1-2.42 1.03v-2.26h5.42v2.26a2.6 2.6 0 0 1-2.42-1.03Zm3.13.8v-9.5c0-.94.37-1.82 1.04-2.49a3.48 3.48 0 0 1 2.5-1.01 3.55 3.55 0 0 1 3.48 3.56v2.56h-.89v-.78a.35.35 0 1 0-.7 0v.78h-3.84V6.77c0-.53.22-1.04.6-1.4.37-.37.85-.57 1.35-.56 1.04.02 1.89.88 1.89 1.92v.42a.35.35 0 1 0 .7 0v-.42A2.63 2.63 0 0 0 16.6 4.1c-.68 0-1.32.26-1.82.75-.5.5-.8 1.2-.8 1.9v8.74c-.12.15-.42.49-.9.72ZM9.67 20.78c-1.3 0-2-1.01-2.04-1.05-.12-.2-.45-.2-.58 0a2.6 2.6 0 0 1-2.04 1.05c-1.3 0-2-1.01-2.04-1.05a.35.35 0 1 0-.58.4c.04.05.94 1.36 2.62 1.36 1.15 0 1.94-.61 2.33-1.02.4.4 1.18 1.02 2.33 1.02a.35.35 0 0 0 0-.7ZM21.52 19.63a.35.35 0 0 0-.5.1A2.6 2.6 0 0 1 19 20.78c-1.3 0-2-1.01-2.04-1.05-.13-.2-.45-.2-.58 0-.01 0-.73 1.05-2.04 1.05-1.3 0-2-1.01-2.04-1.05-.13-.2-.45-.2-.58 0 0 0-.29.41-.81.72a.35.35 0 0 0 .35.6c.33-.18.58-.4.75-.58.4.4 1.18 1.02 2.33 1.02 1.15 0 1.94-.61 2.33-1.02.4.4 1.18 1.02 2.33 1.02a3.31 3.31 0 0 0 2.62-1.37.35.35 0 0 0-.1-.49Z" />
      </g>
    </svg>
  );
}
