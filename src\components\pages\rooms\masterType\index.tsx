"use client";
import React, { useEffect, useState } from "react";
import PageHeader<PERSON>ustom from "@/app/shared/page-header-custom";
import CustomTable from "@/components/layout/custom-table/table";
import { useForm } from "react-hook-form";
import Form from "./form";
import ButtonForm from "@/components/container/button/button-form";
import type { SelectOption } from "rizzui";
import { useMasterType } from "@/lib/hooks/rooms/masterType/useMasterType";
import ButtonDetail from "@/components/container/button/button-detail";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { QueryNames } from "@/lib/hooks/QueryConstants";
import {
  deleteApiAppRoomTypeById,
  postApiAppRoomType,
  putApiAppRoomTypeById,
} from "@/client";
import type { CreateUpdateRoomTypeDto, RoomTypeDto } from "@/client";
import AccessDeniedLayout from "@/components/layout/access-denied";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";
import { swalError } from "@/lib/helper/swal-error";

export default function RoomsMasterType({
  wiithHeader = true,
  className,
}: {
  wiithHeader?: boolean;
  className?: string;
}) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });
  const queryClient = useQueryClient();
  const { can } = useGrantedPolicies();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isRoomType, setDataRoomType] = useState<RoomTypeDto[]>([]);
  const [isStatus, setStatus] = useState<SelectOption | undefined>();
  const [pagination] = useState({
    pageIndex: 0,
    pageSize: 1000,
  });
  const { isLoading, data } = useMasterType(
    pagination.pageIndex,
    pagination.pageSize,
  );

  useEffect(() => {
    if (data?.items) {
      const mappedData = data.items.map((item) => ({
        ...item,
        id: item.id ?? "",
        name: item.name ?? "",
        status: item.status?.name ?? "",
        alias: item.alias ?? "",
      }));
      setDataRoomType(mappedData as RoomTypeDto[]);
    }
  }, [data]);

  const columns = [
    { dataIndex: "alias", title: "Alias", filter: "text" as const },
    { dataIndex: "name", title: "Name", filter: "text" as const },
    { dataIndex: "status.name", title: "Status", filter: "select" as const },
    { dataIndex: "action", title: "Action", filter: "none" as const },
  ];

  const handleReset = () => {
    Object.keys(getValues()).forEach((key) => {
      setValue(key, "");
    });
    setIsEditMode(false);
    setStatus(undefined);
  };

  const handleAction = () => {
    handleReset();
    setIsFormVisible(false);

    void queryClient.invalidateQueries({
      queryKey: [QueryNames.GetRoomType],
    });
  };

  const deleteRoomTypeMutation = useMutation({
    mutationFn: async (id: string) => {
      return deleteApiAppRoomTypeById({
        path: { id },
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room type deleted successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetRoomType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function handleDelete(id: string) {
    const confirmation = (await swal({
      title: "Are you sure?",
      text: "Room type will be deleted!",
      icon: "warning",
      buttons: ["Cancel", "Delete"],
      dangerMode: true,
    })) as unknown as boolean;

    if (confirmation) {
      try {
        // console.log("Delete room type with id:", id);
        await deleteRoomTypeMutation.mutateAsync(id);

        handleAction();
      } catch (error) {
        await swal({
          title: "Error",
          text: "Failed to delete the room type. Please try again later.",
          icon: "error",
        });
      }
    }
  }

  const handleDetail = (id: string) => {
    setIsFormVisible(true);
    const type = isRoomType.find((type) => type.id === id);
    // console.log("Selected room type:", type);

    if (type) {
      setIsEditMode(true);
      Object.entries(type).map(([key, value], _index) => {
        if (key === "status") {
          setStatus({
            label: value as string,
            value: value as string,
          });
        } else {
          setValue(key, value);
        }
      });
    }
  };

  const createRoomTypeMutation = useMutation({
    mutationFn: async (data: CreateUpdateRoomTypeDto) =>
      postApiAppRoomType({
        body: data,
      }),
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room type created successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetRoomType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  const updateRoomTypeMutation = useMutation({
    mutationFn: async (data: RoomTypeDto) => {
      const { id, ...updateData } = data;
      return putApiAppRoomTypeById({
        path: { id: id! },
        body: updateData as CreateUpdateRoomTypeDto,
      });
    },
    onSuccess: async () => {
      await swal({
        title: "Success",
        text: "Room type updated successfully.",
        icon: "success",
        timer: 1200,
      });
      void queryClient.invalidateQueries({
        queryKey: [QueryNames.GetRoomType],
      });
    },
    onError: async (err: unknown) => {
      await swalError(err);
    },
  });

  async function onSubmit(formData: Record<string, string | number | File>) {
    try {
      if (isEditMode) {
        // console.log("Updating room type:", formData);
        await updateRoomTypeMutation.mutateAsync(formData as RoomTypeDto);
      } else {
        // console.log("Creating room type:", formData);
        await createRoomTypeMutation.mutateAsync(
          formData as CreateUpdateRoomTypeDto,
        );
      }
      handleAction();
    } catch (error) {
      console.error("Error in onSubmit:", error);
      await swal({
        title: "Error",
        text: "An unexpected error occurred. Please try again later.",
        icon: "error",
      });
    } finally {
      setIsEditMode(false);
      Object.keys(getValues()).forEach((key) => {
        setValue(key, "");
      });
    }
  }

  if (!can("WismaApp.RoomType.Edit")) return <AccessDeniedLayout />;
  return (
    <div className={"mb-2 mt-2 @container " + className}>
      {wiithHeader && (
        <PageHeaderCustom
          breadcrumb={[
            { name: "Home", href: "/dashboard" },
            { name: "Rooms" },
            { name: "Room Type" },
          ]}
        >
          <ButtonForm
            isLoading={isLoading}
            isFormVisible={isFormVisible}
            setIsFormVisible={(visible) => {
              if (visible) {
                handleReset();
              }
              setIsFormVisible(visible);
            }}
          />
        </PageHeaderCustom>
      )}
      <div className="flex flex-col gap-4">
        {isFormVisible && (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <Form
              isLoading={isLoading}
              onSubmit={(data) => onSubmit(data)}
              register={register}
              errors={errors}
              handleSubmit={handleSubmit}
              setValue={setValue}
              getValues={getValues}
              setIsEditMode={setIsEditMode}
              watch={watch}
              onDelete={handleDelete}
              handleReset={handleReset}
              isStatus={isStatus}
              setStatus={setStatus}
            />
          </div>
        )}
        {/* <div className="rounded-lg bg-white p-4 shadow"> */}
        <CustomTable
          columns={columns}
          dataSource={
            data?.items
              ? data?.items.map((e) => ({
                  ...e,
                  action: (
                    <ButtonDetail
                      itemId={String(e.id)}
                      handleDetail={handleDetail}
                    />
                  ),
                }))
              : []
          }
          pageSize={10}
          isLoading={isLoading}
          rowKey="id"
        />
        {/* </div> */}
      </div>
    </div>
  );
}
