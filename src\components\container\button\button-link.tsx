import Link from "next/link";
import React from "react";
import { Button } from "rizzu<PERSON>";

export default function ButtonLink({
  label = "Link",
  isLoading = false,
  href,
  handleLink,
  className
}: {
  label?: string;
  href?: string;
  isLoading?: boolean;
  handleLink?: () => void;
  className?: string;
}) {
  return (
    <Button
      type="button"
      size="sm"
      onClick={() => { if (handleLink) handleLink() }}
      isLoading={isLoading}
      className={className}
    >
      {/* <PiPlus className="ml-2 size-4" /> */}
      {href ?
        <Link href={href}>{label}</Link> :
        label
      }
    </Button>
  );
}
