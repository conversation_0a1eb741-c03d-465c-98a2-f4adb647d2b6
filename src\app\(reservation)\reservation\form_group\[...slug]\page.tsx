import React from "react";
import FormReservationGroup from "@/components/pages/reservation/form-group";

export default function Page(params: { params: { slug: string[] } }) {
  const page = params.params.slug?.[0] ?? ""; // this return => create, update, detail
  const id = params.params.slug?.[1] ?? ""; // this return id
  return (
    <FormReservationGroup
      page={page}
      id={id}
    />
  );
}
