/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Title, Text, Avatar, Button, Popover } from "rizzui";
import cn from "@/utils/class-names";
// import { routes } from "@/config/routes";
// import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
// import { type CustomSession } from "@/lib/custom-types";
import { PiSignOutBold } from "react-icons/pi";
import { useCurrentUser } from "@/lib/hooks/useCurrentUser";
// const menuItems = [
// {
//   name: "My Profile",
//   // href: routes.profile,
// },
// {
//   name: "Account Set<PERSON><PERSON>",
//   // href: routes.forms.profileSettings,
// },
// {
//   name: "Activity Log",
//   href: "#",
// },
// ];

function DropdownMenu() {
  const currentUser = useCurrentUser();

  // const currentSession: CustomSession = sessions as unknown as CustomSession;
  // // console.log("sessions", sessions);
  return (
    <div className="w-72 text-left rtl:text-right">
      <div className="flex items-center border-gray-300 px-6 pb-5 pt-6">
        <Avatar
          src="https://isomorphic-furyroad.s3.amazonaws.com/public/avatars/avatar-11.webp"
          // name={session?.user?.name}
          name=""
        />
        <div className="ms-3">
          <Title as="h6" className="font-semibold">
            {currentUser?.name}
          </Title>
          <Text className="text-gray-600">{currentUser?.email}</Text>
          {/* <Text className="text-gray-600">
            {currentSession?.role
              .map((role) => role.replace("WISMA ", ""))
              .join(", ")}
          </Text> */}
        </div>
      </div>
      {/* <div className="grid px-3.5 py-3.5 font-medium text-gray-700">
        {menuItems.map((item) => (
          <Link
            key={item.name}
            href={item.href ?? "/"}
            className="group my-0.5 flex items-center rounded-md px-2.5 py-2 hover:bg-gray-100 focus:outline-none"
          >
            {item.name}
          </Link>
        ))}
      </div> */}
      <div className="border-t border-gray-300">
        <Button
          className="h-auto w-full justify-start p-0 px-6 py-3 font-medium text-gray-700 outline-none focus-within:text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus-visible:ring-0"
          variant="text"
          onClick={() => {
            window.location.href = "/auth/logout";
          }}
          // onClick={() => signOut()}
        >
          <div className="flex w-full justify-between">
            <div>Sign Out</div>
            <div>
              <PiSignOutBold size={20} />
            </div>
          </div>
        </Button>
      </div>
    </div>
  );
}

export default function ProfileMenu({
                                      buttonClassName,
                                      avatarClassName,
                                      username = false
                                    }: Readonly<{
  buttonClassName?: string;
  avatarClassName?: string;
  username?: boolean;
}>) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  return (
    <Popover
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      shadow="sm"
      placement="bottom-end"
    >
      <Popover.Trigger>
        <button
          className={cn(
            "w-9 shrink-0 rounded-full outline-none focus-visible:ring-[1.5px] focus-visible:ring-gray-400 focus-visible:ring-offset-2 active:translate-y-px sm:w-10",
            buttonClassName
          )}
        >
          <Avatar
            src="https://isomorphic-furyroad.s3.amazonaws.com/public/avatars/avatar-11.webp"
            name="John Doe"
            className={cn("!h-9 w-9 sm:!h-10 sm:!w-10", avatarClassName)}
          />
          {!!username && (
            <span className="username hidden text-gray-200 md:inline-flex">
              Hi, Andry
            </span>
          )}
        </button>
      </Popover.Trigger>

      <Popover.Content className="z-[9999] p-0">
        <DropdownMenu />
      </Popover.Content>
    </Popover>
  );
}
