// This file is auto-generated by @hey-api/openapi-ts

export type AbpLoginResult = {
  result?: LoginResultType;
  readonly description?: string | null;
};

export type ApplicationAuthConfigurationDto = {
  grantedPolicies?: Record<string, boolean> | null;
};

export type ApplicationConfigurationDto = {
  localization?: ApplicationLocalizationConfigurationDto;
  auth?: ApplicationAuthConfigurationDto;
  setting?: ApplicationSettingConfigurationDto;
  currentUser?: CurrentUserDto;
  features?: ApplicationFeatureConfigurationDto;
  globalFeatures?: ApplicationGlobalFeatureConfigurationDto;
  multiTenancy?: MultiTenancyInfoDto;
  currentTenant?: CurrentTenantDto;
  timing?: TimingDto;
  clock?: ClockDto;
  objectExtensions?: ObjectExtensionsDto;
  extraProperties?: object | null;
};

export type ApplicationFeatureConfigurationDto = {
  values?: Record<string, string | null> | null;
};

export type ApplicationGlobalFeatureConfigurationDto = {
  enabledFeatures?: Array<string> | null;
};

export type ApplicationLocalizationConfigurationDto = {
  values?: Record<string, Record<string, string>> | null;
  resources?: Record<string, ApplicationLocalizationResourceDto> | null;
  languages?: Array<object> | null;
  currentCulture?: CurrentCultureDto;
  defaultResourceName?: string | null;
  languagesMap?: Record<string, Array<NameValue>> | null;
  languageFilesMap?: Record<string, Array<NameValue>> | null;
};

export type ApplicationLocalizationResourceDto = {
  texts?: Record<string, string> | null;
  baseResources?: Array<string> | null;
};

export type ApplicationSettingConfigurationDto = {
  values?: Record<string, string | null> | null;
};

export type AuditLogActionInfoDto = {
  id?: string;
  auditLogId?: string;
  serviceName?: string | null;
  methodName?: string | null;
  parameters?: string | null;
  executionTime?: string;
  executionDuration?: number;
};

export type AuditLogInfoDto = {
  id?: string;
  applicationName?: string | null;
  userId?: string | null;
  userName?: string | null;
  tenantId?: string | null;
  executionTime?: string;
  executionDuration?: number;
  clientIpAddress?: string | null;
  browserInfo?: string | null;
  httpMethod?: string | null;
  url?: string | null;
  httpStatusCode?: number | null;
};

export type Base64ConversionInput = {
  base64Content?: string | null;
  filename?: string | null;
};

export type BulkDownloadDto = {
  fileIds: Array<string>;
  zipFileName?: string | null;
};

export type ChangePasswordInput = {
  currentPassword?: string | null;
  newPassword: string;
};

export type ClockDto = {
  kind?: string | null;
};

export type CompanyDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  address?: string | null;
  alias?: string | null;
  reservationCodePrefix?: string | null;
};

export type ConditionalFormattingOperator =
  | 0
  | 1
  | 2
  | 3
  | 4
  | 5
  | 6
  | 7
  | 8
  | 9;

export type ConditionalFormattingRuleDto = {
  conditionType?: ConditionalFormattingType;
  operator?: ConditionalFormattingOperator;
  comparisonValue?: string | null;
  textPattern?: string | null;
  backgroundColor?: string | null;
  priority?: number;
  caseSensitive?: boolean;
};

export type ConditionalFormattingType = 0 | 1 | 2;

export type CreateDocumentTemplateDto = {
  name: string;
  documentType: DocumentType;
  attachmentId: string;
  description?: string | null;
  isDefault?: boolean;
};

export type CreateReservationDetailDto = {
  rfid?: string | null;
  price?: number | null;
  checkInDate?: string | null;
  checkOutDate?: string | null;
  roomId?: string;
  guestId?: string | null;
  criteriaId?: string | null;
  statusId?: string;
  isExtraBed?: boolean;
  marketCode?: number | null;
  guestData?: CreateReservationGuestDto;
};

export type CreateReservationGuestDto = {
  fullname?: string | null;
  identityNumber?: string | null;
  phoneNumber?: string | null;
  email?: string | null;
  nationality?: string | null;
  city?: string | null;
  district?: string | null;
  companyName?: string | null;
  attachment?: string | null;
  attachments?: Array<GuestAttachmentDto> | null;
  statusId?: string | null;
  gender?: string | null;
  address?: string | null;
  dateOfBirthday?: string | null;
};

export type CreateReservationWithDetailsDto = {
  groupCode?: string | null;
  bookerName?: string | null;
  bookerIdentityNumber?: string | null;
  bookerPhoneNumber?: string | null;
  bookerEmail?: string | null;
  arrivalDate?: string;
  days?: number;
  attachment?: string | null;
  companyId?: string | null;
  statusId?: string | null;
  paymentMethodId?: string | null;
  diningOptionsId?: string | null;
  reservationTypeId?: string;
  reservationDetails?: Array<CreateReservationDetailDto> | null;
};

export type CreateUpdateCompanyDto = {
  name: string;
  address?: string | null;
  alias: string;
  reservationCodePrefix: string;
};

export type CreateUpdateDiningOptionsDto = {
  name: string;
};

export type CreateUpdateFoodAndBeverageDto = {
  name: string;
  price: number;
  information?: string | null;
  typeFoodAndBeverageId: string;
  extraProperties?: object | null;
};

export type CreateUpdateGuestDto = {
  fullname: string;
  identityNumber: string;
  phoneNumber?: string | null;
  email?: string | null;
  nationality: string;
  city?: string | null;
  district?: string | null;
  companyName?: string | null;
  attachment?: string | null;
  statusId: string;
  gender?: string | null;
  address?: string | null;
  dateOfBirthday?: string | null;
  attachments?: Array<GuestAttachmentDto> | null;
};

export type CreateUpdateMasterStatusDto = {
  name: string;
  docType: string;
  color?: string | null;
  code?: string | null;
};

export type CreateUpdatePaymentDetailsDto = {
  id?: string;
  paymentId?: string;
  reservationDetailsId?: string;
  sourceType: PaymentSourceType;
  sourceId: string;
  amount: number;
  qty: number;
  unitPrice: number;
};

export type CreateUpdatePaymentGuestsDto = {
  id?: string;
  paymentId?: string;
  guestId: string;
  amountPaid: number;
};

export type CreateUpdatePaymentMethodDto = {
  name: string;
  information?: string | null;
};

export type CreateUpdatePaymentsDto = {
  totalAmount?: number;
  paidAmount?: number | null;
  vatRate?: number | null;
  vatAmount?: number | null;
  grantTotal?: number;
  paymentCode?: string | null;
  transactionDate: string;
  reservationsId: string;
  paymentMethodId: string;
  statusId: string;
  taxId?: string | null;
  paidCompanyId?: string | null;
  paymentDetails?: Array<CreateUpdatePaymentDetailsDto> | null;
  paymentGuests?: Array<CreateUpdatePaymentGuestsDto> | null;
  paymentAttachments?: Array<PaymentAttachmentDto> | null;
  reservationAttachments?: Array<PaymentReservationAttachmentDto> | null;
};

export type CreateUpdateReportDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name: string;
  description?: string | null;
  queryType: ReportQueryType;
  query: string;
  parameters?: string | null;
  roles?: string | null;
  excelHeaderConfig?: string | null;
  pivotConfig?: string | null;
  isActive?: boolean;
};

export type CreateUpdateReservationDetailsDto = {
  reservationId?: string;
  roomId?: string;
  statusId?: string;
  paymentStatusId?: string | null;
  criteriaId?: string | null;
  guestId?: string | null;
  checkInDate?: string | null;
  checkOutDate?: string | null;
  rfid?: string | null;
  marketCode?: number | null;
  isExtraBed?: boolean;
  price?: number;
  guestData?: GuestDataDto;
  status?: MasterStatusDto;
  paymentStatus?: MasterStatusDto;
};

export type CreateUpdateReservationFoodAndBeveragesDto = {
  id?: string;
  reservationDetailsId: string;
  foodAndBeverageId: string;
  paymentStatusId?: string | null;
  totalPrice: number;
  quantity: number;
  transactionDate: string;
};

export type CreateUpdateReservationRoomsDto = {
  id?: string;
  reservationDetailsId: string;
  serviceId: string;
  paymentStatusId?: string | null;
  totalPrice: number;
  quantity: number;
  transactionDate: string;
};

export type CreateUpdateReservationTypesDto = {
  name: string;
  statusId: string;
};

export type CreateUpdateReservationsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  reservationCode?: string | null;
  groupCode?: string | null;
  bookerName?: string | null;
  bookerIdentityNumber?: string | null;
  bookerPhoneNumber?: string | null;
  bookerEmail?: string | null;
  arrivalDate?: string;
  days?: number;
  attachment?: string | null;
  attachments?: Array<ReservationAttachmentDto> | null;
  statusId: string;
  reservationTypeId: string;
  companyId?: string | null;
  paymentMethodId?: string | null;
  diningOptionsId?: string | null;
  isExtraBed?: boolean;
  reservationDetails?: Array<CreateReservationDetailDto> | null;
};

export type CreateUpdateRoomDto = {
  roomNumber: string;
  roomCode: string;
  size: string;
  isLocked?: boolean;
  information?: string | null;
  roomStatusId?: string;
  roomTypeId?: string;
  price?: number;
  extraProperties?: object | null;
};

export type CreateUpdateRoomStatusDto = {
  name: string;
  color: string;
  code?: string | null;
};

export type CreateUpdateRoomStatusLogsDto = {
  roomId?: string;
  roomStatusId?: string;
  statusSource?: string | null;
};

export type CreateUpdateRoomTypeDto = {
  name: string;
  statusId: string;
  alias?: string | null;
};

export type CreateUpdateServiceTypesDto = {
  name: string;
  statusId: string;
};

export type CreateUpdateServicesDto = {
  name: string;
  price: number;
  usageTime: number;
  information?: string | null;
  serviceTypeId?: string;
  extraProperties?: object | null;
};

export type CreateUpdateTaxDto = {
  name: string;
  code: string;
  rate: number;
  startDate: string;
  endDate: string;
  isActive?: boolean;
};

export type CreateUpdateTypeFoodAndBeverageDto = {
  name: string;
  statusId: string;
};

export type CurrentCultureDto = {
  displayName?: string | null;
  englishName?: string | null;
  threeLetterIsoLanguageName?: string | null;
  twoLetterIsoLanguageName?: string | null;
  isRightToLeft?: boolean;
  cultureName?: string | null;
  name?: string | null;
  nativeName?: string | null;
  dateTimeFormat?: DateTimeFormatDto;
};

export type CurrentTenantDto = {
  id?: string | null;
  name?: string | null;
  isAvailable?: boolean;
};

export type CurrentUserDto = {
  isAuthenticated?: boolean;
  id?: string | null;
  tenantId?: string | null;
  impersonatorUserId?: string | null;
  impersonatorTenantId?: string | null;
  impersonatorUserName?: string | null;
  impersonatorTenantName?: string | null;
  userName?: string | null;
  name?: string | null;
  surName?: string | null;
  email?: string | null;
  emailVerified?: boolean;
  phoneNumber?: string | null;
  phoneNumberVerified?: boolean;
  roles?: Array<string> | null;
  sessionId?: string | null;
};

export type DateRequestDto = {
  date?: string;
};

export type DateTimeFormatDto = {
  calendarAlgorithmType?: string | null;
  dateTimeFormatLong?: string | null;
  shortDatePattern?: string | null;
  fullDateTimePattern?: string | null;
  dateSeparator?: string | null;
  shortTimePattern?: string | null;
  longTimePattern?: string | null;
};

export type DelimiterConfigDto = {
  start: string;
  end: string;
};

export type DiningOptionsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
};

export type DocumentGenerationDto = {
  documentId: string;
  templateId?: string | null;
  includeDetails?: boolean;
  useAdvancedTable?: boolean;
  generatePdf?: boolean;
  customFilename?: string | null;
};

export type DocumentTemplateDto = {
  id?: string;
  name?: string | null;
  documentType?: DocumentType;
  documentTypeName?: string | null;
  attachmentId?: string;
  description?: string | null;
  isDefault?: boolean;
  creationTime?: string;
};

export type DocumentType = 1 | 2 | 3 | 4 | 5 | 6 | 99;

export type DocxToPdfConversionDto = {
  documentType: DocumentType;
  templateId?: string | null;
  config: TemplateConfigDto;
  model: unknown;
};

export type EntityChangeLogDto = {
  id?: string;
  changeTime?: string;
  changeType?: EntityChangeTypeDto;
  entityId?: string | null;
  entityTenantId?: string | null;
  entityTypeFullName?: string | null;
  entityDisplayName?: string | null;
  entityDisplayValue?: string | null;
  auditLogId?: string;
  userName?: string | null;
  propertyChanges?: Array<EntityPropertyChangeLogDto> | null;
  auditLog?: AuditLogInfoDto;
  auditLogActions?: Array<AuditLogActionInfoDto> | null;
};

export type EntityChangeTypeDto = 0 | 1 | 2;

export type EntityExtensionDto = {
  properties?: Record<string, ExtensionPropertyDto> | null;
  configuration?: object | null;
};

export type EntityPropertyChangeLogDto = {
  id?: string;
  entityChangeLogId?: string;
  propertyName?: string | null;
  propertyDisplayName?: string | null;
  originalValue?: string | null;
  newValue?: string | null;
  propertyTypeFullName?: string | null;
  originalValueDisplay?: string | null;
  newValueDisplay?: string | null;
  isReference?: boolean;
};

export type ExcelCellType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;

export type ExtensionEnumDto = {
  fields?: Array<ExtensionEnumFieldDto> | null;
  localizationResource?: string | null;
};

export type ExtensionEnumFieldDto = {
  name?: string | null;
  value?: unknown;
};

export type ExtensionPropertyApiCreateDto = {
  isAvailable?: boolean;
};

export type ExtensionPropertyApiDto = {
  onGet?: ExtensionPropertyApiGetDto;
  onCreate?: ExtensionPropertyApiCreateDto;
  onUpdate?: ExtensionPropertyApiUpdateDto;
};

export type ExtensionPropertyApiGetDto = {
  isAvailable?: boolean;
};

export type ExtensionPropertyApiUpdateDto = {
  isAvailable?: boolean;
};

export type ExtensionPropertyAttributeDto = {
  typeSimple?: string | null;
  config?: object | null;
};

export type ExtensionPropertyDto = {
  type?: string | null;
  typeSimple?: string | null;
  displayName?: LocalizableStringDto;
  api?: ExtensionPropertyApiDto;
  ui?: ExtensionPropertyUiDto;
  policy?: ExtensionPropertyPolicyDto;
  attributes?: Array<ExtensionPropertyAttributeDto> | null;
  configuration?: object | null;
  defaultValue?: unknown;
};

export type ExtensionPropertyFeaturePolicyDto = {
  features?: Array<string> | null;
  requiresAll?: boolean;
};

export type ExtensionPropertyGlobalFeaturePolicyDto = {
  features?: Array<string> | null;
  requiresAll?: boolean;
};

export type ExtensionPropertyPermissionPolicyDto = {
  permissionNames?: Array<string> | null;
  requiresAll?: boolean;
};

export type ExtensionPropertyPolicyDto = {
  globalFeatures?: ExtensionPropertyGlobalFeaturePolicyDto;
  features?: ExtensionPropertyFeaturePolicyDto;
  permissions?: ExtensionPropertyPermissionPolicyDto;
};

export type ExtensionPropertyUiDto = {
  onTable?: ExtensionPropertyUiTableDto;
  onCreateForm?: ExtensionPropertyUiFormDto;
  onEditForm?: ExtensionPropertyUiFormDto;
  lookup?: ExtensionPropertyUiLookupDto;
};

export type ExtensionPropertyUiFormDto = {
  isVisible?: boolean;
};

export type ExtensionPropertyUiLookupDto = {
  url?: string | null;
  resultListPropertyName?: string | null;
  displayPropertyName?: string | null;
  valuePropertyName?: string | null;
  filterParamName?: string | null;
};

export type ExtensionPropertyUiTableDto = {
  isVisible?: boolean;
};

export type FileDto = {
  fileName?: string | null;
  contentType?: string | null;
  content?: string | null;
};

export type FileUploadInputDto = {
  description?: string | null;
  referenceId?: string | null;
  referenceType?: string | null;
  fileName: string;
  contentType: string;
  fileContent: string;
};

export type FileUploadResultDto = {
  id?: string;
  fileName?: string | null;
  contentType?: string | null;
  size?: number;
  url?: string | null;
  streamUrl?: string | null;
  uploadTime?: string;
};

export type FilterCondition = {
  fieldName: string;
  operator: FilterOperator;
  value: unknown;
};

export type FilterGroup = {
  operator: LogicalOperator;
  conditions: Array<FilterCondition>;
};

export type FilterOperator =
  | "Equals"
  | "NotEquals"
  | "Contains"
  | "StartsWith"
  | "EndsWith"
  | "GreaterThan"
  | "GreaterThanOrEqual"
  | "LessThan"
  | "LessThanOrEqual"
  | "In"
  | "NotIn"
  | "Between"
  | "NotBetween"
  | "IsNull"
  | "IsNotNull"
  | "IsEmpty"
  | "IsNotEmpty"
  | "IsTrue"
  | "IsFalse"
  | "IsNullOrEmpty"
  | "IsNotNullOrEmpty"
  | "IsNullOrWhiteSpace"
  | "IsNotNullOrWhiteSpace"
  | "IsNumeric"
  | "IsAlpha"
  | "IsAlphaNumeric"
  | "IsEmail"
  | "IsUrl"
  | "IsIp"
  | "IsIpv4"
  | "IsIpv6"
  | "IsGuid"
  | "IsGuidEmpty"
  | "IsGuidNotEmpty"
  | "IsGuidNull"
  | "IsGuidNotNull"
  | "IsGuidNullOrEmpty"
  | "IsGuidNotNullOrEmpty"
  | "IsGuidNullOrWhiteSpace"
  | "IsGuidNotNullOrWhiteSpace"
  | "IsGuidNumeric"
  | "IsGuidAlpha"
  | "IsGuidAlphaNumeric";

export type FindTenantResultDto = {
  success?: boolean;
  tenantId?: string | null;
  name?: string | null;
  normalizedName?: string | null;
  isActive?: boolean;
};

export type FoodAndBeverageDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  price?: number;
  information?: string | null;
  typeFoodAndBeverageId?: string;
  typeFoodAndBeverageName?: string | null;
  typeFoodAndBeverage?: TypeFoodAndBeverageDto;
  extraProperties?: object | null;
};

export type GuestAttachmentDto = {
  fileName: string;
  contentType: string;
  base64Content: string;
  description?: string | null;
};

export type GuestAttachmentInfoDto = {
  id?: string;
  fileName?: string | null;
  contentType?: string | null;
  size?: number;
  url?: string | null;
  streamUrl?: string | null;
  description?: string | null;
  creationTime?: string;
};

export type GuestDataDto = {
  fullname?: string | null;
  identityNumber?: string | null;
  phoneNumber?: string | null;
  email?: string | null;
  nationality?: string | null;
  companyName?: string | null;
  attachment?: string | null;
};

export type GuestDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  fullname?: string | null;
  email?: string | null;
  phoneNumber?: string | null;
  identityNumber?: string | null;
  companyName?: string | null;
  nationality?: string | null;
  city?: string | null;
  district?: string | null;
  statusId?: string | null;
  gender?: string | null;
  address?: string | null;
  dateOfBirthday?: string | null;
  attachment?: string | null;
  attachments?: Array<GuestAttachmentInfoDto> | null;
  status?: MasterStatusDto;
};

export type IanaTimeZone = {
  timeZoneName?: string | null;
};

export type IdentityUserDtoReadable = {
  readonly extraProperties?: object | null;
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  isDeleted?: boolean;
  deleterId?: string | null;
  deletionTime?: string | null;
  tenantId?: string | null;
  userName?: string | null;
  name?: string | null;
  surname?: string | null;
  email?: string | null;
  emailConfirmed?: boolean;
  phoneNumber?: string | null;
  phoneNumberConfirmed?: boolean;
  isActive?: boolean;
  lockoutEnabled?: boolean;
  accessFailedCount?: number;
  lockoutEnd?: string | null;
  concurrencyStamp?: string | null;
  entityVersion?: number;
  lastPasswordChangeTime?: string | null;
};

export type IdentityUserDtoWritable = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  isDeleted?: boolean;
  deleterId?: string | null;
  deletionTime?: string | null;
  tenantId?: string | null;
  userName?: string | null;
  name?: string | null;
  surname?: string | null;
  email?: string | null;
  emailConfirmed?: boolean;
  phoneNumber?: string | null;
  phoneNumberConfirmed?: boolean;
  isActive?: boolean;
  lockoutEnabled?: boolean;
  accessFailedCount?: number;
  lockoutEnd?: string | null;
  concurrencyStamp?: string | null;
  entityVersion?: number;
  lastPasswordChangeTime?: string | null;
};

export type InvoiceGenerationDto = {
  paymentId: string;
  templateId?: string | null;
  includePaymentDetails?: boolean;
  useAdvancedTable?: boolean;
  generatePdf?: boolean;
  customFilename?: string | null;
};

export type InvoicePaymentDetailDto = {
  sourceType?: string | null;
  checkInDate?: string | null;
  checkOutDate?: string | null;
  roomNumber?: string | null;
  sourceId?: string;
  description?: string | null;
  amount?: number;
  qty?: number;
  transactionDate?: string | null;
};

export type InvoiceTemplateDataDto = {
  invoiceNumber?: string | null;
  invoiceDate?: string;
  paymentCode?: string | null;
  createdBy?: string | null;
  transactionDate?: string | null;
  reservationCode?: string | null;
  bookerName?: string | null;
  guestName?: string | null;
  paymentMethod?: string | null;
  paymentStatus?: string | null;
  totalAmount?: number;
  paidAmount?: number;
  vatRate?: number | null;
  vatAmount?: number | null;
  grantTotal?: number;
  settlementCompany?: string | null;
  paymentDetails?: Array<InvoicePaymentDetailDto> | null;
  companyName?: string | null;
  companyAddress?: string | null;
  companyPhone?: string | null;
  companyEmail?: string | null;
  companyLogoUrl?: string | null;
  checkInDate?: string | null;
  checkOutDate?: string | null;
  roomNumber?: string | null;
};

export type LanguageInfoReadable = {
  cultureName?: string | null;
  uiCultureName?: string | null;
  displayName?: string | null;
  readonly twoLetterISOLanguageName?: string | null;
};

export type LanguageInfoWritable = {
  cultureName?: string | null;
  uiCultureName?: string | null;
  displayName?: string | null;
};

export type LocalizableStringDto = {
  name?: string | null;
  resource?: string | null;
};

export type LogicalOperator = "And" | "Or";

export type LoginResultType = 1 | 2 | 3 | 4 | 5;

export type MasterStatusDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  docType?: string | null;
  color?: string | null;
  code?: string | null;
};

export type ModuleExtensionDto = {
  entities?: Record<string, EntityExtensionDto> | null;
  configuration?: object | null;
};

export type MultiTenancyInfoDto = {
  isEnabled?: boolean;
};

export type NameValue = {
  name?: string | null;
  value?: string | null;
};

export type ObjectExtensionsDto = {
  modules?: Record<string, ModuleExtensionDto> | null;
  enums?: Record<string, ExtensionEnumDto> | null;
};

export type PagedResultDtoOfCompanyDto = {
  items?: Array<CompanyDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfDiningOptionsDto = {
  items?: Array<DiningOptionsDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfFileUploadResultDto = {
  items?: Array<FileUploadResultDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfFoodAndBeverageDto = {
  items?: Array<FoodAndBeverageDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfGuestDto = {
  items?: Array<GuestDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfMasterStatusDto = {
  items?: Array<MasterStatusDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfPaymentDetailsDto = {
  items?: Array<PaymentDetailsDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfPaymentGuestsDto = {
  items?: Array<PaymentGuestsDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfPaymentMethodDto = {
  items?: Array<PaymentMethodDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfPaymentsDto = {
  items?: Array<PaymentsDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfReportDto = {
  items?: Array<ReportDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfReservationDetailsDto = {
  items?: Array<ReservationDetailsDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfReservationFoodAndBeveragesDto = {
  items?: Array<ReservationFoodAndBeveragesDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfReservationRoomsDto = {
  items?: Array<ReservationRoomsDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfReservationTypesDto = {
  items?: Array<ReservationTypesDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfReservationsDto = {
  items?: Array<ReservationsDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfRoomDto = {
  items?: Array<RoomDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfRoomStatusDto = {
  items?: Array<RoomStatusDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfRoomStatusLogsDto = {
  items?: Array<RoomStatusLogsDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfRoomTypeDto = {
  items?: Array<RoomTypeDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfServiceTypesDto = {
  items?: Array<ServiceTypesDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfServicesDto = {
  items?: Array<ServicesDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfTaxDto = {
  items?: Array<TaxDto> | null;
  totalCount?: number;
};

export type PagedResultDtoOfTenantDto = {
  items?: Array<TenantDtoReadable> | null;
  totalCount?: number;
};

export type PagedResultDtoOfTypeFoodAndBeverageDto = {
  items?: Array<TypeFoodAndBeverageDto> | null;
  totalCount?: number;
};

export type PaymentAttachmentDto = {
  fileName: string;
  contentType: string;
  base64Content: string;
  description?: string | null;
};

export type PaymentAttachmentInfoDto = {
  id?: string;
  fileName?: string | null;
  contentType?: string | null;
  size?: number;
  url?: string | null;
  streamUrl?: string | null;
  description?: string | null;
  creationTime?: string;
};

export type PaymentDetailsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  paymentId?: string;
  sourceType?: PaymentSourceType;
  sourceId?: string;
  amount?: number;
  qty?: number;
  unitPrice?: number;
  reservationDetailsId?: string;
  reservationDetails?: ReservationDetailBasicDto;
  reservationFoodAndBeverages?: ReservationFoodAndBeveragesDto;
  reservationRoom?: ReservationRoomsDto;
};

export type PaymentGuestsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  paymentId?: string;
  guestId?: string;
  amountPaid?: number;
};

export type PaymentMethodDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  information?: string | null;
};

export type PaymentReservationAttachmentDto = {
  fileName: string;
  contentType: string;
  base64Content: string;
  description?: string | null;
};

export type PaymentReservationAttachmentInfoDto = {
  id?: string;
  fileName?: string | null;
  contentType?: string | null;
  size?: number;
  url?: string | null;
  streamUrl?: string | null;
  description?: string | null;
  creationTime?: string;
};

export type PaymentSourceType = 1 | 2 | 3;

export type PaymentsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  totalAmount?: number | null;
  vatRate?: number | null;
  vatAmount?: number | null;
  paidAmount?: number | null;
  grantTotal?: number | null;
  paymentCode?: string | null;
  reservationsId?: string;
  paymentMethodId?: string | null;
  statusId?: string;
  taxId?: string | null;
  paidCompanyId?: string | null;
  transactionDate?: string | null;
  reservationDetailsId?: string;
  reservationCode?: string | null;
  bookerName?: string | null;
  guestName?: string | null;
  status?: MasterStatusDto;
  paidCompany?: CompanyDto;
  paymentMethod?: PaymentMethodDto;
  paymentDetails?: Array<PaymentDetailsDto> | null;
  paymentAttachments?: Array<PaymentAttachmentInfoDto> | null;
  reservationAttachments?: Array<PaymentReservationAttachmentInfoDto> | null;
  invoiceStreamUrl?: string | null;
  invoiceFileName?: string | null;
  invoiceFileId?: string | null;
};

export type ProfileDtoReadable = {
  readonly extraProperties?: object | null;
  userName?: string | null;
  email?: string | null;
  name?: string | null;
  surname?: string | null;
  phoneNumber?: string | null;
  isExternal?: boolean;
  hasPassword?: boolean;
  concurrencyStamp?: string | null;
};

export type ProfileDtoWritable = {
  userName?: string | null;
  email?: string | null;
  name?: string | null;
  surname?: string | null;
  phoneNumber?: string | null;
  isExternal?: boolean;
  hasPassword?: boolean;
  concurrencyStamp?: string | null;
};

export type QueryParametersReadable = {
  sorting?: string | null;
  page?: number;
  sort?: Array<SortInfo> | null;
  filterGroup?: FilterGroup;
  readonly skipCount?: number;
  maxResultCount?: number;
};

export type QueryParametersWritable = {
  sorting?: string | null;
  page?: number;
  sort?: Array<SortInfo> | null;
  filterGroup?: FilterGroup;
  maxResultCount?: number;
};

export type RegisterDtoReadable = {
  readonly extraProperties?: object | null;
  userName: string;
  emailAddress: string;
  password: string;
  appName: string;
};

export type RegisterDtoWritable = {
  userName: string;
  emailAddress: string;
  password: string;
  appName: string;
};

export type RemoteServiceErrorInfo = {
  code?: string | null;
  message?: string | null;
  details?: string | null;
  data?: object | null;
  validationErrors?: Array<RemoteServiceValidationErrorInfo> | null;
};

export type RemoteServiceErrorResponse = {
  error?: RemoteServiceErrorInfo;
};

export type RemoteServiceValidationErrorInfo = {
  message?: string | null;
  members?: Array<string> | null;
};

export type ReportDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  description?: string | null;
  queryType?: ReportQueryType;
  query?: string | null;
  parameters?: string | null;
  isActive?: boolean;
  excelHeaderConfig?: string | null;
  pivotConfig?: string | null;
  roles?: string | null;
};

export type ReportExcelColumnConfigDto = {
  columnName?: string | null;
  cellType?: ExcelCellType;
  numberFormat?: string | null;
  style?: ReportExcelStyleDto;
  width?: number;
  conditionalRules?: Array<ConditionalFormattingRuleDto> | null;
};

export type ReportExcelHeaderCellDto = {
  text?: string | null;
  colSpan?: number;
  rowSpan?: number;
  style?: ReportExcelStyleDto;
};

export type ReportExcelHeaderDto = {
  title?: string | null;
  subTitle?: string | null;
  headerRows?: Array<ReportExcelHeaderRowDto> | null;
  titleStyle?: ReportExcelStyleDto;
  subTitleStyle?: ReportExcelStyleDto;
  headerStyle?: ReportExcelStyleDto;
  dataStyle?: ReportExcelStyleDto;
  showGeneratedDate?: boolean;
  dateFormat?: string | null;
  columnConfigs?: Array<ReportExcelColumnConfigDto> | null;
};

export type ReportExcelHeaderRowDto = {
  cells?: Array<ReportExcelHeaderCellDto> | null;
};

export type ReportExcelStyleDto = {
  fontName?: string | null;
  fontSize?: number;
  bold?: boolean;
  backgroundColor?: string | null;
  fontColor?: string | null;
  horizontalAlignment?: string | null;
  verticalAlignment?: string | null;
  border?: boolean;
  wrapText?: boolean;
};

export type ReportExecutionDto = {
  reportId?: string;
  parameters?: object | null;
};

export type ReportExecutionWithCustomHeaderDto = {
  reportId?: string;
  parameters?: object | null;
  customHeader?: ReportExcelHeaderDto;
};

export type ReportParameterDto = {
  name?: string | null;
  type?: string | null;
  required?: boolean;
  defaultValue?: string | null;
  description?: string | null;
};

export type ReportPreviewDto = {
  reportName?: string | null;
  data?: Array<object> | null;
  columns?: Array<string> | null;
  totalRows?: number;
  executedAt?: string;
  parameters?: Array<ReportParameterDto> | null;
  excelHeaderConfig?: ReportExcelHeaderDto;
};

export type ReportQueryType = 1 | 2 | 3 | 4;

export type ReportRoomsDto = {
  alias?: string | null;
  total?: number;
  mtc?: number;
  ls?: number;
  semua_tamu?: number;
  sisa?: number;
  kosong?: number;
};

export type ReservationAttachmentDto = {
  fileName: string;
  contentType: string;
  base64Content: string;
  description?: string | null;
};

export type ReservationAttachmentInfoDto = {
  id?: string;
  fileName?: string | null;
  contentType?: string | null;
  size?: number;
  url?: string | null;
  streamUrl?: string | null;
  description?: string | null;
  creationTime?: string;
};

export type ReservationBasicInfoDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  reservationCode: string | null;
  groupCode?: string | null;
  bookerName?: string | null;
  bookerIdentityNumber?: string | null;
  bookerPhoneNumber?: string | null;
  bookerEmail?: string | null;
  arrivalDate?: string;
  days?: number;
  attachment?: string | null;
  companyId?: string | null;
  company?: CompanyDto;
  statusId?: string;
  status?: MasterStatusDto;
  reservationTypeId?: string;
  reservationTypes?: ReservationTypesDto;
  diningOptions?: DiningOptionsDto;
  paymentMethodId?: string | null;
};

export type ReservationDetailBasicDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  checkInDate?: string | null;
  checkOutDate?: string | null;
  rfid?: string | null;
  price?: number;
  reservationId?: string;
  roomId?: string;
  room?: RoomDto;
  roomNumber?: string | null;
  guestId?: string;
  guest?: GuestDto;
  guestName?: string | null;
  statusId?: string;
  status?: MasterStatusDto;
  criteriaId?: string;
  criteria?: MasterStatusDto;
  paymentStatusId?: string;
  paymentStatus?: MasterStatusDto;
  reservation?: ReservationBasicInfoDto;
};

export type ReservationDetailsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  checkInDate?: string | null;
  checkOutDate?: string | null;
  rfid?: string | null;
  price?: number;
  reservationId?: string;
  roomId?: string;
  room?: RoomDto;
  roomNumber?: string | null;
  guestId?: string;
  guest?: GuestDto;
  guestName?: string | null;
  statusId?: string;
  status?: MasterStatusDto;
  criteriaId?: string;
  criteria?: MasterStatusDto;
  paymentStatusId?: string;
  paymentStatus?: MasterStatusDto;
  isExtraBed?: boolean;
  marketCode?: number | null;
  reservation?: ReservationBasicInfoDto;
  reservationRooms?: Array<ReservationRoomsDto> | null;
  reservationFoodAndBeverages?: Array<ReservationFoodAndBeveragesDto> | null;
  paymentDetails?: Array<PaymentDetailsDto> | null;
  registrationCards?: Array<PaymentAttachmentInfoDto> | null;
};

export type ReservationFoodAndBeveragesDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  reservationDetailsId?: string;
  foodAndBeverageId?: string;
  paymentStatusId?: string | null;
  totalPrice?: number;
  quantity?: number;
  foodAndBeverageName?: string | null;
  foodAndBeverageTypeName?: string | null;
  transactionDate?: string | null;
  paymentStatus?: MasterStatusDto;
  foodAndBeverage?: FoodAndBeverageDto;
  paymentDetails?: PaymentDetailsDto;
};

export type ReservationRoomsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  reservationDetailsId?: string;
  serviceId?: string;
  paymentStatusId?: string | null;
  totalPrice?: number;
  quantity?: number;
  serviceName?: string | null;
  serviceTypeName?: string | null;
  transactionDate?: string | null;
  paymentStatus?: MasterStatusDto;
  services?: ServicesDto;
  paymentDetails?: PaymentDetailsDto;
};

export type ReservationTypesDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  statusId?: string | null;
};

export type ReservationsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  reservationCode: string | null;
  groupCode?: string | null;
  bookerName?: string | null;
  bookerIdentityNumber?: string | null;
  bookerPhoneNumber?: string | null;
  bookerEmail?: string | null;
  arrivalDate?: string;
  days?: number;
  attachment?: string | null;
  attachments?: Array<ReservationAttachmentInfoDto> | null;
  companyId?: string | null;
  company?: CompanyDto;
  diningOptionsId?: string | null;
  diningOptions?: DiningOptionsDto;
  paymentMethodId?: string | null;
  paymentMethod?: PaymentMethodDto;
  statusId?: string;
  status?: MasterStatusDto;
  reservationTypeId?: string;
  reservationType?: ReservationTypesDto;
  reservationDetails?: Array<ReservationDetailsDto> | null;
};

export type ResetPasswordDto = {
  userId?: string;
  resetToken: string;
  password: string;
};

export type RoomDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  price?: number | null;
  size?: string | null;
  isLocked?: boolean | null;
  roomNumber?: string | null;
  roomCode?: string | null;
  information?: string | null;
  roomStatusId?: string;
  roomStatus?: RoomStatusDto;
  roomTypeId?: string;
  roomType?: RoomTypeDto;
  extraProperties?: object | null;
};

export type RoomStatusDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  color?: string | null;
  code?: string | null;
};

export type RoomStatusLogsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  roomId?: string;
  roomStatusId?: string;
  statusSource?: string | null;
  roomStatusName?: string | null;
  roomNumber?: string | null;
  roomCode?: string | null;
};

export type RoomTypeDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  alias?: string | null;
  statusId?: string;
  status?: MasterStatusDto;
};

export type SendPasswordResetCodeDto = {
  email: string;
  appName: string;
  returnUrl?: string | null;
  returnUrlHash?: string | null;
};

export type ServiceTypesDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  statusId?: string | null;
  status?: MasterStatusDto;
};

export type ServicesDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  price?: number;
  usageTime?: number;
  information?: string | null;
  serviceTypeId?: string;
  serviceTypeName?: string | null;
  serviceType?: ServiceTypesDto;
  extraProperties?: object | null;
};

export type SortInfo = {
  field?: string | null;
  desc?: boolean;
};

export type StatusSettingsDto = {
  checkInStatusName: string;
  checkOutStatusName: string;
  occupiedInStayStatusName: string;
  dirtyStatusName: string;
};

export type TaxDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  code?: string | null;
  rate?: number | null;
  startDate?: string | null;
  endDate?: string | null;
  isActive?: boolean;
};

export type TemplateConfigDto = {
  delimiter: DelimiterConfigDto;
};

export type TenantCreateDtoReadable = {
  readonly extraProperties?: object | null;
  name: string;
  adminEmailAddress: string;
  adminPassword: string;
};

export type TenantCreateDtoWritable = {
  name: string;
  adminEmailAddress: string;
  adminPassword: string;
};

export type TenantDtoReadable = {
  readonly extraProperties?: object | null;
  id?: string;
  name?: string | null;
  concurrencyStamp?: string | null;
};

export type TenantDtoWritable = {
  id?: string;
  name?: string | null;
  concurrencyStamp?: string | null;
};

export type TenantUpdateDtoReadable = {
  readonly extraProperties?: object | null;
  name: string;
  concurrencyStamp?: string | null;
};

export type TenantUpdateDtoWritable = {
  name: string;
  concurrencyStamp?: string | null;
};

export type TickerType = 0 | 1;

export type TimeZone = {
  iana?: IanaTimeZone;
  windows?: WindowsTimeZone;
};

export type TimingDto = {
  timeZone?: TimeZone;
};

export type TypeFoodAndBeverageDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  name?: string | null;
  statusId?: string;
  status?: MasterStatusDto;
};

export type UpdateDocumentTemplateDto = {
  name: string;
  documentType: DocumentType;
  description?: string | null;
  isDefault?: boolean;
};

export type UpdateProfileDtoReadable = {
  readonly extraProperties?: object | null;
  userName?: string | null;
  email?: string | null;
  name?: string | null;
  surname?: string | null;
  phoneNumber?: string | null;
  concurrencyStamp?: string | null;
};

export type UpdateProfileDtoWritable = {
  userName?: string | null;
  email?: string | null;
  name?: string | null;
  surname?: string | null;
  phoneNumber?: string | null;
  concurrencyStamp?: string | null;
};

export type UpdateReservationDetailDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  rfid?: string | null;
  price?: number | null;
  checkInDate: string;
  checkOutDate: string;
  roomId: string;
  guestId: string;
  statusId: string;
  criteriaId?: string | null;
  concurrencyStamp?: string | null;
  guest?: UpdateReservationGuestDto;
};

export type UpdateReservationGuestDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  fullname: string;
  identityNumber: string;
  phoneNumber?: string | null;
  email?: string | null;
  nationality?: string | null;
  city?: string | null;
  district?: string | null;
  companyName?: string | null;
  attachment?: string | null;
  concurrencyStamp?: string | null;
};

export type UpdateReservationWithDetailsDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | null;
  lastModificationTime?: string | null;
  lastModifierId?: string | null;
  reservationCode: string;
  groupCode?: string | null;
  bookerName: string;
  bookerIdentityNumber?: string | null;
  bookerPhoneNumber?: string | null;
  bookerEmail?: string | null;
  arrivalDate: string;
  days?: number;
  attachment?: string | null;
  companyId?: string | null;
  statusId: string;
  paymentMethodId?: string | null;
  diningOptionsId?: string | null;
  reservationTypeId: string;
  concurrencyStamp?: string | null;
  reservationDetail?: Array<UpdateReservationDetailDto> | null;
  reservationDetails?: Array<UpdateReservationDetailDto> | null;
};

export type UpdateSettingDto = {
  name: string;
  value: string;
};

export type UploadDocumentTemplateDto = {
  name: string;
  documentType: DocumentType;
  description?: string | null;
  isDefault?: boolean;
  file: Blob | File;
};

export type UserLoginInfo = {
  userNameOrEmailAddress: string;
  password: string;
  rememberMe?: boolean;
};

export type UserSynchronizationConfigDto = {
  isEnabled?: boolean;
  updateExistingUsers?: boolean;
  synchronizeRoles?: boolean;
  synchronizeClaims?: boolean;
  enableLogging?: boolean;
};

export type UserSynchronizationHealthDto = {
  isEnabled?: boolean;
  isHealthy?: boolean;
  configuration?: UserSynchronizationConfigDto;
  lastSynchronization?: string | null;
  totalUsersSynchronized?: number;
  checkTimestamp?: string;
  errorMessage?: string | null;
};

export type VerifyPasswordResetTokenInput = {
  userId?: string;
  resetToken: string;
};

export type WindowsTimeZone = {
  timeZoneId?: string | null;
};

export type GetApiAbpApplicationConfigurationData = {
  body?: never;
  path?: never;
  query?: {
    IncludeLocalizationResources?: boolean;
  };
  url: "/api/abp/application-configuration";
};

export type GetApiAbpApplicationConfigurationErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAbpApplicationConfigurationError =
  GetApiAbpApplicationConfigurationErrors[keyof GetApiAbpApplicationConfigurationErrors];

export type GetApiAbpApplicationConfigurationResponses = {
  /**
   * OK
   */
  200: ApplicationConfigurationDto;
};

export type GetApiAbpApplicationConfigurationResponse =
  GetApiAbpApplicationConfigurationResponses[keyof GetApiAbpApplicationConfigurationResponses];

export type GetApiAbpMultiTenancyTenantsByNameByNameData = {
  body?: never;
  path: {
    name: string;
  };
  query?: never;
  url: "/api/abp/multi-tenancy/tenants/by-name/{name}";
};

export type GetApiAbpMultiTenancyTenantsByNameByNameErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameError =
  GetApiAbpMultiTenancyTenantsByNameByNameErrors[keyof GetApiAbpMultiTenancyTenantsByNameByNameErrors];

export type GetApiAbpMultiTenancyTenantsByNameByNameResponses = {
  /**
   * OK
   */
  200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameResponse =
  GetApiAbpMultiTenancyTenantsByNameByNameResponses[keyof GetApiAbpMultiTenancyTenantsByNameByNameResponses];

export type GetApiAbpMultiTenancyTenantsByIdByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/abp/multi-tenancy/tenants/by-id/{id}";
};

export type GetApiAbpMultiTenancyTenantsByIdByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdError =
  GetApiAbpMultiTenancyTenantsByIdByIdErrors[keyof GetApiAbpMultiTenancyTenantsByIdByIdErrors];

export type GetApiAbpMultiTenancyTenantsByIdByIdResponses = {
  /**
   * OK
   */
  200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdResponse =
  GetApiAbpMultiTenancyTenantsByIdByIdResponses[keyof GetApiAbpMultiTenancyTenantsByIdByIdResponses];

export type PostApiAccountRegisterData = {
  body?: RegisterDtoWritable;
  path?: never;
  query?: never;
  url: "/api/account/register";
};

export type PostApiAccountRegisterErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAccountRegisterError =
  PostApiAccountRegisterErrors[keyof PostApiAccountRegisterErrors];

export type PostApiAccountRegisterResponses = {
  /**
   * OK
   */
  200: IdentityUserDtoReadable;
};

export type PostApiAccountRegisterResponse =
  PostApiAccountRegisterResponses[keyof PostApiAccountRegisterResponses];

export type PostApiAccountSendPasswordResetCodeData = {
  body?: SendPasswordResetCodeDto;
  path?: never;
  query?: never;
  url: "/api/account/send-password-reset-code";
};

export type PostApiAccountSendPasswordResetCodeErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAccountSendPasswordResetCodeError =
  PostApiAccountSendPasswordResetCodeErrors[keyof PostApiAccountSendPasswordResetCodeErrors];

export type PostApiAccountSendPasswordResetCodeResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiAccountVerifyPasswordResetTokenData = {
  body?: VerifyPasswordResetTokenInput;
  path?: never;
  query?: never;
  url: "/api/account/verify-password-reset-token";
};

export type PostApiAccountVerifyPasswordResetTokenErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAccountVerifyPasswordResetTokenError =
  PostApiAccountVerifyPasswordResetTokenErrors[keyof PostApiAccountVerifyPasswordResetTokenErrors];

export type PostApiAccountVerifyPasswordResetTokenResponses = {
  /**
   * OK
   */
  200: boolean;
};

export type PostApiAccountVerifyPasswordResetTokenResponse =
  PostApiAccountVerifyPasswordResetTokenResponses[keyof PostApiAccountVerifyPasswordResetTokenResponses];

export type PostApiAccountResetPasswordData = {
  body?: ResetPasswordDto;
  path?: never;
  query?: never;
  url: "/api/account/reset-password";
};

export type PostApiAccountResetPasswordErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAccountResetPasswordError =
  PostApiAccountResetPasswordErrors[keyof PostApiAccountResetPasswordErrors];

export type PostApiAccountResetPasswordResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAttachmentData = {
  body?: never;
  path?: never;
  query?: {
    ReferenceId?: string;
    ReferenceType?: string;
    FileNameFilter?: string;
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/attachment";
};

export type GetApiAttachmentErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAttachmentError =
  GetApiAttachmentErrors[keyof GetApiAttachmentErrors];

export type GetApiAttachmentResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfFileUploadResultDto;
};

export type GetApiAttachmentResponse =
  GetApiAttachmentResponses[keyof GetApiAttachmentResponses];

export type GetApiAttachmentDownloadByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/attachment/download/{id}";
};

export type GetApiAttachmentDownloadByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAttachmentDownloadByIdError =
  GetApiAttachmentDownloadByIdErrors[keyof GetApiAttachmentDownloadByIdErrors];

export type GetApiAttachmentStreamByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/attachment/stream/{id}";
};

export type GetApiAttachmentStreamByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAttachmentStreamByIdError =
  GetApiAttachmentStreamByIdErrors[keyof GetApiAttachmentStreamByIdErrors];

export type PostApiAttachmentUploadData = {
  body?: FileUploadInputDto;
  path?: never;
  query?: never;
  url: "/api/attachment/upload";
};

export type PostApiAttachmentUploadErrors = {
  /**
   * Bad Request
   */
  400: unknown;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Unsupported Media Type
   */
  415: unknown;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAttachmentUploadError =
  PostApiAttachmentUploadErrors[keyof PostApiAttachmentUploadErrors];

export type PostApiAttachmentUploadResponses = {
  /**
   * OK
   */
  200: FileUploadResultDto;
};

export type PostApiAttachmentUploadResponse =
  PostApiAttachmentUploadResponses[keyof PostApiAttachmentUploadResponses];

export type GetApiAttachmentByReferenceData = {
  body?: never;
  path?: never;
  query?: {
    referenceId?: string;
    referenceType?: string;
  };
  url: "/api/attachment/by-reference";
};

export type GetApiAttachmentByReferenceErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAttachmentByReferenceError =
  GetApiAttachmentByReferenceErrors[keyof GetApiAttachmentByReferenceErrors];

export type GetApiAttachmentByReferenceResponses = {
  /**
   * OK
   */
  200: Array<FileUploadResultDto>;
};

export type GetApiAttachmentByReferenceResponse =
  GetApiAttachmentByReferenceResponses[keyof GetApiAttachmentByReferenceResponses];

export type DeleteApiAttachmentByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/attachment/{id}";
};

export type DeleteApiAttachmentByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAttachmentByIdError =
  DeleteApiAttachmentByIdErrors[keyof DeleteApiAttachmentByIdErrors];

export type DeleteApiAttachmentByIdResponses = {
  /**
   * OK
   */
  200: boolean;
};

export type DeleteApiAttachmentByIdResponse =
  DeleteApiAttachmentByIdResponses[keyof DeleteApiAttachmentByIdResponses];

export type PostApiAttachmentBulkDownloadData = {
  body?: BulkDownloadDto;
  path?: never;
  query?: never;
  url: "/api/attachment/bulk-download";
};

export type PostApiAttachmentBulkDownloadErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAttachmentBulkDownloadError =
  PostApiAttachmentBulkDownloadErrors[keyof PostApiAttachmentBulkDownloadErrors];

export type PostApiAttachmentUploadFormData = {
  body?: {
    /**
     * The file to upload
     */
    File: Blob | File;
    /**
     * Optional description of the file
     */
    Description?: string;
    /**
     * Optional reference ID that this file is associated with
     */
    ReferenceId?: string;
    /**
     * Optional reference type that this file is associated with
     */
    ReferenceType?: string;
  };
  path?: never;
  query?: never;
  url: "/api/attachment/upload-form";
};

export type PostApiAttachmentUploadFormErrors = {
  /**
   * Bad Request
   */
  400: unknown;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Unsupported Media Type
   */
  415: unknown;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAttachmentUploadFormError =
  PostApiAttachmentUploadFormErrors[keyof PostApiAttachmentUploadFormErrors];

export type PostApiAttachmentUploadFormResponses = {
  /**
   * OK
   */
  200: FileUploadResultDto;
};

export type PostApiAttachmentUploadFormResponse =
  PostApiAttachmentUploadFormResponses[keyof PostApiAttachmentUploadFormResponses];

export type GetApiExchangeRatesData = {
  body?: never;
  path?: never;
  query?: {
    currency?: string;
    startDate?: string;
    endDate?: string;
  };
  url: "/api/exchange-rates";
};

export type GetApiExchangeRatesResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiMasterCompanyData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/master/company";
};

export type GetApiMasterCompanyErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterCompanyError =
  GetApiMasterCompanyErrors[keyof GetApiMasterCompanyErrors];

export type GetApiMasterCompanyResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfCompanyDto;
};

export type GetApiMasterCompanyResponse =
  GetApiMasterCompanyResponses[keyof GetApiMasterCompanyResponses];

export type PostApiMasterCompanyData = {
  body?: CreateUpdateCompanyDto;
  path?: never;
  query?: never;
  url: "/api/master/company";
};

export type PostApiMasterCompanyErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiMasterCompanyError =
  PostApiMasterCompanyErrors[keyof PostApiMasterCompanyErrors];

export type PostApiMasterCompanyResponses = {
  /**
   * OK
   */
  200: CompanyDto;
};

export type PostApiMasterCompanyResponse =
  PostApiMasterCompanyResponses[keyof PostApiMasterCompanyResponses];

export type DeleteApiMasterCompanyByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/company/{id}";
};

export type DeleteApiMasterCompanyByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiMasterCompanyByIdError =
  DeleteApiMasterCompanyByIdErrors[keyof DeleteApiMasterCompanyByIdErrors];

export type DeleteApiMasterCompanyByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiMasterCompanyByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/company/{id}";
};

export type GetApiMasterCompanyByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterCompanyByIdError =
  GetApiMasterCompanyByIdErrors[keyof GetApiMasterCompanyByIdErrors];

export type GetApiMasterCompanyByIdResponses = {
  /**
   * OK
   */
  200: CompanyDto;
};

export type GetApiMasterCompanyByIdResponse =
  GetApiMasterCompanyByIdResponses[keyof GetApiMasterCompanyByIdResponses];

export type PutApiMasterCompanyByIdData = {
  body?: CreateUpdateCompanyDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/company/{id}";
};

export type PutApiMasterCompanyByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiMasterCompanyByIdError =
  PutApiMasterCompanyByIdErrors[keyof PutApiMasterCompanyByIdErrors];

export type PutApiMasterCompanyByIdResponses = {
  /**
   * OK
   */
  200: CompanyDto;
};

export type PutApiMasterCompanyByIdResponse =
  PutApiMasterCompanyByIdResponses[keyof PutApiMasterCompanyByIdResponses];

export type GetApiMasterDiningOptionsData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/master/dining-options";
};

export type GetApiMasterDiningOptionsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterDiningOptionsError =
  GetApiMasterDiningOptionsErrors[keyof GetApiMasterDiningOptionsErrors];

export type GetApiMasterDiningOptionsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfDiningOptionsDto;
};

export type GetApiMasterDiningOptionsResponse =
  GetApiMasterDiningOptionsResponses[keyof GetApiMasterDiningOptionsResponses];

export type PostApiMasterDiningOptionsData = {
  body?: CreateUpdateDiningOptionsDto;
  path?: never;
  query?: never;
  url: "/api/master/dining-options";
};

export type PostApiMasterDiningOptionsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiMasterDiningOptionsError =
  PostApiMasterDiningOptionsErrors[keyof PostApiMasterDiningOptionsErrors];

export type PostApiMasterDiningOptionsResponses = {
  /**
   * OK
   */
  200: DiningOptionsDto;
};

export type PostApiMasterDiningOptionsResponse =
  PostApiMasterDiningOptionsResponses[keyof PostApiMasterDiningOptionsResponses];

export type DeleteApiMasterDiningOptionsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/dining-options/{id}";
};

export type DeleteApiMasterDiningOptionsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiMasterDiningOptionsByIdError =
  DeleteApiMasterDiningOptionsByIdErrors[keyof DeleteApiMasterDiningOptionsByIdErrors];

export type DeleteApiMasterDiningOptionsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiMasterDiningOptionsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/dining-options/{id}";
};

export type GetApiMasterDiningOptionsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterDiningOptionsByIdError =
  GetApiMasterDiningOptionsByIdErrors[keyof GetApiMasterDiningOptionsByIdErrors];

export type GetApiMasterDiningOptionsByIdResponses = {
  /**
   * OK
   */
  200: DiningOptionsDto;
};

export type GetApiMasterDiningOptionsByIdResponse =
  GetApiMasterDiningOptionsByIdResponses[keyof GetApiMasterDiningOptionsByIdResponses];

export type PutApiMasterDiningOptionsByIdData = {
  body?: CreateUpdateDiningOptionsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/dining-options/{id}";
};

export type PutApiMasterDiningOptionsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiMasterDiningOptionsByIdError =
  PutApiMasterDiningOptionsByIdErrors[keyof PutApiMasterDiningOptionsByIdErrors];

export type PutApiMasterDiningOptionsByIdResponses = {
  /**
   * OK
   */
  200: DiningOptionsDto;
};

export type PutApiMasterDiningOptionsByIdResponse =
  PutApiMasterDiningOptionsByIdResponses[keyof PutApiMasterDiningOptionsByIdResponses];

export type PostApiDocumentGenerateRcData = {
  body?: DocumentGenerationDto;
  path?: never;
  query?: never;
  url: "/api/document/generate-rc";
};

export type PostApiDocumentGenerateRcErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiDocumentGenerateRcError =
  PostApiDocumentGenerateRcErrors[keyof PostApiDocumentGenerateRcErrors];

export type GetApiDocumentTemplatesAllData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/document/templates/all";
};

export type GetApiDocumentTemplatesAllErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiDocumentTemplatesAllError =
  GetApiDocumentTemplatesAllErrors[keyof GetApiDocumentTemplatesAllErrors];

export type GetApiDocumentTemplatesAllResponses = {
  /**
   * OK
   */
  200: Array<DocumentTemplateDto>;
};

export type GetApiDocumentTemplatesAllResponse =
  GetApiDocumentTemplatesAllResponses[keyof GetApiDocumentTemplatesAllResponses];

export type GetApiDocumentTemplatesByTypeByDocumentTypeData = {
  body?: never;
  path: {
    documentType: DocumentType;
  };
  query?: never;
  url: "/api/document/templates/by-type/{documentType}";
};

export type GetApiDocumentTemplatesByTypeByDocumentTypeErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiDocumentTemplatesByTypeByDocumentTypeError =
  GetApiDocumentTemplatesByTypeByDocumentTypeErrors[keyof GetApiDocumentTemplatesByTypeByDocumentTypeErrors];

export type GetApiDocumentTemplatesByTypeByDocumentTypeResponses = {
  /**
   * OK
   */
  200: Array<DocumentTemplateDto>;
};

export type GetApiDocumentTemplatesByTypeByDocumentTypeResponse =
  GetApiDocumentTemplatesByTypeByDocumentTypeResponses[keyof GetApiDocumentTemplatesByTypeByDocumentTypeResponses];

export type DeleteApiDocumentTemplatesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/document/templates/{id}";
};

export type DeleteApiDocumentTemplatesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiDocumentTemplatesByIdError =
  DeleteApiDocumentTemplatesByIdErrors[keyof DeleteApiDocumentTemplatesByIdErrors];

export type GetApiDocumentTemplatesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/document/templates/{id}";
};

export type GetApiDocumentTemplatesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiDocumentTemplatesByIdError =
  GetApiDocumentTemplatesByIdErrors[keyof GetApiDocumentTemplatesByIdErrors];

export type GetApiDocumentTemplatesByIdResponses = {
  /**
   * OK
   */
  200: DocumentTemplateDto;
};

export type GetApiDocumentTemplatesByIdResponse =
  GetApiDocumentTemplatesByIdResponses[keyof GetApiDocumentTemplatesByIdResponses];

export type PutApiDocumentTemplatesByIdData = {
  body?: UpdateDocumentTemplateDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/document/templates/{id}";
};

export type PutApiDocumentTemplatesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiDocumentTemplatesByIdError =
  PutApiDocumentTemplatesByIdErrors[keyof PutApiDocumentTemplatesByIdErrors];

export type PutApiDocumentTemplatesByIdResponses = {
  /**
   * OK
   */
  200: DocumentTemplateDto;
};

export type PutApiDocumentTemplatesByIdResponse =
  PutApiDocumentTemplatesByIdResponses[keyof PutApiDocumentTemplatesByIdResponses];

export type PostApiDocumentTemplatesData = {
  body?: CreateDocumentTemplateDto;
  path?: never;
  query?: never;
  url: "/api/document/templates";
};

export type PostApiDocumentTemplatesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiDocumentTemplatesError =
  PostApiDocumentTemplatesErrors[keyof PostApiDocumentTemplatesErrors];

export type PostApiDocumentTemplatesResponses = {
  /**
   * OK
   */
  200: DocumentTemplateDto;
};

export type PostApiDocumentTemplatesResponse =
  PostApiDocumentTemplatesResponses[keyof PostApiDocumentTemplatesResponses];

export type PostApiDocumentTemplatesUploadData = {
  body?: {
    Name: string;
    DocumentType: DocumentType;
    Description?: string;
    IsDefault?: boolean;
    File: Blob | File;
  };
  path?: never;
  query?: never;
  url: "/api/document/templates/upload";
};

export type PostApiDocumentTemplatesUploadErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiDocumentTemplatesUploadError =
  PostApiDocumentTemplatesUploadErrors[keyof PostApiDocumentTemplatesUploadErrors];

export type PostApiDocumentTemplatesUploadResponses = {
  /**
   * OK
   */
  200: DocumentTemplateDto;
};

export type PostApiDocumentTemplatesUploadResponse =
  PostApiDocumentTemplatesUploadResponses[keyof PostApiDocumentTemplatesUploadResponses];

export type PostApiDocumentConvertDocxToPdfData = {
  body?: DocxToPdfConversionDto;
  path?: never;
  query?: never;
  url: "/api/document/convert-docx-to-pdf";
};

export type PostApiDocumentConvertDocxToPdfErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiDocumentConvertDocxToPdfError =
  PostApiDocumentConvertDocxToPdfErrors[keyof PostApiDocumentConvertDocxToPdfErrors];

export type GetApiAppDocumentTemplatesData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/app/document/templates";
};

export type GetApiAppDocumentTemplatesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppDocumentTemplatesError =
  GetApiAppDocumentTemplatesErrors[keyof GetApiAppDocumentTemplatesErrors];

export type GetApiAppDocumentTemplatesResponses = {
  /**
   * OK
   */
  200: Array<DocumentTemplateDto>;
};

export type GetApiAppDocumentTemplatesResponse =
  GetApiAppDocumentTemplatesResponses[keyof GetApiAppDocumentTemplatesResponses];

export type GetApiAppDocumentTemplatesByTypeData = {
  body?: never;
  path?: never;
  query?: {
    documentType?: DocumentType;
  };
  url: "/api/app/document/templates-by-type";
};

export type GetApiAppDocumentTemplatesByTypeErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppDocumentTemplatesByTypeError =
  GetApiAppDocumentTemplatesByTypeErrors[keyof GetApiAppDocumentTemplatesByTypeErrors];

export type GetApiAppDocumentTemplatesByTypeResponses = {
  /**
   * OK
   */
  200: Array<DocumentTemplateDto>;
};

export type GetApiAppDocumentTemplatesByTypeResponse =
  GetApiAppDocumentTemplatesByTypeResponses[keyof GetApiAppDocumentTemplatesByTypeResponses];

export type GetApiAppDocumentByIdTemplateByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/document/{id}/template-by-id";
};

export type GetApiAppDocumentByIdTemplateByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppDocumentByIdTemplateByIdError =
  GetApiAppDocumentByIdTemplateByIdErrors[keyof GetApiAppDocumentByIdTemplateByIdErrors];

export type GetApiAppDocumentByIdTemplateByIdResponses = {
  /**
   * OK
   */
  200: DocumentTemplateDto;
};

export type GetApiAppDocumentByIdTemplateByIdResponse =
  GetApiAppDocumentByIdTemplateByIdResponses[keyof GetApiAppDocumentByIdTemplateByIdResponses];

export type PostApiAppDocumentTemplateData = {
  body?: CreateDocumentTemplateDto;
  path?: never;
  query?: never;
  url: "/api/app/document/template";
};

export type PostApiAppDocumentTemplateErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppDocumentTemplateError =
  PostApiAppDocumentTemplateErrors[keyof PostApiAppDocumentTemplateErrors];

export type PostApiAppDocumentTemplateResponses = {
  /**
   * OK
   */
  200: DocumentTemplateDto;
};

export type PostApiAppDocumentTemplateResponse =
  PostApiAppDocumentTemplateResponses[keyof PostApiAppDocumentTemplateResponses];

export type PostApiAppDocumentUploadTemplateData = {
  body?: UploadDocumentTemplateDto;
  path?: never;
  query?: never;
  url: "/api/app/document/upload-template";
};

export type PostApiAppDocumentUploadTemplateErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppDocumentUploadTemplateError =
  PostApiAppDocumentUploadTemplateErrors[keyof PostApiAppDocumentUploadTemplateErrors];

export type PostApiAppDocumentUploadTemplateResponses = {
  /**
   * OK
   */
  200: DocumentTemplateDto;
};

export type PostApiAppDocumentUploadTemplateResponse =
  PostApiAppDocumentUploadTemplateResponses[keyof PostApiAppDocumentUploadTemplateResponses];

export type DeleteApiAppDocumentByIdTemplateData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/document/{id}/template";
};

export type DeleteApiAppDocumentByIdTemplateErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppDocumentByIdTemplateError =
  DeleteApiAppDocumentByIdTemplateErrors[keyof DeleteApiAppDocumentByIdTemplateErrors];

export type DeleteApiAppDocumentByIdTemplateResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PutApiAppDocumentByIdTemplateData = {
  body?: UpdateDocumentTemplateDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/document/{id}/template";
};

export type PutApiAppDocumentByIdTemplateErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppDocumentByIdTemplateError =
  PutApiAppDocumentByIdTemplateErrors[keyof PutApiAppDocumentByIdTemplateErrors];

export type PutApiAppDocumentByIdTemplateResponses = {
  /**
   * OK
   */
  200: DocumentTemplateDto;
};

export type PutApiAppDocumentByIdTemplateResponse =
  PutApiAppDocumentByIdTemplateResponses[keyof PutApiAppDocumentByIdTemplateResponses];

export type PostApiAppDocumentConvertDocxToPdfData = {
  body?: DocxToPdfConversionDto;
  path?: never;
  query?: never;
  url: "/api/app/document/convert-docx-to-pdf";
};

export type PostApiAppDocumentConvertDocxToPdfErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppDocumentConvertDocxToPdfError =
  PostApiAppDocumentConvertDocxToPdfErrors[keyof PostApiAppDocumentConvertDocxToPdfErrors];

export type PostApiAppDocumentConvertDocxToPdfResponses = {
  /**
   * OK
   */
  200: FileDto;
};

export type PostApiAppDocumentConvertDocxToPdfResponse =
  PostApiAppDocumentConvertDocxToPdfResponses[keyof PostApiAppDocumentConvertDocxToPdfResponses];

export type PostApiDocxToPdfConvertData = {
  body?: {
    file?: Blob | File;
  };
  path?: never;
  query?: never;
  url: "/api/docx-to-pdf/convert";
};

export type PostApiDocxToPdfConvertResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiDocxToPdfConvertBase64Data = {
  body?: Base64ConversionInput;
  path?: never;
  query?: never;
  url: "/api/docx-to-pdf/convert-base64";
};

export type PostApiDocxToPdfConvertBase64Responses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiAccountDynamicClaimsRefreshData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/account/dynamic-claims/refresh";
};

export type PostApiAccountDynamicClaimsRefreshErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAccountDynamicClaimsRefreshError =
  PostApiAccountDynamicClaimsRefreshErrors[keyof PostApiAccountDynamicClaimsRefreshErrors];

export type PostApiAccountDynamicClaimsRefreshResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiChangeLogData = {
  body?: never;
  path?: never;
  query?: {
    startTime?: string;
    endTime?: string;
    maxResultCount?: number;
    skipCount?: number;
  };
  url: "/api/change-log";
};

export type GetApiChangeLogResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiChangeLogEntityData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/change-log/entity";
};

export type PostApiChangeLogEntityResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiChangeLogEntityLatestData = {
  body?: never;
  path?: never;
  query?: {
    entityId?: string;
    entityTypeFullName?: string;
  };
  url: "/api/change-log/entity/latest";
};

export type GetApiChangeLogEntityLatestResponses = {
  /**
   * OK
   */
  200: EntityChangeLogDto;
};

export type GetApiChangeLogEntityLatestResponse =
  GetApiChangeLogEntityLatestResponses[keyof GetApiChangeLogEntityLatestResponses];

export type GetApiChangeLogEntityTypeData = {
  body?: never;
  path?: never;
  query?: {
    entityTypeFullName?: string;
    maxResultCount?: number;
    skipCount?: number;
  };
  url: "/api/change-log/entity-type";
};

export type GetApiChangeLogEntityTypeResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiChangeLogTimeRangeData = {
  body?: never;
  path?: never;
  query?: {
    startTime?: string;
    endTime?: string;
    maxResultCount?: number;
    skipCount?: number;
  };
  url: "/api/change-log/time-range";
};

export type GetApiChangeLogTimeRangeResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiChangeLogPropertyHistoryData = {
  body?: never;
  path?: never;
  query?: {
    entityId?: string;
    entityTypeFullName?: string;
    propertyName?: string;
    maxResultCount?: number;
    skipCount?: number;
  };
  url: "/api/change-log/property-history";
};

export type GetApiChangeLogPropertyHistoryResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiChangeLogEntityChangesData = {
  body?: never;
  path?: never;
  query?: {
    entityId?: string;
    entityTypeFullName?: string;
    startTime?: string;
    endTime?: string;
    maxResultCount?: number;
    skipCount?: number;
    sorting?: string;
  };
  url: "/api/change-log/entity-changes";
};

export type GetApiChangeLogEntityChangesResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiAppFoodAndBeverageListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/food-and-beverage/list";
};

export type PostApiAppFoodAndBeverageListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppFoodAndBeverageListError =
  PostApiAppFoodAndBeverageListErrors[keyof PostApiAppFoodAndBeverageListErrors];

export type PostApiAppFoodAndBeverageListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfFoodAndBeverageDto;
};

export type PostApiAppFoodAndBeverageListResponse =
  PostApiAppFoodAndBeverageListResponses[keyof PostApiAppFoodAndBeverageListResponses];

export type GetApiAppFoodAndBeverageData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/food-and-beverage";
};

export type GetApiAppFoodAndBeverageErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppFoodAndBeverageError =
  GetApiAppFoodAndBeverageErrors[keyof GetApiAppFoodAndBeverageErrors];

export type GetApiAppFoodAndBeverageResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfFoodAndBeverageDto;
};

export type GetApiAppFoodAndBeverageResponse =
  GetApiAppFoodAndBeverageResponses[keyof GetApiAppFoodAndBeverageResponses];

export type PostApiAppFoodAndBeverageData = {
  body?: CreateUpdateFoodAndBeverageDto;
  path?: never;
  query?: never;
  url: "/api/app/food-and-beverage";
};

export type PostApiAppFoodAndBeverageErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppFoodAndBeverageError =
  PostApiAppFoodAndBeverageErrors[keyof PostApiAppFoodAndBeverageErrors];

export type PostApiAppFoodAndBeverageResponses = {
  /**
   * OK
   */
  200: FoodAndBeverageDto;
};

export type PostApiAppFoodAndBeverageResponse =
  PostApiAppFoodAndBeverageResponses[keyof PostApiAppFoodAndBeverageResponses];

export type DeleteApiAppFoodAndBeverageByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/food-and-beverage/{id}";
};

export type DeleteApiAppFoodAndBeverageByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppFoodAndBeverageByIdError =
  DeleteApiAppFoodAndBeverageByIdErrors[keyof DeleteApiAppFoodAndBeverageByIdErrors];

export type DeleteApiAppFoodAndBeverageByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppFoodAndBeverageByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/food-and-beverage/{id}";
};

export type GetApiAppFoodAndBeverageByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppFoodAndBeverageByIdError =
  GetApiAppFoodAndBeverageByIdErrors[keyof GetApiAppFoodAndBeverageByIdErrors];

export type GetApiAppFoodAndBeverageByIdResponses = {
  /**
   * OK
   */
  200: FoodAndBeverageDto;
};

export type GetApiAppFoodAndBeverageByIdResponse =
  GetApiAppFoodAndBeverageByIdResponses[keyof GetApiAppFoodAndBeverageByIdResponses];

export type PutApiAppFoodAndBeverageByIdData = {
  body?: CreateUpdateFoodAndBeverageDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/food-and-beverage/{id}";
};

export type PutApiAppFoodAndBeverageByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppFoodAndBeverageByIdError =
  PutApiAppFoodAndBeverageByIdErrors[keyof PutApiAppFoodAndBeverageByIdErrors];

export type PutApiAppFoodAndBeverageByIdResponses = {
  /**
   * OK
   */
  200: FoodAndBeverageDto;
};

export type PutApiAppFoodAndBeverageByIdResponse =
  PutApiAppFoodAndBeverageByIdResponses[keyof PutApiAppFoodAndBeverageByIdResponses];

export type PostApiAppGuestListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/guest/list";
};

export type PostApiAppGuestListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppGuestListError =
  PostApiAppGuestListErrors[keyof PostApiAppGuestListErrors];

export type PostApiAppGuestListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfGuestDto;
};

export type PostApiAppGuestListResponse =
  PostApiAppGuestListResponses[keyof PostApiAppGuestListResponses];

export type GetApiAppGuestData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/guest";
};

export type GetApiAppGuestErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppGuestError =
  GetApiAppGuestErrors[keyof GetApiAppGuestErrors];

export type GetApiAppGuestResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfGuestDto;
};

export type GetApiAppGuestResponse =
  GetApiAppGuestResponses[keyof GetApiAppGuestResponses];

export type PostApiAppGuestData = {
  body?: CreateUpdateGuestDto;
  path?: never;
  query?: never;
  url: "/api/app/guest";
};

export type PostApiAppGuestErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppGuestError =
  PostApiAppGuestErrors[keyof PostApiAppGuestErrors];

export type PostApiAppGuestResponses = {
  /**
   * OK
   */
  200: GuestDto;
};

export type PostApiAppGuestResponse =
  PostApiAppGuestResponses[keyof PostApiAppGuestResponses];

export type DeleteApiAppGuestByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/guest/{id}";
};

export type DeleteApiAppGuestByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppGuestByIdError =
  DeleteApiAppGuestByIdErrors[keyof DeleteApiAppGuestByIdErrors];

export type DeleteApiAppGuestByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppGuestByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/guest/{id}";
};

export type GetApiAppGuestByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppGuestByIdError =
  GetApiAppGuestByIdErrors[keyof GetApiAppGuestByIdErrors];

export type GetApiAppGuestByIdResponses = {
  /**
   * OK
   */
  200: GuestDto;
};

export type GetApiAppGuestByIdResponse =
  GetApiAppGuestByIdResponses[keyof GetApiAppGuestByIdResponses];

export type PutApiAppGuestByIdData = {
  body?: CreateUpdateGuestDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/guest/{id}";
};

export type PutApiAppGuestByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppGuestByIdError =
  PutApiAppGuestByIdErrors[keyof PutApiAppGuestByIdErrors];

export type PutApiAppGuestByIdResponses = {
  /**
   * OK
   */
  200: GuestDto;
};

export type PutApiAppGuestByIdResponse =
  PutApiAppGuestByIdResponses[keyof PutApiAppGuestByIdResponses];

export type PostApiAppGuestUploadAttachmentsByGuestIdData = {
  body?: Array<GuestAttachmentDto>;
  path: {
    guestId: string;
  };
  query?: never;
  url: "/api/app/guest/upload-attachments/{guestId}";
};

export type PostApiAppGuestUploadAttachmentsByGuestIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppGuestUploadAttachmentsByGuestIdError =
  PostApiAppGuestUploadAttachmentsByGuestIdErrors[keyof PostApiAppGuestUploadAttachmentsByGuestIdErrors];

export type PostApiAppGuestUploadAttachmentsByGuestIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiHealthKubernetesData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/health/kubernetes";
};

export type GetApiHealthKubernetesResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiInvoiceGenerateData = {
  body?: InvoiceGenerationDto;
  path?: never;
  query?: never;
  url: "/api/invoice/generate";
};

export type PostApiInvoiceGenerateErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiInvoiceGenerateError =
  PostApiInvoiceGenerateErrors[keyof PostApiInvoiceGenerateErrors];

export type PostApiInvoiceGenerateWismaData = {
  body?: InvoiceGenerationDto;
  path?: never;
  query?: never;
  url: "/api/invoice/generate-wisma";
};

export type PostApiInvoiceGenerateWismaErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiInvoiceGenerateWismaError =
  PostApiInvoiceGenerateWismaErrors[keyof PostApiInvoiceGenerateWismaErrors];

export type PostApiInvoiceGenerateWismaDownloadData = {
  body?: InvoiceGenerationDto;
  path?: never;
  query?: never;
  url: "/api/invoice/generate-wisma-download";
};

export type PostApiInvoiceGenerateWismaDownloadErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiInvoiceGenerateWismaDownloadError =
  PostApiInvoiceGenerateWismaDownloadErrors[keyof PostApiInvoiceGenerateWismaDownloadErrors];

export type GetApiInvoiceTemplateDataByPaymentIdData = {
  body?: never;
  path: {
    paymentId: string;
  };
  query?: never;
  url: "/api/invoice/template-data/{paymentId}";
};

export type GetApiInvoiceTemplateDataByPaymentIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiInvoiceTemplateDataByPaymentIdError =
  GetApiInvoiceTemplateDataByPaymentIdErrors[keyof GetApiInvoiceTemplateDataByPaymentIdErrors];

export type GetApiInvoiceTemplateDataByPaymentIdResponses = {
  /**
   * OK
   */
  200: InvoiceTemplateDataDto;
};

export type GetApiInvoiceTemplateDataByPaymentIdResponse =
  GetApiInvoiceTemplateDataByPaymentIdResponses[keyof GetApiInvoiceTemplateDataByPaymentIdResponses];

export type PostApiAppInvoiceDocumentGenerateInvoiceData = {
  body?: InvoiceGenerationDto;
  path?: never;
  query?: never;
  url: "/api/app/invoice-document/generate-invoice";
};

export type PostApiAppInvoiceDocumentGenerateInvoiceErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppInvoiceDocumentGenerateInvoiceError =
  PostApiAppInvoiceDocumentGenerateInvoiceErrors[keyof PostApiAppInvoiceDocumentGenerateInvoiceErrors];

export type PostApiAppInvoiceDocumentGenerateInvoiceResponses = {
  /**
   * OK
   */
  200: FileDto;
};

export type PostApiAppInvoiceDocumentGenerateInvoiceResponse =
  PostApiAppInvoiceDocumentGenerateInvoiceResponses[keyof PostApiAppInvoiceDocumentGenerateInvoiceResponses];

export type GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdData = {
  body?: never;
  path: {
    paymentId: string;
  };
  query?: never;
  url: "/api/app/invoice-document/invoice-template-data/{paymentId}";
};

export type GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdError =
  GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdErrors[keyof GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdErrors];

export type GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdResponses = {
  /**
   * OK
   */
  200: InvoiceTemplateDataDto;
};

export type GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdResponse =
  GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdResponses[keyof GetApiAppInvoiceDocumentInvoiceTemplateDataByPaymentIdResponses];

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceData = {
  body?: InvoiceGenerationDto;
  path?: never;
  query?: never;
  url: "/api/app/invoice-document/generate-wisma-invoice";
};

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceError =
  PostApiAppInvoiceDocumentGenerateWismaInvoiceErrors[keyof PostApiAppInvoiceDocumentGenerateWismaInvoiceErrors];

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceResponses = {
  /**
   * OK
   */
  200: FileDto;
};

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceResponse =
  PostApiAppInvoiceDocumentGenerateWismaInvoiceResponses[keyof PostApiAppInvoiceDocumentGenerateWismaInvoiceResponses];

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentData = {
  body?: InvoiceGenerationDto;
  path?: never;
  query?: never;
  url: "/api/app/invoice-document/generate-wisma-invoice-as-attachment";
};

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentError =
  PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentErrors[keyof PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentErrors];

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentResponses =
  {
    /**
     * OK
     */
    200: FileUploadResultDto;
  };

export type PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentResponse =
  PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentResponses[keyof PostApiAppInvoiceDocumentGenerateWismaInvoiceAsAttachmentResponses];

export type PostApiAccountLoginData = {
  body?: UserLoginInfo;
  path?: never;
  query?: never;
  url: "/api/account/login";
};

export type PostApiAccountLoginErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAccountLoginError =
  PostApiAccountLoginErrors[keyof PostApiAccountLoginErrors];

export type PostApiAccountLoginResponses = {
  /**
   * OK
   */
  200: AbpLoginResult;
};

export type PostApiAccountLoginResponse =
  PostApiAccountLoginResponses[keyof PostApiAccountLoginResponses];

export type GetApiAccountLogoutData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/account/logout";
};

export type GetApiAccountLogoutErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAccountLogoutError =
  GetApiAccountLogoutErrors[keyof GetApiAccountLogoutErrors];

export type GetApiAccountLogoutResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiAccountCheckPasswordData = {
  body?: UserLoginInfo;
  path?: never;
  query?: never;
  url: "/api/account/check-password";
};

export type PostApiAccountCheckPasswordErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAccountCheckPasswordError =
  PostApiAccountCheckPasswordErrors[keyof PostApiAccountCheckPasswordErrors];

export type PostApiAccountCheckPasswordResponses = {
  /**
   * OK
   */
  200: AbpLoginResult;
};

export type PostApiAccountCheckPasswordResponse =
  PostApiAccountCheckPasswordResponses[keyof PostApiAccountCheckPasswordResponses];

export type PostApiAppMasterStatusListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/master-status/list";
};

export type PostApiAppMasterStatusListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppMasterStatusListError =
  PostApiAppMasterStatusListErrors[keyof PostApiAppMasterStatusListErrors];

export type PostApiAppMasterStatusListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfMasterStatusDto;
};

export type PostApiAppMasterStatusListResponse =
  PostApiAppMasterStatusListResponses[keyof PostApiAppMasterStatusListResponses];

export type GetApiMasterStatusData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/master/status";
};

export type GetApiMasterStatusErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterStatusError =
  GetApiMasterStatusErrors[keyof GetApiMasterStatusErrors];

export type GetApiMasterStatusResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfMasterStatusDto;
};

export type GetApiMasterStatusResponse =
  GetApiMasterStatusResponses[keyof GetApiMasterStatusResponses];

export type PostApiMasterStatusData = {
  body?: CreateUpdateMasterStatusDto;
  path?: never;
  query?: never;
  url: "/api/master/status";
};

export type PostApiMasterStatusErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiMasterStatusError =
  PostApiMasterStatusErrors[keyof PostApiMasterStatusErrors];

export type PostApiMasterStatusResponses = {
  /**
   * OK
   */
  200: MasterStatusDto;
};

export type PostApiMasterStatusResponse =
  PostApiMasterStatusResponses[keyof PostApiMasterStatusResponses];

export type DeleteApiMasterStatusByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/status/{id}";
};

export type DeleteApiMasterStatusByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiMasterStatusByIdError =
  DeleteApiMasterStatusByIdErrors[keyof DeleteApiMasterStatusByIdErrors];

export type DeleteApiMasterStatusByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiMasterStatusByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/status/{id}";
};

export type GetApiMasterStatusByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterStatusByIdError =
  GetApiMasterStatusByIdErrors[keyof GetApiMasterStatusByIdErrors];

export type GetApiMasterStatusByIdResponses = {
  /**
   * OK
   */
  200: MasterStatusDto;
};

export type GetApiMasterStatusByIdResponse =
  GetApiMasterStatusByIdResponses[keyof GetApiMasterStatusByIdResponses];

export type PutApiMasterStatusByIdData = {
  body?: CreateUpdateMasterStatusDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/status/{id}";
};

export type PutApiMasterStatusByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiMasterStatusByIdError =
  PutApiMasterStatusByIdErrors[keyof PutApiMasterStatusByIdErrors];

export type PutApiMasterStatusByIdResponses = {
  /**
   * OK
   */
  200: MasterStatusDto;
};

export type PutApiMasterStatusByIdResponse =
  PutApiMasterStatusByIdResponses[keyof PutApiMasterStatusByIdResponses];

export type GetApiMasterStatusServiceByDocTypeByDocTypeData = {
  body?: never;
  path: {
    docType: string;
  };
  query?: never;
  url: "/api/master/status-service/by-doc-type/{docType}";
};

export type GetApiMasterStatusServiceByDocTypeByDocTypeErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterStatusServiceByDocTypeByDocTypeError =
  GetApiMasterStatusServiceByDocTypeByDocTypeErrors[keyof GetApiMasterStatusServiceByDocTypeByDocTypeErrors];

export type GetApiMasterStatusServiceByDocTypeByDocTypeResponses = {
  /**
   * OK
   */
  200: Array<MasterStatusDto>;
};

export type GetApiMasterStatusServiceByDocTypeByDocTypeResponse =
  GetApiMasterStatusServiceByDocTypeByDocTypeResponses[keyof GetApiMasterStatusServiceByDocTypeByDocTypeResponses];

export type GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeData = {
  body?: never;
  path: {
    id: string;
    docType: string;
  };
  query?: never;
  url: "/api/master/status-service/by-id-and-doc-type/{id}/{docType}";
};

export type GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeError =
  GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeErrors[keyof GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeErrors];

export type GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeResponses = {
  /**
   * OK
   */
  200: MasterStatusDto;
};

export type GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeResponse =
  GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeResponses[keyof GetApiMasterStatusServiceByIdAndDocTypeByIdByDocTypeResponses];

export type GetApiMasterStatusServicePagedByDocTypeByDocTypeData = {
  body?: never;
  path: {
    docType: string;
  };
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/master/status-service/paged-by-doc-type/{docType}";
};

export type GetApiMasterStatusServicePagedByDocTypeByDocTypeErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterStatusServicePagedByDocTypeByDocTypeError =
  GetApiMasterStatusServicePagedByDocTypeByDocTypeErrors[keyof GetApiMasterStatusServicePagedByDocTypeByDocTypeErrors];

export type GetApiMasterStatusServicePagedByDocTypeByDocTypeResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfMasterStatusDto;
};

export type GetApiMasterStatusServicePagedByDocTypeByDocTypeResponse =
  GetApiMasterStatusServicePagedByDocTypeByDocTypeResponses[keyof GetApiMasterStatusServicePagedByDocTypeByDocTypeResponses];

export type PostApiAppPaymentDetailsListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/payment-details/list";
};

export type PostApiAppPaymentDetailsListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppPaymentDetailsListError =
  PostApiAppPaymentDetailsListErrors[keyof PostApiAppPaymentDetailsListErrors];

export type PostApiAppPaymentDetailsListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfPaymentDetailsDto;
};

export type PostApiAppPaymentDetailsListResponse =
  PostApiAppPaymentDetailsListResponses[keyof PostApiAppPaymentDetailsListResponses];

export type GetApiAppPaymentDetailsData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/payment-details";
};

export type GetApiAppPaymentDetailsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppPaymentDetailsError =
  GetApiAppPaymentDetailsErrors[keyof GetApiAppPaymentDetailsErrors];

export type GetApiAppPaymentDetailsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfPaymentDetailsDto;
};

export type GetApiAppPaymentDetailsResponse =
  GetApiAppPaymentDetailsResponses[keyof GetApiAppPaymentDetailsResponses];

export type PostApiAppPaymentDetailsData = {
  body?: CreateUpdatePaymentDetailsDto;
  path?: never;
  query?: never;
  url: "/api/app/payment-details";
};

export type PostApiAppPaymentDetailsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppPaymentDetailsError =
  PostApiAppPaymentDetailsErrors[keyof PostApiAppPaymentDetailsErrors];

export type PostApiAppPaymentDetailsResponses = {
  /**
   * OK
   */
  200: PaymentDetailsDto;
};

export type PostApiAppPaymentDetailsResponse =
  PostApiAppPaymentDetailsResponses[keyof PostApiAppPaymentDetailsResponses];

export type DeleteApiAppPaymentDetailsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payment-details/{id}";
};

export type DeleteApiAppPaymentDetailsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppPaymentDetailsByIdError =
  DeleteApiAppPaymentDetailsByIdErrors[keyof DeleteApiAppPaymentDetailsByIdErrors];

export type DeleteApiAppPaymentDetailsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppPaymentDetailsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payment-details/{id}";
};

export type GetApiAppPaymentDetailsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppPaymentDetailsByIdError =
  GetApiAppPaymentDetailsByIdErrors[keyof GetApiAppPaymentDetailsByIdErrors];

export type GetApiAppPaymentDetailsByIdResponses = {
  /**
   * OK
   */
  200: PaymentDetailsDto;
};

export type GetApiAppPaymentDetailsByIdResponse =
  GetApiAppPaymentDetailsByIdResponses[keyof GetApiAppPaymentDetailsByIdResponses];

export type PutApiAppPaymentDetailsByIdData = {
  body?: CreateUpdatePaymentDetailsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payment-details/{id}";
};

export type PutApiAppPaymentDetailsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppPaymentDetailsByIdError =
  PutApiAppPaymentDetailsByIdErrors[keyof PutApiAppPaymentDetailsByIdErrors];

export type PutApiAppPaymentDetailsByIdResponses = {
  /**
   * OK
   */
  200: PaymentDetailsDto;
};

export type PutApiAppPaymentDetailsByIdResponse =
  PutApiAppPaymentDetailsByIdResponses[keyof PutApiAppPaymentDetailsByIdResponses];

export type GetApiAppPaymentGuestsData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/payment-guests";
};

export type GetApiAppPaymentGuestsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppPaymentGuestsError =
  GetApiAppPaymentGuestsErrors[keyof GetApiAppPaymentGuestsErrors];

export type GetApiAppPaymentGuestsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfPaymentGuestsDto;
};

export type GetApiAppPaymentGuestsResponse =
  GetApiAppPaymentGuestsResponses[keyof GetApiAppPaymentGuestsResponses];

export type PostApiAppPaymentGuestsData = {
  body?: CreateUpdatePaymentGuestsDto;
  path?: never;
  query?: never;
  url: "/api/app/payment-guests";
};

export type PostApiAppPaymentGuestsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppPaymentGuestsError =
  PostApiAppPaymentGuestsErrors[keyof PostApiAppPaymentGuestsErrors];

export type PostApiAppPaymentGuestsResponses = {
  /**
   * OK
   */
  200: PaymentGuestsDto;
};

export type PostApiAppPaymentGuestsResponse =
  PostApiAppPaymentGuestsResponses[keyof PostApiAppPaymentGuestsResponses];

export type DeleteApiAppPaymentGuestsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payment-guests/{id}";
};

export type DeleteApiAppPaymentGuestsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppPaymentGuestsByIdError =
  DeleteApiAppPaymentGuestsByIdErrors[keyof DeleteApiAppPaymentGuestsByIdErrors];

export type DeleteApiAppPaymentGuestsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppPaymentGuestsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payment-guests/{id}";
};

export type GetApiAppPaymentGuestsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppPaymentGuestsByIdError =
  GetApiAppPaymentGuestsByIdErrors[keyof GetApiAppPaymentGuestsByIdErrors];

export type GetApiAppPaymentGuestsByIdResponses = {
  /**
   * OK
   */
  200: PaymentGuestsDto;
};

export type GetApiAppPaymentGuestsByIdResponse =
  GetApiAppPaymentGuestsByIdResponses[keyof GetApiAppPaymentGuestsByIdResponses];

export type PutApiAppPaymentGuestsByIdData = {
  body?: CreateUpdatePaymentGuestsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payment-guests/{id}";
};

export type PutApiAppPaymentGuestsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppPaymentGuestsByIdError =
  PutApiAppPaymentGuestsByIdErrors[keyof PutApiAppPaymentGuestsByIdErrors];

export type PutApiAppPaymentGuestsByIdResponses = {
  /**
   * OK
   */
  200: PaymentGuestsDto;
};

export type PutApiAppPaymentGuestsByIdResponse =
  PutApiAppPaymentGuestsByIdResponses[keyof PutApiAppPaymentGuestsByIdResponses];

export type GetApiMasterPaymentMethodData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/master/payment-method";
};

export type GetApiMasterPaymentMethodErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterPaymentMethodError =
  GetApiMasterPaymentMethodErrors[keyof GetApiMasterPaymentMethodErrors];

export type GetApiMasterPaymentMethodResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfPaymentMethodDto;
};

export type GetApiMasterPaymentMethodResponse =
  GetApiMasterPaymentMethodResponses[keyof GetApiMasterPaymentMethodResponses];

export type PostApiMasterPaymentMethodData = {
  body?: CreateUpdatePaymentMethodDto;
  path?: never;
  query?: never;
  url: "/api/master/payment-method";
};

export type PostApiMasterPaymentMethodErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiMasterPaymentMethodError =
  PostApiMasterPaymentMethodErrors[keyof PostApiMasterPaymentMethodErrors];

export type PostApiMasterPaymentMethodResponses = {
  /**
   * OK
   */
  200: PaymentMethodDto;
};

export type PostApiMasterPaymentMethodResponse =
  PostApiMasterPaymentMethodResponses[keyof PostApiMasterPaymentMethodResponses];

export type DeleteApiMasterPaymentMethodByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/payment-method/{id}";
};

export type DeleteApiMasterPaymentMethodByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiMasterPaymentMethodByIdError =
  DeleteApiMasterPaymentMethodByIdErrors[keyof DeleteApiMasterPaymentMethodByIdErrors];

export type DeleteApiMasterPaymentMethodByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiMasterPaymentMethodByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/payment-method/{id}";
};

export type GetApiMasterPaymentMethodByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMasterPaymentMethodByIdError =
  GetApiMasterPaymentMethodByIdErrors[keyof GetApiMasterPaymentMethodByIdErrors];

export type GetApiMasterPaymentMethodByIdResponses = {
  /**
   * OK
   */
  200: PaymentMethodDto;
};

export type GetApiMasterPaymentMethodByIdResponse =
  GetApiMasterPaymentMethodByIdResponses[keyof GetApiMasterPaymentMethodByIdResponses];

export type PutApiMasterPaymentMethodByIdData = {
  body?: CreateUpdatePaymentMethodDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/master/payment-method/{id}";
};

export type PutApiMasterPaymentMethodByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiMasterPaymentMethodByIdError =
  PutApiMasterPaymentMethodByIdErrors[keyof PutApiMasterPaymentMethodByIdErrors];

export type PutApiMasterPaymentMethodByIdResponses = {
  /**
   * OK
   */
  200: PaymentMethodDto;
};

export type PutApiMasterPaymentMethodByIdResponse =
  PutApiMasterPaymentMethodByIdResponses[keyof PutApiMasterPaymentMethodByIdResponses];

export type PostApiAppPaymentsListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/payments/list";
};

export type PostApiAppPaymentsListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppPaymentsListError =
  PostApiAppPaymentsListErrors[keyof PostApiAppPaymentsListErrors];

export type PostApiAppPaymentsListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfPaymentsDto;
};

export type PostApiAppPaymentsListResponse =
  PostApiAppPaymentsListResponses[keyof PostApiAppPaymentsListResponses];

export type GetApiAppPaymentsData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/payments";
};

export type GetApiAppPaymentsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppPaymentsError =
  GetApiAppPaymentsErrors[keyof GetApiAppPaymentsErrors];

export type GetApiAppPaymentsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfPaymentsDto;
};

export type GetApiAppPaymentsResponse =
  GetApiAppPaymentsResponses[keyof GetApiAppPaymentsResponses];

export type PostApiAppPaymentsData = {
  body?: CreateUpdatePaymentsDto;
  path?: never;
  query?: never;
  url: "/api/app/payments";
};

export type PostApiAppPaymentsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppPaymentsError =
  PostApiAppPaymentsErrors[keyof PostApiAppPaymentsErrors];

export type PostApiAppPaymentsResponses = {
  /**
   * OK
   */
  200: PaymentsDto;
};

export type PostApiAppPaymentsResponse =
  PostApiAppPaymentsResponses[keyof PostApiAppPaymentsResponses];

export type DeleteApiAppPaymentsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payments/{id}";
};

export type DeleteApiAppPaymentsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppPaymentsByIdError =
  DeleteApiAppPaymentsByIdErrors[keyof DeleteApiAppPaymentsByIdErrors];

export type DeleteApiAppPaymentsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppPaymentsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payments/{id}";
};

export type GetApiAppPaymentsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppPaymentsByIdError =
  GetApiAppPaymentsByIdErrors[keyof GetApiAppPaymentsByIdErrors];

export type GetApiAppPaymentsByIdResponses = {
  /**
   * OK
   */
  200: PaymentsDto;
};

export type GetApiAppPaymentsByIdResponse =
  GetApiAppPaymentsByIdResponses[keyof GetApiAppPaymentsByIdResponses];

export type PutApiAppPaymentsByIdData = {
  body?: CreateUpdatePaymentsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/payments/{id}";
};

export type PutApiAppPaymentsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppPaymentsByIdError =
  PutApiAppPaymentsByIdErrors[keyof PutApiAppPaymentsByIdErrors];

export type PutApiAppPaymentsByIdResponses = {
  /**
   * OK
   */
  200: PaymentsDto;
};

export type PutApiAppPaymentsByIdResponse =
  PutApiAppPaymentsByIdResponses[keyof PutApiAppPaymentsByIdResponses];

export type PostApiAppPaymentsByPaymentIdCleanupAttachmentsData = {
  body?: never;
  path: {
    paymentId: string;
  };
  query?: never;
  url: "/api/app/payments/{paymentId}/cleanup-attachments";
};

export type PostApiAppPaymentsByPaymentIdCleanupAttachmentsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppPaymentsByPaymentIdCleanupAttachmentsError =
  PostApiAppPaymentsByPaymentIdCleanupAttachmentsErrors[keyof PostApiAppPaymentsByPaymentIdCleanupAttachmentsErrors];

export type PostApiAppPaymentsByPaymentIdCleanupAttachmentsResponses = {
  /**
   * OK
   */
  200: boolean;
};

export type PostApiAppPaymentsByPaymentIdCleanupAttachmentsResponse =
  PostApiAppPaymentsByPaymentIdCleanupAttachmentsResponses[keyof PostApiAppPaymentsByPaymentIdCleanupAttachmentsResponses];

export type GetApiAccountMyProfileData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/account/my-profile";
};

export type GetApiAccountMyProfileErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAccountMyProfileError =
  GetApiAccountMyProfileErrors[keyof GetApiAccountMyProfileErrors];

export type GetApiAccountMyProfileResponses = {
  /**
   * OK
   */
  200: ProfileDtoReadable;
};

export type GetApiAccountMyProfileResponse =
  GetApiAccountMyProfileResponses[keyof GetApiAccountMyProfileResponses];

export type PutApiAccountMyProfileData = {
  body?: UpdateProfileDtoWritable;
  path?: never;
  query?: never;
  url: "/api/account/my-profile";
};

export type PutApiAccountMyProfileErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAccountMyProfileError =
  PutApiAccountMyProfileErrors[keyof PutApiAccountMyProfileErrors];

export type PutApiAccountMyProfileResponses = {
  /**
   * OK
   */
  200: ProfileDtoReadable;
};

export type PutApiAccountMyProfileResponse =
  PutApiAccountMyProfileResponses[keyof PutApiAccountMyProfileResponses];

export type PostApiAccountMyProfileChangePasswordData = {
  body?: ChangePasswordInput;
  path?: never;
  query?: never;
  url: "/api/account/my-profile/change-password";
};

export type PostApiAccountMyProfileChangePasswordErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAccountMyProfileChangePasswordError =
  PostApiAccountMyProfileChangePasswordErrors[keyof PostApiAccountMyProfileChangePasswordErrors];

export type PostApiAccountMyProfileChangePasswordResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiAppRegistrationCardGenerateDocumentAsAttachmentData = {
  body?: DocumentGenerationDto;
  path?: never;
  query?: never;
  url: "/api/app/registration-card/generate-document-as-attachment";
};

export type PostApiAppRegistrationCardGenerateDocumentAsAttachmentErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppRegistrationCardGenerateDocumentAsAttachmentError =
  PostApiAppRegistrationCardGenerateDocumentAsAttachmentErrors[keyof PostApiAppRegistrationCardGenerateDocumentAsAttachmentErrors];

export type PostApiAppRegistrationCardGenerateDocumentAsAttachmentResponses = {
  /**
   * OK
   */
  200: FileUploadResultDto;
};

export type PostApiAppRegistrationCardGenerateDocumentAsAttachmentResponse =
  PostApiAppRegistrationCardGenerateDocumentAsAttachmentResponses[keyof PostApiAppRegistrationCardGenerateDocumentAsAttachmentResponses];

export type GetApiReportData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/report";
};

export type GetApiReportErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiReportError = GetApiReportErrors[keyof GetApiReportErrors];

export type GetApiReportResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReportDto;
};

export type GetApiReportResponse =
  GetApiReportResponses[keyof GetApiReportResponses];

export type PostApiReportData = {
  body?: CreateUpdateReportDto;
  path?: never;
  query?: never;
  url: "/api/report";
};

export type PostApiReportErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportError = PostApiReportErrors[keyof PostApiReportErrors];

export type PostApiReportResponses = {
  /**
   * OK
   */
  200: ReportDto;
};

export type PostApiReportResponse =
  PostApiReportResponses[keyof PostApiReportResponses];

export type GetApiReportActiveData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/report/active";
};

export type GetApiReportActiveErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiReportActiveError =
  GetApiReportActiveErrors[keyof GetApiReportActiveErrors];

export type GetApiReportActiveResponses = {
  /**
   * OK
   */
  200: Array<ReportDto>;
};

export type GetApiReportActiveResponse =
  GetApiReportActiveResponses[keyof GetApiReportActiveResponses];

export type DeleteApiReportByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/report/{id}";
};

export type DeleteApiReportByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiReportByIdError =
  DeleteApiReportByIdErrors[keyof DeleteApiReportByIdErrors];

export type DeleteApiReportByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiReportByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/report/{id}";
};

export type GetApiReportByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiReportByIdError =
  GetApiReportByIdErrors[keyof GetApiReportByIdErrors];

export type GetApiReportByIdResponses = {
  /**
   * OK
   */
  200: ReportDto;
};

export type GetApiReportByIdResponse =
  GetApiReportByIdResponses[keyof GetApiReportByIdResponses];

export type PutApiReportByIdData = {
  body?: CreateUpdateReportDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/report/{id}";
};

export type PutApiReportByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiReportByIdError =
  PutApiReportByIdErrors[keyof PutApiReportByIdErrors];

export type PutApiReportByIdResponses = {
  /**
   * OK
   */
  200: ReportDto;
};

export type PutApiReportByIdResponse =
  PutApiReportByIdResponses[keyof PutApiReportByIdResponses];

export type PostApiReportPreviewData = {
  body?: ReportExecutionDto;
  path?: never;
  query?: never;
  url: "/api/report/preview";
};

export type PostApiReportPreviewErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportPreviewError =
  PostApiReportPreviewErrors[keyof PostApiReportPreviewErrors];

export type PostApiReportPreviewResponses = {
  /**
   * OK
   */
  200: ReportPreviewDto;
};

export type PostApiReportPreviewResponse =
  PostApiReportPreviewResponses[keyof PostApiReportPreviewResponses];

export type PostApiReportExportCsvData = {
  body?: ReportExecutionDto;
  path?: never;
  query?: never;
  url: "/api/report/export/csv";
};

export type PostApiReportExportCsvErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportExportCsvError =
  PostApiReportExportCsvErrors[keyof PostApiReportExportCsvErrors];

export type PostApiReportExportExcelData = {
  body?: ReportExecutionDto;
  path?: never;
  query?: never;
  url: "/api/report/export/excel";
};

export type PostApiReportExportExcelErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportExportExcelError =
  PostApiReportExportExcelErrors[keyof PostApiReportExportExcelErrors];

export type PostApiReportExportExcelCustomData = {
  body?: ReportExecutionWithCustomHeaderDto;
  path?: never;
  query?: never;
  url: "/api/report/export/excel/custom";
};

export type PostApiReportExportExcelCustomErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportExportExcelCustomError =
  PostApiReportExportExcelCustomErrors[keyof PostApiReportExportExcelCustomErrors];

export type GetApiReportByReportIdParametersData = {
  body?: never;
  path: {
    reportId: string;
  };
  query?: never;
  url: "/api/report/{reportId}/parameters";
};

export type GetApiReportByReportIdParametersErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiReportByReportIdParametersError =
  GetApiReportByReportIdParametersErrors[keyof GetApiReportByReportIdParametersErrors];

export type GetApiReportByReportIdParametersResponses = {
  /**
   * OK
   */
  200: Array<ReportParameterDto>;
};

export type GetApiReportByReportIdParametersResponse =
  GetApiReportByReportIdParametersResponses[keyof GetApiReportByReportIdParametersResponses];

export type GetApiReportByReportIdExcelHeaderData = {
  body?: never;
  path: {
    reportId: string;
  };
  query?: never;
  url: "/api/report/{reportId}/excel-header";
};

export type GetApiReportByReportIdExcelHeaderErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiReportByReportIdExcelHeaderError =
  GetApiReportByReportIdExcelHeaderErrors[keyof GetApiReportByReportIdExcelHeaderErrors];

export type GetApiReportByReportIdExcelHeaderResponses = {
  /**
   * OK
   */
  200: ReportExcelHeaderDto;
};

export type GetApiReportByReportIdExcelHeaderResponse =
  GetApiReportByReportIdExcelHeaderResponses[keyof GetApiReportByReportIdExcelHeaderResponses];

export type PutApiReportByReportIdExcelHeaderData = {
  body?: ReportExcelHeaderDto;
  path: {
    reportId: string;
  };
  query?: never;
  url: "/api/report/{reportId}/excel-header";
};

export type PutApiReportByReportIdExcelHeaderErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiReportByReportIdExcelHeaderError =
  PutApiReportByReportIdExcelHeaderErrors[keyof PutApiReportByReportIdExcelHeaderErrors];

export type PutApiReportByReportIdExcelHeaderResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiReportByReportIdDebugHeaderData = {
  body?: never;
  path: {
    reportId: string;
  };
  query?: never;
  url: "/api/report/{reportId}/debug-header";
};

export type GetApiReportByReportIdDebugHeaderErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiReportByReportIdDebugHeaderError =
  GetApiReportByReportIdDebugHeaderErrors[keyof GetApiReportByReportIdDebugHeaderErrors];

export type PostApiReportByReportIdTestHeaderData = {
  body?: ReportExecutionDto;
  path: {
    reportId: string;
  };
  query?: never;
  url: "/api/report/{reportId}/test-header";
};

export type PostApiReportByReportIdTestHeaderErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportByReportIdTestHeaderError =
  PostApiReportByReportIdTestHeaderErrors[keyof PostApiReportByReportIdTestHeaderErrors];

export type PostApiReportExportExcelConditionalFormattingTestData = {
  body?: ReportExecutionDto;
  path?: never;
  query?: never;
  url: "/api/report/export/excel/conditional-formatting-test";
};

export type PostApiReportExportExcelConditionalFormattingTestErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportExportExcelConditionalFormattingTestError =
  PostApiReportExportExcelConditionalFormattingTestErrors[keyof PostApiReportExportExcelConditionalFormattingTestErrors];

export type PostApiReportListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/report/list";
};

export type PostApiReportListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportListError =
  PostApiReportListErrors[keyof PostApiReportListErrors];

export type PostApiReportListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReportDto;
};

export type PostApiReportListResponse =
  PostApiReportListResponses[keyof PostApiReportListResponses];

export type PostApiReportByReportIdTestPivotData = {
  body?: ReportExecutionDto;
  path: {
    reportId: string;
  };
  query?: never;
  url: "/api/report/{reportId}/test-pivot";
};

export type PostApiReportByReportIdTestPivotErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiReportByReportIdTestPivotError =
  PostApiReportByReportIdTestPivotErrors[keyof PostApiReportByReportIdTestPivotErrors];

export type GetApiAppReportroomsData = {
  body?: never;
  path?: never;
  query?: {
    date?: string;
  };
  url: "/api/app/reportrooms";
};

export type GetApiAppReportroomsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReportroomsError =
  GetApiAppReportroomsErrors[keyof GetApiAppReportroomsErrors];

export type GetApiAppReportroomsResponses = {
  /**
   * OK
   */
  200: Array<ReportRoomsDto>;
};

export type GetApiAppReportroomsResponse =
  GetApiAppReportroomsResponses[keyof GetApiAppReportroomsResponses];

export type PostApiAppReportroomsReportByDateData = {
  body?: DateRequestDto;
  path?: never;
  query?: never;
  url: "/api/app/reportrooms/report-by-date";
};

export type PostApiAppReportroomsReportByDateErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReportroomsReportByDateError =
  PostApiAppReportroomsReportByDateErrors[keyof PostApiAppReportroomsReportByDateErrors];

export type PostApiAppReportroomsReportByDateResponses = {
  /**
   * OK
   */
  200: Array<ReportRoomsDto>;
};

export type PostApiAppReportroomsReportByDateResponse =
  PostApiAppReportroomsReportByDateResponses[keyof PostApiAppReportroomsReportByDateResponses];

export type GetApiAppReportroomsRoomTypesData = {
  body?: never;
  path?: never;
  query?: {
    date?: string;
  };
  url: "/api/app/reportrooms/room-types";
};

export type GetApiAppReportroomsRoomTypesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReportroomsRoomTypesError =
  GetApiAppReportroomsRoomTypesErrors[keyof GetApiAppReportroomsRoomTypesErrors];

export type GetApiAppReportroomsRoomTypesResponses = {
  /**
   * OK
   */
  200: Array<ReportRoomsDto>;
};

export type GetApiAppReportroomsRoomTypesResponse =
  GetApiAppReportroomsRoomTypesResponses[keyof GetApiAppReportroomsRoomTypesResponses];

export type GetApiAppReportRoomsReportRoomsData = {
  body?: never;
  path?: never;
  query?: {
    date?: string;
  };
  url: "/api/app/report-rooms/report-rooms";
};

export type GetApiAppReportRoomsReportRoomsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReportRoomsReportRoomsError =
  GetApiAppReportRoomsReportRoomsErrors[keyof GetApiAppReportRoomsReportRoomsErrors];

export type GetApiAppReportRoomsReportRoomsResponses = {
  /**
   * OK
   */
  200: Array<ReportRoomsDto>;
};

export type GetApiAppReportRoomsReportRoomsResponse =
  GetApiAppReportRoomsReportRoomsResponses[keyof GetApiAppReportRoomsReportRoomsResponses];

export type GetApiAppReportRoomsRoomTypesReportByDateData = {
  body?: never;
  path?: never;
  query?: {
    date?: string;
  };
  url: "/api/app/report-rooms/room-types-report-by-date";
};

export type GetApiAppReportRoomsRoomTypesReportByDateErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReportRoomsRoomTypesReportByDateError =
  GetApiAppReportRoomsRoomTypesReportByDateErrors[keyof GetApiAppReportRoomsRoomTypesReportByDateErrors];

export type GetApiAppReportRoomsRoomTypesReportByDateResponses = {
  /**
   * OK
   */
  200: Array<ReportRoomsDto>;
};

export type GetApiAppReportRoomsRoomTypesReportByDateResponse =
  GetApiAppReportRoomsRoomTypesReportByDateResponses[keyof GetApiAppReportRoomsRoomTypesReportByDateResponses];

export type PostApiAppReservationDetailsListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/reservation-details/list";
};

export type PostApiAppReservationDetailsListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationDetailsListError =
  PostApiAppReservationDetailsListErrors[keyof PostApiAppReservationDetailsListErrors];

export type PostApiAppReservationDetailsListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationDetailsDto;
};

export type PostApiAppReservationDetailsListResponse =
  PostApiAppReservationDetailsListResponses[keyof PostApiAppReservationDetailsListResponses];

export type DeleteApiAppReservationDetailsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-details/{id}";
};

export type DeleteApiAppReservationDetailsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppReservationDetailsByIdError =
  DeleteApiAppReservationDetailsByIdErrors[keyof DeleteApiAppReservationDetailsByIdErrors];

export type DeleteApiAppReservationDetailsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppReservationDetailsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-details/{id}";
};

export type GetApiAppReservationDetailsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationDetailsByIdError =
  GetApiAppReservationDetailsByIdErrors[keyof GetApiAppReservationDetailsByIdErrors];

export type GetApiAppReservationDetailsByIdResponses = {
  /**
   * OK
   */
  200: ReservationDetailsDto;
};

export type GetApiAppReservationDetailsByIdResponse =
  GetApiAppReservationDetailsByIdResponses[keyof GetApiAppReservationDetailsByIdResponses];

export type PutApiAppReservationDetailsByIdData = {
  body?: CreateUpdateReservationDetailsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-details/{id}";
};

export type PutApiAppReservationDetailsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppReservationDetailsByIdError =
  PutApiAppReservationDetailsByIdErrors[keyof PutApiAppReservationDetailsByIdErrors];

export type PutApiAppReservationDetailsByIdResponses = {
  /**
   * OK
   */
  200: ReservationDetailsDto;
};

export type PutApiAppReservationDetailsByIdResponse =
  PutApiAppReservationDetailsByIdResponses[keyof PutApiAppReservationDetailsByIdResponses];

export type GetApiAppReservationDetailsData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/reservation-details";
};

export type GetApiAppReservationDetailsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationDetailsError =
  GetApiAppReservationDetailsErrors[keyof GetApiAppReservationDetailsErrors];

export type GetApiAppReservationDetailsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationDetailsDto;
};

export type GetApiAppReservationDetailsResponse =
  GetApiAppReservationDetailsResponses[keyof GetApiAppReservationDetailsResponses];

export type PostApiAppReservationDetailsData = {
  body?: CreateUpdateReservationDetailsDto;
  path?: never;
  query?: never;
  url: "/api/app/reservation-details";
};

export type PostApiAppReservationDetailsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationDetailsError =
  PostApiAppReservationDetailsErrors[keyof PostApiAppReservationDetailsErrors];

export type PostApiAppReservationDetailsResponses = {
  /**
   * OK
   */
  200: ReservationDetailsDto;
};

export type PostApiAppReservationDetailsResponse =
  PostApiAppReservationDetailsResponses[keyof PostApiAppReservationDetailsResponses];

export type PostApiAppReservationFoodAndBeveragesListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/reservation-food-and-beverages/list";
};

export type PostApiAppReservationFoodAndBeveragesListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationFoodAndBeveragesListError =
  PostApiAppReservationFoodAndBeveragesListErrors[keyof PostApiAppReservationFoodAndBeveragesListErrors];

export type PostApiAppReservationFoodAndBeveragesListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationFoodAndBeveragesDto;
};

export type PostApiAppReservationFoodAndBeveragesListResponse =
  PostApiAppReservationFoodAndBeveragesListResponses[keyof PostApiAppReservationFoodAndBeveragesListResponses];

export type GetApiAppReservationFoodAndBeveragesData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/reservation-food-and-beverages";
};

export type GetApiAppReservationFoodAndBeveragesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationFoodAndBeveragesError =
  GetApiAppReservationFoodAndBeveragesErrors[keyof GetApiAppReservationFoodAndBeveragesErrors];

export type GetApiAppReservationFoodAndBeveragesResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationFoodAndBeveragesDto;
};

export type GetApiAppReservationFoodAndBeveragesResponse =
  GetApiAppReservationFoodAndBeveragesResponses[keyof GetApiAppReservationFoodAndBeveragesResponses];

export type PostApiAppReservationFoodAndBeveragesData = {
  body?: CreateUpdateReservationFoodAndBeveragesDto;
  path?: never;
  query?: never;
  url: "/api/app/reservation-food-and-beverages";
};

export type PostApiAppReservationFoodAndBeveragesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationFoodAndBeveragesError =
  PostApiAppReservationFoodAndBeveragesErrors[keyof PostApiAppReservationFoodAndBeveragesErrors];

export type PostApiAppReservationFoodAndBeveragesResponses = {
  /**
   * OK
   */
  200: ReservationFoodAndBeveragesDto;
};

export type PostApiAppReservationFoodAndBeveragesResponse =
  PostApiAppReservationFoodAndBeveragesResponses[keyof PostApiAppReservationFoodAndBeveragesResponses];

export type DeleteApiAppReservationFoodAndBeveragesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-food-and-beverages/{id}";
};

export type DeleteApiAppReservationFoodAndBeveragesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppReservationFoodAndBeveragesByIdError =
  DeleteApiAppReservationFoodAndBeveragesByIdErrors[keyof DeleteApiAppReservationFoodAndBeveragesByIdErrors];

export type DeleteApiAppReservationFoodAndBeveragesByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppReservationFoodAndBeveragesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-food-and-beverages/{id}";
};

export type GetApiAppReservationFoodAndBeveragesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationFoodAndBeveragesByIdError =
  GetApiAppReservationFoodAndBeveragesByIdErrors[keyof GetApiAppReservationFoodAndBeveragesByIdErrors];

export type GetApiAppReservationFoodAndBeveragesByIdResponses = {
  /**
   * OK
   */
  200: ReservationFoodAndBeveragesDto;
};

export type GetApiAppReservationFoodAndBeveragesByIdResponse =
  GetApiAppReservationFoodAndBeveragesByIdResponses[keyof GetApiAppReservationFoodAndBeveragesByIdResponses];

export type PutApiAppReservationFoodAndBeveragesByIdData = {
  body?: CreateUpdateReservationFoodAndBeveragesDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-food-and-beverages/{id}";
};

export type PutApiAppReservationFoodAndBeveragesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppReservationFoodAndBeveragesByIdError =
  PutApiAppReservationFoodAndBeveragesByIdErrors[keyof PutApiAppReservationFoodAndBeveragesByIdErrors];

export type PutApiAppReservationFoodAndBeveragesByIdResponses = {
  /**
   * OK
   */
  200: ReservationFoodAndBeveragesDto;
};

export type PutApiAppReservationFoodAndBeveragesByIdResponse =
  PutApiAppReservationFoodAndBeveragesByIdResponses[keyof PutApiAppReservationFoodAndBeveragesByIdResponses];

export type PostApiAppReservationFoodAndBeveragesCreateManyData = {
  body?: Array<CreateUpdateReservationFoodAndBeveragesDto>;
  path?: never;
  query?: never;
  url: "/api/app/reservation-food-and-beverages/create-many";
};

export type PostApiAppReservationFoodAndBeveragesCreateManyErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationFoodAndBeveragesCreateManyError =
  PostApiAppReservationFoodAndBeveragesCreateManyErrors[keyof PostApiAppReservationFoodAndBeveragesCreateManyErrors];

export type PostApiAppReservationFoodAndBeveragesCreateManyResponses = {
  /**
   * OK
   */
  200: ReservationFoodAndBeveragesDto;
};

export type PostApiAppReservationFoodAndBeveragesCreateManyResponse =
  PostApiAppReservationFoodAndBeveragesCreateManyResponses[keyof PostApiAppReservationFoodAndBeveragesCreateManyResponses];

export type PutApiAppReservationFoodAndBeveragesUpdateManyData = {
  body?: Array<CreateUpdateReservationFoodAndBeveragesDto>;
  path?: never;
  query?: never;
  url: "/api/app/reservation-food-and-beverages/update-many";
};

export type PutApiAppReservationFoodAndBeveragesUpdateManyErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppReservationFoodAndBeveragesUpdateManyError =
  PutApiAppReservationFoodAndBeveragesUpdateManyErrors[keyof PutApiAppReservationFoodAndBeveragesUpdateManyErrors];

export type PutApiAppReservationFoodAndBeveragesUpdateManyResponses = {
  /**
   * OK
   */
  200: ReservationFoodAndBeveragesDto;
};

export type PutApiAppReservationFoodAndBeveragesUpdateManyResponse =
  PutApiAppReservationFoodAndBeveragesUpdateManyResponses[keyof PutApiAppReservationFoodAndBeveragesUpdateManyResponses];

export type PostApiAppReservationRoomsListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/reservation-rooms/list";
};

export type PostApiAppReservationRoomsListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationRoomsListError =
  PostApiAppReservationRoomsListErrors[keyof PostApiAppReservationRoomsListErrors];

export type PostApiAppReservationRoomsListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationRoomsDto;
};

export type PostApiAppReservationRoomsListResponse =
  PostApiAppReservationRoomsListResponses[keyof PostApiAppReservationRoomsListResponses];

export type DeleteApiAppReservationRoomsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-rooms/{id}";
};

export type DeleteApiAppReservationRoomsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppReservationRoomsByIdError =
  DeleteApiAppReservationRoomsByIdErrors[keyof DeleteApiAppReservationRoomsByIdErrors];

export type DeleteApiAppReservationRoomsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppReservationRoomsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-rooms/{id}";
};

export type GetApiAppReservationRoomsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationRoomsByIdError =
  GetApiAppReservationRoomsByIdErrors[keyof GetApiAppReservationRoomsByIdErrors];

export type GetApiAppReservationRoomsByIdResponses = {
  /**
   * OK
   */
  200: ReservationRoomsDto;
};

export type GetApiAppReservationRoomsByIdResponse =
  GetApiAppReservationRoomsByIdResponses[keyof GetApiAppReservationRoomsByIdResponses];

export type PutApiAppReservationRoomsByIdData = {
  body?: CreateUpdateReservationRoomsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-rooms/{id}";
};

export type PutApiAppReservationRoomsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppReservationRoomsByIdError =
  PutApiAppReservationRoomsByIdErrors[keyof PutApiAppReservationRoomsByIdErrors];

export type PutApiAppReservationRoomsByIdResponses = {
  /**
   * OK
   */
  200: ReservationRoomsDto;
};

export type PutApiAppReservationRoomsByIdResponse =
  PutApiAppReservationRoomsByIdResponses[keyof PutApiAppReservationRoomsByIdResponses];

export type GetApiAppReservationRoomsData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/reservation-rooms";
};

export type GetApiAppReservationRoomsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationRoomsError =
  GetApiAppReservationRoomsErrors[keyof GetApiAppReservationRoomsErrors];

export type GetApiAppReservationRoomsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationRoomsDto;
};

export type GetApiAppReservationRoomsResponse =
  GetApiAppReservationRoomsResponses[keyof GetApiAppReservationRoomsResponses];

export type PostApiAppReservationRoomsData = {
  body?: CreateUpdateReservationRoomsDto;
  path?: never;
  query?: never;
  url: "/api/app/reservation-rooms";
};

export type PostApiAppReservationRoomsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationRoomsError =
  PostApiAppReservationRoomsErrors[keyof PostApiAppReservationRoomsErrors];

export type PostApiAppReservationRoomsResponses = {
  /**
   * OK
   */
  200: ReservationRoomsDto;
};

export type PostApiAppReservationRoomsResponse =
  PostApiAppReservationRoomsResponses[keyof PostApiAppReservationRoomsResponses];

export type PostApiAppReservationRoomsCreateManyData = {
  body?: Array<CreateUpdateReservationRoomsDto>;
  path?: never;
  query?: never;
  url: "/api/app/reservation-rooms/create-many";
};

export type PostApiAppReservationRoomsCreateManyErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationRoomsCreateManyError =
  PostApiAppReservationRoomsCreateManyErrors[keyof PostApiAppReservationRoomsCreateManyErrors];

export type PostApiAppReservationRoomsCreateManyResponses = {
  /**
   * OK
   */
  200: ReservationRoomsDto;
};

export type PostApiAppReservationRoomsCreateManyResponse =
  PostApiAppReservationRoomsCreateManyResponses[keyof PostApiAppReservationRoomsCreateManyResponses];

export type PostApiAppReservationsListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/reservations/list";
};

export type PostApiAppReservationsListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationsListError =
  PostApiAppReservationsListErrors[keyof PostApiAppReservationsListErrors];

export type PostApiAppReservationsListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationsDto;
};

export type PostApiAppReservationsListResponse =
  PostApiAppReservationsListResponses[keyof PostApiAppReservationsListResponses];

export type GetApiAppReservationsData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/reservations";
};

export type GetApiAppReservationsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationsError =
  GetApiAppReservationsErrors[keyof GetApiAppReservationsErrors];

export type GetApiAppReservationsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationsDto;
};

export type GetApiAppReservationsResponse =
  GetApiAppReservationsResponses[keyof GetApiAppReservationsResponses];

export type PostApiAppReservationsData = {
  body?: CreateUpdateReservationsDto;
  path?: never;
  query?: never;
  url: "/api/app/reservations";
};

export type PostApiAppReservationsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationsError =
  PostApiAppReservationsErrors[keyof PostApiAppReservationsErrors];

export type PostApiAppReservationsResponses = {
  /**
   * OK
   */
  200: ReservationsDto;
};

export type PostApiAppReservationsResponse =
  PostApiAppReservationsResponses[keyof PostApiAppReservationsResponses];

export type PostApiAppReservationsCreateWithDetailsData = {
  body?: CreateReservationWithDetailsDto;
  path?: never;
  query?: never;
  url: "/api/app/reservations/create-with-details";
};

export type PostApiAppReservationsCreateWithDetailsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationsCreateWithDetailsError =
  PostApiAppReservationsCreateWithDetailsErrors[keyof PostApiAppReservationsCreateWithDetailsErrors];

export type PutApiAppReservationsByIdUpdateWithDetailsData = {
  body?: UpdateReservationWithDetailsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservations/{id}/update-with-details";
};

export type PutApiAppReservationsByIdUpdateWithDetailsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppReservationsByIdUpdateWithDetailsError =
  PutApiAppReservationsByIdUpdateWithDetailsErrors[keyof PutApiAppReservationsByIdUpdateWithDetailsErrors];

export type PutApiAppReservationsByIdUpdateWithDetailsResponses = {
  /**
   * OK
   */
  200: ReservationsDto;
};

export type PutApiAppReservationsByIdUpdateWithDetailsResponse =
  PutApiAppReservationsByIdUpdateWithDetailsResponses[keyof PutApiAppReservationsByIdUpdateWithDetailsResponses];

export type DeleteApiAppReservationsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservations/{id}";
};

export type DeleteApiAppReservationsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppReservationsByIdError =
  DeleteApiAppReservationsByIdErrors[keyof DeleteApiAppReservationsByIdErrors];

export type DeleteApiAppReservationsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppReservationsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservations/{id}";
};

export type GetApiAppReservationsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationsByIdError =
  GetApiAppReservationsByIdErrors[keyof GetApiAppReservationsByIdErrors];

export type GetApiAppReservationsByIdResponses = {
  /**
   * OK
   */
  200: ReservationsDto;
};

export type GetApiAppReservationsByIdResponse =
  GetApiAppReservationsByIdResponses[keyof GetApiAppReservationsByIdResponses];

export type PutApiAppReservationsByIdData = {
  body?: CreateUpdateReservationsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservations/{id}";
};

export type PutApiAppReservationsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppReservationsByIdError =
  PutApiAppReservationsByIdErrors[keyof PutApiAppReservationsByIdErrors];

export type PutApiAppReservationsByIdResponses = {
  /**
   * OK
   */
  200: ReservationsDto;
};

export type PutApiAppReservationsByIdResponse =
  PutApiAppReservationsByIdResponses[keyof PutApiAppReservationsByIdResponses];

export type GetApiAppReservationsByIdWithDetailsData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservations/{id}/with-details";
};

export type GetApiAppReservationsByIdWithDetailsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationsByIdWithDetailsError =
  GetApiAppReservationsByIdWithDetailsErrors[keyof GetApiAppReservationsByIdWithDetailsErrors];

export type GetApiAppReservationsByIdWithDetailsResponses = {
  /**
   * OK
   */
  200: ReservationsDto;
};

export type GetApiAppReservationsByIdWithDetailsResponse =
  GetApiAppReservationsByIdWithDetailsResponses[keyof GetApiAppReservationsByIdWithDetailsResponses];

export type GetApiAppReservationTypesData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/reservation-types";
};

export type GetApiAppReservationTypesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationTypesError =
  GetApiAppReservationTypesErrors[keyof GetApiAppReservationTypesErrors];

export type GetApiAppReservationTypesResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfReservationTypesDto;
};

export type GetApiAppReservationTypesResponse =
  GetApiAppReservationTypesResponses[keyof GetApiAppReservationTypesResponses];

export type PostApiAppReservationTypesData = {
  body?: CreateUpdateReservationTypesDto;
  path?: never;
  query?: never;
  url: "/api/app/reservation-types";
};

export type PostApiAppReservationTypesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppReservationTypesError =
  PostApiAppReservationTypesErrors[keyof PostApiAppReservationTypesErrors];

export type PostApiAppReservationTypesResponses = {
  /**
   * OK
   */
  200: ReservationTypesDto;
};

export type PostApiAppReservationTypesResponse =
  PostApiAppReservationTypesResponses[keyof PostApiAppReservationTypesResponses];

export type DeleteApiAppReservationTypesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-types/{id}";
};

export type DeleteApiAppReservationTypesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppReservationTypesByIdError =
  DeleteApiAppReservationTypesByIdErrors[keyof DeleteApiAppReservationTypesByIdErrors];

export type DeleteApiAppReservationTypesByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppReservationTypesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-types/{id}";
};

export type GetApiAppReservationTypesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppReservationTypesByIdError =
  GetApiAppReservationTypesByIdErrors[keyof GetApiAppReservationTypesByIdErrors];

export type GetApiAppReservationTypesByIdResponses = {
  /**
   * OK
   */
  200: ReservationTypesDto;
};

export type GetApiAppReservationTypesByIdResponse =
  GetApiAppReservationTypesByIdResponses[keyof GetApiAppReservationTypesByIdResponses];

export type PutApiAppReservationTypesByIdData = {
  body?: CreateUpdateReservationTypesDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/reservation-types/{id}";
};

export type PutApiAppReservationTypesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppReservationTypesByIdError =
  PutApiAppReservationTypesByIdErrors[keyof PutApiAppReservationTypesByIdErrors];

export type PutApiAppReservationTypesByIdResponses = {
  /**
   * OK
   */
  200: ReservationTypesDto;
};

export type PutApiAppReservationTypesByIdResponse =
  PutApiAppReservationTypesByIdResponses[keyof PutApiAppReservationTypesByIdResponses];

export type GetApiAppRoomData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/room";
};

export type GetApiAppRoomErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppRoomError = GetApiAppRoomErrors[keyof GetApiAppRoomErrors];

export type GetApiAppRoomResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfRoomDto;
};

export type GetApiAppRoomResponse =
  GetApiAppRoomResponses[keyof GetApiAppRoomResponses];

export type PostApiAppRoomData = {
  body?: CreateUpdateRoomDto;
  path?: never;
  query?: never;
  url: "/api/app/room";
};

export type PostApiAppRoomErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppRoomError =
  PostApiAppRoomErrors[keyof PostApiAppRoomErrors];

export type PostApiAppRoomResponses = {
  /**
   * OK
   */
  200: RoomDto;
};

export type PostApiAppRoomResponse =
  PostApiAppRoomResponses[keyof PostApiAppRoomResponses];

export type DeleteApiAppRoomByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room/{id}";
};

export type DeleteApiAppRoomByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppRoomByIdError =
  DeleteApiAppRoomByIdErrors[keyof DeleteApiAppRoomByIdErrors];

export type DeleteApiAppRoomByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppRoomByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room/{id}";
};

export type GetApiAppRoomByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppRoomByIdError =
  GetApiAppRoomByIdErrors[keyof GetApiAppRoomByIdErrors];

export type GetApiAppRoomByIdResponses = {
  /**
   * OK
   */
  200: RoomDto;
};

export type GetApiAppRoomByIdResponse =
  GetApiAppRoomByIdResponses[keyof GetApiAppRoomByIdResponses];

export type PutApiAppRoomByIdData = {
  body?: CreateUpdateRoomDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room/{id}";
};

export type PutApiAppRoomByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppRoomByIdError =
  PutApiAppRoomByIdErrors[keyof PutApiAppRoomByIdErrors];

export type PutApiAppRoomByIdResponses = {
  /**
   * OK
   */
  200: RoomDto;
};

export type PutApiAppRoomByIdResponse =
  PutApiAppRoomByIdResponses[keyof PutApiAppRoomByIdResponses];

export type PostApiAppRoomsListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/rooms/list";
};

export type PostApiAppRoomsListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppRoomsListError =
  PostApiAppRoomsListErrors[keyof PostApiAppRoomsListErrors];

export type PostApiAppRoomsListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfRoomDto;
};

export type PostApiAppRoomsListResponse =
  PostApiAppRoomsListResponses[keyof PostApiAppRoomsListResponses];

export type GetApiAppRoomStatusData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/room-status";
};

export type GetApiAppRoomStatusErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppRoomStatusError =
  GetApiAppRoomStatusErrors[keyof GetApiAppRoomStatusErrors];

export type GetApiAppRoomStatusResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfRoomStatusDto;
};

export type GetApiAppRoomStatusResponse =
  GetApiAppRoomStatusResponses[keyof GetApiAppRoomStatusResponses];

export type PostApiAppRoomStatusData = {
  body?: CreateUpdateRoomStatusDto;
  path?: never;
  query?: never;
  url: "/api/app/room-status";
};

export type PostApiAppRoomStatusErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppRoomStatusError =
  PostApiAppRoomStatusErrors[keyof PostApiAppRoomStatusErrors];

export type PostApiAppRoomStatusResponses = {
  /**
   * OK
   */
  200: RoomStatusDto;
};

export type PostApiAppRoomStatusResponse =
  PostApiAppRoomStatusResponses[keyof PostApiAppRoomStatusResponses];

export type DeleteApiAppRoomStatusByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-status/{id}";
};

export type DeleteApiAppRoomStatusByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppRoomStatusByIdError =
  DeleteApiAppRoomStatusByIdErrors[keyof DeleteApiAppRoomStatusByIdErrors];

export type DeleteApiAppRoomStatusByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppRoomStatusByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-status/{id}";
};

export type GetApiAppRoomStatusByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppRoomStatusByIdError =
  GetApiAppRoomStatusByIdErrors[keyof GetApiAppRoomStatusByIdErrors];

export type GetApiAppRoomStatusByIdResponses = {
  /**
   * OK
   */
  200: RoomStatusDto;
};

export type GetApiAppRoomStatusByIdResponse =
  GetApiAppRoomStatusByIdResponses[keyof GetApiAppRoomStatusByIdResponses];

export type PutApiAppRoomStatusByIdData = {
  body?: CreateUpdateRoomStatusDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-status/{id}";
};

export type PutApiAppRoomStatusByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppRoomStatusByIdError =
  PutApiAppRoomStatusByIdErrors[keyof PutApiAppRoomStatusByIdErrors];

export type PutApiAppRoomStatusByIdResponses = {
  /**
   * OK
   */
  200: RoomStatusDto;
};

export type PutApiAppRoomStatusByIdResponse =
  PutApiAppRoomStatusByIdResponses[keyof PutApiAppRoomStatusByIdResponses];

export type GetApiAppRoomStatusLogsData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/room-status-logs";
};

export type GetApiAppRoomStatusLogsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppRoomStatusLogsError =
  GetApiAppRoomStatusLogsErrors[keyof GetApiAppRoomStatusLogsErrors];

export type GetApiAppRoomStatusLogsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfRoomStatusLogsDto;
};

export type GetApiAppRoomStatusLogsResponse =
  GetApiAppRoomStatusLogsResponses[keyof GetApiAppRoomStatusLogsResponses];

export type PostApiAppRoomStatusLogsData = {
  body?: CreateUpdateRoomStatusLogsDto;
  path?: never;
  query?: never;
  url: "/api/app/room-status-logs";
};

export type PostApiAppRoomStatusLogsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppRoomStatusLogsError =
  PostApiAppRoomStatusLogsErrors[keyof PostApiAppRoomStatusLogsErrors];

export type PostApiAppRoomStatusLogsResponses = {
  /**
   * OK
   */
  200: RoomStatusLogsDto;
};

export type PostApiAppRoomStatusLogsResponse =
  PostApiAppRoomStatusLogsResponses[keyof PostApiAppRoomStatusLogsResponses];

export type DeleteApiAppRoomStatusLogsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-status-logs/{id}";
};

export type DeleteApiAppRoomStatusLogsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppRoomStatusLogsByIdError =
  DeleteApiAppRoomStatusLogsByIdErrors[keyof DeleteApiAppRoomStatusLogsByIdErrors];

export type DeleteApiAppRoomStatusLogsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppRoomStatusLogsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-status-logs/{id}";
};

export type GetApiAppRoomStatusLogsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppRoomStatusLogsByIdError =
  GetApiAppRoomStatusLogsByIdErrors[keyof GetApiAppRoomStatusLogsByIdErrors];

export type GetApiAppRoomStatusLogsByIdResponses = {
  /**
   * OK
   */
  200: RoomStatusLogsDto;
};

export type GetApiAppRoomStatusLogsByIdResponse =
  GetApiAppRoomStatusLogsByIdResponses[keyof GetApiAppRoomStatusLogsByIdResponses];

export type PutApiAppRoomStatusLogsByIdData = {
  body?: CreateUpdateRoomStatusLogsDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-status-logs/{id}";
};

export type PutApiAppRoomStatusLogsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppRoomStatusLogsByIdError =
  PutApiAppRoomStatusLogsByIdErrors[keyof PutApiAppRoomStatusLogsByIdErrors];

export type PutApiAppRoomStatusLogsByIdResponses = {
  /**
   * OK
   */
  200: RoomStatusLogsDto;
};

export type PutApiAppRoomStatusLogsByIdResponse =
  PutApiAppRoomStatusLogsByIdResponses[keyof PutApiAppRoomStatusLogsByIdResponses];

export type GetApiAppRoomTypeData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/room-type";
};

export type GetApiAppRoomTypeErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppRoomTypeError =
  GetApiAppRoomTypeErrors[keyof GetApiAppRoomTypeErrors];

export type GetApiAppRoomTypeResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfRoomTypeDto;
};

export type GetApiAppRoomTypeResponse =
  GetApiAppRoomTypeResponses[keyof GetApiAppRoomTypeResponses];

export type PostApiAppRoomTypeData = {
  body?: CreateUpdateRoomTypeDto;
  path?: never;
  query?: never;
  url: "/api/app/room-type";
};

export type PostApiAppRoomTypeErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppRoomTypeError =
  PostApiAppRoomTypeErrors[keyof PostApiAppRoomTypeErrors];

export type PostApiAppRoomTypeResponses = {
  /**
   * OK
   */
  200: RoomTypeDto;
};

export type PostApiAppRoomTypeResponse =
  PostApiAppRoomTypeResponses[keyof PostApiAppRoomTypeResponses];

export type DeleteApiAppRoomTypeByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-type/{id}";
};

export type DeleteApiAppRoomTypeByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppRoomTypeByIdError =
  DeleteApiAppRoomTypeByIdErrors[keyof DeleteApiAppRoomTypeByIdErrors];

export type DeleteApiAppRoomTypeByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppRoomTypeByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-type/{id}";
};

export type GetApiAppRoomTypeByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppRoomTypeByIdError =
  GetApiAppRoomTypeByIdErrors[keyof GetApiAppRoomTypeByIdErrors];

export type GetApiAppRoomTypeByIdResponses = {
  /**
   * OK
   */
  200: RoomTypeDto;
};

export type GetApiAppRoomTypeByIdResponse =
  GetApiAppRoomTypeByIdResponses[keyof GetApiAppRoomTypeByIdResponses];

export type PutApiAppRoomTypeByIdData = {
  body?: CreateUpdateRoomTypeDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/room-type/{id}";
};

export type PutApiAppRoomTypeByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppRoomTypeByIdError =
  PutApiAppRoomTypeByIdErrors[keyof PutApiAppRoomTypeByIdErrors];

export type PutApiAppRoomTypeByIdResponses = {
  /**
   * OK
   */
  200: RoomTypeDto;
};

export type PutApiAppRoomTypeByIdResponse =
  PutApiAppRoomTypeByIdResponses[keyof PutApiAppRoomTypeByIdResponses];

export type PostApiAppServicesListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/services/list";
};

export type PostApiAppServicesListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppServicesListError =
  PostApiAppServicesListErrors[keyof PostApiAppServicesListErrors];

export type PostApiAppServicesListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfServicesDto;
};

export type PostApiAppServicesListResponse =
  PostApiAppServicesListResponses[keyof PostApiAppServicesListResponses];

export type GetApiAppServicesData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/services";
};

export type GetApiAppServicesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppServicesError =
  GetApiAppServicesErrors[keyof GetApiAppServicesErrors];

export type GetApiAppServicesResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfServicesDto;
};

export type GetApiAppServicesResponse =
  GetApiAppServicesResponses[keyof GetApiAppServicesResponses];

export type PostApiAppServicesData = {
  body?: CreateUpdateServicesDto;
  path?: never;
  query?: never;
  url: "/api/app/services";
};

export type PostApiAppServicesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppServicesError =
  PostApiAppServicesErrors[keyof PostApiAppServicesErrors];

export type PostApiAppServicesResponses = {
  /**
   * OK
   */
  200: ServicesDto;
};

export type PostApiAppServicesResponse =
  PostApiAppServicesResponses[keyof PostApiAppServicesResponses];

export type DeleteApiAppServicesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/services/{id}";
};

export type DeleteApiAppServicesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppServicesByIdError =
  DeleteApiAppServicesByIdErrors[keyof DeleteApiAppServicesByIdErrors];

export type DeleteApiAppServicesByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppServicesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/services/{id}";
};

export type GetApiAppServicesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppServicesByIdError =
  GetApiAppServicesByIdErrors[keyof GetApiAppServicesByIdErrors];

export type GetApiAppServicesByIdResponses = {
  /**
   * OK
   */
  200: ServicesDto;
};

export type GetApiAppServicesByIdResponse =
  GetApiAppServicesByIdResponses[keyof GetApiAppServicesByIdResponses];

export type PutApiAppServicesByIdData = {
  body?: CreateUpdateServicesDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/services/{id}";
};

export type PutApiAppServicesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppServicesByIdError =
  PutApiAppServicesByIdErrors[keyof PutApiAppServicesByIdErrors];

export type PutApiAppServicesByIdResponses = {
  /**
   * OK
   */
  200: ServicesDto;
};

export type PutApiAppServicesByIdResponse =
  PutApiAppServicesByIdResponses[keyof PutApiAppServicesByIdResponses];

export type GetApiAppServiceTypesData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/service-types";
};

export type GetApiAppServiceTypesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppServiceTypesError =
  GetApiAppServiceTypesErrors[keyof GetApiAppServiceTypesErrors];

export type GetApiAppServiceTypesResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfServiceTypesDto;
};

export type GetApiAppServiceTypesResponse =
  GetApiAppServiceTypesResponses[keyof GetApiAppServiceTypesResponses];

export type PostApiAppServiceTypesData = {
  body?: CreateUpdateServiceTypesDto;
  path?: never;
  query?: never;
  url: "/api/app/service-types";
};

export type PostApiAppServiceTypesErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppServiceTypesError =
  PostApiAppServiceTypesErrors[keyof PostApiAppServiceTypesErrors];

export type PostApiAppServiceTypesResponses = {
  /**
   * OK
   */
  200: ServiceTypesDto;
};

export type PostApiAppServiceTypesResponse =
  PostApiAppServiceTypesResponses[keyof PostApiAppServiceTypesResponses];

export type DeleteApiAppServiceTypesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/service-types/{id}";
};

export type DeleteApiAppServiceTypesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppServiceTypesByIdError =
  DeleteApiAppServiceTypesByIdErrors[keyof DeleteApiAppServiceTypesByIdErrors];

export type DeleteApiAppServiceTypesByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppServiceTypesByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/service-types/{id}";
};

export type GetApiAppServiceTypesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppServiceTypesByIdError =
  GetApiAppServiceTypesByIdErrors[keyof GetApiAppServiceTypesByIdErrors];

export type GetApiAppServiceTypesByIdResponses = {
  /**
   * OK
   */
  200: ServiceTypesDto;
};

export type GetApiAppServiceTypesByIdResponse =
  GetApiAppServiceTypesByIdResponses[keyof GetApiAppServiceTypesByIdResponses];

export type PutApiAppServiceTypesByIdData = {
  body?: CreateUpdateServiceTypesDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/service-types/{id}";
};

export type PutApiAppServiceTypesByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppServiceTypesByIdError =
  PutApiAppServiceTypesByIdErrors[keyof PutApiAppServiceTypesByIdErrors];

export type PutApiAppServiceTypesByIdResponses = {
  /**
   * OK
   */
  200: ServiceTypesDto;
};

export type PutApiAppServiceTypesByIdResponse =
  PutApiAppServiceTypesByIdResponses[keyof PutApiAppServiceTypesByIdResponses];

export type GetApiStatusSettingsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/status-settings";
};

export type GetApiStatusSettingsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiStatusSettingsError =
  GetApiStatusSettingsErrors[keyof GetApiStatusSettingsErrors];

export type GetApiStatusSettingsResponses = {
  /**
   * OK
   */
  200: StatusSettingsDto;
};

export type GetApiStatusSettingsResponse =
  GetApiStatusSettingsResponses[keyof GetApiStatusSettingsResponses];

export type PatchApiStatusSettingsData = {
  body?: UpdateSettingDto;
  path?: never;
  query?: never;
  url: "/api/status-settings";
};

export type PatchApiStatusSettingsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PatchApiStatusSettingsError =
  PatchApiStatusSettingsErrors[keyof PatchApiStatusSettingsErrors];

export type PatchApiStatusSettingsResponses = {
  /**
   * OK
   */
  200: boolean;
};

export type PatchApiStatusSettingsResponse =
  PatchApiStatusSettingsResponses[keyof PatchApiStatusSettingsResponses];

export type PutApiStatusSettingsData = {
  body?: StatusSettingsDto;
  path?: never;
  query?: never;
  url: "/api/status-settings";
};

export type PutApiStatusSettingsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiStatusSettingsError =
  PutApiStatusSettingsErrors[keyof PutApiStatusSettingsErrors];

export type PutApiStatusSettingsResponses = {
  /**
   * OK
   */
  200: StatusSettingsDto;
};

export type PutApiStatusSettingsResponse =
  PutApiStatusSettingsResponses[keyof PutApiStatusSettingsResponses];

export type GetApiAppStatusSettingsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/app/status-settings";
};

export type GetApiAppStatusSettingsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppStatusSettingsError =
  GetApiAppStatusSettingsErrors[keyof GetApiAppStatusSettingsErrors];

export type GetApiAppStatusSettingsResponses = {
  /**
   * OK
   */
  200: StatusSettingsDto;
};

export type GetApiAppStatusSettingsResponse =
  GetApiAppStatusSettingsResponses[keyof GetApiAppStatusSettingsResponses];

export type PutApiAppStatusSettingsData = {
  body?: StatusSettingsDto;
  path?: never;
  query?: never;
  url: "/api/app/status-settings";
};

export type PutApiAppStatusSettingsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppStatusSettingsError =
  PutApiAppStatusSettingsErrors[keyof PutApiAppStatusSettingsErrors];

export type PutApiAppStatusSettingsResponses = {
  /**
   * OK
   */
  200: StatusSettingsDto;
};

export type PutApiAppStatusSettingsResponse =
  PutApiAppStatusSettingsResponses[keyof PutApiAppStatusSettingsResponses];

export type PostApiAppTaxListData = {
  body?: QueryParametersWritable;
  path?: never;
  query?: never;
  url: "/api/app/tax/list";
};

export type PostApiAppTaxListErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppTaxListError =
  PostApiAppTaxListErrors[keyof PostApiAppTaxListErrors];

export type PostApiAppTaxListResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfTaxDto;
};

export type PostApiAppTaxListResponse =
  PostApiAppTaxListResponses[keyof PostApiAppTaxListResponses];

export type GetApiAppTaxData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/tax";
};

export type GetApiAppTaxErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppTaxError = GetApiAppTaxErrors[keyof GetApiAppTaxErrors];

export type GetApiAppTaxResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfTaxDto;
};

export type GetApiAppTaxResponse =
  GetApiAppTaxResponses[keyof GetApiAppTaxResponses];

export type PostApiAppTaxData = {
  body?: CreateUpdateTaxDto;
  path?: never;
  query?: never;
  url: "/api/app/tax";
};

export type PostApiAppTaxErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppTaxError = PostApiAppTaxErrors[keyof PostApiAppTaxErrors];

export type PostApiAppTaxResponses = {
  /**
   * OK
   */
  200: TaxDto;
};

export type PostApiAppTaxResponse =
  PostApiAppTaxResponses[keyof PostApiAppTaxResponses];

export type DeleteApiAppTaxByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/tax/{id}";
};

export type DeleteApiAppTaxByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppTaxByIdError =
  DeleteApiAppTaxByIdErrors[keyof DeleteApiAppTaxByIdErrors];

export type DeleteApiAppTaxByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppTaxByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/tax/{id}";
};

export type GetApiAppTaxByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppTaxByIdError =
  GetApiAppTaxByIdErrors[keyof GetApiAppTaxByIdErrors];

export type GetApiAppTaxByIdResponses = {
  /**
   * OK
   */
  200: TaxDto;
};

export type GetApiAppTaxByIdResponse =
  GetApiAppTaxByIdResponses[keyof GetApiAppTaxByIdResponses];

export type PutApiAppTaxByIdData = {
  body?: CreateUpdateTaxDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/tax/{id}";
};

export type PutApiAppTaxByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppTaxByIdError =
  PutApiAppTaxByIdErrors[keyof PutApiAppTaxByIdErrors];

export type PutApiAppTaxByIdResponses = {
  /**
   * OK
   */
  200: TaxDto;
};

export type PutApiAppTaxByIdResponse =
  PutApiAppTaxByIdResponses[keyof PutApiAppTaxByIdResponses];

export type DeleteApiMultiTenancyTenantsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/multi-tenancy/tenants/{id}";
};

export type DeleteApiMultiTenancyTenantsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdError =
  DeleteApiMultiTenancyTenantsByIdErrors[keyof DeleteApiMultiTenancyTenantsByIdErrors];

export type DeleteApiMultiTenancyTenantsByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiMultiTenancyTenantsByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/multi-tenancy/tenants/{id}";
};

export type GetApiMultiTenancyTenantsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdError =
  GetApiMultiTenancyTenantsByIdErrors[keyof GetApiMultiTenancyTenantsByIdErrors];

export type GetApiMultiTenancyTenantsByIdResponses = {
  /**
   * OK
   */
  200: TenantDtoReadable;
};

export type GetApiMultiTenancyTenantsByIdResponse =
  GetApiMultiTenancyTenantsByIdResponses[keyof GetApiMultiTenancyTenantsByIdResponses];

export type PutApiMultiTenancyTenantsByIdData = {
  body?: TenantUpdateDtoWritable;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/multi-tenancy/tenants/{id}";
};

export type PutApiMultiTenancyTenantsByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdError =
  PutApiMultiTenancyTenantsByIdErrors[keyof PutApiMultiTenancyTenantsByIdErrors];

export type PutApiMultiTenancyTenantsByIdResponses = {
  /**
   * OK
   */
  200: TenantDtoReadable;
};

export type PutApiMultiTenancyTenantsByIdResponse =
  PutApiMultiTenancyTenantsByIdResponses[keyof PutApiMultiTenancyTenantsByIdResponses];

export type GetApiMultiTenancyTenantsData = {
  body?: never;
  path?: never;
  query?: {
    Filter?: string;
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/multi-tenancy/tenants";
};

export type GetApiMultiTenancyTenantsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsError =
  GetApiMultiTenancyTenantsErrors[keyof GetApiMultiTenancyTenantsErrors];

export type GetApiMultiTenancyTenantsResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfTenantDto;
};

export type GetApiMultiTenancyTenantsResponse =
  GetApiMultiTenancyTenantsResponses[keyof GetApiMultiTenancyTenantsResponses];

export type PostApiMultiTenancyTenantsData = {
  body?: TenantCreateDtoWritable;
  path?: never;
  query?: never;
  url: "/api/multi-tenancy/tenants";
};

export type PostApiMultiTenancyTenantsErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiMultiTenancyTenantsError =
  PostApiMultiTenancyTenantsErrors[keyof PostApiMultiTenancyTenantsErrors];

export type PostApiMultiTenancyTenantsResponses = {
  /**
   * OK
   */
  200: TenantDtoReadable;
};

export type PostApiMultiTenancyTenantsResponse =
  PostApiMultiTenancyTenantsResponses[keyof PostApiMultiTenancyTenantsResponses];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/multi-tenancy/tenants/{id}/default-connection-string";
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringError =
  DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/multi-tenancy/tenants/{id}/default-connection-string";
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringError =
  GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
  /**
   * OK
   */
  200: string;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponse =
  GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
  body?: never;
  path: {
    id: string;
  };
  query?: {
    defaultConnectionString?: string;
  };
  url: "/api/multi-tenancy/tenants/{id}/default-connection-string";
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringError =
  PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTestAuthorizationPublicData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/test-authorization/public";
};

export type GetApiTestAuthorizationPublicResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTestAuthorizationAuthenticatedData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/test-authorization/authenticated";
};

export type GetApiTestAuthorizationAuthenticatedResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTestAuthorizationAdminOnlyData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/test-authorization/admin-only";
};

export type GetApiTestAuthorizationAdminOnlyResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTestAuthorizationWismaRoomData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/test-authorization/wisma-room";
};

export type GetApiTestAuthorizationWismaRoomResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiOptionsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/options";
};

export type GetApiOptionsResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiCronTickersData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/cron-tickers";
};

export type GetApiCronTickersResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTimeTickersData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/time-tickers";
};

export type GetApiTimeTickersResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTimeTickersGraphDataRangeData = {
  body?: never;
  path?: never;
  query?: {
    pastDays?: number;
    futureDays?: number;
  };
  url: "/api/time-tickers/:graph-data-range";
};

export type GetApiTimeTickersGraphDataRangeResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTimeTickersGraphDataData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/time-tickers/:graph-data";
};

export type GetApiTimeTickersGraphDataResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiCronTickersGraphDataRangeData = {
  body?: never;
  path?: never;
  query?: {
    pastDays?: number;
    futureDays?: number;
  };
  url: "/api/cron-tickers/:graph-data-range";
};

export type GetApiCronTickersGraphDataRangeResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiCronTickersGraphDataRangeIdData = {
  body?: never;
  path?: never;
  query?: {
    id?: string;
    pastDays?: number;
    futureDays?: number;
  };
  url: "/api/cron-tickers/:graph-data-range-id";
};

export type GetApiCronTickersGraphDataRangeIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiCronTickersGraphDataData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/cron-tickers/:graph-data";
};

export type GetApiCronTickersGraphDataResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiCronTickerOccurrencesCronTickerIdData = {
  body?: never;
  path?: never;
  query?: {
    cronTickerId?: string;
  };
  url: "/api/cron-ticker-occurrences/:cronTickerId";
};

export type GetApiCronTickerOccurrencesCronTickerIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiCronTickerOccurrencesCronTickerIdGraphDataData = {
  body?: never;
  path?: never;
  query?: {
    cronTickerId?: string;
  };
  url: "/api/cron-ticker-occurrences/:cronTickerId/:graph-data";
};

export type GetApiCronTickerOccurrencesCronTickerIdGraphDataResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiTickerCancelData = {
  body?: never;
  path?: never;
  query?: {
    id?: string;
  };
  url: "/api/ticker/:cancel";
};

export type PostApiTickerCancelResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type DeleteTimeTickerDeleteData = {
  body?: never;
  path?: never;
  query?: {
    id?: string;
  };
  url: "/time-ticker/:delete";
};

export type DeleteTimeTickerDeleteResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type DeleteApiCronTickerDeleteData = {
  body?: never;
  path?: never;
  query?: {
    id?: string;
  };
  url: "/api/cron-ticker/:delete";
};

export type DeleteApiCronTickerDeleteResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type DeleteApiCronTickerOccurrenceDeleteData = {
  body?: never;
  path?: never;
  query?: {
    id?: string;
  };
  url: "/api/cron-ticker-occurrence/:delete";
};

export type DeleteApiCronTickerOccurrenceDeleteResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTickerRequestIdData = {
  body?: never;
  path?: never;
  query?: {
    tickerId?: string;
    tickerType?: TickerType;
  };
  url: "/api/ticker-request/:id";
};

export type GetApiTickerRequestIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTickerFunctionsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker-functions";
};

export type GetApiTickerFunctionsResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PutApiTimeTickerUpdateData = {
  body?: unknown;
  path?: never;
  query?: {
    id?: string;
  };
  url: "/api/time-ticker/:update";
};

export type PutApiTimeTickerUpdateResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiTimeTickerAddData = {
  body?: unknown;
  path?: never;
  query?: never;
  url: "/api/time-ticker/:add";
};

export type PostApiTimeTickerAddResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiCronTickerAddData = {
  body?: unknown;
  path?: never;
  query?: never;
  url: "/api/cron-ticker/:add";
};

export type PostApiCronTickerAddResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PutCronTickerUpdateData = {
  body?: unknown;
  path?: never;
  query?: {
    id?: string;
  };
  url: "/cron-ticker/:update";
};

export type PutCronTickerUpdateResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTickerHostNextTickerData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker-host/:next-ticker";
};

export type GetApiTickerHostNextTickerResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiTickerHostStopData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker-host/:stop";
};

export type PostApiTickerHostStopResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiTickerHostStartData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker-host/:start";
};

export type PostApiTickerHostStartResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type PostApiTickerHostRestartData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker-host/:restart";
};

export type PostApiTickerHostRestartResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTickerHostStatusData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker-host/:status";
};

export type GetApiTickerHostStatusResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTickerStatusesGetLastWeekData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker/statuses/:get-last-week";
};

export type GetApiTickerStatusesGetLastWeekResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTickerStatusesGetData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker/statuses/:get";
};

export type GetApiTickerStatusesGetResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiTickerMachineJobsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/ticker/machine/:jobs";
};

export type GetApiTickerMachineJobsResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppTypeFoodAndBeverageData = {
  body?: never;
  path?: never;
  query?: {
    Sorting?: string;
    SkipCount?: number;
    MaxResultCount?: number;
  };
  url: "/api/app/type-food-and-beverage";
};

export type GetApiAppTypeFoodAndBeverageErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppTypeFoodAndBeverageError =
  GetApiAppTypeFoodAndBeverageErrors[keyof GetApiAppTypeFoodAndBeverageErrors];

export type GetApiAppTypeFoodAndBeverageResponses = {
  /**
   * OK
   */
  200: PagedResultDtoOfTypeFoodAndBeverageDto;
};

export type GetApiAppTypeFoodAndBeverageResponse =
  GetApiAppTypeFoodAndBeverageResponses[keyof GetApiAppTypeFoodAndBeverageResponses];

export type PostApiAppTypeFoodAndBeverageData = {
  body?: CreateUpdateTypeFoodAndBeverageDto;
  path?: never;
  query?: never;
  url: "/api/app/type-food-and-beverage";
};

export type PostApiAppTypeFoodAndBeverageErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PostApiAppTypeFoodAndBeverageError =
  PostApiAppTypeFoodAndBeverageErrors[keyof PostApiAppTypeFoodAndBeverageErrors];

export type PostApiAppTypeFoodAndBeverageResponses = {
  /**
   * OK
   */
  200: TypeFoodAndBeverageDto;
};

export type PostApiAppTypeFoodAndBeverageResponse =
  PostApiAppTypeFoodAndBeverageResponses[keyof PostApiAppTypeFoodAndBeverageResponses];

export type DeleteApiAppTypeFoodAndBeverageByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/type-food-and-beverage/{id}";
};

export type DeleteApiAppTypeFoodAndBeverageByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type DeleteApiAppTypeFoodAndBeverageByIdError =
  DeleteApiAppTypeFoodAndBeverageByIdErrors[keyof DeleteApiAppTypeFoodAndBeverageByIdErrors];

export type DeleteApiAppTypeFoodAndBeverageByIdResponses = {
  /**
   * OK
   */
  200: unknown;
};

export type GetApiAppTypeFoodAndBeverageByIdData = {
  body?: never;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/type-food-and-beverage/{id}";
};

export type GetApiAppTypeFoodAndBeverageByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type GetApiAppTypeFoodAndBeverageByIdError =
  GetApiAppTypeFoodAndBeverageByIdErrors[keyof GetApiAppTypeFoodAndBeverageByIdErrors];

export type GetApiAppTypeFoodAndBeverageByIdResponses = {
  /**
   * OK
   */
  200: TypeFoodAndBeverageDto;
};

export type GetApiAppTypeFoodAndBeverageByIdResponse =
  GetApiAppTypeFoodAndBeverageByIdResponses[keyof GetApiAppTypeFoodAndBeverageByIdResponses];

export type PutApiAppTypeFoodAndBeverageByIdData = {
  body?: CreateUpdateTypeFoodAndBeverageDto;
  path: {
    id: string;
  };
  query?: never;
  url: "/api/app/type-food-and-beverage/{id}";
};

export type PutApiAppTypeFoodAndBeverageByIdErrors = {
  /**
   * Bad Request
   */
  400: RemoteServiceErrorResponse;
  /**
   * Unauthorized
   */
  401: RemoteServiceErrorResponse;
  /**
   * Forbidden
   */
  403: RemoteServiceErrorResponse;
  /**
   * Not Found
   */
  404: RemoteServiceErrorResponse;
  /**
   * Internal Server Error
   */
  500: RemoteServiceErrorResponse;
  /**
   * Not Implemented
   */
  501: RemoteServiceErrorResponse;
};

export type PutApiAppTypeFoodAndBeverageByIdError =
  PutApiAppTypeFoodAndBeverageByIdErrors[keyof PutApiAppTypeFoodAndBeverageByIdErrors];

export type PutApiAppTypeFoodAndBeverageByIdResponses = {
  /**
   * OK
   */
  200: TypeFoodAndBeverageDto;
};

export type PutApiAppTypeFoodAndBeverageByIdResponse =
  PutApiAppTypeFoodAndBeverageByIdResponses[keyof PutApiAppTypeFoodAndBeverageByIdResponses];

export type GetApiAppUserSynchronizationHealthData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/app/user-synchronization/health";
};

export type GetApiAppUserSynchronizationHealthResponses = {
  /**
   * OK
   */
  200: UserSynchronizationHealthDto;
};

export type GetApiAppUserSynchronizationHealthResponse =
  GetApiAppUserSynchronizationHealthResponses[keyof GetApiAppUserSynchronizationHealthResponses];

export type GetApiAppUserSynchronizationHealthDetailedData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/app/user-synchronization/health/detailed";
};

export type GetApiAppUserSynchronizationHealthDetailedResponses = {
  /**
   * OK
   */
  200: UserSynchronizationHealthDto;
};

export type GetApiAppUserSynchronizationHealthDetailedResponse =
  GetApiAppUserSynchronizationHealthDetailedResponses[keyof GetApiAppUserSynchronizationHealthDetailedResponses];

export type ClientOptions = {
  baseUrl: string;
};
