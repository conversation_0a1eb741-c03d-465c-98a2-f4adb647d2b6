import { postApiAppInvoiceDocumentGenerateWismaInvoice } from "@/client";
import StreamAttachment from "@/components/layout/preview-stream/stream-attachment";
import { useApiAttachmentStreamById } from "@/lib/hooks/useApiAppAttachmentById";
import { usePostApiInvoice } from "@/lib/hooks/useApiInvoices";
import React, { useEffect, useState } from "react";
import { PiX } from "react-icons/pi";
import { ActionIcon, Modal, Title } from "rizzui";

export default function ModalInvoice({
  modalPreview,
  setModalPreview,
  paymentId,
  attachmentId,
}: {
  modalPreview: boolean;
  setModalPreview: React.Dispatch<React.SetStateAction<boolean>>;
  paymentId?: string;
  attachmentId: string;
}) {
  const [blobUrl, setBlobUrl] = useState<string>();
  // const { data } = useApiAttachmentStreamById("B5E6E008-F009-035E-6FD9-3A1A18CB9F66");
  const { data } = useApiAttachmentStreamById(attachmentId);
  useEffect(() => { if (data && attachmentId) setBlobUrl(URL.createObjectURL(data as Blob)); }, [data]);
  
  return (
    <>
      {/* PREVIEW FORM */}
      <Modal
        isOpen={modalPreview}
        size={"xl"}
        onClose={() => setModalPreview(false)}
        customSize="80vw"
      >
        <div className="m-auto px-7 pb-8 pt-6">
          <div className="mb-4 flex items-center justify-between">
            <Title as="h3">Preview Invoice</Title>
            <ActionIcon
              size="sm"
              variant="text"
              onClick={() => setModalPreview(false)}
            >
              <PiX className="h-auto w-6" strokeWidth={1.8} />
            </ActionIcon>
          </div>
          <div className="mb-3">
            <iframe
              src={blobUrl}
              className="w-full h-[80vh] border-2 rounded-lg"
              title="PDF Preview"
            ></iframe>
            {/* {paymentId} */}
          </div>
        </div>
      </Modal>
    </>
  );
}
