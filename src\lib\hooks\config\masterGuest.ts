import { postApiAppGuestList } from "@/client";
import { useQuery } from "@tanstack/react-query";
import type { FilterGroup, SortInfo } from "@/client";
import { QueryNames } from "../QueryConstants";

export const useMasterGuest = (
  pageIndex?: number,
  pageSize?: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [QueryNames.GetGuest, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      const { data } = await postApiAppGuestList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
        },
      });

      return data;
    },
  });
};

export const useMasterGuestList = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [
      QueryNames.postApiAppGuestList,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
    ],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppGuestList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
        },
      });

      return data;
    },
  });
};
