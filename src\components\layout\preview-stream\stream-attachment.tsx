import { useApiAttachmentStreamById } from "@/lib/hooks/useApiAppAttachmentById";
import React, { useEffect, useState } from "react";
import { PiArrowSquareOut, PiX } from "react-icons/pi";
import { <PERSON>Icon, Button, Modal, Text, Title } from "rizzui";

export default function StreamAttachment({
  url,
  label,
  size = "sm",
}: {
  url: string;
  label?: string;
  size?: "xs" | "sm" | "md" | "lg" | "xl";
}) {
  const [blobUrl, setBlobUrl] = useState<string>();
  const [modal, setModal] = useState<boolean>(false);
  const { data } = useApiAttachmentStreamById(url);
  // const blobUrl = URL.createObjectURL(data as Blob);
  // console.log("data", data);

  useEffect(() => { if (data) setBlobUrl(URL.createObjectURL(data as Blob)); }, [data]);

  return (
    <>
      {/* PREVIEW FORM */}
      <div className="">
        {label &&
          <Text as="p" className={"w-full font-medium mb-0 " + ("text-" + size)}>{label}</Text>
        }
        <div className={"p-0 rounded " + (!label && "mt-5")}>
          <Button
            size="sm"
            className="h-8"
            onClick={() => { setModal(true) }}
          >
            <PiArrowSquareOut className="w-4 h-4" />
          </Button>
        </div>
      </div>
      <Modal
        isOpen={modal}
        size={"xl"}
        onClose={() => setModal(false)}
        customSize="80vw"
      >
        <div className="m-auto px-7 pb-8 pt-6">
          <div className="mb-3 flex items-center justify-between">
            <Title as="h3">Preview Reservation Attachment</Title>
            <ActionIcon
              size="sm"
              variant="text"
              onClick={() => setModal(false)}
            >
              <PiX className="h-auto w-6" strokeWidth={1.8} />
            </ActionIcon>
          </div>
          <div className="mb-3">
            {/* {blobUrl} */}
            <iframe
              src={blobUrl}
              className="w-full h-[80vh] border-2 rounded-lg"
              title="PDF Preview"
            ></iframe>
          </div>
        </div>
      </Modal>
    </>
  );
}
