export default function RefundIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" {...props}>
      <g fillRule="evenodd" clipRule="evenodd">
        <path
          fill="#ffd6bd"
          d="m66.95 358.24 33.97 126.79c8.21-6.83 15.55-12.33 22.4-16.63 8.02-5.35 16.16-9.04 24.87-10.82 9.23-2.01 18.43-1.64 28.82.74h.03L246 474.15c15.7 3.6 30.46 2.2 44.41-5.85l172.44-99.56a32.4 32.4 0 0 0 11.83-44.13 32.4 32.4 0 0 0-44.13-11.82l-120.1 69.33a35.88 35.88 0 0 0-24.84-44.78l-120.37-32.77c-15.98-2.11-30.49 1.76-43.28 11.56z"
        />
        <path
          fill="#f9c6aa"
          d="M271.09 390.47a35.9 35.9 0 0 0 40.86-17.56 35.4 35.4 0 0 1-1.23 8.29 35.83 35.83 0 0 1-43.87 25.1c-23.43-6.4-46.9-12.57-70.37-18.86a8.24 8.24 0 0 1-5.81-10.07 8.25 8.25 0 0 1 10.07-5.81c23.42 6.27 46.81 12.52 70.35 18.91z"
        />
        <path
          fill="#64b5f6"
          d="m75.63 331.38 41.53 154.99a7.14 7.14 0 0 1-5.03 8.71l-28.65 7.68a7.14 7.14 0 0 1-8.71-5.03L33.24 342.74a7.14 7.14 0 0 1 5.03-8.71l28.64-7.68a7.14 7.14 0 0 1 8.72 5.04z"
        />
        <circle
          cx="308.64"
          cy="159.02"
          r="106.22"
          fill="#fdb800"
          transform="matrix(.025 -1 1 .025 141.97 463.59)"
        />
        <circle cx="308.64" cy="159.02" r="81.21" fill="#ffda2d" />
        <path d="M306.76 167.01h3.81a8.92 8.92 0 0 1 0 17.82h-5.37a8.95 8.95 0 0 1-7.2-3.72 8 8 0 1 0-12.96 9.37 25.07 25.07 0 0 0 15.64 9.93v6.87a7.99 7.99 0 1 0 15.97 0v-7.2a24.93 24.93 0 0 0-6.08-49.07h-3.82a8.92 8.92 0 0 1 0-17.82h5.33c2.87 0 5.56 1.4 7.25 3.73a8 8 0 1 0 12.95-9.37 25.02 25.02 0 0 0-15.63-9.93v-6.87a7.99 7.99 0 1 0-15.97 0v7.2a24.95 24.95 0 0 0-18.84 24.15 24.96 24.96 0 0 0 24.92 24.91zm-71.32-8c0-40.36 32.83-73.2 73.2-73.2s73.25 32.84 73.25 73.2c0 40.37-32.88 73.21-73.25 73.21s-73.2-32.84-73.2-73.21zm-16.01 0c0-49.19 40.03-89.2 89.21-89.2 49.22 0 89.21 40.01 89.21 89.2 0 49.19-39.99 89.21-89.2 89.21-49.19 0-89.22-40.02-89.22-89.21zm-9 0c0-54.16 44.09-98.22 98.21-98.22 54.17 0 98.26 44.06 98.26 98.22s-44.09 98.23-98.26 98.23c-54.12 0-98.2-44.07-98.2-98.23zm-15.97 0c0-62.98 51.2-114.22 114.18-114.22 62.98 0 114.22 51.24 114.22 114.22 0 63-51.24 114.23-114.22 114.23S194.46 222 194.46 159.01zm273.3 169.6A24.1 24.1 0 0 0 453 317.3a24.21 24.21 0 0 0-18.46 2.4l-117.48 67.83a43.9 43.9 0 0 1-40.84 28.01c-3.76 0-7.63-.5-11.49-1.54l-64.3-17.5a8 8 0 0 1 4.2-15.44l64.34 17.5a27.75 27.75 0 0 0 14.55-53.53l-119.87-32.62c-13.7-1.7-25.81 1.59-36.84 10.04l-37.59 28.79 29.63 110.52c19.22-12.8 38.06-16.26 59.96-11.24l68.96 15.81c14.46 3.31 27.09 1.68 38.63-4.98l172.44-99.56a24.31 24.31 0 0 0 8.9-33.2zM109.2 487.57l-26.95 7.22L41.2 341.52l26.94-7.2zm357.65-111.92L294.42 475.2a64.52 64.52 0 0 1-32.79 8.77c-5.65 0-11.44-.68-17.38-2.05l-68.96-15.81c-19.12-4.38-34.85-.75-52 12.13l1.6 6.05c1.04 3.9.52 7.96-1.5 11.46a15 15 0 0 1-9.19 7.05l-28.64 7.67a15.11 15.11 0 0 1-18.51-10.69L25.5 344.81a15.03 15.03 0 0 1 1.56-11.46 14.89 14.89 0 0 1 9.14-7.05l28.64-7.68a15.1 15.1 0 0 1 18.5 10.7l1.42 5.21 32.36-24.77c14.5-11.1 31.04-15.53 49.17-13.13a8 8 0 0 1 1.04.21l120.4 32.77a43.75 43.75 0 0 1 32.03 37.89l106.78-61.65a40.1 40.1 0 0 1 30.61-3.99 39.86 39.86 0 0 1 24.45 18.74 40.33 40.33 0 0 1-14.74 55.06zm-316.1-210.28a156.92 156.92 0 0 1 41.77-113.5A156.87 156.87 0 0 1 302.28 1.13c87.05-3.5 160.76 64.46 164.25 151.52 3.53 87.06-64.44 160.74-151.53 164.25a157.35 157.35 0 0 1-85.77-21.28 7.98 7.98 0 0 1-2.88-10.95 7.99 7.99 0 0 1 10.93-2.88 141.4 141.4 0 0 0 77.1 19.12c78.25-3.15 139.34-69.37 136.18-147.62-3.15-78.24-69.38-139.33-147.62-136.17a141.07 141.07 0 0 0-98.68 45.6 140.99 140.99 0 0 0-37.5 102.02c.9 22.8 7.17 44.63 18.24 64.15l.47-5.32a8 8 0 0 1 15.92 1.32l-2.31 27.99a8.02 8.02 0 0 1-9.7 7.15l-27.42-6a8 8 0 1 1 3.44-15.63l7.58 1.66a157.01 157.01 0 0 1-22.23-74.68z" />
      </g>
    </svg>
  );
}
