"use server";

import { type ReservationTypesResponse } from "@/interfaces/reservationTypes/reservationTypes";
import axios from "axios";

export const getDataReservationTypes = () => {
  return axios.get<ReservationTypesResponse>(`${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/reservation-types`)
    .then((response) => {
      // console.log('GET getDataReservationTypes', response.data);
      return response.data;
    })
    .catch((error) => {
      console.error("Error fetching reservations:", error);
      throw error;
    });
}


// --- Example response data structure ---
// {
//   "totalCount": 1,
//   "items": [
//     {
//       "name": "INDIVIDU (BSG)",
//       "status": 1,
//       "lastModificationTime": null,
//       "lastModifierId": null,
//       "creationTime": "2025-04-22T00:34:51.9733476",
//       "creatorId": null,
//       "id": "9396072e-f88f-b34d-09a8-3a196d18238a"
//     }
//   ]
// }