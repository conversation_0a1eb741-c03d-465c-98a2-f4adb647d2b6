export default function SuitcaseIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" {...props}>
      <g fillRule="evenodd" clipRule="evenodd">
        <path
          fill="#ce9875"
          d="M494.036 176.994v197.543c0 21.714-17.773 39.515-39.486 39.515H57.478c-21.742 0-39.514-17.801-39.514-39.515V176.994z"
        />
        <path
          fill="#c6865c"
          d="M494.036 176.994v197.543c0 21.714-17.773 39.515-39.486 39.515h-24.292c21.739 0 39.513-17.801 39.513-39.515V176.994z"
        />
        <path
          fill="#c6865c"
          d="M96.963 255.994h318.072c43.454 0 79.001-35.546 79.001-79V137.48c0-21.742-17.773-39.515-39.486-39.515H57.478c-21.742 0-39.514 17.772-39.514 39.515v39.515c0 43.454 35.545 78.999 78.999 78.999z"
        />
        <path
          fill="#b16e3d"
          d="M390.772 255.994h24.264c43.454 0 79.001-35.546 79.001-79V137.48c0-21.742-17.773-39.515-39.486-39.515h-24.292c21.739 0 39.513 17.772 39.513 39.515v39.515c-.001 43.454-35.545 78.999-79 78.999z"
        />
        <path
          fill="#0c5e7f"
          d="M146.853 123.193c0-5.301 2.834-9.41 6.746-12.387 14.23-10.914 23.387-28.092 23.387-47.423 0-22.648-12.898-41.867-31.238-52.498-5.216-3.033-7.341.794-7.341 4.536v40.081c0 5.104-2.24 9.411-6.52 12.33l-8.135 5.499c-4.48 3.033-8.59 3.033-13.068 0l-8.107-5.499c-3.826-2.579-6.549-6.519-6.549-12.33V15.421c0-3.686-2.777-7.115-7.199-4.62-18.51 10.431-31.351 29.877-31.351 52.582 0 19.444 9.298 36.651 23.612 47.621 5.102 3.884 6.49 7.059 6.49 12.188v265.604c0 5.13-1.389 8.305-6.49 12.217-14.314 10.97-23.612 28.176-23.612 47.593 0 22.734 12.841 42.18 31.351 52.582 4.422 2.495 7.199-.906 7.199-4.592v-40.082c0-5.811 2.723-9.75 6.549-12.329l8.107-5.5c4.478-3.032 8.588-3.061 13.068 0l8.135 5.5c4.279 2.919 6.52 7.227 6.52 12.329v40.082c0 3.742 2.125 7.568 7.341 4.535 18.34-10.629 31.238-29.849 31.238-52.525 0-19.303-9.156-36.509-23.387-47.395-3.912-3.005-6.746-7.087-6.746-12.415z"
        />
        <path
          fill="#174b67"
          d="M146.853 123.193c0-5.301 2.834-9.41 6.746-12.387 14.23-10.914 23.387-28.092 23.387-47.423 0-22.648-12.898-41.867-31.238-52.498-5.216-3.033-7.341.794-7.341 4.536v10.062c8.616 10.347 13.89 23.415 13.89 37.899 0 19.331-9.156 36.509-23.386 47.423-3.912 2.976-6.746 7.086-6.746 12.387v265.604c0 5.328 2.834 9.41 6.746 12.415 14.229 10.886 23.386 28.092 23.386 47.395 0 14.514-5.273 27.581-13.89 37.928v10.062c0 3.742 2.125 7.568 7.341 4.535 18.34-10.629 31.238-29.849 31.238-52.525 0-19.303-9.156-36.509-23.387-47.395-3.912-3.005-6.746-7.087-6.746-12.415z"
        />
        <path
          fill="#ffbe83"
          d="M222.764 228.357h66.473c4.252 0 7.765 3.486 7.765 7.767v39.771c0 4.28-3.513 7.767-7.765 7.767h-66.473c-4.252 0-7.767-3.486-7.767-7.767v-39.771c0-4.281 3.515-7.767 7.767-7.767z"
        />
        <path
          fill="#ffab68"
          d="M264.971 228.357h24.266c4.252 0 7.765 3.486 7.765 7.767v39.771c0 4.28-3.513 7.767-7.765 7.767h-24.266c4.252 0 7.768-3.486 7.768-7.767v-39.771c0-4.281-3.516-7.767-7.768-7.767z"
        />
      </g>
      <path d="M25.447 176.994c0 17.942 6.689 34.412 17.688 46.969 9.582 10.97 22.423 18.992 36.964 22.535V119.48a68.009 68.009 0 0 1-15.336-14.003h-7.284c-8.814 0-16.809 3.6-22.619 9.383-5.812 5.811-9.412 13.833-9.412 22.62v39.514zm217.215 86.512c-4.139 0-7.482-3.373-7.482-7.512s3.344-7.483 7.482-7.483h26.674a7.476 7.476 0 0 1 7.484 7.483c0 4.139-3.346 7.512-7.484 7.512zm56.04-173.054V58.054h-85.406v32.398zm-100.4 0v-39.91a7.475 7.475 0 0 1 7.482-7.483h100.43a7.476 7.476 0 0 1 7.484 7.483v39.91H454.55c12.926 0 24.69 5.301 33.193 13.806 8.531 8.532 13.805 20.296 13.805 33.222V292.846a7.507 7.507 0 0 1-7.512 7.511c-4.139 0-7.484-3.373-7.484-7.511v-67.323a90.62 90.62 0 0 1-10.402 12.586c-15.676 15.676-37.331 25.398-61.114 25.398h-110.55v20.154c0 4.139-3.346 7.512-7.484 7.512h-82.004c-4.139 0-7.482-3.373-7.482-7.512v-20.154h-53.15v129.031a67.848 67.848 0 0 1 15.307 14.003H454.55c8.787 0 16.781-3.6 22.592-9.411s9.41-13.805 9.41-22.592v-47.111c0-4.166 3.346-7.512 7.484-7.512a7.49 7.49 0 0 1 7.512 7.512v47.111c0 12.926-5.301 24.69-13.805 33.193-8.504 8.532-20.268 13.805-33.193 13.805H178.8c3.656 8.306 5.668 17.462 5.668 27.07 0 14.145-4.365 27.298-11.877 38.154a67.283 67.283 0 0 1-30.981 24.52c-4.904 2.324-10.688-1.247-10.688-6.774v-51.96l-13.691-9.268-13.691 9.268v51.96c0 5.159-5.215 8.844-10.146 7.001-12.869-4.875-23.84-13.605-31.55-24.746-7.482-10.857-11.877-24.01-11.877-38.154a67.174 67.174 0 0 1 5.697-27.099c-12.217-.453-23.271-5.641-31.406-13.776-8.504-8.504-13.805-20.268-13.805-33.193V137.48c0-12.954 5.301-24.689 13.805-33.222 8.135-8.107 19.189-13.295 31.406-13.748a67.204 67.204 0 0 1-5.697-27.127c0-14.116 4.395-27.27 11.877-38.126C69.44 14.259 80.211 5.641 92.824.709c4.934-2.296 10.715 1.247 10.715 6.803v51.931l13.691 9.297 13.691-9.297V7.512c0-5.158 5.217-8.844 10.121-7.001a67.256 67.256 0 0 1 31.548 24.746c7.512 10.857 11.877 24.01 11.877 38.126 0 9.637-2.012 18.793-5.668 27.069zM80.098 261.806c-19.048-3.799-35.858-13.891-48.188-27.979a87.49 87.49 0 0 1-6.463-8.334v149.044c0 8.787 3.6 16.781 9.412 22.592 5.81 5.811 13.805 9.411 22.619 9.411h7.284a68.009 68.009 0 0 1 15.336-14.003zm-8.135 160.608c-4.451 7.683-7.002 16.64-7.002 26.191 0 11.055 3.4 21.26 9.184 29.65 3.855 5.556 8.73 10.347 14.371 14.06v-43.71c0-2.834 1.588-5.3 3.912-6.575l20.607-13.975c2.637-1.786 6.039-1.645 8.504.084l21.062 14.287a7.468 7.468 0 0 1 3.316 6.179v43.71a52.574 52.574 0 0 0 14.371-14.06c5.812-8.391 9.186-18.596 9.186-29.65 0-9.552-2.553-18.509-7.002-26.191-4.621-7.937-11.367-14.627-19.332-19.19-2.353-1.332-3.771-3.826-3.798-6.52V204.687c0-4.139 3.372-7.512 7.511-7.512s7.512 3.373 7.512 7.512v43.823h53.15v-20.154c0-4.139 3.344-7.512 7.482-7.512h82.004c4.139 0 7.484 3.373 7.484 7.512v20.154h110.55c19.645 0 37.53-8.051 50.483-21.033 12.984-12.953 21.033-30.84 21.033-50.483V137.48c0-8.787-3.6-16.81-9.41-22.592-5.81-5.812-13.805-9.411-22.592-9.411H169.67a67.85 67.85 0 0 1-15.307 14.003v50.626c0 4.139-3.373 7.512-7.512 7.512s-7.511-3.373-7.511-7.512v-54.821c0-2.892 1.672-5.414 4.081-6.661 7.852-4.536 14.484-11.169 19.049-19.021 4.449-7.682 7.002-16.64 7.002-26.22 0-11.026-3.373-21.231-9.186-29.622-3.826-5.584-8.73-10.347-14.371-14.06v43.682c0 2.834-1.588 5.3-3.883 6.575l-20.636 13.975c-2.637 1.814-6.01 1.673-8.477-.057l-21.09-14.314a7.578 7.578 0 0 1-3.316-6.179V19.702a52.592 52.592 0 0 0-14.368 14.059c-5.783 8.391-9.184 18.596-9.184 29.622 0 9.58 2.551 18.538 7.002 26.22a52.794 52.794 0 0 0 19.359 19.19 7.506 7.506 0 0 1 3.771 6.491v281.42a7.47 7.47 0 0 1-4.055 6.661c-7.85 4.564-14.512 11.198-19.075 19.049zM289.49 235.84h-66.982v40.309h66.982z" />
    </svg>
  );
}
