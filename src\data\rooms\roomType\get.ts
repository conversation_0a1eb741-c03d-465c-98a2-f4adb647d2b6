"use server";
import { getSession } from "@/lib/actions";
import type { RoomTypeResponse } from "@/interfaces/rooms/roomType";
import axios from "axios";

export const getRoomType = async () => {
  try {
    const session = await getSession();

    const res = await axios.get<RoomTypeResponse>(
      `${process.env.NEXT_PUBLIC_API_WISMA_DEV}/api/app/room-type`,
      {
        headers: {
          Authorization: "Bearer " + session?.access_token,
          "Accept-Language": "en_US",
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      },
    );

    return res.data.items;
  } catch (error) {
    console.error("Failed to fetch rooms:", error);
    return [];
  }
};
