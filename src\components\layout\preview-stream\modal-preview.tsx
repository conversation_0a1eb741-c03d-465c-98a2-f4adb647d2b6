// components/ModalPreview.tsx
import React from "react";
import { PiX } from "react-icons/pi";
import { ActionIcon, Modal, Title } from "rizzui";

interface ModalPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  blobUrl?: string;
  title?: string;
}

export default function ModalPreview({
  isOpen,
  onClose,
  blobUrl,
  title = "Preview Registration Card",
}: ModalPreviewProps) {
  return (
    <Modal isOpen={isOpen} size="xl" onClose={onClose} customSize="80vw">
      <div className="m-auto px-7 pb-8 pt-6">
        <div className="mb-3 flex items-center justify-between">
          <Title as="h3">{title}</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <PiX className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>
        <div className="mb-3">
          <iframe
            src={blobUrl}
            className="h-[80vh] w-full rounded-lg border-2"
            title="PDF Preview"
          ></iframe>
        </div>
      </div>
    </Modal>
  );
}
