export default function VisaIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlSpace="preserve"
      viewBox="0 0 504 504"
      {...props}
    >
      <path
        d="m184.8 324.4 25.6-144h40l-24.8 144z"
        style={{
          fill: '#3c58bf',
        }}
      />
      <path
        d="m184.8 324.4 32.8-144h32.8l-24.8 144z"
        style={{
          fill: '#293688',
        }}
      />
      <path
        d="M370.4 182c-8-3.2-20.8-6.4-36.8-6.4-40 0-68.8 20-68.8 48.8 0 21.6 20 32.8 36 40s20.8 12 20.8 18.4c0 9.6-12.8 14.4-24 14.4-16 0-24.8-2.4-38.4-8l-5.6-2.4-5.6 32.8c9.6 4 27.2 8 45.6 8 42.4 0 70.4-20 70.4-50.4 0-16.8-10.4-29.6-34.4-40-14.4-7.2-23.2-11.2-23.2-18.4 0-6.4 7.2-12.8 23.2-12.8 13.6 0 23.2 2.4 30.4 5.6l4 1.6 6.4-31.2z"
        style={{
          fill: '#3c58bf',
        }}
      />
      <path
        d="M370.4 182c-8-3.2-20.8-6.4-36.8-6.4-40 0-61.6 20-61.6 48.8 0 21.6 12.8 32.8 28.8 40s20.8 12 20.8 18.4c0 9.6-12.8 14.4-24 14.4-16 0-24.8-2.4-38.4-8l-5.6-2.4-5.6 32.8c9.6 4 27.2 8 45.6 8 42.4 0 70.4-20 70.4-50.4 0-16.8-10.4-29.6-34.4-40-14.4-7.2-23.2-11.2-23.2-18.4 0-6.4 7.2-12.8 23.2-12.8 13.6 0 23.2 2.4 30.4 5.6l4 1.6 6.4-31.2z"
        style={{
          fill: '#293688',
        }}
      />
      <path
        d="M439.2 180.4c-9.6 0-16.8.8-20.8 10.4l-60 133.6h43.2l8-24h51.2l4.8 24H504l-33.6-144h-31.2zm-18.4 96c2.4-7.2 16-42.4 16-42.4s3.2-8.8 5.6-14.4l2.4 13.6s8 36 9.6 44h-33.6v-.8z"
        style={{
          fill: '#3c58bf',
        }}
      />
      <path
        d="M448.8 180.4c-9.6 0-16.8.8-20.8 10.4l-69.6 133.6h43.2l8-24h51.2l4.8 24H504l-33.6-144h-21.6zm-28 96c3.2-8 16-42.4 16-42.4s3.2-8.8 5.6-14.4l2.4 13.6s8 36 9.6 44h-33.6v-.8z"
        style={{
          fill: '#293688',
        }}
      />
      <path
        d="m111.2 281.2-4-20.8c-7.2-24-30.4-50.4-56-63.2l36 128h43.2l64.8-144H152l-40.8 100z"
        style={{
          fill: '#3c58bf',
        }}
      />
      <path
        d="m111.2 281.2-4-20.8c-7.2-24-30.4-50.4-56-63.2l36 128h43.2l64.8-144H160l-48.8 100z"
        style={{
          fill: '#293688',
        }}
      />
      <path
        d="m0 180.4 7.2 1.6c51.2 12 86.4 42.4 100 78.4l-14.4-68c-2.4-9.6-9.6-12-18.4-12H0z"
        style={{
          fill: '#ffbc00',
        }}
      />
      <path
        d="M0 180.4c51.2 12 93.6 43.2 107.2 79.2l-13.6-56.8c-2.4-9.6-10.4-15.2-19.2-15.2L0 180.4z"
        style={{
          fill: '#f7981d',
        }}
      />
      <path
        d="M0 180.4c51.2 12 93.6 43.2 107.2 79.2l-9.6-31.2c-2.4-9.6-5.6-19.2-16.8-23.2L0 180.4z"
        style={{
          fill: '#ed7c00',
        }}
      />
      <path
        d="M151.2 276.4 124 249.2l-12.8 30.4-3.2-20c-7.2-24-30.4-50.4-56-63.2l36 128h43.2l20-48zm74.4 48-34.4-35.2-6.4 35.2zm92-49.6c3.2 3.2 4.8 5.6 4 8.8 0 9.6-12.8 14.4-24 14.4-16 0-24.8-2.4-38.4-8l-5.6-2.4-5.6 32.8c9.6 4 27.2 8 45.6 8 25.6 0 46.4-7.2 58.4-20l-34.4-33.6zm46.4 49.6h37.6l8-24h51.2l4.8 24H504L490.4 266l-48-46.4 2.4 12.8s8 36 9.6 44h-33.6c3.2-8 16-42.4 16-42.4s3.2-8.8 5.6-14.4"
        style={{
          fill: '#051244',
        }}
      />
    </svg>
  );
}
