"use client"
import React, { useState } from "react";
import { AdvancedRadio, Button, RadioGroup, Text } from "rizzui";
import { FieldValues, useForm } from "react-hook-form";
import { DateInput } from "@/components/theme/ui/input-type/dates-input";
import { PiCheckCircleFill } from "react-icons/pi";
import { useReportRoomDaily, useReportRoomMonthly } from "@/lib/hooks/useReportRoom";
import { ReportRoomHotTable } from "../_component/reservation-group-hot-table";
import { formatDate } from "@/lib/helper/format-date";

export default function ReportRoom() {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const [reportType, setReportType] = useState("daily");
  const reportTypeOptions = [
    {
      value: 'daily',
      title: 'Daily Report',
      // description: 'daily activities and operational data.',
      description: '',
    },
    {
      value: 'monthly',
      title: 'Monthly Report',
      // description: 'summary of rooms data over the month.',
      description: '',
    }
  ]

  const dailyReport = useReportRoomDaily("", "");
  const monthLyReport = useReportRoomMonthly("", "");
  const report = reportType == 'daily' ? dailyReport : monthLyReport;

  const columnsDaily = [
    {
      title: "TYPE",
      data: "type",
      type: "text",
      width: 100,
    },
    {
      title: "TOTAL",
      data: "total",
      type: "text",
      width: 100,
    },
    {
      title: "MOD",
      data: "mod",
      type: "text",
      width: 100,
    },
    {
      title: "MTC",
      data: "mtc",
      type: "text",
      width: 100,
    },
    {
      title: "LS",
      data: "ls",
      type: "text",
      width: 100,
    },
    {
      title: "TAMU C/I",
      data: "tamu_ci",
      type: "text",
      width: 100,
    },
    {
      title: "SISA",
      data: "sisa",
      type: "text",
      width: 100,
    },
    {
      title: "% KOSONG",
      data: "persen_kosong",
      type: "text",
      width: 100,
    },
  ]
  const columnsMonthly = [
    {
      title: "DATE",
      data: "date",
      type: "text",
      width: 100,
    },
    {
      title: "TOTAL KAMAR",
      data: "total_kamar",
      type: "text",
      width: 100,
    },
    {
      title: "MOD + MTC",
      data: "mod_mtc",
      type: "text",
      width: 100,
    },
    {
      title: "LS",
      data: "mod_mtc",
      type: "text",
      width: 100,
    },
    {
      title: "SEMUA TAMU",
      data: "tamu",
      type: "text",
      width: 100,
    },
    {
      title: "SISA KAMAR",
      data: "sisa",
      type: "text",
      width: 100,
    },
    {
      title: "% KOSONG",
      data: "persen_kosong",
      type: "text",
      width: 100,
    },
  ]
  const columns = reportType == 'daily' ? columnsDaily : columnsMonthly;

  const reportTitle =
    reportType == 'daily' ?
      `Daily Report at ${formatDate(String(watch('dateStart')), 'date')}` :
      `Monthly Report at ${formatDate(String(watch('dateStart')), 'date')} to ${formatDate(String(watch('dateEnd')), 'date')}`;


  async function onSubmit(submitData: FieldValues) {
    try {
      console.log('submitData', submitData)
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  }
  return (<div className={"mb-2 @container"}>
    {/* <PageHeaderCustom
      // title={"Reservations"}
      breadcrumb={[
        { name: "Report" },
        { name: "Room" },
      ]}
    >
    </PageHeaderCustom> */}
    <div className="flex justify-center">
      {/* FORM FILTER */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="flex gap-4">
          <div className="">
            <Text as="p" className={"mb-1.5 font-medium"}>
              Select Report type
            </Text>
            <RadioGroup
              value={reportType}
              setValue={setReportType}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // console.log('e', e.target.value)
                setValue('reportType', e.target.value)
              }}
              className="grid grid-cols-1 sm:grid-cols-2 max-w-md mx-auto gap-4 mb-3"
            >
              {reportTypeOptions.map((item) => (
                <AdvancedRadio
                  key={item.value}
                  name="payment"
                  value={item.value}
                  inputClassName="[&:checked~span_.icon]:block"
                >
                  <span className="flex justify-between">
                    <Text as="b">{item.title}</Text>
                    <PiCheckCircleFill className="icon hidden h-5 w-5 text-secondary" />
                  </span>
                  <Text>{item.description}</Text>
                </AdvancedRadio>
              ))}
            </RadioGroup>
          </div>
          <div className="flex gap-4">
            <DateInput
              label={"Select Report Date"}
              name={"dateStart"}
              register={register}
              setValue={setValue}
              errors={errors}
              watch={watch}
              getValues={getValues}
            // required={true}
            // size="sm"
            // inline={true}
            />
            {
              reportType == 'monthly' &&
              <DateInput
                label={"Select Report Date"}
                name={"dateEnd"}
                register={register}
                setValue={setValue}
                errors={errors}
                watch={watch}
                getValues={getValues}
              // className="w-full"
              // required={true}
              // size="sm"
              // inline={true}
              />
            }
            <div className="pt-6">
              <Button
                // size="sm"
                type="submit"
                // disabled={isLoading}
                className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-green-500`}
              >
                Generate Report
              </Button>
            </div>
            <div className="pt-6">
              <Button
                // size="sm"
                type="button"
                // disabled={isLoading}
                className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 bg-blue-500`}
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = '/reportRoomSample.xlsx';
                  link.download = 'reportRoomSample.xlsx'; // Optional: customize file name
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                Download Report
              </Button>
            </div>
          </div>
        </div>
        {/* {JSON.stringify(errors)} */}
      </form>

    </div>

    <div className="w-full">
      {/* HOT TABLE */}
      <ReportRoomHotTable
        isLoading={false}
        title={reportTitle}
        data={report}
        columns={columns}
      />
    </div>
  </div>
  );
}
