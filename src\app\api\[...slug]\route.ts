﻿import { getSession } from '@/lib/actions'
import { type NextRequest, NextResponse } from 'next/server'

const EXTERNAL_API_URL = process.env.NEXT_PUBLIC_API_URL
const IDENTITY_API_URL = process.env.NEXT_PUBLIC_IMIP_AUTH_URL

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE'

// Define more specific types for API errors and responses
interface ApiErrorDetails {
  message?: string;
  [key: string]: unknown;
}

interface ApiError extends Error {
  status?: number;
  details?: ApiErrorDetails;
}

// Define a type for file-like objects
interface FileObject {
  name: string;
  size: number;
  type: string;
  [key: string]: unknown;
}

const getHeaders = async (_method: RequestMethod, contentType?: string): Promise<HeadersInit> => {
  const session = await getSession()
  // // console.log('bearer', session.access_token)
  return {
    Authorization: `Bearer ${session.access_token ?? ''}`,
    'Content-Type': contentType ?? 'application/json',
    Accept: 'application/json',
    __tenant: session.tenantId ?? '',
  }
}

const makeApiRequest = async (
  request: NextRequest,
  method: RequestMethod,
  includeBody = false
): Promise<Response> => {
  try {
    const path = request.nextUrl.pathname

    // Determine which base URL to use based on the path
    // console.log('path', path)
    const useIdentityApi = path.startsWith('/auth/') || path === '/api/abp/application-configuration'
    const baseUrl = useIdentityApi ? IDENTITY_API_URL : EXTERNAL_API_URL
    const url = `${baseUrl}${path}${request.nextUrl.search}`

    // console.log(`Using ${useIdentityApi ? 'IDENTITY_API_URL' : 'EXTERNAL_API_URL'} for path: ${path}`)

    // Check if the request is a multipart/form-data request (file upload)
    const contentType = request.headers.get('content-type') ?? '';
    const isFormData = contentType.includes('multipart/form-data');

    // For file uploads, we need special handling
    if (isFormData && method === 'POST') {
      // console.log('Handling file upload to:', url);
      // console.log(`Using ${useIdentityApi ? 'IDENTITY_API_URL' : 'EXTERNAL_API_URL'} for file upload to path: ${path}`);

      try {
        // Extract the form data from the request
        const formData = await request.formData();
        // console.log('Form data keys:', [...formData.keys()]);

        // Create a new FormData to send to backend
        const newFormData = new FormData();

        // Log what fields we're getting
        for (const [key, value] of formData.entries()) {
          // Type guard to check if the value is file-like
          const isFileLike = (val: unknown): val is FileObject => {
            return typeof val === 'object' &&
              val !== null &&
              'name' in val &&
              'size' in val &&
              'type' in val;
          };

          // console.log(`Form field: ${key}, Type: ${typeof value}, Is File-like: ${isFileLike(value)}`);

          if (isFileLike(value)) {
            // This is a file
            // console.log(`File: ${value.name}, Size: ${value.size}, Type: ${value.type}`);

            // Add with both lowercase and uppercase key names to maximize compatibility
            newFormData.append('file', value as Blob);
            newFormData.append('File', value as Blob);
          } else {
            // For other fields, add with both casing variants
            newFormData.append(key.toLowerCase(), value);

            // Only capitalize the first letter if the key has at least one character
            if (key.length > 0) {
              newFormData.append(key.charAt(0).toUpperCase() + key.slice(1), value);
            }
          }
        }

        // Get session and set authorization header only
        const session = await getSession();

        // Make the fetch request with the new FormData
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            '__tenant': session.tenantId ?? '',
          },
          body: newFormData,
        });

        // Handle response
        if (!response.ok) {
          let errorDetails: Record<string, unknown> = {};
          try {
            errorDetails = await response.json() as Record<string, unknown>;
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (_e) {
            // If we can't parse JSON, use text
            const text = await response.text();
            errorDetails = { message: text };
          }

          console.error('File upload error:', {
            status: response.status,
            details: errorDetails
          });

          return NextResponse.json(
            {
              error: 'File upload failed',
              details: errorDetails,
              status: response.status
            },
            { status: response.status }
          );
        }

        // Return the successful response
        const responseData = await response.json() as Record<string, unknown>;
        return NextResponse.json(responseData);
      } catch (error) {
        console.error('Error processing form data:', error);
        return NextResponse.json(
          {
            error: 'Failed to process file upload',
            message: (error as Error).message,
            status: 500
          },
          { status: 500 }
        );
      }
    }

    // For non-form-data requests, use the original approach
    const headers = await getHeaders(method, isFormData ? undefined : contentType);

    // For regular multipart/form-data requests, don't set the Content-Type header
    if (isFormData) {
      const headersObj = headers as Record<string, string>;
      if (headersObj['Content-Type']) {
        delete headersObj['Content-Type'];
      }
    }

    // console.log('Forwarding regular request to:', url);
    // console.log('With content type:', contentType);

    const options: RequestInit = {
      method,
      headers,
      ...(includeBody && {
        body: request.body,
        duplex: 'half',
      }),
      cache: 'no-store',
    };

    const response = await fetch(url, options)

    // Clone the response to handle streaming
    const clonedResponse = response.clone()

    if (!response.ok) {
      let errorMessage = `API request failed with status ${response.status}`
      let errorDetails: Record<string, unknown> = {}

      try {
        const errorData = await clonedResponse.json() as Record<string, unknown>
        errorMessage = typeof errorData.message === 'string' ? errorData.message : errorMessage
        errorDetails = errorData
      } catch {
        // Use default error message if JSON parsing fails
      }

      // Log the error for server-side debugging
      console.error('API request error:', {
        url,
        status: response.status,
        message: errorMessage,
        details: errorDetails,
      })

      // Return an error response that will be propagated to the client
      return NextResponse.json(
        {
          error: errorMessage,
          message: errorMessage,
          details: errorDetails,
          status: response.status,
        },
        { status: response.status }
      )
    }

    // Handle different content types
    const responseContentType = response.headers.get('content-type');
    if (responseContentType?.includes('application/json')) {
      try {
        const data = await clonedResponse.json() as Record<string, unknown>
        return NextResponse.json(data, {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      } catch (jsonError) {
        return NextResponse.json(
          {
            error: 'Invalid JSON response from server',
            message: 'The server returned invalid JSON data',
            details: {
              originalStatus: response.status,
              parseError: (jsonError as Error).message,
            },
            status: 500,
          },
          { status: 500 }
        )
      }
    }

    return response
  } catch (error) {
    // Type guard to check if the error is an ApiError
    const isApiError = (err: unknown): err is ApiError => {
      return err instanceof Error &&
        (('status' in err) || ('details' in err));
    };

    // Create a properly typed ApiError
    let apiError: ApiError;
    if (isApiError(error)) {
      apiError = error;
    } else {
      apiError = new Error('Unknown error') as ApiError;
      apiError.status = 500;
      apiError.details = {};
    }
    // console.log('API request error throw:', apiError);

    return NextResponse.json(
      {
        error: apiError.message,
        message: apiError.message,
        details: apiError.details ?? {},
        status: apiError.status ?? 500,
      },
      { status: apiError.status ?? 500 }
    )
  }
}

export async function GET(request: NextRequest): Promise<Response> {
  return makeApiRequest(request, 'GET')
}

export async function POST(request: NextRequest): Promise<Response> {
  return makeApiRequest(request, 'POST', true)
}

export async function PUT(request: NextRequest): Promise<Response> {
  return makeApiRequest(request, 'PUT', true)
}

export async function DELETE(request: NextRequest): Promise<Response> {
  return makeApiRequest(request, 'DELETE')
}
