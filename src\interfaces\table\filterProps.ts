import { type Column } from "./tableType";

export interface FilterProps {
  column: Column;
  searchTerms: Record<string, string>;
  handleTextFilterChange: (dataIndex: string, value: string) => void;
  selectFilters: Record<string, string[]>;
  setSelectFilters: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >;
  dateFilters: Record<string, string>;
  setDateFilters: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  handleDateFilterChange: (dataIndex: string, value: string) => void;
  tempSelectFilters: Record<string, string[]>;
  setTempSelectFilters: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >;
  handleSelectFilterChange: (dataIndex: string, values: string[]) => void;
  filterSelectTable?: Record<string, string[]>;
  showDropdown: string | null;
  setShowDropdown: React.Dispatch<React.SetStateAction<string | null>>;
}
