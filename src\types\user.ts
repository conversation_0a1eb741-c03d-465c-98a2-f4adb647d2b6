import { type StaticImageData } from "next/image";

export interface User {
  id: string;
  name?: string;
  username?: string;
  company?: string;
  department?: string;
  position?: string;
  email?: string;
  picture?: string;
  email_verified_at?: string;
}

export interface ISiteConfig {
  title: string;
  description: string;
  logo: string | StaticImageData;
  logoWhite: string | StaticImageData;
  icon: string | StaticImageData;
  mode: string;
  layout: string;
}
