import { getApiAppFoodAndBeverage } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "../QueryConstants";
import type { SelectOption } from "rizzui";

export const useMasterFnb = (
  pageIndex?: number,
  pageSize?: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetFnb, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex && pageSize) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppFoodAndBeverage({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};

export const useMasterFnbOption = (
  pageIndex?: number,
  pageSize?: number,
  filter?: string,
  sorting?: string,
  // refreshId?: string,
) => {
  return useQuery({
    queryKey: [
      QueryNames.GetFnb,
      pageIndex,
      pageSize,
      filter,
      sorting,
      // refreshId,
    ],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex && pageSize) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppFoodAndBeverage({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      // GENERATE OPTIONS
      const options: SelectOption[] =
        data?.items
          ?.filter((val) => val.typeFoodAndBeverage?.status?.name === "Active")
          ?.map((val) => ({
            label: `${val.name}`,
            value: val.id ?? "",
            id: val.id ?? 0,
            ...val,
          })) ?? [];

      // RETURN OPTIONS
      return [
        // { label: "--Select--", value: 0, disabled: true },
        ...options,
      ];
    },
  });
};
