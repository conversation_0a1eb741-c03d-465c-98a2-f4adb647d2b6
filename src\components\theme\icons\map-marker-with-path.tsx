export default function MapMarkerWithPathIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/currentColor/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#FFDB56"
        d="M4.913 19.707c1.753 0 3.173-.579 3.173-1.293s-1.42-1.293-3.173-1.293c-1.753 0-3.173.579-3.173 1.293s1.42 1.293 3.173 1.293Z"
      />
      <path
        fill="#FFBB24"
        d="M7.81 17.887c-.496.45-1.606.765-2.897.765s-2.401-.314-2.897-.765c-.177.161-.276.34-.276.528 0 .714 1.42 1.293 3.173 1.293 1.752 0 3.173-.58 3.173-1.293 0-.188-.1-.367-.276-.528Z"
      />
      <path
        fill="#FF4A4A"
        d="M4.913 6.305a4.61 4.61 0 0 0-4.61 4.61c0 3.878 4.61 7.445 4.61 7.445s4.61-3.567 4.61-7.445a4.61 4.61 0 0 0-4.61-4.61Zm0 6.449a1.839 1.839 0 1 1 0-3.678 1.839 1.839 0 0 1 0 3.678Z"
      />
      <path
        fill="#E7343F"
        d="M4.913 6.305c-.179 0-.355.01-.528.03a4.61 4.61 0 0 1 4.082 4.58c0 3.061-2.872 5.928-4.082 7 .323.286.528.445.528.445s4.61-3.567 4.61-7.445a4.61 4.61 0 0 0-4.61-4.61Z"
      />
      <path
        fill="#FFDB56"
        d="M16.154 10.593c1.347 0 2.439-.445 2.439-.994 0-.549-1.092-.994-2.44-.994-1.346 0-2.438.445-2.438.994 0 .55 1.092.994 2.439.994Z"
      />
      <path
        fill="#FFBB24"
        d="M18.22 9.07c-.432.28-1.195.466-2.066.466-.871 0-1.635-.186-2.066-.466-.236.153-.373.334-.373.528 0 .55 1.092.994 2.439.994s2.439-.445 2.439-.994c0-.194-.137-.375-.373-.528Z"
      />
      <path
        fill="#FF4A4A"
        d="M16.154.293a3.543 3.543 0 0 0-3.544 3.543c0 2.98 3.544 5.723 3.544 5.723s3.543-2.742 3.543-5.723A3.543 3.543 0 0 0 16.154.293Zm0 4.957a1.413 1.413 0 1 1 0-2.827 1.413 1.413 0 0 1 0 2.827Z"
      />
      <path
        fill="#E7343F"
        d="M16.154.293c-.18 0-.356.013-.528.04a3.544 3.544 0 0 1 3.015 3.503c0 2.255-2.028 4.373-3.015 5.27.318.29.528.452.528.452s3.543-2.742 3.543-5.722A3.543 3.543 0 0 0 16.154.293Z"
      />
      <path
        fill="currentColor"
        d="M4.913 6.012a4.91 4.91 0 0 0-4.904 4.903c0 1.68.799 3.484 2.374 5.365.234.28.467.539.69.775-1.016.278-1.627.776-1.627 1.358 0 1.03 1.786 1.587 3.467 1.587s3.466-.556 3.466-1.586c0-.582-.61-1.081-1.626-1.359A15.96 15.96 0 0 0 8.01 15.56a.293.293 0 1 0-.472-.348c-.5.68-1.042 1.276-1.515 1.75a.294.294 0 0 0-.128.127c-.423.416-.78.725-.982.894-.2-.168-.55-.472-.969-.883a.294.294 0 0 0-.147-.146C2.47 15.618.596 13.31.596 10.915a4.322 4.322 0 0 1 4.317-4.317 4.322 4.322 0 0 1 4.317 4.317c0 1.04-.354 2.163-1.053 3.337a.293.293 0 0 0 .505.3c.753-1.266 1.135-2.49 1.135-3.637a4.91 4.91 0 0 0-4.904-4.903Zm0 12.642c.063 0 .127-.02.18-.061.036-.028.526-.41 1.179-1.05.989.203 1.52.59 1.52.87 0 .21-.285.456-.746.643-.565.23-1.323.357-2.133.357-1.758 0-2.88-.592-2.88-1 0-.28.532-.668 1.522-.87a14.905 14.905 0 0 0 1.179 1.05.292.292 0 0 0 .179.06Z"
      />
      <path
        fill="currentColor"
        d="M3.226 10.127a.293.293 0 0 0-.363.201 2.135 2.135 0 0 0 2.05 2.721 2.134 2.134 0 0 0 2.132-2.132A2.135 2.135 0 0 0 3.637 9.21a.293.293 0 0 0 .351.47 1.547 1.547 0 0 1 2.47 1.239c0 .852-.693 1.545-1.545 1.545a1.547 1.547 0 0 1-1.486-1.973.293.293 0 0 0-.2-.363Zm15.66-.527c0-.465-.437-.852-1.192-1.08.149-.16.303-.332.457-.516 1.22-1.459 1.84-2.86 1.84-4.167A3.841 3.841 0 0 0 16.154 0c-1.545 0-2.933.92-3.535 2.343a.293.293 0 1 0 .54.229A3.246 3.246 0 0 1 16.154.587a3.254 3.254 0 0 1 3.25 3.25c0 2.423-2.544 4.745-3.25 5.343-.156-.132-.4-.347-.688-.628a.293.293 0 0 0-.13-.13c-1.013-1.02-2.432-2.772-2.432-4.585a.293.293 0 1 0-.587 0c0 1.306.62 2.708 1.84 4.167.155.184.308.356.457.516-.756.227-1.192.614-1.192 1.08 0 .392.318.738.897.974.495.202 1.147.313 1.835.313s1.34-.111 1.835-.313c.579-.236.897-.582.897-.974Zm-2.732.7c-1.388 0-2.146-.463-2.146-.7 0-.155.345-.439 1.08-.595.496.481.863.767.887.786a.292.292 0 0 0 .359 0c.023-.019.39-.305.886-.786.327.07.611.172.81.293.169.103.27.216.27.302 0 .237-.758.7-2.146.7Z"
      />
      <path
        fill="currentColor"
        d="M17.86 3.835c0-.94-.765-1.706-1.706-1.706s-1.707.765-1.707 1.706.766 1.707 1.707 1.707c.94 0 1.706-.766 1.706-1.707Zm-2.826 0a1.121 1.121 0 0 1 2.24 0 1.121 1.121 0 0 1-2.24 0ZM12.35 9.891a.293.293 0 0 0 0-.586h-.134c-.098 0-.196.006-.291.019a.293.293 0 0 0 .077.581c.07-.009.142-.014.214-.014h.134Zm-.97 8.214h-.002l-.387.002a.293.293 0 0 0 .002.587l.388-.002a.293.293 0 0 0-.001-.586Zm1.318-4.413a.293.293 0 1 0 0-.587h-.387a.293.293 0 1 0 0 .587h.387Zm-2.197-3.062a.293.293 0 0 0 .41-.068c.063-.088.136-.17.216-.245a.293.293 0 0 0-.397-.43c-.11.1-.21.212-.296.333a.293.293 0 0 0 .067.41Zm2.429 7.468h-.002l-.387.001a.293.293 0 0 0 .001.587h.002l.387-.002a.293.293 0 0 0-.001-.586Zm-3.099.015H9.83l-.388.002a.293.293 0 0 0 .002.587h.001l.387-.002a.293.293 0 0 0 0-.587Zm1.373-4.714a.293.293 0 0 0 .157-.541 1.613 1.613 0 0 1-.258-.202.293.293 0 0 0-.407.423c.108.103.226.196.352.275a.29.29 0 0 0 .156.045Zm-.564-1.59a1.62 1.62 0 0 1-.03-.308v-.017a.293.293 0 0 0-.29-.297h-.003c-.16 0-.292.13-.293.29v.023c0 .142.013.284.04.422a.293.293 0 1 0 .576-.112Zm3.607 1.883a.293.293 0 1 0 0-.587h-.387a.293.293 0 1 0 0 .587h.387Zm4.527.532a.293.293 0 0 0 .182-.524 2.788 2.788 0 0 0-.359-.24.293.293 0 1 0-.287.51c.098.056.194.12.283.19a.292.292 0 0 0 .181.064Zm-1.237 3.834c-.096.013-.195.02-.293.02h-.055a.293.293 0 0 0 .002.587h.056c.124 0 .248-.01.37-.026a.293.293 0 0 0-.08-.581Zm1.21-.582a2.208 2.208 0 0 1-.264.215.293.293 0 0 0 .334.482c.118-.082.23-.173.335-.273a.293.293 0 0 0-.405-.424Zm.986-1.416a.293.293 0 0 0-.343.234 2.198 2.198 0 0 1-.089.329.293.293 0 1 0 .553.197c.048-.136.086-.276.113-.418a.293.293 0 0 0-.234-.342Zm-.368-1.49a.293.293 0 0 0-.142.39c.048.103.088.21.12.32a.293.293 0 1 0 .563-.162 2.772 2.772 0 0 0-.152-.406.293.293 0 0 0-.39-.142Zm-3.568-.878a.293.293 0 1 0 0-.587h-.388a.293.293 0 1 0 0 .587h.388Zm-1.318 4.398-.388.002a.293.293 0 0 0 .001.586h.001l.388-.002a.293.293 0 0 0-.002-.586Zm2.756-4.398a2.317 2.317 0 0 1 .112.003.293.293 0 0 0 .013-.587 2.898 2.898 0 0 0-.125-.002h-.276a.293.293 0 1 0 0 .586h.276Zm-1.207 4.394h-.001l-.387.002a.293.293 0 0 0 0 .586h.002l.388-.002a.293.293 0 0 0-.002-.586Z"
      />
    </svg>
  );
}
