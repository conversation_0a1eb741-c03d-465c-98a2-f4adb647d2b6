import { DateInput } from "@/components/theme/ui/input-type/dates-input";
import { UploadFile } from "@/components/theme/ui/file-upload/upload-file";
import React, { useCallback, useEffect, useState } from "react";
import { type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";
import { Input, type SelectOption, Title } from "rizzui";
import { type Reservation } from "@/interfaces/reservations/reservation";
import { useApiMasterPaymentMethodOptions } from "@/lib/hooks/useApiMasterPaymentMethod";
import { useApiMasterDiningOptionsOptions } from "@/lib/hooks/useApiMasterDiningOptions";
import { useMasterStatusByDocTypeOptions } from "@/lib/hooks/useMasterStatusByDocType";
import { useApiAppReservationTypesOptions } from "@/lib/hooks/useMasterReservationTypes";
import { useApiMasterCompanyOptions } from "@/lib/hooks/useApiMasterCompany";
import { useApiAppGuestOptions } from "@/lib/hooks/useApiAppGuest";
import { AutocompleteSelect } from "@/components/theme/ui/input-type/autocomplete";
import DropdownInput from "@/components/theme/ui/input-type/dropdown-input";

export default function ReservationGroupDetail({
  isLoading = false,
  register,
  errors,
  setValue,
  getValues,
  watch,
}: {
  isLoading: boolean;
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  errors: FieldErrors<Reservation>;
}) {

  // OPTIONS
  const { data: paymentMethodOptions } = useApiMasterPaymentMethodOptions(0, 1000, "",);
  const { data: diningOptions } = useApiMasterDiningOptionsOptions(0, 1000, "",);
  const { data: statusResvOptions } = useMasterStatusByDocTypeOptions("reservations")
  const { data: statusResvDetOptions } = useMasterStatusByDocTypeOptions("reservationDetails")
  const { data: guestCriteriaOptions } = useMasterStatusByDocTypeOptions("reservationDetailsGuestCriteria")
  const { data: reservationTypesOptions } = useApiAppReservationTypesOptions(0, 1000, "")
  const { data: companyOptions } = useApiMasterCompanyOptions(0, 1000, "")
  const { data: dataGuestOptions } = useApiAppGuestOptions(0, 1000, "",)
  const [guestOptions, setGuestOptions] = useState<SelectOption[]>([])
  useEffect(() => { setGuestOptions(dataGuestOptions ?? []) }, [dataGuestOptions])

  // INIT
  const init = useCallback(() => {
    register("reservationDetails.0.rfid", { value: "" })
    // register("reservationTypeId", { value: "9396072e-f88f-b34d-09a8-3a196d18238a" })
  }, [])
  useEffect(() => { init() }, [init])

  // INIT STATUS ID
  useEffect(() => {
    const statusId = statusResvOptions?.find(opt => opt.code == "open")?.value // code fore Open
    setValue("statusId", statusId ?? 0)
  }, [statusResvOptions])

  // INIT RESERVATION TYPE ID
  useEffect(() => {
    const reservationTypesId = reservationTypesOptions?.find(opt => opt.name == "GROUP (GRP)") // code fore Individu
    setValue("reservationTypeId", reservationTypesId?.value ?? 0)
    setValue("groupCode", reservationTypesId?.label ?? 0)
  }, [reservationTypesOptions])

  // INIT STATUS RESERVATION ID
  useEffect(() => {
    const statusId = statusResvDetOptions?.find(opt => opt.code == "reserved")?.value // code fore Reserved
    setValue("reservationDetailsStatus", statusId ?? 0)
    // setValue("reservationDetails.0.statusId", statusId ?? 0)
  }, [statusResvDetOptions])


  // UPDATE GUEST WHEN IDENTITY NUMBER CHANGED
  const identityNumber = watch("reservationDetails.0.guest.identityNumber") as string
  useEffect(() => {
    const selectedOpt = guestOptions?.find(opt => opt.value == identityNumber)
    if (selectedOpt?.fullname) {
      setValue("reservationDetails.0.guestId", selectedOpt.id)
      setValue("reservationDetails.0.guest", selectedOpt)
    } else {
      setValue("reservationDetails.0.guestId", null)
      setValue("reservationDetails.0.guest.fullname", null)
      setValue("reservationDetails.0.guest.nationality", null)
      setValue("reservationDetails.0.guest.phoneNumber", null)
      setValue("reservationDetails.0.guest.companyName", null)
    }
  }, [identityNumber])

  const onChange = () => {
    const identityNumber = getValues("reservationDetails.0.guest.identityNumber") as string
    const tempOpt: SelectOption = { label: identityNumber, value: 0, id: 0 }
    setGuestOptions(prev => [tempOpt, ...prev.slice(1)])
  }

  return (
    <div className="">
      <div className="grid grid-cols-4 gap-2">
        {/* RESERVATION DETAIL */}
        <Title as="h6" className="mb-0 col-span-4">
          Reservation Detail
        </Title>
        <Input
          label="Booker Identity Number"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Booker Identity Number"
          {...register("bookerIdentityNumber", { required: true })}
          error={errors.bookerIdentityNumber ? "Booker Identity Number is required" : undefined}
          className="w-full"
        />
        <Input
          label="Booker Name"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Booker Name"
          {...register("bookerName", { required: true })}
          error={errors.bookerName ? "Booker Name is required" : undefined}
          className="w-full"
        />
        <AutocompleteSelect
          label={"Company Name"}
          size="sm"
          name={"companyId"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          onChange={onChange}
          options={companyOptions}
          required={true}
        />
        <Input
          label="Email"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Email"
          {...register("bookerEmail", { required: true })}
          error={errors.bookerEmail ? "Email is required" : undefined}
          className="w-full"
        />
        <Input
          label="Phone Number"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Phone Number"
          {...register("bookerPhoneNumber", { required: true })}
          error={errors.bookerPhoneNumber ? "Phone Number is required" : undefined}
          className="w-full"
        />
        <DateInput
          label={"Arrival Date"}
          size="sm"
          name={"arrivalDate"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          required={true}
        />
        <DropdownInput
          label={"Payment Method"}
          size="sm"
          name={"paymentMethodId"}
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={paymentMethodOptions}
          required={true}
        />
        <Input
          label="Days"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in days"
          {...register("days", { required: true })}
          error={errors.days ? "days is required" : undefined}
          className="w-full"
        />
        {/* <Input
          label="Group Code"
          size="sm"
          disabled={isLoading}
          placeholder="Please fill in Group Code"
          {...register("groupCode", { required: true })}
          error={errors.groupCode ? "Group Code is required" : undefined}
          className="w-full"
        /> */}
        <DropdownInput
          label={"Dining Options"}
          name={"diningOptionsId"}
          size="sm"
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={diningOptions}
          required={true}
        />
        <DropdownInput
          label={"Criteria Options"}
          name={"criteriaId"}
          size="sm"
          register={register}
          setValue={setValue}
          errors={errors}
          watch={watch}
          getValues={getValues}
          options={guestCriteriaOptions}
          required={true}
        />
        <UploadFile
          label={"Upload Document Reservation"}
          name={"attachment"}
          setValue={setValue}
          getValues={getValues}
          register={register}
          errors={errors}
          // required={true}
          accept="application/pdf"
          size="sm"
        />
      </div>
    </div>
  );
}
