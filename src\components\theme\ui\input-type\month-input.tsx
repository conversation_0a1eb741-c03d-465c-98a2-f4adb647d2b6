import { Text } from "rizzui";
import DropdownInput from "./dropdown-input";
import { type FieldErrors, type FieldValues, type UseFormGetValues, type UseFormRegister, type UseFormSetValue, type UseFormWatch } from "react-hook-form";


interface DateInputProps {
  label?: string;
  name: string;
  className?: string;
  names?: string[];
  register: UseFormRegister<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  errors: FieldErrors<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  required?: boolean;
  readOnly?: boolean;
  inline?: boolean;
  minDate?: Date | undefined;
  maxDate?: Date | undefined;
  size?: "sm" | "md" | "lg" | undefined;
  dateClassName?: string;
  popperPlacement?:
  | "bottom-start"
  | "top"
  | "right"
  | "bottom"
  | "left"
  | "top-start"
  | "top-end"
  | "right-start"
  | "right-end"
  | "bottom-end"
  | "left-start"
  | "left-end";
  onChange?: (date: Date | null, name: string) => void;
}


export function MonthInput(props: DateInputProps) {
  const options = [
    {
      label: "Januari",
      value: "01",
    },
    {
      label: "Februari",
      value: "02",
    },
    {
      label: "Maret",
      value: "03",
    },
    {
      label: "April",
      value: "04",
    },
    {
      label: "Mei",
      value: "05",
    },
    {
      label: "Juni",
      value: "06",
    },
    {
      label: "Juli",
      value: "07",
    },
    {
      label: "Agustus",
      value: "08",
    },
    {
      label: "September",
      value: "09",
    },
    {
      label: "Oktober",
      value: "10",
    },
    {
      label: "November",
      value: "11",
    },
    {
      label: "Desember",
      value: "12",
    },
  ]

  return (
    <div className={props.className ?? ""}>
      {props.inline && (
        <Text as="p" className={"mb-1.5 font-medium " + ("text-" + props.size)}>
          {props.label}
        </Text>
      )}
      <div className={"" + props.readOnly && "cursor-not-allowed"}>
        <DropdownInput
          className="col-span-2"
          label={"Month"}
          name={props.name}
          register={props.register}
          setValue={props.setValue}
          errors={props.errors}
          watch={props.watch}
          getValues={props.getValues}
          options={options}
          required={true}
          size={props.size}
        />
        {/* <DatePicker
          popperPlacement={props.popperPlacement ?? "bottom-start"}
          selected={date}
          onChange={onChange}
          selectsStart
          showMonthYearPicker
          dateFormat="yyyy-MM"
          placeholderText={props.label}
          className={cn(
            props.dateClassName,
            "date-picker-event-calendar w-full",
            props.readOnly &&
            "cursor-not-allowed rounded-md bg-[#e3e3e3b3] text-gray-700",
          )}
          readOnly={props.readOnly}
          inputProps={{
            error:
              get(props.errors, props.name) && !props.getValues(props.name)
                ? props.label + " is required"
                : undefined,
            label: props.label,
            size: props.size ?? "md",
          }}
          inline={props.inline}
          minDate={props.minDate}
          maxDate={props.maxDate}
        /> */}
      </div>
    </div>
  );
}
