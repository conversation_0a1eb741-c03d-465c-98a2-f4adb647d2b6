import React, { useEffect } from "react";
import { Input } from "rizzui";
import { formatCurrencyIDR } from "@/lib/helper/format-currency-IDR";
import { type InputProps } from "@/interfaces/form/inputType";
import { get } from "lodash";

export default function CurrencyIDR(props: InputProps) {
  const rawValue = String(props.watch(props.name)) || "";
  const [numericValue, setNumericValue] = React.useState<string>(rawValue.replace(/[^\d]/g, ""));
  // let numericValue = rawValue.replace(/[^\d]/g, ""); // Hanya angka

  useEffect(() => {
    // // console.log("props.getValues", props.getValues(props.name));
    setNumericValue(rawValue.replace(/[^\d]/g, rawValue));
  }, [props.watch(props.name)])

  return (
    <div className={props.className ?? ""}>
      <Input
        size={props.size}
        label={props.label ?? ""}
        disabled={props.disabled}
        placeholder={"Please fill in " + props.label}
        ref={props.register(props.name, { required: true }).ref} // Hanya gunakan ref
        value={numericValue ? formatCurrencyIDR(numericValue) : ""}
        onChange={(e) => {
          const onlyNumbers = e.target.value.replace(/[^\d]/g, "");
          props.setValue(props.name, Number(onlyNumbers), {
            shouldValidate: true,
          }); // Simpan angka murni
        }}
        error={
          get(props.errors, props.name) ? `${props.label} is required` : undefined
        }
        className={"w-full"}
        inputClassName={`bg-white ${props.inputClassName}`}
      />
    </div>
  );
}
