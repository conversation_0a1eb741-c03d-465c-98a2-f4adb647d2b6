import { getApiAppTypeFoodAndBeverage } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "../QueryConstants";

export const useMasterFnbType = (
  pageIndex?: number,
  pageSize?: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [QueryNames.GetFnbType, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex && pageSize) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppTypeFoodAndBeverage({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};
