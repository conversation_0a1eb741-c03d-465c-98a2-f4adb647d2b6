export default function DinnerIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fill="#DD636E"
        d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0Z"
      />
      <path
        fill="#DA4A54"
        d="M10 18.912c-5.34 0-9.702-4.186-9.985-9.456C.005 9.636 0 9.818 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10c0-.182-.005-.364-.015-.544-.283 5.27-4.645 9.456-9.985 9.456Z"
      />
      <path
        fill="#EAEAEA"
        d="M6.256 4c-1.43 0-2.589 2.288-2.589 4.452 0 1.062.565 1.983 1.393 2.443.422.234.683.68.66 1.164l-.317 6.824a9.932 9.932 0 0 0 1.739.702l-.35-7.527c-.022-.482.238-.929.66-1.163.828-.46 1.393-1.38 1.393-2.443C8.845 6.288 7.686 4 6.256 4Z"
      />
      <path
        fill="#CBCBCB"
        d="m5.453 17.82-.05 1.063a9.932 9.932 0 0 0 1.739.702l-.051-1.104a9.94 9.94 0 0 1-1.638-.66Z"
      />
      <path
        fill="#EAEAEA"
        d="M15.386 4.463a.489.489 0 0 0-.977.037l.082 3.598a.454.454 0 1 1-.907.015l-.038-3.7a.416.416 0 0 0-.832 0l-.038 3.7a.454.454 0 1 1-.907-.015l.082-3.618a.469.469 0 0 0-.937-.036l-.218 4.177a2.46 2.46 0 0 0 1.277 2.287c.418.23.662.685.64 1.162l-.356 7.674a9.918 9.918 0 0 0 1.759-.584l-.33-7.094a1.252 1.252 0 0 1 .64-1.16 2.456 2.456 0 0 0 1.277-2.285l-.217-4.158Z"
      />
      <path
        fill="#CBCBCB"
        d="m12.308 18.644-.051 1.1a9.923 9.923 0 0 0 1.759-.583l-.05-1.067a9.93 9.93 0 0 1-1.658.55Z"
      />
    </svg>
  );
}
