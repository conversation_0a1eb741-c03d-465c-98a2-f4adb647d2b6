import React from "react";
import { contrastingColor } from "@/lib/helper/format-color";

export default function StatusColor({
  color,
  name = "N/A",
}: {
  color?: string | null;
  name?: string | null;
}) {
  return (
    <span
      className="rounded-md px-4 py-1 text-center text-xs font-normal"
      style={{
        backgroundColor: color ?? "#fff",
        color: contrastingColor(color ?? "#fff", -50),
        display: "inline-block",
      }}
    >
      {name}
    </span>
  );
}
