﻿import {
  postApiReportExportExcel,
  postApiReportList,
} from "./../../client/sdk.gen";
import type {
  FilterGroup,
  ReportDto,
  ReportExecutionDto,
  SortInfo,
} from "@/client";
import { getApiReportById } from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";

export const useApiReport = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
  refreshId?: string,
) => {
  return useQuery({
    queryKey: [
      QueryNames.postApiReportList,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
      refreshId,
    ],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await postApiReportList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      return data;
    },
  });
};

export const useApiReportById = (id: string, refreshId?: string) => {
  // if(id == "0") return [];
  return useQuery({
    queryKey: [QueryNames.getApiReportById, id, refreshId],
    queryFn: async () => {
      const { data } = await getApiReportById({
        path: { id },
      });
      return data;
    },
    enabled: id !== "create", // mencegah fetch jika id adalah "create"
    initialData: id === "create" ? ({} as ReportDto) : undefined, // mengembalikan [] jika id "create"
  });
};

export const useApiReportExportExcel = (body: ReportExecutionDto) => {
  return useQuery({
    queryKey: [QueryNames.postApiReportExportExcel],
    queryFn: async () => {
      const { data } = await postApiReportExportExcel({
        body: body,
      });

      return data;
    },
  });
};
