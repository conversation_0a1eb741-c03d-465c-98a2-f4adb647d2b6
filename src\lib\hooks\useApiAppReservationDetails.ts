﻿﻿import {
  getApiAppReservationDetails,
  postApiAppReservationDetailsList,
} from "@/client";
import { useQuery } from "@tanstack/react-query";
import { QueryNames } from "./QueryConstants";
import type { SelectOption } from "rizzui";
import type { FilterGroup, SortInfo } from "@/client";

export const useApiAppReservationDetails = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
) => {
  return useQuery({
    queryKey: [
      QueryNames.getApiAppReservationDetails,
      pageIndex,
      pageSize,
      filter,
      sorting,
    ],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppReservationDetails({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      return data;
    },
  });
};

export const useApiAppReservationDetaislList = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
) => {
  return useQuery({
    queryKey: [
      QueryNames.getApiAppReservationDetails,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
    ],
    queryFn: async () => {
      // let skip = 0;
      // if (pageIndex > 0) {
      //   skip = pageIndex * pageSize;
      // }
      const { data } = await postApiAppReservationDetailsList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      return data;
    },
  });
};

export const useApiAppReservationDetailOptions = (
  pageIndex: number,
  pageSize: number,
  filter?: string,
  sorting?: string,
  refreshId?: string,
) => {
  return useQuery({
    queryKey: [
      QueryNames.getApiAppReservationDetails,
      pageIndex,
      pageSize,
      filter,
      sorting,
      refreshId,
    ],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await getApiAppReservationDetails({
        query: {
          MaxResultCount: pageSize,
          SkipCount: skip,
          Sorting: sorting,
        },
      });

      // GENERATE OPTIONS
      const options: SelectOption[] =
        data?.items?.map((val) => ({
          label: `${val.id}`,
          value: val.id ?? "",
          id: val.id ?? 0,
          ...val,
        })) ?? [];

      // RETURN OPTIONS
      return [
        // { label: "--Select--", value: 0, disabled: true },
        ...options,
      ];
    },
  });
};

export const useApiAppReservationDetailsList = (
  pageIndex: number,
  pageSize: number,
  filter?: FilterGroup,
  sorting?: string,
  sort?: Array<SortInfo> | null,
  refreshId?: string | null,
) => {
  return useQuery({
    queryKey: [
      QueryNames.postApiAppReservationDetailsList,
      pageIndex,
      pageSize,
      filter,
      sorting,
      sort,
      refreshId,
    ],
    queryFn: async () => {
      let skip = 0;
      if (pageIndex > 0) {
        skip = pageIndex * pageSize;
      }
      const { data } = await postApiAppReservationDetailsList({
        body: {
          sorting: sorting,
          page: pageIndex,
          sort: sort,
          filterGroup: filter,
          maxResultCount: pageSize,
          // skipCount: skip,
          // SkipCount: skip,
        },
      });

      return data;
    },
  });
};
