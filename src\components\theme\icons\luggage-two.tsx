export default function LuggageTwoIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_4003_26629)">
        <path
          d="M13.9999 5.33333H11.9999V1.66667H12.6666C12.8507 1.66667 12.9999 1.51742 12.9999 1.33333V0.333333C12.9999 0.149251 12.8507 0 12.6666 0H7.33325C7.14917 0 6.99992 0.149251 6.99992 0.333333V1.33333C6.99992 1.51742 7.14917 1.66667 7.33325 1.66667H7.99992V5.33333H5.99992C5.07983 5.33447 4.33439 6.07992 4.33325 7V17.3333C4.33439 18.2534 5.07983 18.9989 5.99992 19V19.6667C5.99992 19.8507 6.14917 20 6.33325 20H7.66659C7.85067 20 7.99992 19.8507 7.99992 19.6667V19H11.9999V19.6667C11.9999 19.8507 12.1492 20 12.3333 20H13.6666C13.8507 20 13.9999 19.8507 13.9999 19.6667V19C14.92 18.9989 15.6654 18.2534 15.6666 17.3333V7C15.6654 6.07992 14.92 5.33447 13.9999 5.33333ZM7.66659 0.666667H12.3333V1H7.66659V0.666667ZM8.66659 1.66667H11.3333V5.33333H8.66659V1.66667ZM7.33325 19.3333H6.66659V19H7.33325V19.3333ZM13.3333 19.3333H12.6666V19H13.3333V19.3333ZM14.9999 17.3333C14.9999 17.8856 14.5522 18.3333 13.9999 18.3333H5.99992C5.44767 18.3333 4.99992 17.8856 4.99992 17.3333V7C4.99992 6.44775 5.44767 6 5.99992 6H13.9999C14.5522 6 14.9999 6.44775 14.9999 7V17.3333Z"
          fill="currentColor"
        />
        <path
          d="M9.99984 6.66699C9.81576 6.66699 9.6665 6.81624 9.6665 7.00033V17.3337C9.6665 17.5177 9.81576 17.667 9.99984 17.667C10.1839 17.667 10.3332 17.5177 10.3332 17.3337V7.00033C10.3332 6.81624 10.1839 6.66699 9.99984 6.66699Z"
          fill="currentColor"
        />
        <path
          d="M12.6668 7.33301C12.4827 7.33301 12.3335 7.48226 12.3335 7.66634V16.6663C12.3335 16.8504 12.4827 16.9997 12.6668 16.9997C12.8509 16.9997 13.0002 16.8504 13.0002 16.6663V7.66634C13.0002 7.48226 12.8509 7.33301 12.6668 7.33301Z"
          fill="currentColor"
        />
        <path
          d="M7.33333 7.33301C7.14925 7.33301 7 7.48226 7 7.66634V16.6663C7 16.8504 7.14925 16.9997 7.33333 16.9997C7.51742 16.9997 7.66667 16.8504 7.66667 16.6663V7.66634C7.66667 7.48226 7.51742 7.33301 7.33333 7.33301Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_4003_26629">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
