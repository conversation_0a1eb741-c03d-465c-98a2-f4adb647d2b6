import React, { useEffect, useState } from "react";
import ButtonReset from "@/components/container/button/button-reset";
import CurrencyIDR from "@/components/theme/ui/input-type/currency-IDR";
import { useMasterStatus } from "@/lib/hooks/rooms/masterStatus/useMasterStatus";
import { useMasterType } from "@/lib/hooks/rooms/masterType/useMasterType";
import { Button, Input, Select, type SelectOption } from "rizzui";
import type { FormProps } from "@/interfaces/form/formType";
import type { SelectOptionType } from "@/interfaces/form/selectOptionType";
import type { SingleValue } from "react-select";
import { useGrantedPolicies } from "@/lib/hooks/useGrantedPolicies";

export default function Form({
  isLoading,
  onSubmit,
  register,
  errors,
  handleSubmit,
  setValue,
  getValues,
  watch,
  onDelete,
  handleReset,
  isRoomStatus,
  setRoomStatus,
  isRoomType,
  setRoomType,
  roomStatusOptions,
  roomTypeOptions,
}: FormProps & {
  handleReset: () => void;
  isRoomStatus: SelectOption | undefined;
  setRoomStatus: (option: SelectOption) => void;
  isRoomType: SelectOption | undefined;
  setRoomType: (option: SelectOption) => void;
  roomStatusOptions: SelectOptionType[];
  roomTypeOptions: SelectOptionType[];
}) {
  const { can } = useGrantedPolicies();
  // const [isRoomStatusOpt, setRoomStatusOpt] = useState<SelectOptionType[]>([]);
  // const [isRoomTypeOpt, setRoomTypeOpt] = useState<SelectOptionType[]>([]);

  // const { data: masterStatusData } = useMasterStatus();
  // const { data: masterTypeData } = useMasterType();
  // useEffect(() => {
  //   if (masterStatusData?.items) {
  //     const mappedStatusOptions = masterStatusData.items.map((item) => ({
  //       label: item.name ?? "",
  //       value: item.id ?? "",
  //     }));
  //     setRoomStatusOpt(mappedStatusOptions);
  //   }

  //   if (masterTypeData?.items) {
  //     const mappedTypeOptions = masterTypeData.items.map((item) => ({
  //       label: item.name ?? "",
  //       value: item.id ?? "",
  //     }));
  //     setRoomTypeOpt(mappedTypeOptions);
  //   }
  // }, [masterStatusData, masterTypeData]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-7 gap-2">
        <div>
          <Input
            size="sm"
            label="Room Number"
            disabled={isLoading}
            placeholder="Please fill in Room Number"
            {...register("roomNumber", { required: true })}
            error={errors.roomNumber ? "Room Number is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Room Code"
            disabled={isLoading}
            placeholder="Please fill in Room Code"
            {...register("roomCode", { required: true })}
            error={errors.roomCode ? "Room Code is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Room Type"
            options={roomTypeOptions}
            value={isRoomType}
            onChange={(newValue: unknown) => {
              const selectedValue = newValue as SingleValue<SelectOptionType>;
              if (selectedValue) {
                setRoomType(selectedValue);
                setValue("roomTypeId", selectedValue.value);
              }
            }}
            disabled={isLoading}
            placeholder="Please select Room Type"
            error={errors.roomTypeId ? "Room Type is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Size"
            disabled={isLoading}
            placeholder="Please fill in Size"
            {...register("size", { required: true })}
            error={errors.size ? "Size is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <CurrencyIDR
            size={"sm"}
            label="Price"
            name="price"
            register={register}
            setValue={setValue}
            errors={errors}
            watch={watch}
            getValues={getValues}
          />
        </div>
        <div>
          <Select
            size="sm"
            label="Room Status"
            options={roomStatusOptions}
            value={isRoomStatus}
            onChange={(newValue: unknown) => {
              const selectedValue = newValue as SingleValue<SelectOptionType>;
              if (selectedValue) {
                setRoomStatus(selectedValue);
                setValue("roomStatusId", selectedValue.value);
                // console.log(selectedValue.label);
              }
            }}
            disabled={isLoading}
            placeholder="Please select Room Status"
            error={errors.roomStatusId ? "Room Status is required" : undefined}
            className="w-full"
          />
        </div>
        <div>
          <Input
            size="sm"
            label="Information"
            disabled={isLoading}
            placeholder="Please fill in Information"
            {...register("information", { required: true })}
            error={errors.information ? "Information is required" : undefined}
            className="w-full"
          />
        </div>
      </div>

      <div className="flex justify-end gap-2">
        {(can("WismaApp.Room.Create") && can("WismaApp.Room.Edit")) &&
          <Button
            size="sm"
            type="submit"
            disabled={isLoading}
            className={`rounded-lg px-4 py-2 text-white disabled:bg-gray-400 ${getValues("id") ? "bg-blue-500" : "bg-green-500"
              }`}
          >
            {getValues("id") ? "Update" : "Create"}
          </Button>
        }
        {(getValues("id") && can("WismaApp.Room.Delete")) && (
          <Button
            size="sm"
            disabled={isLoading}
            className="rounded-lg bg-red-500 px-4 py-2 text-white disabled:bg-gray-400"
            onClick={() => {
              onDelete(String(getValues("id")));
            }}
          >
            Delete
          </Button>
        )}
        <ButtonReset isLoading={isLoading} handleReset={handleReset} />
      </div>
    </form>
  );
}
