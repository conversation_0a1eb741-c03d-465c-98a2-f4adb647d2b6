export default function SkyscraperIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="-52 0 511 512" {...props}>
      <path fill="#ffe6c0" d="M205.26 28.52 157.1 74.16h96.35zm0 0" />
      <path
        fill="#ffe6c0"
        d="M253.44 74.16h-96.35s2.3 28.89-27.16 28.89v90.1h150.66v-90.1c-29.46 0-27.15-28.9-27.15-28.9zm0 0"
      />
      <path fill="#ffcb7c" d="M91.54 193.16h225.24v223.03H91.54zm0 0" />
      <g fill="#ff9d6c">
        <path d="M53.01 416.19h302.3V502H53zm0 0" />
        <path d="M355.3 451.66h42.01V502h-42zM10.5 451.66h42.51V502H10.5zm0 0" />
      </g>
      <path fill="#98a1b3" d="M183.8 460.43h42.92V502H183.8zm0 0" />
      <path d="M397.31 441.65h-32V416.2a10 10 0 0 0-10-10h-28.53V193.16a10 10 0 0 0-10-10h-26.19v-80.11a10 10 0 0 0-10-10c-5.9 0-10.1-1.42-12.85-4.35-3.99-4.25-4.48-11.4-4.33-13.81a10.1 10.1 0 0 0-3.1-8l-45.05-42.68V10a10 10 0 1 0-20 0v14.21l-44.59 42.25a10.02 10.02 0 0 0-3.56 8.42c.15 2.4-.33 9.57-4.32 13.82-2.75 2.92-6.95 4.35-12.86 4.35a10 10 0 0 0-10 10v80.11h-28.4a10 10 0 0 0-10 10v213.03H53.02a10 10 0 0 0-10 10v25.46H10.5a10 10 0 0 0-10 10V502a10 10 0 0 0 10 10h386.81a10 10 0 0 0 10-10v-50.35a10 10 0 0 0-10-10zM205.26 42.3l23.08 21.87h-46.16zM139.93 112c8.63-1.9 14.21-6.12 17.62-9.8 5.27-5.72 7.68-12.49 8.76-18.04h77.9c1.08 5.55 3.49 12.32 8.77 18.04 3.4 3.68 8.98 7.9 17.61 9.8v71.16H139.93zm-38.4 91.16h205.25v203.03h-26.19V232.15a10 10 0 0 0-20 0v174.04h-23.91V232.15a10 10 0 0 0-20 0v174.04h-23.91v-41.17a10 10 0 0 0-20 0v41.17h-23.92V232.15a10 10 0 1 0-20 0v174.04h-27.31zM42.52 492H20.5v-30.35h22.01zm20.5-65.81H345.3V492H236.72v-31.57a10 10 0 0 0-10-10H183.8a10 10 0 0 0-10 10V492H63v-65.81zM193.81 492v-21.57h22.92V492zm193.5 0h-22v-30.35h22zm0 0" />
      <path d="M148.66 458.93a10.1 10.1 0 0 0-8.38-7.94 10.08 10.08 0 0 0-10.32 5.32 10.1 10.1 0 0 0 1.6 11.4c2.8 3.01 7.32 3.98 11.12 2.4a10.08 10.08 0 0 0 5.98-11.18zM108.48 457.05a10.09 10.09 0 0 0-9.33-6.16c-4.2.04-8 2.8-9.35 6.77-1.36 4 0 8.59 3.37 11.15a10.1 10.1 0 0 0 11.44.51 10.06 10.06 0 0 0 3.87-12.27zM300.96 464.7a10.07 10.07 0 0 0 9.7 6.17c4.14-.2 7.76-3 9.04-6.94a10.07 10.07 0 0 0-3.64-11.14 10.09 10.09 0 0 0-11.23-.35 10.07 10.07 0 0 0-3.87 12.26zM261.36 464.7a10.09 10.09 0 0 0 9.56 6.18c4.24-.15 8-3.05 9.24-7.1a10.1 10.1 0 0 0-3.74-11.03 10.12 10.12 0 0 0-11.22-.28 10.07 10.07 0 0 0-3.84 12.23zM182.77 283.77a10 10 0 0 0 10-10v-41.62a10 10 0 1 0-20 0v41.62a10 10 0 0 0 10 10zM184.77 123.4h40.77a10 10 0 1 0 0-20h-40.77a10 10 0 0 0 0 20zM240.91 141.62h-71.98a10 10 0 0 0 0 20h71.98a10 10 0 1 0 0-20zM188.46 311.33a10.08 10.08 0 0 0-11.76.49 10.08 10.08 0 0 0-3.35 10.73 10.1 10.1 0 0 0 9.17 7.1 10.1 10.1 0 0 0 9.75-6.48c1.63-4.34 0-9.24-3.8-11.84zm0 0" />
    </svg>
  );
}
